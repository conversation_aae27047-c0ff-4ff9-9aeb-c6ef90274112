import Typography from "@mui/material/Typography";
import { useContext, useState, React, Fragment, useEffect } from "react";

// ** Custom Components Imports
import AccordionBasic from "src/@core//components/custom-components/AccordionBasic";
import authConfig from "src/configs/auth";
// ** Demo Components Imports
import { useTheme } from "@emotion/react";

// ** Styled Component
import {
  Card,
  Table,
  TableBody,
  TableContainer,
  TableRow,
  TableCell,
  Tooltip,
} from "@mui/material";
import ProfileEdit from "./ProfileEdit";
import { AuthContext } from "src/context/AuthContext";
import { DataGrid } from "@mui/x-data-grid";
import CustomChip from "src/@core/components/mui/chip";
const userStatusObj = {
  true: "Active",
  false: "InActive",
};
const fieldLabelStyle = {
  fontSize: "12.75px",
};

const tablecellLabelStyle = {
  width: { xs: "50%", sm: "50%", md: "40%" },
  padding: "4px",
  textAlign: "right",
  verticalAlign: "middle",
  textIndent: { lg: "80px", md: "80px", sm: "100px", xs: "0" },
};

const tablecellValueStyle = {
  justifyContent: "flex-start",
  alignItems: "center",
  width: { xs: "50%", sm: "50%", md: "60%" },
  height: "10px",
};

const fieldValueStyle = {
  fontWeight: "bold",
  wordWrap: "break-word",
  whiteSpace: "pre-wrap",
  color: "#108A00",
  fontSize: "14px",
  lineHeight: "1.2",
};

const ProfileView = ({ data, userData, employeesData }) => {
  const { listValues,getAllListValuesByListNameId } = useContext(AuthContext);

  const servicesProvidedNames = data?.servicesProvided
    ?.map((serviceId) => {
      const serviceObject = listValues?.find((item) => item.id === serviceId);
      return serviceObject ? serviceObject.name : null;
    })
    .filter(Boolean);

    const areaOfOperationNames = data?.areaOfOperation
    ?.map((areaId) => {
      const serviceObject = listValues?.find((item) => item.id === areaId);
      return serviceObject ? serviceObject.name : null;
    })
    .filter(Boolean);

    const clientNames = data?.typeOfClientServed
    ?.map((serviceId) => {
      const serviceObject = listValues?.find((item) => item.id === serviceId);
      return serviceObject ? serviceObject.name : null;
    })
    .filter(Boolean);

    const salesTeamMembers = data?.noOfSalesTeamMembers
    ? listValues?.find((item) => item.id === data?.noOfSalesTeamMembers)?.name
    : null;

    const companyType = data?.companyType
    ? listValues?.find((item) => item.id === data?.companyType)?.name
    : null;

  const locationName = data?.locationId
    ? listValues?.find((item) => item.id === data.locationId)?.name
    : null;

  const portalsRegisteredName = data?.portalsRegistered
    ? listValues?.find((item) => item.id === data.portalsRegistered)?.name
    : null;

  const designation = data?.designation
    ? listValues?.find((item) => item.id === data?.designation)?.name
    : null;

  const leadStatus = data?.leadStatus
    ? listValues?.find((item) => item?.id === data?.leadStatus)?.name
    : null;

  const referenceType = data?.referenceType
    ? listValues?.find((item) => item?.id === data?.referenceType)?.name
    : null;

  const referralName = data?.referralName
    ? listValues?.find((item) => item?.id === data?.referralName)?.name
    : null;
  const leadPriority = data?.leadPriority
    ? listValues?.find((item) => item?.id === data?.leadPriority)?.name
    : null;

  const portalsRegistered = data?.portalsRegistered
    ? listValues?.find((item) => item?.id === data?.portalsRegistered)?.name
    : null;

  const [assignedTo, setAssignedTo] = useState(data?.assignedTo);
  const handleAssignedToChange = (event) => {
    const selectedId = event.target.value;
    setAssignedTo(selectedId);
  };

  useEffect(() => {
    if (!!data && !!data.assignedTo) {
      setAssignedTo(data.assignedTo);
    }
  }, [data]);

  const [assignedToName, setAssignedToName] = useState("");
  const [curatedName,setCuratedName] = useState("")
  useEffect(() => {
    if (!!assignedTo && employeesData && employeesData.length > 0) {
      setAssignedToName(
        employeesData?.find((item) => item.id == assignedTo)?.name
      );
      setCuratedName(
        employeesData?.find((item) => item.id == data?.curatedBy)?.name
      );
    }
  }, [assignedTo,data?.curatedBy, employeesData]);

  const [areaOfOperationList, setAreaOfOperationList] = useState([]);
  const [typeOfClientServedOptions, setTypeOfClientServedOptions] = useState(
    []
  );

  const handleError = (error) => {
    console.error("Basic profile: All Services:", error);
  };
    useEffect(() => {
      if (!!authConfig) {
        getAllListValuesByListNameId(
          authConfig.areaOfOperation,
          (data) =>
            setAreaOfOperationList(
              data?.listValues?.map((item) => ({
                value: item.id,
                key: item.listValue,
              }))
            ),
          handleError
        );    
        getAllListValuesByListNameId(
          authConfig.typeOfProjects,
          (data) =>
            setTypeOfClientServedOptions(
              data?.listValues?.map((item) => ({
                value: item.id,
                key: item.listValue,
              }))
            ),
          handleError
        );
       
      }
    }, [authConfig]);

  const theme = useTheme();

  const [state, setState] = useState("view");

  const viewClick = () => {
    setState("edit");
  };

  const editClick = () => {
    setState("view");
  };

  const portalRegister = listValues?.find(
    (portalsRegistered) => portalsRegistered?.id === data?.portalsRegistered
  )?.name;

  const sections = [
    {
      title: "Company Information",
      fields: [
        {
          label: "Name of the Company",
          variable: "companyName",
        },{
          label: "Company Type",
          variable: "companyType",
        },
        {
          label: "Type of Vendor",
          variable: "servicesProvided",
        },
        ...(servicesProvidedNames?.includes('Any other')
          ? [
              {
                label: "Other Service",
                variable: "anyOtherServiceProvided",
              },
            ]
          : []),
        {
          label: "Individual Name",
          variable: "individualName",
        },
        {
          label: "Designation",
          variable: "designation",
        },
        ...(data?.otherDesignation
          ? [
              {
                label: "Other Designation",
                variable: "otherDesignation",
              },
            ]
          : []),
        {
          label: "Mobile Number",
          variable: "mobileNumber",
        },
        {
          label: "Alternate Mobile Number",
          variable: "alternateMobileNumber",
        },
        {
          label: "Email",
          variable: "email",
        },
        {
          label: "Website",
          variable: "websiteUrl",
        },
        {
          label: "Social Media Presence",
          variable: "socialMediaPresence",
        },
        {
          label: "Area Of Operation",
          variable: "areaOfOperation",
        }
      ],
    },
    {
      title: "Business Information",
      fields: [
        {
          label: "Portals Registered",
          variable: "portalsRegistered",
        },
        ...(portalRegister === "any other"
          ? [
              {
                label: "Other Portals Registered",
                variable: "anyOtherPortalRegistered",
              },
            ]
          : []),
        {
          label: "PAN Number",
          variable: "panNo",
        },
        {
          label: "GST No",
          variable: "gstNo",
        },
        {
          label: "TAN Number",
          variable: "tanNo",
        },
        {
          label: "CIN No (If Applicable)",
          variable: "cinNo",
        },
        {
          label: "Year of Experience",
          variable: "yearsOfExperience",
        },
        {
          label: "Number of Team Members",
          variable: "teamSize",
        },
        {
          label: "Number of Sales Team Members",
          variable: "noOfSalesTeamMembers",
        },
        {
          label: "Type of Client Served",
          variable: "typeOfClientServed",
        },
        {
          label: "Last 3 years' Turnover (in Lakhs)",
          variable: "lastThreeYearsTurnOver",
        },
        {
          label: "Completed Projects/Cases",
          variable: "completedProjectsOrCases",
        },
        {
          label: "Ongoing Projects/Cases",
          variable: "onGoingProjectsOrCases",
        },
      ],
    },
    {
      title: "Bank Details",
      fields: [
        {
          label: "Bank Name",
          variable: "bankName",
        },
        { label: "Branch", variable: "branch" },
        {
          label: "Account Number",
          variable: "accountNumber",
        },

        {
          label: "IFSC Code",
          variable: "ifscCode",
        },
      ],
    },
  ];

  if (userData && userData.id !== undefined) {
    sections.push({
      title: "Status and Assignment Details",
      fields: [
        {
          label: "Assigned To",
          variable: "assignedTo",
        },
        { label: "Lead Status", variable: "leadStatus" },
        { label: "Lead Priority", variable: "leadPriority" },
        {
          label: "Reference",
          variable: "referenceType",
        },
        {
          label: "Referral Name",
          variable: "referralName",
        },
        ...(data?.otherReference
          ? [
              {
                label: "Other Reference",
                variable: "otherReference",
              },
            ]
          : []),
          {
            label: "Curated By",
            variable: "curatedBy",
          },
          {
            label: "Curated On",
            variable: "curatedOn",
          },
          {
            label: "Created On",
            variable: "createdOn",
          },
        {
          label: "Created By",
          variable: "createdByName",
        },
        {
          label: "Smart Summary",
          variable: "remarks",
        },
      ],
    });
  }

    const mapIsActiveToLabel = (isActive) => {
    return userStatusObj[isActive] || "Unknown";
  };

  const cols = [
    {
      field: "name",
      headerName: "Name",
      minWidth: 105,
      flex: 1,
      renderCell: (params) => (
        <Tooltip title={params.row.name}>
          <span>{params.row.name}</span>
        </Tooltip>
      ),
    },
    {
      field: "mobileNumber",
      headerName: "Contact No.",
      minWidth: 105,
      flex: 1,
      renderCell: (params) => (
        <Tooltip title={params.row.mobileNumber}>
          <a
            href={`tel:${params.row.mobileNumber}`}
            style={{ textDecoration: "none", color: "blue" }}
          >
            {params.row.mobileNumber}
          </a>
        </Tooltip>
      ),
    },
    {
      field: "alternateMobileNumber",
      headerName: "Alternate No.",
      minWidth: 105,
      flex: 1,
      renderCell: (params) => (
        <Tooltip title={params.row.alternateMobileNumber}>
          <a
            href={`tel:${params.row.alternateMobileNumber}`}
            style={{ textDecoration: "none", color: "blue" }}
          >
            {params.row.alternateMobileNumber}
          </a>
        </Tooltip>
      ),
    },
    {
      field: "email",
      headerName: "Email",
      minWidth: 110,
      flex: 1,
      renderCell: (params) => (
        <Tooltip title={params.row.email}>
          <a
            href={`mailto:${params.row.email}`}
            style={{ textDecoration: "none", color: "blue" }}
          >
            {params.row.email}
          </a>
        </Tooltip>
      ),
    },
    {
      field: "designation",
      headerName: "Designation",
      minWidth: 95,
      flex: 1,
      renderCell: (params) => {
        const designation = listValues?.find(
          (item) => item?.id === params?.row?.designation
        );
        return (
          <Tooltip title={designation?.name || ""}>
            <span>{designation ? designation?.name : ""}</span>
          </Tooltip>
        );
      },
    },
    {
          field: "isActive",
          headerName: "Is Active",
          flex: 1,
          minWidth: 100,
          renderCell: ({ row }) => {
            return (
              <CustomChip
                rounded={true}
                skin="light"
                size="small"
                label={mapIsActiveToLabel(row.isActive)}
                color={row.isActive === true ? "success" : "error"}
                sx={{ textTransform: "capitalize" }}
              />
            );
          },
        },
  ];

  const columns = [
    {
      field: "address",
      headerName: "Address",
      minWidth: 105,
      flex: 1.5,
      renderCell: (params) => (
        <Tooltip title={params.row.address}>
          <span>{params.row.address}</span>
        </Tooltip>
      ),
    },
    {
      field: "addressType",
      headerName: "Address Type",
      minWidth: 95,
      flex: 0.7,
      renderCell: (params) => {
        const designation = listValues?.find(
          (item) => item?.id === params?.row?.addressType
        );
        return (
          <Tooltip title={designation?.name || ""}>
            <span>{designation ? designation?.name : ""}</span>
          </Tooltip>
        );
      },
    },
    {
        field: "location",
        headerName: "Location",
        minWidth: 95,
        flex: 0.7,
        renderCell: (params) => {
          const designation = listValues?.find(
            (item) => item?.id === params?.row?.locationId
          );
          return (
            <Tooltip title={designation?.name || ""}>
              <span>{designation ? designation?.name : ""}</span>
            </Tooltip>
          );
        },
      }
  ];

  function formatDateTime(createdOn) {
    const dateObj = new Date(createdOn);

    // Extract date components
    const day = String(dateObj.getDate()).padStart(2, '0');
    const month = String(dateObj.getMonth() + 1).padStart(2, '0'); // Months are zero-based
    const year = dateObj.getFullYear();

    // Extract time components
    let hours = dateObj.getHours();
    const minutes = String(dateObj.getMinutes()).padStart(2, '0');
    const ampm = hours >= 12 ? 'PM' : 'AM';

    // Convert hours to 12-hour format
    hours = hours % 12 || 12; // Converts 0 to 12

    return ` ${day}-${month}-${year} at ${String(hours).padStart(2, '0')}:${minutes} ${ampm}`;
}

const createdOn = formatDateTime(data?.createdOn)

  return (
    <>
      {state === "view" && (
        <TableContainer
          sx={{
            // padding: "4px 6px",
            width: "100%",
            overflowX: "auto",
            "&:hover": {
              cursor:
                "url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='24' height='24' fill='currentColor'%3E%3Cpath d='M11.423 1A3.577 3.577 0 0 1 15 4.577c0 .27-.108.53-.3.722l-.528.529l-1.971 1.971l-5.059 5.059a3 3 0 0 1-1.533.82l-2.638.528a1 1 0 0 1-1.177-1.177l.528-2.638a3 3 0 0 1 .82-1.533l5.059-5.059l2.5-2.5c.191-.191.451-.299.722-.299m-2.31 4.009l-4.91 4.91a1.5 1.5 0 0 0-.41.766l-.38 1.903l1.902-.38a1.5 1.5 0 0 0 .767-.41l4.91-4.91a2.08 2.08 0 0 0-1.88-1.88m3.098.658a3.6 3.6 0 0 0-1.878-1.879l1.28-1.28c.995.09 1.788.884 1.878 1.88z'/%3E%3C/svg%3E\") 12 12, pointer",
            },
          }}
          onClick={viewClick}
        >
          <Table>
            {sections.map((section, sectionIndex) => (
              <TableBody
                key={sectionIndex}
                sx={{
                  "& .MuiTableCell-root": {
                    p: "10.8px 9px !important",
                    wordWrap: "break-word",
                    whiteSpace: "pre-wrap",
                  },
                }}
              >
                <TableRow
                  sx={{
                    backgroundColor: "#f2f7f2",
                    height: "32px !important",
                    mt: 2,
                  }}
                >
                  <TableCell colSpan={2} sx={{ padding: "4px" }}>
                    <Typography variant="body1" fontWeight={"bold"}>
                      {section?.title}
                    </Typography>
                  </TableCell>
                </TableRow>
                {section.fields.map((field, fieldIndex) => (
                  <TableRow key={fieldIndex}>
                    <TableCell sx={tablecellLabelStyle}>
                      <Typography sx={fieldLabelStyle}>
                        {field?.label}:
                      </Typography>
                    </TableCell>
                    <TableCell sx={tablecellValueStyle}>
                      <Typography style={fieldValueStyle}>
                        {field.label === "Location"
                          ? locationName
                          : field.label === "Portals Registered"
                          ? portalsRegisteredName
                          : field.label === "Type of Vendor"
                          ? servicesProvidedNames?.join(", ") 
                          : field.label === "Area Of Operation"
                          ? areaOfOperationNames?.join(", ")
                          : field.label === "Type of Client Served"
                          ? clientNames?.join(", ")
                          : field.label === "Number of Sales Team Members"
                          ? salesTeamMembers
                          : field.label === "Company Type"
                          ? companyType
                          : field.label === "Designation"
                          ? designation || data?.otherDesignation
                          : field.label === "Lead Status"
                          ? leadStatus
                          : field.label === "Lead Priority"
                          ? leadPriority
                          : field.label === "Reference"
                          ? referenceType
                          : field.label === "Referral Name"
                          ? referralName
                          : field.label === "Assigned To"
                          ? assignedToName 
                          : field.label === "Curated By"
                          ? curatedName
                          : field.label === "Created By"
                          ? data?.createdByName
                          : field.label === "Created On"
                          ? data?.createdOn?.split("T")[0]
                          : field.label === "Specify Profession Type"
                          ? data?.anyOtherServiceProvided
                          : field.label === "Specify Portals Registered"
                          ? data?.anyOtherPortalRegistered
                          : field.label === "Portals Registered"
                          ? portalsRegistered
                          : field.label === "GST No"
                          ? data?.doYouHaveGst === "yes" ? data?.gstNo : ""
                          : data?.[field?.variable]}
                      </Typography>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            ))}
          </Table>
          <TableRow sx={{ height: "32px",marginBottom: "16px"  }}>
                  <TableCell colSpan={2} sx={{ padding: "4px" }}>
                    <Typography variant="body1" fontWeight="bold">{"List of Company Members"}</Typography>
                  </TableCell>
                </TableRow>
          <DataGrid
            rows={data?.serviceProviders || []}
            columns={cols}
            autoHeight
            rowHeight={38}
            headerHeight={38}
          />
            <TableRow sx={{ height: "32px",marginBottom: "16px"  }}>
                  <TableCell colSpan={2} sx={{ padding: "4px" }}>
                    <Typography variant="body1" fontWeight="bold">{"List of Company Addresses"}</Typography>
                  </TableCell>
                </TableRow>
          <DataGrid
            rows={data?.spAddresses || []}
            columns={columns}
            autoHeight
            rowHeight={38}
            headerHeight={38}
          />
        </TableContainer>
      )}
      {state === "edit" && (
        <ProfileEdit
          userData={userData}
          formData={data}
          onCancel={editClick}
          employeesData={employeesData}
          handleAssignedToChange={handleAssignedToChange}
          assignedTo={assignedTo}
          assignedToName={assignedToName}
          createdBy={data?.createdByName}
          areaOfOperationList={areaOfOperationList}
          typeOfClientServedOptions={typeOfClientServedOptions}
        />
      )}
    </>
  );
};

export default ProfileView;