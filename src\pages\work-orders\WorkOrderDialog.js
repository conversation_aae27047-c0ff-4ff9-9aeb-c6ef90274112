import React, { useState } from "react";
import {
  Box,
  <PERSON>ton,
  Dialog,
  <PERSON>alogA<PERSON>,
  DialogContent,
  DialogTitle,
  Divider,
  FormControl,
  Grid,
  IconButton,
  Paper,
  styled,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TextareaAutosize,
  TextField,
  Typography,
} from "@mui/material";
import {
  Add as AddIcon,
  Check as CheckIcon,
  Close as CloseIcon,
  Delete as DeleteIcon,
  DragIndicator as DragIndicatorIcon,
  Edit as EditIcon,
} from "@mui/icons-material";
import { Controller, useForm } from "react-hook-form";
import { DragDropContext, Draggable, Droppable } from "react-beautiful-dnd";
import MultiSelectAutoCompleteStatic from "src/@core/components/custom-components/MultiSelectAutoCompleteStatic";
import ScopeOfWork from "./ScopeOfWork";
import PaymentSchedule from "./PaymentSchedule";
import SRInformation from "./SRInformation";
import UploadFile from "src/pages/documents/UploadFile";
import Icon from "src/@core/components/icon";
import { Download as DownloadIcon } from "@mui/icons-material";
import ViewSnapshotByLocation from "src/@core/components/custom-components/ViewSnapshotByLocation";
import NewSection from "./NewSection";

const StyledTextarea = styled(TextareaAutosize)(({ theme }) => ({
  width: '100%',           // Always full width of container
  minWidth: '100%',        // Minimum width
  minHeight: '100px',       // Minimum height
  maxHeight: '200px',       // Maximum height (optional)
  padding: '8px',
  fontSize: '16px',
  borderRadius: '8px',
  border: '1px solid #ccc',
  resize: 'vertical',       // Allow only vertical resizing
}));

const WorkOrderDialog = ({
  open,
  handleCloseDialog,
  workOrderDetails,
  onSubmit,
  handleUpdate,
  societyOptions,
  serviceRequirement,
  setServiceRequirement,
  societySelected,
  setSocietySelected,
  requirements,
  scopeData,
  setScopeData,
  editablePaymentData,
  setEditablePaymentData,
  contractValue,
  setContractValue,
  gstPercentage,
  setGstPercentage,
  templatesList,
  selectedTemplate,
  handleTemplateChange,
  terms,
  setTerms,
  editDescription,
  setEditDescription,
  isMobile,
  handleOpenDialog,
  selectedFiles,
  setSelectedFiles,
  setUpdatedFile,
  document,
  setDocument,
  newSectionData,
  setNewSectionData,
  paymentData,
  setPaymentData,
}) => {
  const {
    formState: { errors },
    control,
  } = useForm();

  const innerTableTermStylings = {
    fontWeight: "bold", // Make text bold
    padding: "6px 16px",
  };

  const [newTerm, setNewTerm] = useState("");
  const [editIdx, setEditIdx] = useState(-1);
  // Function to handle adding a new term
  const handleAdd = () => {
    if (newTerm.trim() === "") return;
    setTerms([
      ...terms,
      {
        id: crypto.randomUUID(),
        description: newTerm,
        sequence: terms?.length + 1,
      },
    ]);
    setNewTerm("");
  };

  const [selectedDocument, setSelectedDocument] = useState(null);
  const handleDialogClose = () => {
    setSelectedDocument(null);
  };

  // Functions to handle editing, saving, and deleting terms
  const handleEdit = (idx) => {
    setEditIdx(idx);
    setEditDescription(terms[idx].description);
  };

  const handleSave = (idx) => {
    const updatedTerms = [...terms];
    updatedTerms[idx].description = editDescription;
    setTerms(updatedTerms);
    setEditIdx(-1);
    setEditDescription("");
  };

  const handleCancel = () => {
    setEditIdx(-1);
    setEditDescription("");
  };

  const handleDelete = (idx) => {
    setTerms(terms?.filter((_, index) => index !== idx));
  };

  // Function to handle drag and drop reordering
  const onDragDescription = (result) => {
    if (!result.destination) return;
    const reorderedTerms = Array.from(terms);
    const [removed] = reorderedTerms.splice(result.source.index, 1);
    reorderedTerms.splice(result.destination.index, 0, removed);
    setTerms(reorderedTerms);
  };

  const onClose = () => {
    setNewTerm("");
    setNewSectionData(null);
    handleCloseDialog();
  };

  return (
    <Dialog open={open} onClose={onClose} fullScreen>
      <DialogTitle
        sx={{
          position: "relative",
          borderBottom: (theme) => `1px solid ${theme.palette.divider}`,
          p: (theme) => `${theme.spacing(1.5)} !important`,
          display: "flex",
          alignItems: "center",
          justifyContent: "start",
          fontSize: { xs: 19, md: 20 },
          height: "50px",
          ml: 2,
        }}
        textAlign={"center"}
      >
        <Box
          sx={{
            justifyContent: "start",
            marginLeft: 3,
          }}
        >
          {!workOrderDetails || Object.keys(workOrderDetails)?.length === 0
            ? "Add Work Order"
            : "Edit Work Order"}
        </Box>
        <Box
          sx={{
            justifyContent: "space-between",
            alignItems: "flex-end",
            position: "absolute",
            top: "9px",
            right: "12px",
            marginRight: { xl: 7, xs: 3 },
          }}
        >
          <IconButton
            size="small"
            onClick={onClose}
            sx={{
              borderRadius: 1,
              color: "common.white",
              backgroundColor: "primary.main",
              "&:hover": {
                backgroundColor: "#66BB6A",
                transition: "background 0.5s ease, transform 0.5s ease",
              },
            }}
          >
            <Icon icon="tabler:x" fontSize="1rem" />
          </IconButton>
        </Box>
      </DialogTitle>

      <DialogContent
        sx={{
          position: "relative",
          p: (theme) => `${theme.spacing(1.5, 6)} !important`,
          height: "100%",
          width: "100%",
        }}
        style={{
          overflowY: "scroll",
        }}
      >
        <Grid
          sx={{
            backgroundColor: "#f2f7f2",
            mt: 4,
            paddingTop: 0,
            height: "36px",
            display: "flex",
            alignItems: "center",
          }}
        >
          <Typography variant="body1" fontWeight={"bold"} sx={{ mt: 0, ml: 2 }}>
            Service Requisition Information
          </Typography>
          <Divider />
        </Grid>
        <Divider />

        <SRInformation
          societyOptions={societyOptions}
          serviceRequirement={serviceRequirement}
          setServiceRequirement={setServiceRequirement}
          societySelected={societySelected}
          setSocietySelected={setSocietySelected}
          serviceRequirements={requirements}
          data={workOrderDetails}
        />

        {serviceRequirement ? (
          <>
            {/* Main Points List */}
            <Grid
              sx={{
                backgroundColor: "#f2f7f2",
                mt: 4,
                paddingTop: 0,
                height: "36px",
                display: "flex",
                alignItems: "center",
              }}
            >
              <Typography
                variant="body1"
                fontWeight={"bold"}
                sx={{ mt: 0, ml: 2 }}
              >
                Scope of Work
              </Typography>
              <Divider />
            </Grid>
            <Divider />
            <ScopeOfWork scopeData={scopeData} setScopeData={setScopeData} />
            <Divider sx={{ mt: 2 }} />

            <Grid
              sx={{
                backgroundColor: "#f2f7f2",
                mt: 4,
                paddingTop: 0,
                height: "36px",
                display: "flex",
                alignItems: "center",
              }}
            >
              <Typography
                variant="body1"
                fontWeight={"bold"}
                sx={{ mt: 0, ml: 1 }}
              >
                Payment Schedule
              </Typography>
              <Divider />
            </Grid>

            <PaymentSchedule
              editablePaymentData={editablePaymentData}
              setEditablePaymentData={setEditablePaymentData}
              contractValue={contractValue}
              setContractValue={setContractValue}
              gstPercentage={gstPercentage}
              setGstPercentage={setGstPercentage}
              paymentData={paymentData}
              setPaymentData={setPaymentData}
            />

            <Grid
              sx={{
                backgroundColor: "#f2f7f2",
                mt: 4,
                paddingTop: 0,
                height: "36px",
                display: "flex",
                alignItems: "center",
              }}
            >
              <Typography
                variant="body1"
                fontWeight={"bold"}
                sx={{ mt: 0, ml: 1 }}
              >
                Work Engagement Details
              </Typography>
              <Divider />
            </Grid>

            <NewSection
              newSectionData={newSectionData}
              setNewSectionData={setNewSectionData}
            />

            {/* Section 4: Terms & Conditions */}
            <Grid
              sx={{
                backgroundColor: "#f2f7f2",
                mt: 4,
                paddingTop: 0,
                height: "36px",
                display: "flex",
                alignItems: "center",
              }}
            >
              <Typography
                variant="body1"
                fontWeight={"bold"}
                sx={{ mt: 0, ml: 2 }}
              >
                Terms & Conditions
              </Typography>
              <Divider />
            </Grid>
            <Divider />
            <Grid container spacing={3}>
              <Grid item xs={12} sm={6} sx={{ m: 4 }}>
                {/* Service Type 1 Templates */}
                <FormControl fullWidth error={Boolean(errors?.templatesList)}>
                  <Controller
                    name="selectedTemplates"
                    control={control}
                    rules={{ required: false }}
                    render={({ field }) => (
                      <MultiSelectAutoCompleteStatic
                        id="templatesList"
                        label="Select Templates"
                        sx={{
                          "& label": { fontWeight: "bold" },
                        }}
                        nameArray={templatesList}
                        value={selectedTemplate}
                        onChange={(newValue) => {
                          field.onChange(newValue);
                          handleTemplateChange(newValue);
                        }}
                        error={Boolean(errors?.templatesList)}
                      />
                    )}
                  />
                </FormControl>
              </Grid>
            </Grid>
            <Divider />

            {/* Terms and Conditions Section */}
            {/* Desktop View */}
            {!isMobile && (
              <TableContainer component={Paper}>
                <DragDropContext onDragEnd={onDragDescription}>
                  <Table size="small">
                    <TableHead>
                      <TableRow>
                        <TableCell style={innerTableTermStylings}>
                          Sequence
                        </TableCell>
                        <TableCell style={innerTableTermStylings}>
                          Description
                        </TableCell>
                        <TableCell style={innerTableTermStylings}>
                          Actions
                        </TableCell>
                      </TableRow>
                    </TableHead>
                    <Droppable droppableId="terms" type="term">
                      {(droppableProvided) => (
                        <TableBody
                          ref={droppableProvided.innerRef}
                          {...droppableProvided.droppableProps}
                        >
                          {terms?.map((term, idx) => (
                            <Draggable
                              key={term.id}
                              draggableId={`term-${term.id}`}
                              index={idx}
                            >
                              {(draggableProvided, snapshot) => (
                                <TableRow
                                  ref={draggableProvided.innerRef}
                                  {...draggableProvided.draggableProps}
                                  {...draggableProvided.dragHandleProps}
                                  sx={{
                                    backgroundColor: snapshot.isDragging
                                      ? "#f0f0f0"
                                      : "inherit",
                                    cursor: "grab",
                                  }}
                                >
                                  <TableCell sx={{ padding: "4px" }}>
                                    <IconButton size="small">
                                      <DragIndicatorIcon fontSize="small" />
                                    </IconButton>
                                    {idx + 1}
                                  </TableCell>
                                  <TableCell sx={{ padding: "4px" }}>
                                    {editIdx === idx ? (
                                      <StyledTextarea
                                        value={editDescription}
                                        onChange={(e) =>
                                          setEditDescription(e.target.value)
                                          }
                                          minRows={10}   // Minimum 4 lines
                                          maxRows={10}  // Maximum 10 lines
                                      />
                                    ) : (
                                      term.description
                                    )}
                                  </TableCell>
                                  <TableCell sx={{ padding: "4px" }}>
                                    {editIdx === idx ? (
                                      <>
                                        <IconButton
                                          onClick={() => handleSave(idx)}
                                          size="small"
                                          color="primary"
                                        >
                                          <CheckIcon fontSize="small" />
                                        </IconButton>
                                        <IconButton
                                          onClick={handleCancel}
                                          size="small"
                                          color="secondary"
                                        >
                                          <CloseIcon fontSize="small" />
                                        </IconButton>
                                      </>
                                    ) : (
                                      <>
                                        <IconButton
                                          onClick={() => handleEdit(idx)}
                                          size="small"
                                        >
                                          <EditIcon fontSize="small" />
                                        </IconButton>
                                        <IconButton
                                          onClick={() => handleDelete(idx)}
                                          size="small"
                                        >
                                          <DeleteIcon fontSize="small" />
                                        </IconButton>
                                      </>
                                    )}
                                  </TableCell>
                                </TableRow>
                              )}
                            </Draggable>
                          ))}
                          {droppableProvided.placeholder}
                          <TableRow>
                            <TableCell sx={{ padding: "4px" }} />
                            <TableCell sx={{ padding: "4px" }}>
                              <StyledTextarea 
                                aria-label="Add New Description"
                                value={newTerm}
                                onChange={(e) => setNewTerm(e.target.value)}
                                minRows={4}   // Minimum 4 lines
                                maxRows={10}  // Maximum 10 lines
                              />
                            </TableCell>
                            <TableCell sx={{ padding: "4px" }}>
                              <IconButton
                                onClick={handleAdd}
                                color="primary"
                                size="small"
                              >
                                <AddIcon fontSize="small" />
                              </IconButton>
                            </TableCell>
                          </TableRow>
                        </TableBody>
                      )}
                    </Droppable>
                  </Table>
                </DragDropContext>
              </TableContainer>
            )}

            {/* Mobile View with Drag-and-Drop */}
            {isMobile && (
              <DragDropContext onDragEnd={onDragDescription}>
                <Droppable droppableId="terms-mobile" type="term">
                  {(droppableProvided) => (
                    <div
                      {...droppableProvided.droppableProps}
                      ref={droppableProvided.innerRef}
                      style={{ marginTop: "16px" }}
                    >
                      {terms?.map((term, idx) => (
                        <Draggable
                          key={term.id}
                          draggableId={`term-${term.id}`}
                          index={idx}
                        >
                          {(draggableProvided, snapshot) => (
                            <div
                              ref={draggableProvided.innerRef}
                              {...draggableProvided.draggableProps}
                              {...draggableProvided.dragHandleProps}
                              style={{
                                ...draggableProvided.draggableProps.style,
                                marginBottom: "16px",
                                backgroundColor: snapshot.isDragging
                                  ? "#f0f0f0"
                                  : "inherit",
                                cursor: "grab",
                              }}
                            >
                              {/* Mobile item content */}
                              <div
                                style={{
                                  display: "flex",
                                  alignItems: "center",
                                }}
                              >
                                <IconButton size="small">
                                  <DragIndicatorIcon fontSize="small" />
                                </IconButton>
                                <div style={{ flexGrow: 1, marginLeft: "8px" }}>
                                  {editIdx === idx ? (
                                    <TextField
                                      value={editDescription}
                                      onChange={(e) =>
                                        setEditDescription(e.target.value)
                                      }
                                      fullWidth
                                      size="small"
                                    />
                                  ) : (
                                    <Typography>{term.description}</Typography>
                                  )}
                                </div>
                                <div>
                                  {editIdx === idx ? (
                                    <>
                                      <IconButton
                                        onClick={() => handleSave(idx)}
                                        size="small"
                                        color="primary"
                                      >
                                        <CheckIcon fontSize="small" />
                                      </IconButton>
                                      <IconButton
                                        onClick={handleCancel}
                                        size="small"
                                        color="secondary"
                                      >
                                        <CloseIcon fontSize="small" />
                                      </IconButton>
                                    </>
                                  ) : (
                                    <>
                                      <IconButton
                                        onClick={() => handleEdit(idx)}
                                        size="small"
                                      >
                                        <EditIcon fontSize="small" />
                                      </IconButton>
                                      <IconButton
                                        onClick={() => handleDelete(idx)}
                                        size="small"
                                      >
                                        <DeleteIcon fontSize="small" />
                                      </IconButton>
                                    </>
                                  )}
                                </div>
                              </div>
                            </div>
                          )}
                        </Draggable>
                      ))}
                      {droppableProvided.placeholder}
                      {/* Add new term input */}
                      <div
                        style={{
                          marginTop: "16px",
                          display: "flex",
                          alignItems: "center",
                        }}
                      >
                        <TextField
                          label="Add New Description"
                          value={newTerm}
                          onChange={(e) => setNewTerm(e.target.value)}
                          fullWidth
                          size="small"
                        />
                        <IconButton
                          onClick={handleAdd}
                          color="primary"
                          size="small"
                        >
                          <AddIcon fontSize="small" />
                        </IconButton>
                      </div>
                    </div>
                  )}
                </Droppable>
              </DragDropContext>
            )}

            <Grid
              sx={{
                backgroundColor: "#f2f7f2",
                mt: 4,
                paddingTop: 0,
                height: "36px",
                display: "flex",
                alignItems: "center",
              }}
            >
              <Typography
                variant="body1"
                fontWeight={"bold"}
                sx={{ mt: 0, ml: 2 }}
              >
                Download/ Upload WO
              </Typography>
              <Divider />
            </Grid>

            <Grid container spacing={4} sx={{ mt: 4, mb: 2 }}>
              {/* Step 1 */}
              <Grid
                item
                xs={12}
                sm={4}
                sx={{
                  display: "flex",
                  flexDirection: "column",
                  alignItems: "center",
                }}
              >
                <Typography
                  sx={{
                    fontWeight: "bold",
                    // fontSize: { xs: "1.1rem", sm: "1.25rem" },
                    fontSize: "14px",
                  }}
                >
                  Step 1:
                </Typography>
                <Typography sx={{ mt: 2, textAlign: "center" }}>
                  Click on download to download Work Order
                </Typography>
                <Button
                  variant="contained"
                  sx={{ mt: 2 }}
                  disabled={
                    !societySelected ||
                    !serviceRequirement ||
                    scopeData?.length === 0 ||
                    editablePaymentData?.length === 0 ||
                    terms?.length === 0
                  }
                  onClick={handleOpenDialog}
                  startIcon={<DownloadIcon />}
                >
                  Download
                </Button>
              </Grid>

              {/* Step 2 */}
              {/* <Grid
                item
                xs={12}
                sm={4}
            
                sx={{
                  display: "flex",
                  flexDirection: "column",
                  alignItems: "center",
                }}
              >
                <Typography
                  sx={{
                    fontWeight: "bold",
                    // fontSize: { xs: "1.1rem", sm: "1.25rem" },
                    fontSize: "14px",
                  }}
                >
                  Step 2:
                </Typography>
                <Typography sx={{ mt: 1, textAlign: "center" }}>
                  Please Verify Email before Uploading File
                </Typography>
                <Grid container spacing={2} sx={{ mt: 2, ml: 18 }}>
                  <Grid item xs={8} sm={9} md={7} lg={6} >
                    <FormControl fullWidth error={Boolean(errors.email)}>
                      <Controller
                        name="email"
                        control={control}
                        rules={{ required: "Email is required" }}
                        render={({ field }) => (
                          <EmailTextField
                            {...field}
                            type="email"
                            label="Email Address"
                            size="small"
                            error={Boolean(errors.email)}
                            inputProps={{ maxLength: 50 }}
                            InputLabelProps={{ shrink: true }}
                            placeholder="Enter Email Address"
                            helperText={errors.email?.message}
                            defaultValue="<EMAIL>"
                          />
                        )}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item >
                        
                  <Button
                    variant="contained"
                    component="label"

                    sx={{ mt: {xl:0, }, ml: {
                      xs:15,
                      xl:1,
                      lg:0,
                      md:1,
                      sm:6,

                      justifyContent:{
                        xs:"center"
                      }
          

                    } }}
                    startIcon={<Icon icon="tabler:check" />}
                  >
                    Verify
                  </Button>
                </Grid>
                </Grid>
              </Grid> */}

              {/* Step 3 */}

              {!workOrderDetails ||
              Object.keys(workOrderDetails)?.length === 0 ? null : (
                <>
                  <Grid
                    item
                    xs={12}
                    sm={4}
                    sx={{
                      display: "flex",
                      flexDirection: "column",
                      alignItems: "center",
                    }}
                  >
                    <Typography
                      sx={{
                        fontWeight: "bold",
                        // fontSize: { xs: "1.1rem", sm: "1.25rem" },
                        fontSize: "14px",
                      }}
                    >
                      Step 2:
                    </Typography>
                    <Typography sx={{ mt: 1, textAlign: "center" }}>
                      Click to upload Signed Copy
                    </Typography>
                  </Grid>
                  <Grid item xs={12} sm={4}>
                    <Grid item xs={12}>
                      <UploadFile
                        selectedDocument={document}
                        setSelectedDocument={setDocument}
                        selectedFiles={selectedFiles}
                        setSelectedFiles={setSelectedFiles}
                        setUpdatedFile={setUpdatedFile}
                      />
                    </Grid>
                  </Grid>
                  <Grid container spacing={2}>
                    <Grid item xs={12} sx={{ mt: 2 }}>
                      {document?.location && (
                        <TableContainer
                          style={{ overflowX: "auto", maxHeight: "90px" }}
                        >
                          <Table sx={{ width: "100%" }}>
                            <TableHead>
                              <TableRow style={{ backgroundColor: "#f2f7f2" }}>
                                <TableCell
                                  style={{
                                    fontWeight: "bold",
                                    fontSize: "12px",
                                    height: "10px",
                                    paddingTop: "1px",
                                    paddingBottom: "1px",
                                  }}
                                >
                                  File Name
                                </TableCell>
                                <TableCell
                                  style={{
                                    fontWeight: "bold",
                                    fontSize: "12px",
                                    height: "10px",
                                    paddingTop: "1px",
                                    paddingBottom: "1px",
                                  }}
                                >
                                  Actions
                                </TableCell>
                              </TableRow>
                            </TableHead>
                            <TableBody>
                              <TableRow>
                                <TableCell>
                                  <Typography className="data-field">
                                    {document?.location &&
                                      document?.location?.split("/").pop()}
                                  </Typography>
                                </TableCell>
                                <TableCell>
                                  <IconButton
                                    onClick={() => {
                                      setSelectedDocument(document);
                                    }}
                                    color="error"
                                  >
                                    <Icon icon="iconamoon:eye" />
                                  </IconButton>
                                </TableCell>
                              </TableRow>
                            </TableBody>
                          </Table>
                        </TableContainer>
                      )}
                    </Grid>
                  </Grid>
                </>
              )}
            </Grid>
            <Divider />
          </>
        ) : (
          ""
        )}
      </DialogContent>

      <DialogActions
        sx={{
          justifyContent: "center",
          borderTop: (theme) => `1px solid ${theme.palette.divider}`,
          p: (theme) => `${theme.spacing(2.5)} !important`,
          justifyContent: "end",
          marginRight: { xl: 7.5, xs: 3.5 },
        }}
      >
        {!workOrderDetails || Object.keys(workOrderDetails)?.length === 0 ? (
          <Button
            variant="contained"
            disabled={!(societySelected && serviceRequirement)} // Disable if either is false
            onClick={onSubmit}
          >
            Save
          </Button>
        ) : (
          <Button variant="contained" onClick={handleUpdate}>
            Update
          </Button>
        )}

        {/* <Button
        variant="contained"
        sx={{ ml: 2 }}
        disabled={!(societySelected && serviceRequirement)} // Disable if either is false
      >
        Publish
      </Button>
      <Button
        variant="contained"
        sx={{ ml: 2, marginRight:{xl:7.5, xs:3.5 },
          
          
          
          padding:{
          xs: "7px 5px",
          xl: "7px 7px"
        } }}
        disabled={!(societySelected && serviceRequirement)} // Disable if either is false
      >
        Generate PDF
      </Button> */}
      </DialogActions>

      <ViewSnapshotByLocation
        location={selectedDocument}
        setSelectedLocation={setSelectedDocument}
        onClose={handleDialogClose}
      />
    </Dialog>
  );
};

export default WorkOrderDialog;
