// ** MUI Imports
import Typography from "@mui/material/Typography";
import { useState, useEffect, useContext } from "react";

// ** Custom Components Imports
import AccordionBasic from "src/@core/components/custom-components/AccordionBasic";

// ** Demo Components Imports
import { useTheme } from "@emotion/react";

// ** Styled Component
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
} from "@mui/material";
import ProjectEdit from "./ProjectEdit";
import ServicesTabs from "./ServiceTabs";
import { AuthContext } from "src/context/AuthContext";

const field = {
  fontWeight: 500,
  fontSize: "12.75px",
};
const fieldValueStyle = {
  fontWeight: "bold",
  wordWrap: "break-word",
  whiteSpace: "pre-wrap",
  // color: "#66BB6A", // Sets color for the TableCell
  color:'#108A00',
  fontSize: "13px",
};

const ProjectView = ({ data, expanded, serviceId, userData }) => {
  // ** Hook
  const theme = useTheme();
  const [state3, setState3] = useState("view");

  const { user, fetchProjects, projectsData} = useContext(AuthContext)

  const viewClick3 = () => {
    setState3("edit");
  };

  const editClick3 = () => {
    setState3("view");
  };

  const userUniqueId =
  userData && userData.organisationId !== undefined ? userData.organisationId : user.orgId;
    useEffect(() =>{
      fetchProjects(userUniqueId,serviceId)

    },[serviceId])

  return (
    <>
      <AccordionBasic
        id={"panel-header-2"}
        ariaControls={"panel-content-2"}
        heading={"Project"}
        body={
          <>
            {state3 === "view" && (
              <>
                {projectsData?.length > 0 ? (
                  <TableContainer
                    sx={{ padding: "4px 6px",  cursor: 'url("data:image/svg+xml,%3Csvg xmlns=\'http://www.w3.org/2000/svg\' viewBox=\'0 0 24 24\' width=\'24\' height=\'24\' fill=\'currentColor\'%3E%3Cpath d=\'M11.423 1A3.577 3.577 0 0 1 15 4.577c0 .27-.108.53-.3.722l-.528.529l-1.971 1.971l-5.059 5.059a3 3 0 0 1-1.533.82l-2.638.528a1 1 0 0 1-1.177-1.177l.528-2.638a3 3 0 0 1 .82-1.533l5.059-5.059l2.5-2.5c.191-.191.451-.299.722-.299m-2.31 4.009l-4.91 4.91a1.5 1.5 0 0 0-.41.766l-.38 1.903l1.902-.38a1.5 1.5 0 0 0 .767-.41l4.91-4.91a2.08 2.08 0 0 0-1.88-1.88m3.098.658a3.6 3.6 0 0 0-1.878-1.879l1.28-1.28c.995.09 1.788.884 1.878 1.88z\'/%3E%3C/svg%3E") 12 12, pointer' }}
                    className="tableBody"
                    onClick={viewClick3}
                  >
                    <Table>
                      <TableHead>
                        <TableRow>
                          <TableCell sx={{ paddingLeft: "20px" }}>Name</TableCell>
                          <TableCell>Image</TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody
                        sx={{
                          "& .MuiTableCell-root": {
                            p: `${theme.spacing(1.35, 1.125)} !important`,
                          },
                        }}
                      >
                        {projectsData?.map((project, index) => (
                          <TableRow key={index}>
                            <TableCell>
                              <Typography
                                className="data-field"
                                marginLeft={"12px"}
                                sx={fieldValueStyle}
                              >
                                {project?.projectName}
                              </Typography>
                            </TableCell>

                            <TableCell>
                              <Typography className="data-field" sx={fieldValueStyle}>
                                {project?.documentDTO?.location &&
                                  project.documentDTO.location.split("/").pop()}
                              </Typography>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </TableContainer>
                ) : (
                  <Typography
                    variant="body1"
                    sx={{ textAlign: "left", mt: 3,  cursor: 'url("data:image/svg+xml,%3Csvg xmlns=\'http://www.w3.org/2000/svg\' viewBox=\'0 0 24 24\' width=\'24\' height=\'24\' fill=\'currentColor\'%3E%3Cpath d=\'M11.423 1A3.577 3.577 0 0 1 15 4.577c0 .27-.108.53-.3.722l-.528.529l-1.971 1.971l-5.059 5.059a3 3 0 0 1-1.533.82l-2.638.528a1 1 0 0 1-1.177-1.177l.528-2.638a3 3 0 0 1 .82-1.533l5.059-5.059l2.5-2.5c.191-.191.451-.299.722-.299m-2.31 4.009l-4.91 4.91a1.5 1.5 0 0 0-.41.766l-.38 1.903l1.902-.38a1.5 1.5 0 0 0 .767-.41l4.91-4.91a2.08 2.08 0 0 0-1.88-1.88m3.098.658a3.6 3.6 0 0 0-1.878-1.879l1.28-1.28c.995.09 1.788.884 1.878 1.88z\'/%3E%3C/svg%3E") 12 12, pointer' }}
                    onClick={viewClick3}
                  >
                    Click here to add Projects
                  </Typography>
                )}
              </>
            )}
            {state3 === "edit" && (
              <ProjectEdit
                data={projectsData}
                onCancel={editClick3}
                serviceId={serviceId}
                userData={userData}
              />
            )}
          </>
        }
        expanded={expanded}
      />
    </>
  );
};
export default ProjectView;
