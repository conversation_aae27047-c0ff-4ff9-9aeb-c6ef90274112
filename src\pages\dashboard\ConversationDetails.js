import { DataGrid } from "@mui/x-data-grid";
import { useContext, useEffect, useState } from "react";
import authConfig from "src/configs/auth";
import { getAuthorizationHeaders, getUrl } from "src/helpers/utils";
import { useAuth } from "src/hooks/useAuth";
import { useRBAC } from "src/pages/permission/RBACContext";
import {
  Button,
  Card,
  CardContent,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Grid,
  IconButton,
  Link,
  Tooltip,
  Typography,
} from "@mui/material";
import Icon from "src/@core/components/icon";

import CustomAvatar from "src/@core/components/mui/avatar";
import axios from "axios";

import { Box } from "@mui/system";

import { AuthContext } from "src/context/AuthContext";
import Index from "../conversations";
import IndexCHS from "../conversations/index-chs";

const ConversationDetails = ({ conversationType, role }) => {
  const { user, listValues, taskData, setTaskData, taskDataDetails } =
    useContext(AuthContext);

  const [expanded, setExpanded] = useState(true);

  const handleToggle = () => {
    setExpanded(!expanded);
  };
  // Constants
  const rowsPerPageOptions = [10, 15, 20, 25, 50, 100];
  const [employeesData, setEmployeesData] = useState([]);

  // Define columns
  const columns = [
    {
      field: "fullName",
      minWidth: 120,
      headerName: "Name",
      flex: 0.2, // Adjust the flex as needed
      renderCell: (params) => (
        <Tooltip
          title={`${
            role === "SP" ? params.row.firstName : params.row.fullName
          }`}
        >
          <span>
            {role === "SP" ? params.row.firstName : params.row.fullName}
          </span>
        </Tooltip>
      ),
    },
    {
      field: "orgName",
      minWidth: 120,
      headerName: role === "SP" ? "Company Name" : "Society Name",
      flex: 0.3,
      renderCell: (params) => (
        <Tooltip title={`${params.row.orgName}`}>
          <span>{params.value}</span>
        </Tooltip>
      ),
    },
    {
      field: "email",
      minWidth: 115,
      headerName: "Email",
      flex: 0.2,
      renderCell: (params) => {
        const email = params?.value;

        return (
          <Tooltip title={email}>
            <Link
              href={`mailto:${email}`}
              target="_blank"
              rel="noopener noreferrer"
              sx={{
                color: "#6666ff",
                textDecoration: "underline",
                cursor: "pointer",
              }}
            >
              {email}
            </Link>
          </Tooltip>
        );
      },
    },
    {
      field: "nextConversationDate",
      minWidth: 150,
      headerName: "Follow up Date",
      flex: 0.16,
    },
    {
      field: "age",
      headerName: "Age",
      minWidth: 80,
      flex: 0.1,
      valueGetter: (params) => {
        const nextConversationDate = params.row.nextConversationDate;
        if (nextConversationDate) {
          const today = new Date();
          const nextDate = new Date(nextConversationDate);
          // Extract only date parts
          const todayDate = new Date(
            today.getFullYear(),
            today.getMonth(),
            today.getDate()
          );
          const nextDateOnly = new Date(
            nextDate.getFullYear(),
            nextDate.getMonth(),
            nextDate.getDate()
          );
          // Calculate the difference in milliseconds
          const timeDiff = Math.abs(
            nextDateOnly.getTime() - todayDate.getTime()
          );
          // Convert milliseconds to days
          const ageInDays = Math.ceil(timeDiff / (1000 * 3600 * 24));
          return ageInDays;
        }
        return "";
      },
    },
    {
      flex: 0.25,
      minWidth: 150,
      field: "remarks",
      headerName: "smart summary",
    },
    {
      flex: 0.11,
      minWidth: 120,
      field: "target",
      headerName: "Target",
      renderCell: (params) => {
        const target = listValues?.find(
          (item) => item.id === params.row.target
        );
        return <span>{target ? target.name : ""}</span>;
      },
    },
    {
      flex: 0.077,
      field: "edit",
      headerName: "Actions",
      sortable: false,
      minWidth: 120,
      disableClickEventBubbling: true,
      renderCell: (params) => {
        const onClick = () => {
          //   openEditDialog(params.row);
          openConversationDialog(params.row);
          const row = params.row;
        };

        return (
          <div
            style={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
            }}
          >
            <Tooltip title="Edit or View">
              <CustomAvatar
                skin="light"
                variant="rounded"
                sx={{
                  mr: { xs: 2, lg: 4 },
                  width: 34,
                  height: 34,
                  cursor: "pointer",
                }}
                onClick={onClick}
              >
                <Icon icon="iconamoon:edit" />
              </CustomAvatar>
            </Tooltip>
          </div>
        );
      },
    },
  ];

  const auth = useAuth();
  const [userList, setUserList] = useState([]);
  const [pageSize, setPageSize] = useState(rowsPerPageOptions[0]);
  const [loading, setLoading] = useState(false);
  const [page, setPage] = useState(1);
  const [rowCount, setRowCount] = useState(0);
  const [searchKeyword, setSearchKeyword] = useState("");
  const [currentRow, setCurrentRow] = useState("");

  const [isConversationDialogOpen, setConversationDialogOpen] = useState(false);

  useEffect(() => {
    // Fetch all employees
    axios({
      method: "get",
      url: getUrl(authConfig.selectDropdownNew) + "?selectionType=EMPLOYEES",
      headers: getAuthorizationHeaders(),
    })
      .then((res) => {
        setEmployeesData(res.data.data);
      })
      .catch((err) => console.log("Employees error", err));
  }, []);

  useEffect(() => {
    fetchUsers(page, pageSize, searchKeyword);
  }, [page, pageSize, searchKeyword]);

  const fetchUsers = async (currentPage, currentPageSize, searchKeyword) => {
    setLoading(true);
    let url;
    if (role === "SP") {
      url =
        getUrl(authConfig.getAllTimelineConversations) +
        (conversationType ? `?conversationTimeLines=${conversationType}` : "");
    } else {
      url =
        getUrl(authConfig.getAllTimelineConversationsCHS) +
        (conversationType ? `?conversationTimeLines=${conversationType}` : "");
    }

    const headers = getAuthorizationHeaders();

    const data = {
      page: currentPage,
      pageSize: currentPageSize,
      searchKeyword: searchKeyword,
    };

    try {
      const response = await axios({
        method: "post",
        url: url,
        headers: headers,
        data: data,
      });

      if (response?.data) {
        setUserList(response.data?.conversations || []);
        setRowCount(response.data?.rowCount || 0);
      } else {
        console.error("Unexpected API response format:", response);
      }
    } catch (error) {
      console.error("Error fetching users:", error);
    } finally {
      setLoading(false);
    }
  };

  const handlePageChange = (direction) => {
    if (direction === page) {
      setPage(page + 1);
    } else {
      setPage(page - 1);
    }
  };

  const handlePageSizeChange = (params) => {
    if (params) {
      setPageSize(params);
      setPage(1);
    }
  };

  const closeConversationDialog = () => {
    setConversationDialogOpen(false);
    fetchUsers();
  };

  const openConversationDialog = (row) => {
    setConversationDialogOpen(true);
    setCurrentRow(row);
  };

  return (
    <>
      <Dialog
        fullScreen
        open={isConversationDialogOpen}
        onClose={closeConversationDialog}
      >
        <DialogTitle
          sx={{
            position: "relative",
            borderBottom: (theme) => `1px solid ${theme.palette.divider}`,
            p: (theme) => `${theme.spacing(1.5, 4)} !important`,
            display: "flex",
            alignItems: "center",
            justifyContent: "start",
            fontSize: { xs: 19, md: 20 },
          }}
        >
          Conversations
          <Box sx={{ position: "absolute", top: "0px", right: "5px" }}>
            <IconButton
              size="small"
              onClick={closeConversationDialog}
              sx={{
                // p: "0.438rem",
                borderRadius: 1,
                mr: 6,
                mt: 1,
                color: "common.white",
                backgroundColor: "primary.main",
                "&:hover": {
                  backgroundColor: "#66BB6A",
                  transition: "background 0.5s ease, transform 0.5s ease",
                },
              }}
            >
              <Icon icon="tabler:x" fontSize="1rem" />
            </IconButton>
          </Box>
        </DialogTitle>

        <DialogContent>
          {employeesData && (
            <>
              {role === "SP" ? (
                <Index
                  currentRow={currentRow}
                  setCurrentRow={setCurrentRow}
                  closeConversationDialog={close}
                  employeeData={employeesData}
                />
              ) : (
                <IndexCHS
                  currentRow={currentRow}
                  setCurrentRow={setCurrentRow}
                  closeConversationDialog={close}
                  employeeData={employeesData}
                />
              )}
            </>
          )}
        </DialogContent>

        <DialogActions sx={{ justifyContent: "center" }}></DialogActions>
      </Dialog>
      <Box style={{ height: 380, width: "100%" }}>
        <DataGrid
          rows={userList || []}
          columns={columns}
          pagination
          pageSize={pageSize}
          page={page - 1}
          rowsPerPageOptions={rowsPerPageOptions}
          rowCount={rowCount}
          paginationMode="server"
          onPageChange={handlePageChange}
          onPageSizeChange={handlePageSizeChange}
          rowHeight={38}
          headerHeight={38}
          components={{
            NoRowsOverlay: () => (
              <Typography
              variant="body1"
              align="center"
              sx={{ marginTop: "120px" }}
              >
                {userList?.length === 0 ? "No Data" : "No Rows"}
              </Typography>
            )
          }}
        />
      </Box>
    </>
  );
};

export default ConversationDetails;
