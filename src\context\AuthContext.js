// ** React Imports
import { createContext, use, useEffect, useState } from "react";

// ** Next Import
import { useRouter } from "next/router";

// ** Axios
import axios from "axios";

// ** Config
import authConfig from "src/configs/auth";

// ** JWT import
import jwt from "jsonwebtoken";

// ** Default AuthConfig
import defaultAuthConfig from "src/configs/auth";
import {
  returnEntity,
  getUrl,
  getAuthorizationHeaders,
  getFileUploadHeaders,
} from "src/helpers/utils";
import SessionExpirationDialog from "./SessionExpirationDialog";

const jwtConfig = {
  secret: process.env.NEXT_PUBLIC_JWT_SECRET,
  expirationTime: process.env.NEXT_PUBLIC_JWT_EXPIRATION,
  refreshTokenSecret: process.env.NEXT_PUBLIC_JWT_REFRESH_TOKEN_SECRET,
};

// ** Defaults
const defaultProvider = {
  user: null,
  topMenuData: null,
  quickLinksData: null,
  loading: true,
  loginLoad: false,
  pageLoad: false,
  entityData: null,
  employeeData: null,
  leftMenuData: null,
  readinessProfileData: null,
  roleTypeData: null,
  roleData: null,
  basicProfileGetData: null,
  listValues: null,
  listNames: null,
  shortFormData: null,
  masterData: null,
  masterDataDetails: null,
  requisitionData:null,
  requisitionDataDetails:null,
  snapshotsData:null,
  snapshotsDataDetails:null,
  contactGroupsData:null,
  contactGroupsDataDetails:null,
  taskData:null,
  taskDataDetails:null,
  servicesData:null,
  servicesDataDetails:null,
  listNamesData:null,
  listNamesDataDetails:null,
  locationsData:null,
  userPackage:null,
  userPackageData:null,
  subscriptionResponseData:null,
  locationsDataDetails:null,
  fsiRulesData: null,
  fsiRulesDataDetails: null,
  fsiData: null,
  fsiDataDetails: null,
  serviceProfileDataDetails: null,
  roleId:null,
  rolesDetails:null,
  setRoleId: () => Promise.resolve(),
  setRolesDetails:() => Promise.resolve(),
  documents:null,
  documentDetails:null,
  setDocuments: () => Promise.resolve(),
  setDocumentDetails: () => Promise.resolve(),
  individualId:null,
  individualDetails:null,
  setIndividualId: () => Promise.resolve(),
  setIndividualDetails: () => Promise.resolve(),
  templateId:null,
  templateDetails:null,
  setTemplateId: () => Promise.resolve(),
  setTemplateDetails: () => Promise.resolve(),
  setMasterData: () => Promise.resolve(),
  setMasterDataDetails: () => Promise.resolve(),
  setRequisitionData: () => Promise.resolve(),
  setRequisitionDataDetails: () => Promise.resolve(),
  setSpecificationData: () => Promise.resolve(),
  setSpecificationDataDetails: () => Promise.resolve(),
  setSnapshotsData: () => Promise.resolve(),
  setSnapshotsDataDetails: () => Promise.resolve(),
  setContactGroupsData: () => Promise.resolve(),
  setContactGroupsDataDetails: () => Promise.resolve(),
  setTaskData: () => Promise.resolve(),
  setTaskDataDetails: () => Promise.resolve(),
  setServicesData: () => Promise.resolve(),
  setServicesDataDetails: () => Promise.resolve(),
  setListNamesData: () => Promise.resolve(),
  setListNamesDataDetails: () => Promise.resolve(),
  setLocationsData: () => Promise.resolve(),
  setSubscriptionResponseData: () => Promise.resolve(),
  setUserPackage: () => Promise.resolve(),
  setUserPackageData: () => Promise.resolve(),
  setLocationsDataDetails: () => Promise.resolve(),
  setFsiRulesData: () => Promise.resolve(),
  setFsiRulesDataDetails: () => Promise.resolve(),
  setFsiData: () => Promise.resolve(),
  CHSCreate: () => Promise.resolve(),
  SPCreate: () => Promise.resolve(),
  setFsiDataDetails: () => Promise.resolve(),
  setServiceProfileDataDetails: () => Promise.resolve(),
  roleTypeUser: null,
  setRoleTypeUser: () => null,
  roleUser: null,
  setRoleUser: () => null,
  setEmployeeProfile: () => null,
  setEmployeeData: () => Promise.resolve(),
  setRoleTypeData: () => Promise.resolve(),
  setRoleData: () => Promise.resolve(),
  setListValues: () => Promise.resolve(),
  setListNames: () => Promise.resolve(),
  setBasicProfileGetData: () => Promise.resolve(),
  setShortFormData: () => Promise.resolve(),
  employeeProfile: null,
  setUser: () => null,
  setTopMenuData: () => null,
  setLeftMenuData: () => null,
  setQuickLinksData: () => null,
  setLoading: () => Boolean,
  setLoginLoad: () => Boolean,
  setPageLoad: () => Boolean,
  setEntityData: () => Promise.resolve(),
  home: () => Promise.resolve(),
  login: () => Promise.resolve(),
  loginNew: () => Promise.resolve(),
  logout: () => Promise.resolve(),
  register: () => Promise.resolve(),
  resetPassword: () => Promise.resolve(),
  triggerPasswordReset: () => Promise.resolve(),
  emailVerify: () => Promise.resolve(),
  updateEntity: () => Promise.resolve(),
  updateEntityServices: () => Promise.resolve(),
  updateReadiness: () => Promise.resolve(),
  updateIsEmpanelled: () => Promise.resolve(),
  updateIsStrategicPartner: () => Promise.resolve(),
  getAllMembers: () => Promise.resolve(),
  postEmployee: () => Promise.resolve(),
  postRequisition: () => Promise.resolve(),
  postOTP: ()=> Promise.resolve(),
  postForgotPassword: () => Promise.resolve(),
  postResetPassword: () => Promise.resolve(),
  verifyOTP: () => Promise.resolve(),
  resendOTP: () => Promise.resolve(),
  postTemplate: () => Promise.resolve(),
  putTemplate: () => Promise.resolve(),
  patchSiteVisitStatusBookOrCancel: () => Promise.resolve(),
  postBroadCastSrToSp: () => Promise.resolve(),
  postBroadCastSrToSpFinal: () => Promise.resolve(),
  rolePost: () => Promise.resolve(),
  rolePatch: () => Promise.resolve(),
  updateWorkOrder: () => Promise.resolve(),
  patchIndividualPermissions: () => Promise.resolve(),
  postContactGroup: () => Promise.resolve(),
  patchContactGroup: () => Promise.resolve(),
  patchRequisition: () => Promise.resolve(),
  patchRescheduleSlot: () => Promise.resolve(),
  patchSiteVisit: () => Promise.resolve(),
  postRole: () => Promise.resolve(),
  postAssignRole: () => Promise.resolve(),
  patchRoles: () => Promise.resolve(),
  submitFinalBOQ: () => Promise.resolve(),
  patchEmployee: () => Promise.resolve(),
  patchConfiguration: () => Promise.resolve(),
  postMasterData: () => Promise.resolve(),
  postListName: () => Promise.resolve(),
  postService: () => Promise.resolve(),
  postLocationZoneMap: () => Promise.resolve(),
  postQuestions: () => Promise.resolve(),
  mapQuestions: () => Promise.resolve(),
  postSpecification: () => Promise.resolve(),
  patchMasterData: () => Promise.resolve(),
  patchServicesData: () => Promise.resolve(),
  patchSpecification: () => Promise.resolve(),
  patchLocation: () => Promise.resolve(),
  patchLocationZone: () => Promise.resolve(),
  patchListNames: () => Promise.resolve(),
  patchLabel: () => Promise.resolve(),
  postFSIRule: () => Promise.resolve(),
  patchServices: () => Promise.resolve(),
  postCalculatedFSI: () => Promise.resolve(),
  patchFSIRule: () => Promise.resolve(),
  basicProfile: () => Promise.resolve(),
  allProfilesBasicProfile: () => Promise.resolve(),
  patchCalculatedFsi: () => Promise.resolve(),
  getSocietyProfile: () => Promise.resolve(),
  getBasicProfileData: () => Promise.resolve(),
  projectData: () => Promise.resolve(),
  addNewUser: () => Promise.resolve(),
  assignRoleCreate: () => Promise.resolve(),
  createRole: () => Promise.resolve(),
  updateRole: () => Promise.resolve(),
  updateProfile: () => Promise.resolve(),
  signupV2: () => promise.resolve(),
  signupV3: () => Promise.resolve(),
  logOutAllDevices: () => promise.resolve(),
  tempGetArchitectEntityProfile: () => Promise.resolve(),
  patchMicrosite: () => Promise.resolve(),
  micrositeLevel2Patch: () => Promise.resolve(),
  micrositeGetEndpoint: () => Promise.resolve(),
  fetchOne: () => Promise.resolve(),
  micrositeLevel1Patch: () => Promise.resolve(),
  fetchUserProjects: () => Promise.resolve(),
  fetchProjects: () => Promise.resolve(),
  fetchRazorPayId: () => Promise.resolve(),
  conversationPost: () => Promise.resolve(),
  fetchStatistics: () => Promise.resolve(),
  statisticsUpdate: () => Promise.resolve(),
  conversationData: null,
  taskPost: () => Promise.resolve(),
  userSaveSpPost: () => Promise.resolve(),
  createSubscriptionPost: () => Promise.resolve(),
  updateSubscription : () => Promise.resolve(),
  createPaymentPost: () => Promise.resolve(),
  taskPatch: () => Promise.resolve(),
  conversation: null,
  setConversation: () => null,
  setConversationData: () => Promise.resolve(),
  conversationData: null,
  basicProfileAllProfiles: null,
  setBasicProfileAllProfiles: () => null,
  setBasicProfileDataAllProfiles: () => Promise.resolve(),
  basicProfileDataAllProfiles: null,
  sendWatiMessage: () => Promise.resolve(),
  sendWatiMessages: () => Promise.resolve(),
};

const AuthContext = createContext(defaultProvider);

const AuthProvider = ({ children }) => {
  // ** User States
  const [user, setUser] = useState(defaultProvider.user);
  const [loading, setLoading] = useState(defaultProvider.loading);
  const [count, setCount] = useState(0);

  const [loginLoad,setLoginLoad] = useState(false);
  const [pageLoad, setPageLoad] = useState(false);

  // Admin Sates
  const [profileUser, setProfileUser] = useState(null);

  const [shortFormData, setShortFormData] = useState(null);

  const [profileEntityData, setProfileEntityData] = useState(null);
  const [readinessProfileData, setReadinessProfileData] = useState({});

  const [convsCreated, setConvsCreated] = useState(null);

  //User Data States
  const [entityData, setEntityData] = useState({});

  //Employee States
  const [employeeProfile, setEmployeeProfile] = useState(null);
  const [employeeData, setEmployeeData] = useState(null);

  const [configurationData, setConfigurationData] = useState(null);

  //Master Data States
  const [masterData, setMasterData] = useState(null);
  const [masterDataDetails, setMasterDataDetails] = useState(null);

  const [requisitionData, setRequisitionData] = useState(null);
  const [requisitionDataDetails, setRequisitionDataDetails] = useState(null);

  const [specificationData, setSpecificationData] = useState(null);
  const [specificationDataDetails, setSpecificationDataDetails] =
    useState(null);

  const [snapshotsData, setSnapshotsData] = useState(null);
  const [snapshotsDataDetails, setSnapshotsDataDetails] = useState(null);

  const [contactGroupsData, setContactGroupsData] = useState(null);
  const [contactGroupsDataDetails, setContactGroupsDataDetails] =
    useState(null);

  //Tasks Data States
  const [taskData, setTaskData] = useState(null);
  const [taskDataDetails, setTaskDataDetails] = useState(null);

  //Services Master Data

  const [servicesData, setServicesData] = useState(null);
  const [servicesDataDetails, setServicesDataDetails] = useState(null);

  // List names data

  const [listNamesData, setListNamesData] = useState(null);
  const [listNamesDataDetails, setListNamesDataDetails] = useState(null);

  //Subscriptions & User Packages Data
  const[subscriptionResponseData,setSubscriptionResponseData]=useState(null);
  const [userPackageData, setUserPackageData] = useState(null);
  const[userPackage,setUserPackage]=useState(null);
  

  //Locations Master Data FSI

  const [locationsData, setLocationsData] = useState(null);
  const [locationsDataDetails, setLocationsDataDetails] = useState(null);

  // FSI Rules Data States

  const [fsiRulesData, setFsiRulesData] = useState(null);
  const [fsiRulesDataDetails, setFsiRulesDataDetails] = useState(null);

  //Calculated FSI Data

  const [fsiData, setFsiData] = useState(null);
  const [fsiDataDetails, setFsiDataDetails] = useState(null);

  // Service Profile Data
  // const [fsiData, setFsiData] = useState(null);
  const [serviceProfileDataDetails, setServiceProfileDataDetails] =
    useState(null);
  const [userData, setUserData] = useState(null);
  // ** Hooks
  const router = useRouter();
  const [redirectToLogin, setRedirectToLogin] = useState(false);
  const [showWarning, setShowWarning] = useState(false);
  const [message, setMessage] = useState("");
  useEffect(() => {
    const token = localStorage.getItem('accessToken');
    if (token) {
      const decodedToken = jwt.decode(token);
      const expiry = decodedToken.exp * 1000; // Convert to milliseconds
      const currentTime = Date.now();

      // Check the time remaining before the token expires
      const timeRemaining = expiry - currentTime;

      if (timeRemaining > 0) {
        // Show warning dialog after expiration
        const warningTimeout = setTimeout(() => {
          setMessage("Your session has expired, please log in again.");
          setRedirectToLogin(true);
          setShowWarning(true);
        }, timeRemaining);

        // Cleanup timeout on component unmount
        return () => clearTimeout(warningTimeout);
      }
    }
  }, [user]);

  useEffect(() => {
    const token = localStorage.getItem("accessToken");
    if (token) {
      const decodedToken = jwt.decode(token);
      const expiry = decodedToken.exp * 1000; // Convert to milliseconds
      const currentTime = Date.now();

      // Check the time remaining before the token expires
      const timeRemaining = expiry - currentTime;

      if (timeRemaining > 0) {
        const warningTime = 5 * 60 * 1000; // 5 minutes in milliseconds

        // Show warning dialog 5 minutes before expiration
        const warningTimeout = setTimeout(() => {
          setMessage(
            "Your session is going to expire soon. Please save your work to avoid losing any changes."
          );
          setShowWarning(true);
        }, timeRemaining - warningTime);

        // Cleanup timeout on component unmount
        return () => clearTimeout(warningTimeout);
      }
    }
  }, [user]);

  const handleCloseDialog = () => {
    setShowWarning(false);
  };

  const handleCloseDialogRedirectToLogin = () => {
    setShowWarning(false);
    router.push("/login");
  };

  //RoleType States
  const [roleTypeUser, setRoleTypeUser] = useState(null);
  const [roleTypeData, setRoleTypeData] = useState(null);


  // Documents

  const [documents,setDocuments] = useState(null);
  const [documentDetails,setDocumentDetails] = useState(null);

  // Roles

  const [roleId,setRoleId] = useState(null)
  const [rolesDetails,setRolesDetails] = useState(null)

  //Work Orders
  const [workOrderId, setWorkOrderId] = useState(null)
  const [workOrderDetails,setWorkOrderDetails] = useState(null)

  //Invoices
  const [invoiceId,setInvoiceId] = useState(null)
  const [invoiceDetails,setInvoiceDetails] = useState(null)

  // Individual Permissions
  const [individualId, setIndividualId] = useState(null)
  const [individualDetails,setIndividualDetails] = useState(null)

  // Templates

  const [templateId,setTemplateId] = useState(null)
  const [templateDetails,setTemplateDetails] = useState(null)

  //Menu
  const [leftMenuData, setLeftMenuData] = useState(null);
  const [topMenuData, setTopMenuData] = useState(null);
  const [quickLinksData, setQuickLinksData] = useState(null);

  const [micrositeBasicData, setMicrositeBasicData] = useState({});

  // const refreshToken = async () => {
  //   try {
  //     const storedAccessToken = localstorage.getItem(authConfig.storageTokenKeyName)
  //     const storedRefreshToken = localStorage.getItem(authConfig.refreshTokenKeyName);

  //     if (storedRefreshToken) {
  //       const refreshResponse = await axios.post(
  //         getUrl(authConfig.refreshEndpoint),
  //         {
  //           accessToken: storedAccessToken,
  //           refreshToken: storedRefreshToken,
  //         }
  //       );
  //       const newAccessToken = refreshResponse.data.token.refreshToken;
  //       localStorage.setItem(authConfig.storageTokenKeyName, newAccessToken);
  //       return newAccessToken;
  //     }
  //     return null;
  //   } catch (error) {
  //     console.error("Failed to refresh token:", error);
  //     return null;
  //   }
  // };

  let isRefreshing = false;
  let refreshQueue = [];

  axios.interceptors.request.use(
    async (config) => {
      const accessToken = localStorage.getItem(authConfig.storageTokenKeyName);
      if (accessToken) {
        config.headers.Authorization = `Bearer ${accessToken}`;
      }
      return config;
    },
    (error) => {
      return Promise.reject(error);
    }
  );

  axios.interceptors.response.use(
    (response) => {
      return response;
    },
    async (error) => {
      const originalRequest = error.config;

      if (error.response && error.response.status === 401) {
        // if (!isRefreshing) {
        //   isRefreshing = true;
        //   const newAccessToken = await refreshToken();
        //   if (newAccessToken) {
        //     originalRequest.headers.Authorization = `Bearer ${newAccessToken}`;
        //     return axios(originalRequest);
        //   } else {
        //     window.location.href = "/login";
        //   }
        //   isRefreshing = false;
        // } else {
        //   return new Promise((resolve) => {
        //     refreshQueue.push(() => {
        //       originalRequest.headers.Authorization = `Bearer ${localStorage.getItem(
        //         authConfig.storageTokenKeyName
        //       )}`;
        //       resolve(axios(originalRequest));
        //     });
        //   });
        // }
        window.location.href = "/login";
      }
      return Promise.reject(error);
    }
  );

  const [listValues, setListValues] = useState(null);
  const [listNames, setListNames] = useState(null);

  useEffect(() => {
    if (user) {
      
      // // API call for LEFT_MENU

      // // Function to select the appropriate menu based on user category
      // const getMenuType = (userCategory) => {
      //   switch (userCategory) {
      //     case "Service Provider":
      //       return "SP_LEFT_MENU";
      //     case "Society":
      //       return "CHS_LEFT_MENU";
      //     case "Employee":
      //       return "EMP_LEFT_MENU";
      //     case "Super Admin":
      //       return "ADMIN_LEFT_MENU";
      //     default:
      //       return "ADMIN_LEFT_MENU";
      //   }
      // };

      // // Determine the menu type using user category
      // const menuType = getMenuType(user?.userCategory);

      // // API call for LEFT_MENU with the appropriate settings type
      // axios({
      //   method: "get",
      //   url: `${getUrl(authConfig.settings)}/?settingsType=${menuType}`,
      //   headers: getAuthorizationHeaders(),
      // })
      //   .then((res) => {
      //     console.log("LEFT MENU JSON---------", res.data);
      //     setLeftMenuData(res.data);
      //   })
      //   .catch((error) => {
      //     console.error("Error fetching left menu data:", error);
      //   });

      // // API call for TOP_MENU
      // axios({
      //   method: "get",
      //   url: getUrl(authConfig.settings) + "/?settingsType=TOP_MENU",
      //   headers: getAuthorizationHeaders(),
      // }).then((res) => {
      //   setTopMenuData(res.data);
      // });

      // // API call for QUICK_LINKS
      // axios({
      //   method: "get",
      //   url: getUrl(authConfig.settings) + "/?settingsType=QUICK_LINKS",
      //   headers: getAuthorizationHeaders(),
      // }).then((res) => {
      //   setQuickLinksData(res.data);
      // });

      // API Call for fetching List values

      axios({
        method: "post",
        url: getUrl(authConfig.allListNamesValues) + "?selectType=LIST_VALUES",
      })
        .then((res) => {
          setListValues(res.data.data);
          window.localStorage.setItem(
            authConfig.listValues,
            JSON.stringify(res.data.data)
          );
        })
        .catch((err) => console.log("List values error", err));

      axios({
        method: "post",
        url: getUrl(authConfig.allListNamesValues) + "?selectType=LIST_NAMES",
      })
        .then((res) => {
          setListNames(res.data.data);
          window.localStorage.setItem(
            authConfig.listNames,
            JSON.stringify(res.data.data)
          );
        })
        .catch((err) => console.log("List values error", err));


    }
  }, [user]);

  async function sendOTP(params, errorCallback) {
    let response;
    await axios({
      method: "post",
      url: getUrl(authConfig.sendOTP),
      headers : ({
        "Content-Type": authConfig.OTP_SENT_REQUEST,
        "Accept": authConfig.OTP_SENT_RESPONSE
      }),
      data: params,
    })
      .then((res) => {
        response = res.data;
      })

      .catch((err) => (errorCallback ? errorCallback(err) : null));
    return response;
  }
  async function patchVerifyOTP(params, errorCallback) {
    let response;
    await axios({
      method: "patch",
      url: getUrl(authConfig.verifyOTP),
      headers: ({
            "Content-Type": authConfig.OTP_VERIFY_REQUEST,
            "Accept": authConfig.OTP_VERIFY_RESPONSE,
      }),
      data: params,
    })
      .then((res) => {
        response = res.data;
      })

      .catch((err) => (errorCallback ? errorCallback(err) : null));
    return response;
  }

  async function patchResendOTP(params, errorCallback) {
    let response;
    await axios({
      method: "patch",
      url: getUrl(authConfig.resendOTP),
      headers: {
          "Content-Type": authConfig.OTP_RE_SENT_REQUEST,
          "Accept": authConfig.OTP_RE_SENT_RESPONSE,
      },
      data: params,
    })
      .then((res) => {
        response = res.data;
      })

      .catch((err) => (errorCallback ? errorCallback(err) : null));
    return response;
  }


  async function forgotPassword(email, errorCallback) {
      try {
        const response = await axios.post(
          getUrl(authConfig.forgotPasswordEndpoint) + `?email=${email}`
        );
    
        // If the request is successful, return true
        return true;
      } catch (error) {
        // Invoke the error callback if provided
        if (errorCallback) errorCallback(error);
    
        // Return the error
        return error;
      }
  }

  async function resetPassword(data, errorCallback) {
    try {
      await axios({
        method : "post",
        url: getUrl(authConfig.resetPasswordEndpoint),
        data: data
      })
  
      // If the request is successful, return true
      return true;
    } catch (error) {
      // Invoke the error callback if provided
      if (errorCallback) errorCallback(error);
  
      // Return the error
      return error;
    }
}





useEffect(() => {
  if (user || (!!userPackage)) { 

    // API call for User Package Data
    axios({
      method: "get",
      url: getUrl(authConfig.subscriptionEndpoint),
      headers: getAuthorizationHeaders(),
    })
    .then((res) => {
      setUserPackageData(res?.data);
    })
    .catch((err) => {
      console.error("Error fetching user package data:", err);
    });
  }
}, [userPackage, user]);


  const [basicProfileGetData, setBasicProfileGetData] = useState(null);

  // Used in AllProfile then the Baisc-Profile tab is active, Used in Basic-Profile Page.
  // From All-Profile we pass the userId, from basic-profile page we don't pass anything.
  async function getBasicProfileData(userId) {
    const userUniqueId = userId || user?.orgId;
    axios({
      method: "get",
      url:
        getUrl(authConfig.organisationsEndpoint) +
        "/organisation-individual-sp-data/" +
        userUniqueId,
      headers: getAuthorizationHeaders({
        accept:authConfig.SP_ORGANISATION_META_DATA_GET_BY_ID_V1
      }),
    }).then((res) => {
      setBasicProfileGetData(res.data)
      setBasicProfileDataAllProfiles(res.data);
    });
  }

  // Used to uypdate the basic-profile data.
  // This is directly used in the basicProfile edit component,
  // Which in-direcltly used in both all-profiles and basic-profile page.
  async function basicProfile(params, userUniqueId) {
    try {
      const response = await axios({
        method: "patch",
        url: getUrl(`${authConfig.organisationsEndpoint}/${userUniqueId}/sp-basic-profile-meta-data`),
        headers: getAuthorizationHeaders({
          contentType: authConfig.ORGANISATION_PATCH_SP_META_DATA_BY_ID_REQ_V1,
          accept: authConfig.ORGANISATION_PATCH_SP_META_DATA_BY_ID_RES_V1
        }),
        data: params,
      });

      getBasicProfileData(userUniqueId); //This func will Fetch the new data and update the state variable.
      // TODO:: Need to review data in response we get and we can avoid this get call.

      return response.data;
    } catch (error) {
      console.error("Error", error);
      // throw error;
    }
  }

  const [conversation, setConversation] = useState(null);

  const [conversationData, setConversationData] = useState(null);

  useEffect(() => {
    if (!!conversation) {
      console.log("conversationUser --->", conversation);

      axios({
        method: "get",

        url:
          getUrl(authConfig.conversationEndpoint) +
          "/getConversation/" +
          conversation?.id,

        headers: getAuthorizationHeaders(),
      }).then((res) => {
        setConversationData(res.data);

        console.log("Res- CONVERSATION", res.data);
      });
    }
  }, [conversation]);

  useEffect(() => {
    if (!!masterData) {
      axios({
        method: "get",
        url: getUrl(authConfig.masterDataCreateEndpoint) + "/" + masterData.id,
        headers: getAuthorizationHeaders(),
      }).then((res) => {
        setMasterDataDetails(res.data);
      });
    }
  }, [masterData]);

  useEffect(() => {
    if (templateId) {
      axios({
        method: "get",
        url: getUrl(authConfig.templateEndpoint) + "/" + templateId?.id,
        headers: getAuthorizationHeaders(authConfig.TEMPLATE_GET_DETAILS_RES_V1)
      })
      .then((res) => {
        setTemplateDetails(res.data);
      })
      .catch((error) => {
        console.error("Error fetching role by ID:", error);
      });
    }
  }, [templateId]);

  useEffect(() => {
    // Only proceed if roleId is valid
    if (roleId) {
      axios({
        method: "get",
        url: getUrl(authConfig.rolesEndpoint) + "/" + roleId?.id,
        headers: getAuthorizationHeaders(authConfig.getMIMEType)
      })
      .then((res) => {
        setRolesDetails(res.data);
      })
      .catch((error) => {
        console.error("Error fetching role by ID:", error);
      });
    }
  }, [roleId]);


  useEffect(() => {
    // Only proceed if roleId is valid
    if (workOrderId) {
      axios({
        method: "get",
        url: getUrl(authConfig.workOrdersEndpoint) + "/" + workOrderId?.id,
        headers: getAuthorizationHeaders(authConfig.WORK_ORDERS_GET_BY_ID_RES_V1)
      })
      .then((res) => {
        setWorkOrderDetails(res.data);
      })
      .catch((error) => {
        console.error("Error fetching role by ID:", error);
      });
    }
  }, [workOrderId]);

  useEffect(() => {
    // Only proceed if roleId is valid
    if (invoiceId) {
      axios({
        method: "get",
        url: getUrl(authConfig.invoicesEndpoint) + "/" + invoiceId?.id,
        headers: getAuthorizationHeaders(authConfig.INVOICES_GET_BY_ID_RES_V1)
      })
      .then((res) => {
        setInvoiceDetails(res.data);
      })
      .catch((error) => {
        console.error("Error fetching role by ID:", error);
      });
    }
  }, [invoiceId]);


  useEffect(() => {
    // Only proceed if roleId is valid
    if (documents) {
      axios({
        method: "get",
        url: getUrl(authConfig.documentEndpoint) + "/" + documents?.id,
        headers: getAuthorizationHeaders()
      })
      .then((res) => {
        setDocumentDetails(res.data);
      })
      .catch((error) => {
        console.error("Error fetching document by ID:", error);
      });
    }
  }, [documents]);

  useEffect(() => {
    // Only proceed if roleId is valid
    if (individualId) {
      axios({
        method: "get",
        url: getUrl(authConfig.individualEndpoint) + "/" + individualId?.id,
        headers: getAuthorizationHeaders(authConfig.individualGetMIMEType)
      })
      .then((res) => {
        setIndividualDetails(res.data);
      })
      .catch((error) => {
        console.error("Error fetching role by ID:", error);
      });
    }
  }, [individualId]);


  useEffect(() => {

    if (!!specificationData) {
      const url = `${getUrl(
        authConfig.getAllServiceProfiles
      )}/${specificationData.id}/requisitionFields`;
      axios({
        method: "get",
        url:url,
        headers: getAuthorizationHeaders(),
      }).then((res) => {
        console.log("Specifications for serivce",res.data)
        setSpecificationDataDetails(res.data);
      });
    }
  }, [specificationData]);

  useEffect(() => {
    if (!!requisitionData) {
      axios({
        method: "get",
        url: getUrl(authConfig.serviceRequisitionsEndpoint) + "/" + requisitionData.id,
        headers: getAuthorizationHeaders(),
      }).then((res) => {
        setRequisitionDataDetails(res.data);
      });
    }
  }, [requisitionData]);

  useEffect(() => {
    if (!!snapshotsData) {
      axios({
        method: "get",
        url: getUrl(authConfig.snapshots) + "/" + snapshotsData.id,
        headers: getAuthorizationHeaders(),
      }).then((res) => {
        setSnapshotsDataDetails(res.data);
      });
    }
  }, [snapshotsData]);

  useEffect(() => {
    if (!!contactGroupsData) {
      axios({
        method: "get",
        url:
          getUrl(authConfig.contactGroupsEndpoint) + "/" + contactGroupsData.id,
        headers: getAuthorizationHeaders(),
      }).then((res) => {
        setContactGroupsDataDetails(res.data);
      });
    }
  }, [contactGroupsData]);

  useEffect(() => {
    if (!!taskData) {
      console.log("-------------use effect Task Data.", taskData);

      axios({
        method: "get",
        url: getUrl(authConfig.taskEndpoint) + "/" + taskData.id,
        headers: getAuthorizationHeaders(),
      }).then((res) => {
        console.log("taskData GET response", res.data);
        setTaskDataDetails(res.data);
      });
    }
  }, [taskData]);

  useEffect(() => {
    if (!!servicesData) {
      axios({
        method: "get",
        url: getUrl(authConfig.listValuesEndpoint) + "/" + servicesData.id,
        headers: getAuthorizationHeaders(),
      }).then((res) => {
        setServicesDataDetails(res.data);
      });
    }
  }, [servicesData]);

  useEffect(() => {
    if (!!listNamesData) {
      axios({
        method: "get",
        url: getUrl(authConfig.listNameEndpoint) + "/" + listNamesData.id,
        headers: getAuthorizationHeaders(),
      }).then((res) => {
        setListNamesDataDetails(res.data);
      });
    }
  }, [listNamesData]);

  useEffect(() => {
    if (!!locationsData) {
      axios({
        method: "get",
        url: getUrl(authConfig.locationZoneMapping) + "/" + locationsData.id,
        headers: getAuthorizationHeaders(),
      }).then((res) => {
        setLocationsDataDetails(res.data);
      });
    }
  }, [locationsData]);

  useEffect(() => {
    if (!!fsiRulesData) {
      console.log("-------------use effect FSI Rule Data.", fsiRulesData);

      axios({
        method: "get",
        url: getUrl(authConfig.fsiRuleEndpoint) + "/" + fsiRulesData.id,
        headers: getAuthorizationHeaders(),
      }).then((res) => {
        setFsiRulesDataDetails(res.data);
      });
    }
  }, [fsiRulesData]);

  useEffect(() => {
    if (!!fsiData) {

      axios({
        method: "get",
        url: getUrl(authConfig.fsiCalculatorEndpoint) + "/" + fsiData.id,
        headers: getAuthorizationHeaders(),
      }).then((res) => {
        setFsiDataDetails(res.data);
      });
    }
  }, [fsiData]);

  useEffect(() => {
    if (!!roleTypeUser) {
      axios({
        method: "get",
        url:
          getUrl(authConfig.roleTypeEndpoint) + "/" + roleTypeUser.newValue.id,
        headers: getAuthorizationHeaders(),
      }).then((res) => {
        console.log("***---Role Type RESPONSE", res);
        setRoleTypeData(res.data);
      });
    }
  }, [roleTypeUser]);

  //Role States
  const [roleUser, setRoleUser] = useState(null);
  const [roleData, setRoleData] = useState(null);

  useEffect(() => {
    if (!!roleUser) {
      console.log("Role USER--->", roleUser);
      axios({
        method: "get",
        url: getUrl(authConfig.roleEndpoint) + "/" + roleUser?.id,
        headers: getAuthorizationHeaders(),
      }).then((res) => {
        setRoleData(res.data);
        console.log(
          "Fetched Role Data for roleId:",
          roleUser.id,
          "Response:",
          res.data
        );
      });
    }
  }, [roleUser]);

  // Basic Profile States
  // The following states and useEffect are utilized across all profiles page.
  // They are triggered when the "New Conversation" menu item is selected from the 3-vertical-dots icon in the Actions column.
  // These are specifically used in the "Conversation Files" under the conversation section.

  const [basicProfileAllProfiles, setBasicProfileAllProfiles] = useState(null);
  const [basicProfileDataAllProfiles, setBasicProfileDataAllProfiles] =
    useState(null);

  useEffect(() => {
    if (!!basicProfileAllProfiles) {
      axios({
        method: "get",
        url:
          getUrl(authConfig.organisationsEndpoint) +
          "/organisation-individual-sp-data/" +
          basicProfileAllProfiles,
        headers: getAuthorizationHeaders({
          accept:authConfig.SP_ORGANISATION_META_DATA_GET_BY_ID_V1
        }),
      }).then((res) => {
        setBasicProfileDataAllProfiles(res.data);
      });
    }
  }, [basicProfileAllProfiles, convsCreated]);

  useEffect(() => {
    if (shortFormData) {
      console.log("-------------SHORT FORM DATA.", shortFormData);
    }
  }, [shortFormData]);

  useEffect(() => {
    if (!!profileUser) {
      console.log("-------------use effect profileUser.", profileUser);
      axios({
        method: "get",
        url: getUrl(authConfig.entityPatch) + "/" + profileUser.entityId,
        headers: getAuthorizationHeaders(),
      }).then((res) => {
        const profileData = returnEntity(res.data, profileUser.entityCategory);
        console.log("*****---Super Admin Entity Profile---*****", profileData);
        console.log("***---RESPONSE", res);
        setProfileEntityData(profileData);
      });
    }
  }, [profileUser]);

  useEffect(() => {
    if (!!employeeProfile) {
      console.log("-------------use effect employee profile.", employeeProfile);

      axios({
        method: "get",
        url:
          getUrl(authConfig.employeeUpdateEndpoint) + "/" + employeeProfile.id,
        headers: getAuthorizationHeaders(),
      }).then((res) => {
        console.log("Employee GET response", res.data);
        setEmployeeData(res.data);
      });
    }
  }, [employeeProfile]);

  async function getAdminEntityProfile() {
    await axios({
      method: "get",
      url: getUrl(authConfig.entityPatch) + "/" + profileUser.entityId,
      headers: getAuthorizationHeaders(),
    })
      .then((res) => {
        const response = returnEntity(res.data, profileUser.entityCategory);
        setProfileEntityData(response);
      })
      .catch((err) => (errorCallback ? errorCallback(err) : null));
  }

  useEffect(() => {
    const initAuth = async () => {
      const storedToken = window.localStorage.getItem(
        authConfig.storageTokenKeyName
      );
      if (storedToken) {
        setLoading(true);
        await axios
          .get(getUrl(authConfig.individualPermissionsEndpoint), {
            headers: {
              Authorization: "Bearer " + storedToken,
            },
          })
          .then(async (response) => {
            response.data.role = "admin";
            setLoading(false);
            setUser({ ...response.data });
          })
          .catch((error) => {
            console.error("**************useEffect->initAuth: error:", error);
            localStorage.removeItem(authConfig.storageUserKeyName); //userData
            localStorage.removeItem(authConfig.storageTokenKeyName); //accessToken
            localStorage.removeItem("refreshToken");
            setUser(null);
            setLoading(false);
            if (
              authConfig.onTokenExpiration === "logout" &&
              !router.pathname.includes("login")
            ) {
              console.log(
                "***************useEffect->initAuth: redirecting to login. Token Expired."
              );
              router.replace("/login");
            }
          });
      } else {
        setLoading(false);
      }
    };
    initAuth();
    setCount(count + 1);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  function mapTheBody(params, reqEntityId, entityCategory, entityType) {
    const body = {
      id: reqEntityId,
      entityCategory: entityCategory,
      entityType: entityType,
    };

    //params.id = reqEntityId;

    switch (entityCategory) {
      case "ARCHITECT":
        body.architectRegisterDTO = params;
        break;
      case "SOCIETY":
        body.societyRegisterDTO = params;
        break;
      case "STRUCTURAL_ENGINEER":
        body.structuralEngineerRegisterDTO = params;
        break;
      case "PMC":
        body.pmcRegisterDTO = params;
        break;
      case "BROKER":
        body.brokerRegisterDTO = params;
        break;
      case "CHARTERED_ACCOUNTANT":
        body.charteredAccountantRegisterDTO = params;
        break;
      case "LEGAL":
        body.legalRegisterDTO = params;
        break;
      default:
    }
    return body;
  }

  async function handleGetAllMembers(errorCallback) {
    let response;
    await axios({
      method: "get",
      url: getUrl(authConfig.getAllMembersEndpoint),
      headers: getAuthorizationHeaders(),
    })
      .then((res) => {
        response = res.data;
      })
      .catch((err) => (errorCallback ? errorCallback(err) : null));

    return response;
  }

  async function patchEntityServices(params, errorCallback) {
    const reqEntityId =
      user.entityCategory == "SUPER_ADMIN"
        ? profileUser.entityId
        : user.entityId;
    const reqEntityCategory =
      user.entityCategory == "SUPER_ADMIN"
        ? profileUser.entityCategory
        : user.entityCategory;
    const reqEntityType =
      user.entityCategory == "SUPER_ADMIN" ? profileUser.entityType : user.type;

    await axios({
      method: "patch",
      url: getUrl(authConfig.entityPatch) + "/services",
      headers: getAuthorizationHeaders(),
      data: mapTheBody(params, reqEntityId, reqEntityCategory, reqEntityType),
    })
      .then((res) => {
        const response = returnEntity(res.data, reqEntityCategory);
        user.entityCategory == "SUPER_ADMIN"
          ? setProfileEntityData(response)
          : setEntityData(response);
      })
      .catch((err) => (errorCallback ? errorCallback(err) : null));
  }

  async function patchArchitectAdditionalData(
    data,
    successCallback,
    errorCallback
  ) {
    const reqEntityId =
      user.entityCategory == "SUPER_ADMIN"
        ? profileUser.entityId
        : user.entityId;
    const reqEntityCategory =
      user.entityCategory == "SUPER_ADMIN"
        ? profileUser.entityCategory
        : user.entityCategory;
    const reqEntityType =
      user.entityCategory == "SUPER_ADMIN" ? profileUser.entityType : user.type;

    await axios({
      method: "patch",
      url: getUrl(
        authConfig.entityPatch + "/" + reqEntityId + "/architectData/"
      ),
      headers: getAuthorizationHeaders(),
      data: data,
    })
      .then((res) => {
        const response = returnEntity(res.data, reqEntityCategory);
        user.entityCategory == "SUPER_ADMIN"
          ? setProfileEntityData(response)
          : setEntityData(response);
      })
      .catch((err) => (errorCallback ? errorCallback(err) : null));
  }

  async function patchReadiness(orgId,data,setReadinessUpdated,errorCallback, handleSuccess) {
    await axios({
      method: "put",
      url: getUrl(authConfig.getReadinessDataEndpoint) + "/" + orgId + "/readiness",
      headers: getAuthorizationHeaders({
        accept:authConfig.ORGANISATION_CONTEXTUAL_DATA_GET_BY_ID_V1
      }),
      data: data,
    })
      .then((res) => {
        const response = res.data;
        handleSuccess(data?.percentage, data?.generateReport);
      })
      .catch((err) => (errorCallback ? errorCallback(err) : null));
  }

  async function patchIsEmpanelled(data) {
    let response;
    await axios({
      method: "patch",
      url: getUrl(authConfig.usersEndpoint + "/empanelled"),
      headers: getAuthorizationHeaders(),
      data: data,
    })
      .then((res) => {
        response = res.data;
      })
      .catch((err) => (errorCallback ? errorCallback(err) : null));
    return response;
  }

  async function getAllListValuesByListNameId(
    listNameId,
    successHandle,
    errorHandle
  ) {
    try {
      // Here (!!listNameId) is true when the variabe is valid. So we want to handle error when the variable is not valid.
      // So added (!(!!listNameId))

      if (!!!listNameId) {
        errorHandle("Invalid listNameId: ", listNameId);
      }
      await axios({
        method: "get",
        url: getUrl(authConfig.getAllListValuesByListNameId),
        params: {
          id: listNameId,
        },
      })
        .then((response) => {
          successHandle(response.data);
        })
        .catch((error) => {
          errorHandle(error);
        });
    } catch (error) {
      console.error("AuthContext: getAllListValuesByListNameId Error", error);
    }
  }

  async function patchIsStrategicPartner(data) {
    let response;
    await axios({
      method: "patch",
      url: getUrl(authConfig.organisationsEndpoint + "/strategicPartner"),
      headers: getAuthorizationHeaders(),
      data: data,
    })
      .then((res) => {
        response = res.data;
      })
      .catch((err) => (errorCallback ? errorCallback(err) : null));
    return response;
  }

  async function projectData(params, errorCallback) {
    const reqEntityId =
      user.entityCategory == "SUPER_ADMIN"
        ? profileUser.entityId
        : user.entityId;
    const reqEntityCategory =
      user.entityCategory == "SUPER_ADMIN"
        ? profileUser.entityCategory
        : user.entityCategory;

    await axios({
      method: "patch",
      url:
        getUrl(authConfig.projectDataEndpoint) +
        "/" +
        reqEntityId +
        "/projectData",
      headers: getAuthorizationHeaders(),
      data: params,
    })
      .then((res) => {
        const response = returnEntity(res.data, reqEntityCategory);
        user.entityCategory == "SUPER_ADMIN"
          ? setProfileEntityData(response)
          : setEntityData(response);
      })
      .catch((err) => (errorCallback ? errorCallback(err) : null));
  }

  async function addNewUser(params, errorCallback) {
    try {
      await axios({
        method: "post",
        url: getUrl(authConfig.usersEndpoint + "/createUser"),
        headers: getAuthorizationHeaders(),
        data: params,
      });

      return true;
    } catch (err) {
      if (errorCallback) errorCallback(err);

      console.log("error");
      return false;
    }
  }

  async function createRoleType(params, errorCallback) {
    let response;
    await axios({
      method: "post",
      url: getUrl(authConfig.roleTypeEndpoint),
      headers: getAuthorizationHeaders(),
      data: params,
    })
      .then((res) => {
        response = res.data;
      })

      .catch((err) => (errorCallback ? errorCallback(err) : null));
    return response;
  }

  async function updateRoleType(data, errorCallback) {
    console.log("data sent", data);

    try {
      const response = await axios.patch(
        getUrl(authConfig.roleTypeEndpoint),
        data,
        { headers: getAuthorizationHeaders() }
      );
      return response.data;
    } catch (err) {
      if (errorCallback) {
        errorCallback(err);
      }
    }
  }

  async function handleUpdateProfile(params, errorCallback) {
    let response;
    await axios({
      method: "patch",
      url:
        getUrl(authConfig.profileUpdateEndpoint) +
        "/" +
        user.id +
        "/profileUpdate",
      headers: getAuthorizationHeaders(),
      data: params,
    })
      .then((res) => {
        response = res.data;
        console.log(response);
      })
      .catch((err) => (errorCallback ? errorCallback(err) : null));
  }

  async function patchShortForm(params, errorCallback) {
    let response;
    await axios({
      method: "patch",
      url:
        getUrl(authConfig.profileUpdateEndpoint) +
        "/" +
        shortFormData.id +
        "/profileUpdate",
      headers: getAuthorizationHeaders(),
      data: params,
    })
      .then((res) => {
        response = res.data;
        setShortFormData(res.data);
        console.log(
          "Response Short Form Submission",
          response,
          "M id",
          shortFormData.id
        );
      })
      .catch((err) => (errorCallback ? errorCallback(err) : null));
  }

  //  Used to update the Society Profile, This func is old func used for all the profiles as common.
  async function patchEntity(params, userUniqueId) {
    try {
      const response = await axios({
        method: "patch",
        url: getUrl(authConfig.userPatch) + "/" + userUniqueId,
        headers: getAuthorizationHeaders(),
        data: params,
      });
      setEntityData(response.data);
      return response.data;
    } catch (error) {
      console.error("Error", error);
      // throw error;
    }
  }

  // Used to fetch the Society profile data.
  async function getSocietyProfile(userId) {
    // let value = localStorage.getItem("userData");
    const userUniqueId = userId || user?.id;
    await axios({
      method: "get",
      url: getUrl(authConfig.userGet) + "/" + userUniqueId,
      headers: getAuthorizationHeaders(),
    })
      .then((res) => {
        setEntityData(res.data);
      })
      .catch((err) => console.log("error", err));
  }

  //TODO:: TEMP function nly for architect projects list to refresh after save. Later we can replace getSocietyProfile with this function.
  async function tempGetArchitectEntityProfile(errorCallback) {
    const reqEntityId =
      user.entityCategory == "SUPER_ADMIN"
        ? profileUser.entityId
        : user.entityId;

    const reqEntityCategory =
      user.entityCategory == "SUPER_ADMIN"
        ? profileUser.entityCategory
        : user.entityCategory;

    await axios({
      method: "get",
      url: getUrl(authConfig.entityPatch) + "/" + reqEntityId,
      headers: getAuthorizationHeaders(),
    })
      .then((res) => {
        const response = returnEntity(res.data, reqEntityCategory);
        user.entityCategory == "SUPER_ADMIN"
          ? setProfileEntityData(response)
          : setEntityData(response);
      })
      .catch((err) => (errorCallback ? errorCallback(err) : null));
  }

  async function getReadinessProfile(orgId,errorCallback) {
    let response;
    await axios({
      method: "get",
      url: getUrl(authConfig.getReadinessDataEndpoint) + "/"+ orgId + "/readiness",
      headers: getAuthorizationHeaders({
        accept: authConfig.ORGANISATION_CONTEXTUAL_DATA_GET_BY_ID_V1
      }),
    })
      .then((res) => {
        response = res.data;
        setReadinessProfileData(response);
      })
      .catch((err) => (errorCallback ? errorCallback(err) : null));
  }

  async function handleLogin(
    params,
    errorCallback,
    successCallback,
    handlePopup
  ) {
    window.localStorage.setItem("test", "Test");
    await axios
      .post(getUrl(authConfig.loginEndpointNew), params)
      .then(async (response) => {
        if (response.data.token.message === "Already Logged in") {
          console.log("********************data");
          handlePopup();
        }
        console.log("##########################data");

        const accessToken = response.data.token.accessToken;
        const refreshToken = response.data.token.refreshToken;
        console.log("USER INFO", response.data);
        console.log("refreshToken", refreshToken);
        // localStorage.setItem(authConfig.storageTokenKeyName,accessToken)
        localStorage.setItem(authConfig.refreshTokenKeyName, refreshToken);

        jwt.verify(
          accessToken,
          Buffer.from(jwtConfig.secret, "base64"),
          {
            algorithms: ["HS512"],
          },
          async (err, decoded) => {
            // ** If token is expired
            if (err) {
              // ** If onTokenExpiration === 'logout' then send 401 error
              if (defaultAuthConfig.onTokenExpiration === "logout") {
                // ** 401 response will logout user from AuthContext file
                router.replace("/login");
              } else {
                errorCallback(err);
              }
            } else {
              successCallback();
              await new Promise((resolve) => setTimeout(resolve, 3000));
              window.localStorage.setItem(
                authConfig.storageTokenKeyName,
                accessToken
              );
              window.localStorage.setItem(
                authConfig.refreshTokenKeyName,
                refreshToken
              );

              await fetchProfile(accessToken);

              console.log("Starting fetch profile", accessToken);
              console.log("After fetch profile", accessToken);
              router.replace("/dashboard");
            }
          }
        );
      })
      .catch((err) => {
        if (errorCallback) errorCallback(err);
      });
  }

  async function handleLoginNew(
    params,
    errorCallback,
    successCallback,
    handlePopup
  ) {
    await axios
      .post(getUrl(authConfig.loginEndpoint), params)
      .then(async (response) => {
        console.log("##########################data",response.data);
        if (response.data.message === "Already Logged in") {
          console.log("********************data");
          handlePopup();
        }
        console.log("##########################data",response.data);

        const accessToken = response.data.accessToken;
        const refreshToken = response.data.refreshToken;
        console.log("USER INFO", response.data);
        console.log("refreshToken", refreshToken);
        // localStorage.setItem(authConfig.storageTokenKeyName,accessToken)
        localStorage.setItem(authConfig.refreshTokenKeyName, refreshToken);

        jwt.verify(
          accessToken,
          Buffer.from(jwtConfig.secret, "base64"),
          {
            algorithms: ["HS512"],
          },
          async (err, decoded) => {
            // ** If token is expired
            if (err) {
              // ** If onTokenExpiration === 'logout' then send 401 error
              if (defaultAuthConfig.onTokenExpiration === "logout") {
                // ** 401 response will logout user from AuthContext file
                router.replace("/login");
              } else {
                errorCallback(err);
              }
            } else {
              successCallback();
              await new Promise((resolve) => setTimeout(resolve, 3000));
              setLoginLoad(true)
              window.localStorage.setItem(
                authConfig.storageTokenKeyName,
                accessToken
              );
              window.localStorage.setItem(
                authConfig.refreshTokenKeyName,
                refreshToken
              );
              await new Promise((resolve) => setTimeout(resolve, 3000));
              await fetchProfile(accessToken);

              console.log("Starting fetch profile", accessToken);
              console.log("After fetch profile", accessToken);
                // Force Next.js Router to refresh
    router.replace("/dashboard").then(() => router.reload());
            }
          }
        );
      })
      .catch((err) => {
        if (errorCallback) errorCallback(err);
      });
  }

  async function sendWatiMessage(
    whatsappNumber,
    templateName,
    broadcastName,
    parameters,
    errorCallback,
    handleSuccess
  ) {
    try {
      const payload = {
        template_name: templateName,
        broadcast_name: broadcastName,
        parameters: parameters,
      };
      console.log("Payload being sent", payload);
      const response = await axios.post(
        `${getUrl(authConfig.watiEndPoint)}?whatsappNumber=${encodeURIComponent(
          whatsappNumber
        )}`,
        payload,
        {
          headers: getAuthorizationHeaders(),
        }
      );

      handleSuccess(response.data);
      return response.data;
    } catch (err) {
      if (errorCallback) {
        errorCallback(err);
      }
      return null;
    }
  }

  async function sendWatiMessages(
    templateName,
    broadcastName,
    receivers,
    errorCallback,
    handleSuccess
  ) {
    try {
      const payload = {
        template_name: templateName,
        broadcast_name: broadcastName,
        receivers: receivers,
      };

      const response = await axios.post(
        getUrl(authConfig.watiMutliMessageEndPoint),
        payload,
        {
          headers: getAuthorizationHeaders(),
        }
      );

      handleSuccess(response.data);
      return response.data;
    } catch (err) {
      if (errorCallback) {
        errorCallback(err);
      }
      return null;
    }
  }

  async function fetchProfile(accessToken) {
    await axios
      .get(getUrl(authConfig.individualPermissionsEndpoint), {
        headers: {
          Authorization: "Bearer " + accessToken,
        },
      })
      .then(async (response) => {
       
        // Save user data to local storage
        window.localStorage.setItem(
          authConfig.storageUserKeyName,
          JSON.stringify(response.data)
        );
  
        // Update loading state and user data
        setLoading(false);
        setUser({ ...response.data });
        console.log("data", response.data);
        

        

         // Set a default profile URL without handling entity categories
         response.data.profileURL = "/dashboard";
  
  
      })
      .catch((error) => {
        console.error("############# fetchProfile error", error);
        // Clear stored tokens and user data on error
        localStorage.removeItem(authConfig.storageUserKeyName);
        localStorage.removeItem(authConfig.storageTokenKeyName);
        localStorage.removeItem(authConfig.refreshEndpoint);
        setUser(null);
        setLoading(false);
        // Uncomment below if redirect to login is required on token expiration
        // if (authConfig.onTokenExpiration === "logout") {
        //   console.log("################ Redirect to Login");
        //   window.location.href = "/login";
        // }
      });
  }
  

  const handleLogout = () => {
    setUser(null);
    window.localStorage.removeItem(authConfig.storageUserKeyName);
    window.localStorage.removeItem(authConfig.storageTokenKeyName);
    window.localStorage.removeItem(authConfig.refreshEndpoint);
    //router.push("/login");
  };

  const handleHome = () => {
    router.push("/");
  };

  const handleRegister = (params, errorCallback, successCallback) => {
    axios
      .post(getUrl(authConfig.registerEndpointNewV2), params)
      .then((res) => {
        if (res.data.error) {
          if (errorCallback) errorCallback(res.data.error);
        } else {
          successCallback(res.data);
        }
      })
      .catch((err) => (errorCallback ? errorCallback(err) : null));
  };

  const handleSignup = (params, errorCallback, successCallback) => {
    axios
      .post(getUrl(authConfig.registerEndpointNewV3), params)
      .then((res) => {
        if (res.data.error) {
          if (errorCallback) errorCallback(res.data.error);
        } else {
          successCallback(res.data);
          console.log("Success callback", res.data);
        }
      })
      .catch((err) => (errorCallback ? errorCallback(err) : null));
  };

  const handleTriggerForgetPassword = (email, errorCallback) => {
    return new Promise((resolve, reject) => {
      axios
        .post(getUrl(authConfig.triggerForgetPasswordEndpoint), null, {
          params: { email: email },
        })
        .then((res) => {
          if (res.data.error) {
            if (errorCallback) errorCallback(res.data.error);
            reject(res.data.error);
          } else {
            resolve();
          }
        })
        .catch((err) => {
          if (errorCallback) errorCallback(err);
          reject(err);
        });
    });
  };

  async function handleEmailVerification(params, errorCallback, isOkayClicked) {
    console.log("Start handleEmailVerification", params);
    await axios
      .post(getUrl(authConfig.emailVerificationEndpoint), params)
      .then(async (res) => {
        console.log("Then handleEmailVerification; res.data:", res.data);
        if (res.data.error) {
          if (errorCallback) errorCallback(res.data.error);
        }
      })
      .catch((err) => (errorCallback ? errorCallback(err) : null));

    router.push("/reset-password");
  }

  async function handleForgotResetVerification(
    params,
    errorCallback,
    successCallback
  ) {
    try {
      const res = await axios.post(
        getUrl(authConfig.passwordResetEndpoint),
        params
      );
      if (res.data.error) {
        errorCallback(res.data.error);
      } else {
        successCallback();
      }
    } catch (err) {
      errorCallback(
        err.response ? err.response.data : "Unknown error occurred."
      );
    }
  }

  async function listNamesCreate(params, errorCallback, handleSuccess) {
    let response;
    await axios({
      method: "post",
      url: getUrl(authConfig.listNameEndpoint),
      headers: getAuthorizationHeaders(),
      data: params,
    })
      .then((res) => {
        response = res.data;
        handleSuccess();
      })

      .catch((err) => (errorCallback ? errorCallback(err) : null));
    return response;
  }

  async function masterDataCreate(params, errorCallback, handleSuccess) {
    let response;
    await axios({
      method: "post",
      url: getUrl(authConfig.masterDataCreateEndpoint),
      headers: getAuthorizationHeaders(),
      data: params,
    })
      .then((res) => {
        response = res.data;
        handleSuccess();
      })

      .catch((err) => (errorCallback ? errorCallback(err) : null));
    return response;
  }

  async function masterDataServiceCreate(params, errorCallback,successCallback) {
    let response;
    await axios({
      method: "post",
      url: getUrl(authConfig.listValuesEndpoint),
      headers: getAuthorizationHeaders(),
      data: params,
    })
      .then((res) => {
        response = res.data;
        successCallback()
        return response;
      })

      .catch((err) => (errorCallback ? errorCallback(err) : null));
    return response;
  }

  async function locationZoneMapCreate(params, errorCallback) {
    let response;
    await axios({
      method: "post",
      url: getUrl(authConfig.locationZoneMapping),
      headers: getAuthorizationHeaders(),
      data: params,
    })
      .then((res) => {
        response = res.data;
      })

      .catch((err) => (errorCallback ? errorCallback(err) : null));

    return response;
  }

  async function questionCreate(params, errorCallback) {
    let response;
    await axios({
      method: "post",
      url: getUrl(authConfig.listNameStatisticsQuestion),
      headers: getAuthorizationHeaders(),
      data: params,
    })
      .then((res) => {
        response = res.data;
      })

      .catch((err) => {
        response = err.response.data;
      });
    return response;
  }

  async function questionMapCreate(params, errorCallback) {
    let response;
    await axios({
      method: "post",
      url: getUrl(authConfig.getAllServiceProfiles),
      headers: getAuthorizationHeaders(),
      data: params,
    })
      .then((res) => {
        response = res.data;
      })

      .catch((err) => (errorCallback ? errorCallback(err) : null));
    return response;
  }

  async function specificationCreate(params, errorCallback) {
    let response;
    await axios({
      method: "post",
      url: getUrl(authConfig.getAllServiceProfiles),
      headers: getAuthorizationHeaders(),
      data: params,
    })
      .then((res) => {
        response = res.data;
      })

      .catch((err) => (errorCallback ? errorCallback(err) : null));
    return response;
  }

  async function fsiRuleCreate(params, errorCallback, handleSuccess) {
    let response;
    await axios({
      method: "post",
      url: getUrl(authConfig.fsiRuleEndpoint),
      headers: getAuthorizationHeaders(),
      data: params,
    })
      .then((res) => {
        response = res.data;
        handleSuccess();
      })

      .catch((err) => (errorCallback ? errorCallback(err) : null));
    return response;
  }

  async function calculatedFsiCreate(params, errorCallback, handleSuccess) {
    let response;
    await axios({
      method: "post",
      url: getUrl(authConfig.fsiCalculatorEndpoint),
      headers: getAuthorizationHeaders(),
      data: params,
    })
      .then((res) => {
        response = res.data;
        handleSuccess();
      })

      .catch((err) => (errorCallback ? errorCallback(err) : null));
    return response;
  }

  async function calculatedFsiUpdate(data, errorCallback) {
    try {
      const response = await axios.patch(
        getUrl(authConfig.fsiCalculatorEndpoint),
        data,
        { headers: getAuthorizationHeaders() }
      );
      setFsiDataDetails(response.data);
      console.log("FSI Calculator Update Response", response);
      return response.data;
    } catch (err) {
      if (errorCallback) {
        errorCallback(err);
      }
    }
  }

  async function fsiRuleUpdate(data, errorCallback) {
    try {
      const response = await axios.patch(
        getUrl(authConfig.fsiRuleEndpoint),
        data,
        { headers: getAuthorizationHeaders() }
      );
      setFsiRulesDataDetails(response.data);
      return response.data;
    } catch (err) {
      if (errorCallback) {
        errorCallback(err);
      }
    }
  }

  async function masterDataUpdate(data, errorCallback) {
    try {
      const response = await axios.patch(
        getUrl(authConfig.masterDataCreateEndpoint),
        data,
        { headers: getAuthorizationHeaders() }
      );
      setMasterDataDetails(response.data);
      console.log("Master Data Update Response", response.data);
      return response.data;
    } catch (err) {
      if (errorCallback) {
        errorCallback(err);
      }
    }
  }

  async function serviceDataUpdate(data, errorCallback) {
    try {
      const response = await axios.patch(
        getUrl(authConfig.listValuesEndpoint),
        data,
        { headers: getAuthorizationHeaders() }
      );
      setServicesDataDetails(response.data);
      return response.data;
    } catch (err) {
      if (errorCallback) {
        errorCallback(err);
      }
    }
  }

  async function locationUpdate(data, errorCallback) {
    try {
      const response = await axios.patch(
        getUrl(authConfig.listValuesEndpoint),
        data,
        { headers: getAuthorizationHeaders() }
      );
      return response.data;
    } catch (err) {
      if (errorCallback) {
        errorCallback(err);
      }
    }
  }

  async function locationZoneUpdate(data, errorCallback) {
    try {
      const response = await axios.patch(
        getUrl(authConfig.locationZoneMapping),
        data,
        { headers: getAuthorizationHeaders() }
      );
      setLocationsDataDetails(response?.data);
      return response.data;
    } catch (err) {
      if (errorCallback) {
        errorCallback(err);
      }
    }
  }

  async function listNamesUpdate(data, errorCallback) {
    try {
      const response = await axios.patch(
        getUrl(authConfig.listNameEndpoint),
        data,
        { headers: getAuthorizationHeaders() }
      );
      setListNamesDataDetails(response.data);
      return response.data;
    } catch (err) {
      if (errorCallback) {
        errorCallback(err);
      }
    }
  }

  async function labelUpdate(data, errorCallback) {
    try {
      const response = await axios.patch(
        getUrl(authConfig.getAllServiceProfiles),
        data,
        { headers: getAuthorizationHeaders() }
      );
      // setServicesDataDetails(response.data);
      return response.data;
    } catch (err) {
      if (errorCallback) {
        errorCallback(err);
      }
    }
  }

  async function employeeCreate(params, errorCallback, handleSuccess) {
    let response;
    await axios({
      method: "post",
      url: getUrl(authConfig.employeeCreateEndpoint),
      headers: getAuthorizationHeaders(),
      data: params,
    })
      .then((res) => {
        response = res.data;
        handleSuccess();
      })

      .catch((err) => (errorCallback ? errorCallback(err) : null));
    return response;
  }

  async function employeeCreate(params, errorCallback, handleSuccess) {
    let response;
    await axios({
      method: "post",
      url: getUrl(authConfig.employeeCreateEndpoint),
      headers: getAuthorizationHeaders(),
      data: params,
    })
      .then((res) => {
        response = res.data;
        handleSuccess();
      })

      .catch((err) => (errorCallback ? errorCallback(err) : null));
    return response;
  }

  async function employeeUpdate(data, errorCallback) {
    try {
      const response = await axios.patch(
        getUrl(authConfig.employeeUpdateEndpoint),
        data,
        { headers: getAuthorizationHeaders() }
      );
      setEmployeeData(response.data);
      console.log("Employee Update Response", response.data);
      return response.data;
    } catch (err) {
      if (errorCallback) {
        errorCallback(err);
      }
    }
  }

  async function requisitionCreate(params, errorCallback, handleSuccess) {
    let response;
    await axios({
      method: "post",
      url: getUrl(authConfig.serviceRequisitionsEndpoint),
      headers : getAuthorizationHeaders({
        contentType: authConfig.postSRMIMEType,
        accept: authConfig.postSRAcceptMIMEType
      }),
      data: params,
    })
      .then((res) => {
        response = res.data;
        handleSuccess();
      })

      .catch((err) => (errorCallback ? errorCallback(err) : null));
    return response;
  }

  async function templateCreate(params, errorCallback, handleSuccess) {
    let response;
    await axios({
      method: "post",
      url: getUrl(authConfig.templateEndpoint),
      headers : getAuthorizationHeaders({
        contentType: authConfig.TEMPLATE_CREATE_REQ_V1,
        accept: authConfig.TEMPLATE_CREATE_RES_V1
      }),
      data: params,
    })
      .then((res) => {
        response = res.data;
        handleSuccess();
      })

      .catch((err) => (errorCallback ? errorCallback(err) : null));
    return response;
  }


  async function templateUpdate(templateId, params, errorCallback, handleSuccess) {
    let response;
    await axios({
      method: "put",
      url: getUrl(authConfig.templateEndpoint) + "/" + templateId, 
      headers : getAuthorizationHeaders({
        contentType: authConfig.TEMPLATE_UPDATE_REQ_V1,
        accept: authConfig.TEMPLATE_UPDATE_RES_V1
      }),
      data: params,
    })
      .then((res) => {
        response = res.data;
        setTemplateDetails(res.data)
        handleSuccess();
      })

      .catch((err) => (errorCallback ? errorCallback(err) : null));
  }

  async function updateSiteVisitStatusBookOrCancel(serviceRequisitionId, siteVisitId, statusValue, data, handleFailure, handleSuccess) {
    let response;

    await axios({
      method: "patch",
      url: getUrl(authConfig.siteVisitsEndpoint+"/"+siteVisitId+'/service-requisition'+'/'+ serviceRequisitionId),
      headers : getAuthorizationHeaders({
        contentType: authConfig.SITE_VISIT_TIMINGS_PATCH_STATUS_RES_V1
      }),
      data: data,
      params: {status:statusValue},
    })
      .then((res) => {
        response = res.data;
        console.log("REPSONSE SV",response)
        handleSuccess();
      })

      .catch((err) => (handleFailure ? handleFailure(err) : null));
    return response;
  }

  async function broadCastSrToSpCreate(params, type, errorCallback, handleSuccess) {
    try {
        const response = await axios({
            method: "post",
            url: getUrl(authConfig.postBroadCastSrToSpEndpoint),
            headers: getAuthorizationHeaders({
                contentType: authConfig.SR_QUOTATIONS_CREATE_REQ_V1,
                accept: authConfig.SR_QUOTATIONS_CREATE_RES_V1,
            }),
            data: params, 
            params: { type }
        });

        handleSuccess();
        return response.data;
    } catch (error) {
        console.error("Error broadcasting SR to SP:", error);
        if (errorCallback) errorCallback(error);
        return null; // Return null to indicate failure
    }
}


async function broadCastSrToSpFinalCreate(params, templateId, errorCallback, handleSuccess) {
  try {
      const response = await axios({
          method: "post",
          url: getUrl(authConfig.postBroadCastSrToSpEndpoint) + "/broadcast-final-boq/"+templateId+"?type=FINAL",
          headers: getAuthorizationHeaders({
              contentType: authConfig.SR_QUOTATIONS_CREATE_REQ_V1,
              accept: authConfig.SR_QUOTATIONS_CREATE_RES_V1,
          }),
          data: params
      });

      handleSuccess();
      return response.data;
  } catch (error) {
      console.error("Error broadcasting SR to SP Final BOQ:", error);
      if (errorCallback) errorCallback(error);
      return null; // Return null to indicate failure
  }
}

  async function createRole(params, handleFailure, handleSuccess) {
    let response;
    await axios({
      method: "post",
      url: getUrl(authConfig.rolesEndpoint),
      headers : getAuthorizationHeaders({
        contentType: authConfig.postMIMEType,
        accept: authConfig.postAcceptMIMEType
      }),
      data: params,
    })
      .then((res) => {
        response = res.data;
        handleSuccess();
        return response;
      })

      .catch((err) =>(handleFailure ? handleFailure(err) : null));
  
  }

  async function patchRole(roleId, params, errorCallback, handleSuccess) {
    let response;
    await axios({
      method: "patch",
      url: getUrl(authConfig.rolesEndpoint) + "/" + roleId, 
      headers : getAuthorizationHeaders({
        contentType: authConfig.patchMIMEType,
        accept: authConfig.patchAcceptMIMEType
      }),
      data: params,
    })
      .then((res) => {
        response = res.data;
        setRolesDetails(res.data)
        handleSuccess();
      })

      .catch((err) => (errorCallback ? errorCallback(err) : null));
  }


  async function patchWorkOrder(workOrderId, params, errorCallback, handleSuccess) {
    let response;
    await axios({
      method: "put",
      url: getUrl(authConfig.workOrdersEndpoint) + "/" + workOrderId, 
      headers : getAuthorizationHeaders({
        contentType: authConfig.WORK_ORDERS_PUT_REQ_V1,
        accept: authConfig.WORK_ORDERS_PUT_RES_V1
      }),
      data: params,
    })
      .then((res) => {
        response = res.data;
        handleSuccess();
      })

      .catch((err) => (errorCallback ? errorCallback(err) : null));
  }

  async function individualPermissionsPatch(individualPermissionId, params, errorCallback, handleSuccess) {
    let response;
    await axios({
      method: "put",
      url: getUrl(authConfig.individualEndpoint) + "/" + individualPermissionId + "/role-permissions",
      headers:  getAuthorizationHeaders({
        contentType: authConfig.individualPutMIMEType,
        accept: authConfig.individualPutAcceptMIMEType
      }),
      data: params,
    })
      .then((res) => {
        response = res.data;
        setIndividualDetails(res.data)
        handleSuccess();
      })

      .catch((err) => (errorCallback ? errorCallback(err) : null));
  }


  async function contactGroupCreate(params, errorCallback, handleSuccess) {
    let response;
    await axios({
      method: "post",
      url: getUrl(authConfig.contactGroupsEndpoint),
      headers: getAuthorizationHeaders(),
      data: params,
    })
      .then((res) => {
        response = res.data;
        handleSuccess();
      })

      .catch((err) => (errorCallback ? errorCallback(err) : null));
    return response;
  }

  async function contactGroupUpdate(data, errorCallback, handleSuccess) {
    try {
      const response = await axios.patch(
        getUrl(authConfig.contactGroupsEndpoint) + "/" + data?.id,
        data,
        { headers: getAuthorizationHeaders() }
      );
      setContactGroupsDataDetails(response.data);
      handleSuccess();
      return response.data;
    } catch (err) {
      if (errorCallback) {
        errorCallback(err);
      }
    }
  }

  async function requisitionUpdate(reqId,data, errorCallback,handleSuccess) {
    try {
      const response = await axios.patch(
        getUrl(authConfig.serviceRequisitionsEndpoint) + "/"+reqId,
        data,
        { headers: getAuthorizationHeaders({
          contentType: authConfig.PATCH_SR_MIMETYPE,
          accept: authConfig.PATCH_SR_ACCEPT_MIMETYPE
        }) }
      );
      setRequisitionDataDetails(response.data);
      handleSuccess();
      return response.data;
    } catch (err) {
      if (errorCallback) {
        errorCallback(err);
      }
    }
  }


  async function rescheduleSlot(rowData,slotId,data, errorCallback,handleSuccess) {
    try {
      const response = await axios.patch(
        getUrl(authConfig.siteVisitsEndpoint) + "/"+rowData?.siteVisitTimingsList[0]?.id+"/reschedule/site-visit/" + slotId + "/service-requisition/" + rowData?.serviceRequisitionId,
        data,
        { headers: getAuthorizationHeaders({
          contentType: authConfig.SITE_VISIT_TIMINGS_RESCHEDULE_STATUS_REQ_V1
        }) }
      );
      handleSuccess();
      return response.data;
    } catch (err) {
      if (errorCallback) {
        errorCallback(err);
      }
    }
  }

  async function updateSiteVisit(rowData,data, errorCallback,handleSuccess) {
    try {
      const response = await axios.patch(
        getUrl(authConfig.siteVisitsEndpoint) + "/"+ rowData?.siteVisitTimingsList[0]?.id + "/service-requisition/" + rowData?.serviceRequisitionId,
        data,
        { headers: getAuthorizationHeaders({
          contentType: authConfig.SITE_VISIT_TIMINGS_UPDATE_REQ_V1
        }) }
      );
      handleSuccess();
      return response.data;
    } catch (err) {
      if (errorCallback) {
        errorCallback(err);
      }
    }
  }

  async function specificationUpdate(id, data, errorCallback, handleSuccess) {
    try {
      const response = await axios.patch(
        getUrl(authConfig.getAllServiceProfiles + "/" + id + "/update-fields"),
        data,
        { headers: getAuthorizationHeaders() }
      );
      setSpecificationDataDetails(response.data);
      handleSuccess();
      return response.data;
    } catch (err) {
      if (errorCallback) {
        errorCallback(err);
      }
    }
  }

  async function configurationUpdate(data, errorCallback) {
    try {
      const response = await axios.patch(getUrl(authConfig.settings), data, {
        headers: getAuthorizationHeaders(),
      });
      setConfigurationData(response.data);
      return response.data;
    } catch (err) {
      if (errorCallback) {
        errorCallback(err);
      }
    }
  }

  async function assignRoleCreate(params) {
    try {
      const response = await axios({
        method: "post",
        url: getUrl(authConfig.assignRoleEndpoint),
        headers: getAuthorizationHeaders(),
        data: params,
      });
      console.log("assignRoleCreate: response:", response);
      return response;
    } catch (error) {
      console.error("Error", error);
      // throw error;
    }
  }

  async function roleCreate(params) {
    try {
      const response = await axios({
        method: "post",
        url: getUrl(authConfig.roleEndpoint),
        headers: getAuthorizationHeaders(),
        data: params,
      });
      return response.data;
    } catch (error) {
      console.error("Error", error);
      // throw error;
    }
  }

  async function roleUpdate(params, id) {
    try {
      const response = await axios({
        method: "patch",
        url: getUrl(authConfig.roleEndpoint + "/" + id),
        headers: getAuthorizationHeaders(),
        data: params,
      });
      setRoleData(response.data);
      return response.data;
    } catch (error) {
      console.error("Error", error);
      // throw error;
    }
  }

  async function uploadFinalBOQ(srId,organisationId,formData, successCallback, errorCallback) {
    await axios({
      method: "patch",
      url: getUrl(`${authConfig.quotationsEndpoint}/final-boq/service-requisition/${srId}/sp-org-id/${organisationId}`),
      headers: getAuthorizationHeaders({
        contentType:authConfig.SR_QUOTATIONS_PATCH_SUBMIT_BOQ_REQ_V1,
        accept: authConfig.SR_QUOTATIONS_PATCH_SUBMIT_BOQ_RES_V1
      }),
      data: formData,
    })
      .then((res) => {
        successCallback();
      })

      .catch((err) => (errorCallback ? errorCallback(err) : null));
  }


  async function allProfilesBasicProfile(params) {
    try {
      const response = await axios({
        method: "post",
        url: getUrl(authConfig.allProfilesBasicProfileEndpoint),
        headers: getAuthorizationHeaders(),
        data: params,
      });
      console.log("RESPONSE BACKEND", response);
      if (response.status === 201) {
        return true;
      }
      return response.data;
    } catch (error) {
      console.error("Error", error);
      errorCallback(error);
      // throw error;
    }
  }

  async function allProfilesSocietyCreate(params) {
    try {
      const response = await axios({
        method: "post",
        url: getUrl(authConfig.chsCreateEndpoint)+"?contactType=EMAIL",
        headers : getAuthorizationHeaders({
          contentType: authConfig.INDIVIDUAL_CREATE_REQ_V1,
          accept: authConfig.INDIVIDUAL_CREATE_RES_V1
        }),
        data: params,
      });
      if (response.status === 201) {
        return true;
      }
      return response.data;
    } catch (error) {
      console.error("Error", error);
      errorCallback(error);
    }
  }

  async function SPShortFormCreate(params) {
    try {
      const response = await axios({
        method: "post",
        url: getUrl(authConfig.spCreateEndpoint)+"?verifiedOtpContactType=EMAIL",
        headers : getAuthorizationHeaders({
          contentType: authConfig.INDIVIDUAL_CREATE_SP_REQ_V1,
          accept: authConfig.INDIVIDUAL_CREATE_SP_RES_V1
        }),
        data: params,
      });
      if (response.status === 201) {
        return true;
      }
      return response.data;
    } catch (error) {
      console.error("Error", error);
      errorCallback(error);
    }
  }

  // Users Data
  const [usersData, setUsersData] = useState([]);

  async function getUsers(successCallback, errorCallback) {
    await axios({
      method: "get",
      url: getUrl(authConfig.usersEndpoint),
      headers: getAuthorizationHeaders(),
    })
      .then((res) => {
        setUsersData(res.data);
        console.log("Users Response", res.data);
        successCallback();
      })

      .catch((err) => (errorCallback ? errorCallback(err) : null));
  }

  async function loginEndpointNew(params, errorCallback, handleSuccess) {
    let response;
    await axios({
      method: "post",
      url: getUrl(authConfig.logOutAllDevices),
      data: params,
    })
      .then((res) => {
        response = res.data;
        handleSuccess();
      })

      .catch((err) => (errorCallback ? errorCallback(err) : null));
    return response;
  }

  async function uploadDocuments(
    formData,
    userUniqueId,
    successCallback,
    errorCallback
  ) {
    await axios({
      method: "post",
      url: getUrl(`${authConfig.organisationsEndpoint}/${userUniqueId}/upload-images`),
      headers: getFileUploadHeaders(),
      data: formData,
    })
      .then((res) => {
        console.log("Documents Uploaded", res.data);
        successCallback();
      })

      .catch((err) => (errorCallback ? errorCallback(err) : null));
  }

  async function uploadSnapshots(formData, successCallback, errorCallback) {
    await axios({
      method: "post",
      url: getUrl(`${authConfig.snapshots}`),
      headers: getFileUploadHeaders(),
      data: formData,
    })
      .then((res) => {
        console.log("Documents Uploaded", res.data);
        successCallback();
      })

      .catch((err) => (errorCallback ? errorCallback(err) : null));
  }

  async function uploadFiles(formData, successCallback, errorCallback) {
    await axios({
      method: "post",
      url: getUrl(`${authConfig.documentEndpoint}/upload`),
      headers: getFileUploadHeaders({
        accept: authConfig.DOCUMENTS_CREATE_RES_V1
      }),
      data: formData,
    })
      .then((res) => {
        successCallback();
      })

      .catch((err) => (errorCallback ? errorCallback(err) : null));
  }

  async function postWorkOrder(formData, successCallback, errorCallback) {
    await axios({
      method: "post",
      url: getUrl(`${authConfig.workOrdersEndpoint}`),
      headers: getFileUploadHeaders({
        accept: authConfig.WORK_ORDERS_INSERT_RES_V1
      }),
      data: formData,
    })
      .then((res) => {
        successCallback();
      })
      .catch((err) => (errorCallback ? errorCallback(err) : null));
  }

  async function postInvoice(payload, successCallback, errorCallback) {
    await axios({
      method: "post",
      url: getUrl(`${authConfig.invoicesEndpoint}`),
      headers : getAuthorizationHeaders({
        contentType: authConfig.INVOICE_CREATE_REQ_V1,
        accept: authConfig.INVOICE_CREATE_RES_V1
      }),
      data: payload,
    })
      .then((res) => {
        successCallback();
      })
      .catch((err) => (errorCallback ? errorCallback(err) : null));
  }


  async function putInvoice(invoiceId,payload, successCallback, errorCallback) {
    await axios({
      method: "put",
      url: getUrl(`${authConfig.invoicesEndpoint}/${invoiceId}`),
      headers : getAuthorizationHeaders({
        contentType: authConfig.INVOICES_PUT_REQ_V1,
        accept: authConfig.INVOICES_PUT_RES_V1
      }),
      data: payload,
    })
      .then((res) => {
        successCallback();
      })
      .catch((err) => (errorCallback ? errorCallback(err) : null));
  }

  async function patchCHSProfile(orgId,payload, successCallback, errorCallback) {
    await axios({
      method: "patch",
      url: getUrl(`${authConfig.organisationsEndpoint}/${orgId}/organisation-meta-data`),
      headers : getAuthorizationHeaders({
        contentType: authConfig.ORGANISATION_PATCH_META_DATA_BY_ID_REQ_V1,
        accept: authConfig.ORGANISATION_PATCH_META_DATA_BY_ID_RES_V1
      }),
      data: payload,
    })
      .then((res) => {
        successCallback();
      })
      .catch((err) => (errorCallback ? errorCallback(err) : null));
  }

  async function patchWorkOrderUpload(workOrderId,formData, successCallback, errorCallback) {
    await axios({
      method: "patch",
      url: getUrl(`${authConfig.workOrdersEndpoint}/${workOrderId}`),
      headers: getFileUploadHeaders({
        accept: authConfig.WORK_ORDERS_PATCH_FILE_LOCATION_RES_V1
      }),
      data: formData,
    })
      .then((res) => {
        successCallback();
      })
      .catch((err) => (errorCallback ? errorCallback(err) : null));
  }

  async function uploadPreliminaryQuote(srId,orgId,formData, successCallback, errorCallback) {
    await axios({
      method: "patch",
      url: getUrl(`${authConfig.quotationsEndpoint}/service-requisition/${srId}/sp-org-id/${orgId}`),
      headers: getFileUploadHeaders({
        accept: authConfig.SR_QUOTATIONS_PATCH_RES_V1
      }),
      data: formData,
    })
      .then((res) => {
        successCallback();
      })

      .catch((err) => (errorCallback ? errorCallback(err) : null));
  }

  async function updateFiles(id, formData, successCallback, errorCallback) {
    await axios({
      method: "put",
      url: getUrl(`${authConfig.documentEndpoint}`) + "/" + id,
      headers: getFileUploadHeaders({
        accept: authConfig.DOCUMENT_REPO_UPDATE_RES_V1
      }),
      data: formData,
    })
      .then((res) => {
        console.log("Documents Updated", res.data);
        successCallback();
      })

      .catch((err) => (errorCallback ? errorCallback(err) : null));
  }

  async function updateSnapshots(id, formData, successCallback, errorCallback) {
    await axios({
      method: "patch",
      url: getUrl(`${authConfig.snapshots}`) + "/" + id,
      headers: getFileUploadHeaders(),
      data: formData,
    })
      .then((res) => {
        console.log("Documents Uploaded", res.data);
        successCallback();
      })

      .catch((err) => (errorCallback ? errorCallback(err) : null));
  }

  const [allCategories, setAllCategories] = useState([]);
  const [allSubCategories, setAllSubCategories] = useState([]);

  async function getAllDocumentCategories() {
    try {
      const response = await axios({
        method: "post",
        url: getUrl(authConfig.documentCategories),
      });

      setAllCategories(response.data);
      localStorage.setItem("allCategories", response.data);
      return response.data;
    } catch (error) {
      console.error("An error occurred", error);
    }
  }

  async function getAllDocumentSubCategories() {
    try {
      const response = await axios({
        method: "post",
        url: getUrl(authConfig.documentSubCategories),
      });

      setAllSubCategories(response.data);
      localStorage.setItem("allSubCategories", response.data);
      return response.data;
    } catch (error) {
      console.error("An error occurred", error);
    }
  }

  const [allSiteMapDataFromSettings, setAllSiteMapDataFromSettings] = useState(
    []
  );

  async function getSiteMapDataFromSettings() {
    try {
      const response = await axios({
        method: "get",
        url: getUrl(authConfig.settings) + "?settingsType=USER_MANAGEMENT_DATA",
      });

      setAllSiteMapDataFromSettings(response.data.userManagementDTO.pages);
      return response.data;
    } catch (error) {
      console.error("An error occurred", error);
    }
  }

  //Updating the all categories and subcategories in localStorage.
  //Updating the Sitemap data from the settings.
  useEffect(() => {
    getAllDocumentCategories();
    getAllDocumentSubCategories();
    getSiteMapDataFromSettings();
  }, []);

  async function uploadProject(formData, userUniqueId) {
    try {
      const response = await axios({
        method: "post",
        url: getUrl(`${authConfig.organisationProjects}`),
        headers: getFileUploadHeaders(),
        data: formData,
      });

      // headers: getFileUploadHeaders({
      //   accept: authConfig.DOCUMENT_REPO_UPDATE_RES_V1
      // }),

      // setEntityData((prevEntityData) => ({
      //   ...prevEntityData,
      //   projectsList: response.data,
      // }));

      return response;
    } catch (error) {
      console.error("An error occurred", error);
    }
  }
  async function getAllDocuments(Data,isActive) {
    try {
      const response = await axios({
        method: "post",
        url: getUrl(authConfig.documentEndpoint)+"/allDocuments?isActive="+isActive,
        headers: getAuthorizationHeaders(),
        data: Data,
      });

      return response;
    } catch (error) {
      console.error("An error occurred", error);
    }
  }



  async function documentDelete(data, userUniqueId) {
    try {
      const response = await axios({
        method: "delete",
        url: getUrl(authConfig.documents + "/deleteFile"),
        headers: getAuthorizationHeaders(),
        params: {
          imageURL: data,
          individualId: userUniqueId,
        },
      });
      console.log("Items deleted successfully", response);
      return response;
    } catch (error) {
      console.error("An error occurred", error);
    }
  }

  async function documentMicrositeDelete(userUniqueId,documentId,sectionEnum) {
    try {
      const response = await axios({
        method: "post",
        url : `${getUrl(authConfig.organisationsEndpoint)}/${userUniqueId}/de-activate-images/${documentId}?documentSectionsEnum=${sectionEnum}`,
        headers: getAuthorizationHeaders(),
      });
      console.log("Items deleted successfully", response);
      return response;
    } catch (error) {
      console.error("An error occurred", error);
    }
  }

  async function snapshotDelete(awardToDelete, data) {
    try {
      const response = await axios({
        method: "patch",
        url:
          getUrl(authConfig.snapshotsDeleteById) +
          "/" +
          data.id +
          "/" +
          awardToDelete.id,
        headers: getAuthorizationHeaders(),
      });
      console.log("Items deleted successfully", response);
      return true;
    } catch (error) {
      console.error("An error occurred", error);
    }
  }

  async function getFileByLocation(location) {
    try {
      const response = await axios.get(getUrl(authConfig.getFileByLocation), {
        headers: getAuthorizationHeaders(),
        params: {
          location: location,
        },
      });
      return response;
    } catch (error) {
      throw error;
    }
  }

  //Used in the Users page for the admin.
  async function getAllUsersNewTable(data) {
    try {
      const response = await axios({
        method: "post",
        url: getUrl(authConfig.getAllUsersEndpointNewTable),
        headers: getAuthorizationHeaders(),
        data: data,
      });

      return response.data;
    } catch (err) {
      console.error("An error occurred", err);
      throw err;
    }
  }

  async function selectDropdown(data, profileType) {
    try {
      const response = await axios({
        method: "get",
        url: getUrl(authConfig.selectDropdown),
        headers: getAuthorizationHeaders(),
        params: {
          selectionType: data,
          entityCategory: profileType,
        },
      });
      return response;
    } catch (error) {
      throw error;
    }
  }

  async function getSettingsByType(data) {
    try {
      const response = await axios({
        method: "get",
        url: getUrl(authConfig.settings),
        headers: getAuthorizationHeaders(),
        params: {
          settingsType: data,
        },
      });
      return response;
    } catch (error) {
      throw error;
    }
  }

  async function deleteProject(userId, entityId, location) {
    try {
      const response = await axios({
        method: "delete",
        url: getUrl(authConfig.documents + "/deleteProject"),
        headers: getAuthorizationHeaders(),
        params: {
          userId: userId,
          location: location,
          entityId: entityId,
        },
      });
      console.log("Items deleted successfully", response);

      if (user.entityCategory === "SUPER_ADMIN") {
        setProfileEntityData(response?.data);
      } else {
        setEntityData(response?.data);
      }

      return response;
    } catch (error) {
      console.error("An error occurred", error);
    }
  }

  async function deleteMicroSiteProject(documentId, orgId,serviceId) {
    try {
      const response = await axios({
        method: "patch",
        url: getUrl(authConfig.organisationsEndpoint)+ "/delete-projects?documentId="+documentId+"&orgId="+orgId+"&serviceNameId="+serviceId,
        headers: getAuthorizationHeaders(),
      });
      console.log("Items deleted successfully", response);

      return response;
    } catch (error) {
      console.error("An error occurred", error);
    }
  }


  async function micrositeListsPatch(
    data,
    userUniqueId,
    errorCallback
  ) {
    await axios({
      method: "patch",
      url: getUrl(`${authConfig.organisationsEndpoint}/${userUniqueId}/${authConfig.micrositeListsEndpoint}`),
      headers: getAuthorizationHeaders({
        contentType:authConfig.ORGANISATION_PATCH_SP_LISTS_CONTEXTUAL_DATA_BY_ID_REQ_V1,
        accept: authConfig.ORGANISATION_PATCH_SP_LISTS_CONTEXTUAL_DATA_BY_ID_RES_V1
      }),
      data: data,
    })
      .then((res) => {
        setMicrositeBasicData(res.data);
      })
      .catch((err) => (errorCallback ? errorCallback(err) : null));
  }

  async function getMicrositeData(userId) {
    try {
      const individualId = userId || user?.id;
      console.log("API call getMicrositeData: userId:", individualId);
      const url = `${getUrl(authConfig.micrositeBasicProfileGetEndpoint)}?individualId=${individualId}`;
      const res = await axios.get(url);
      setMicrositeBasicData(res.data.microSite);
    } catch (err) {
      console.log("error", err);
    }
  }

  async function micrositeLevel2Patch(data, userUniqueId, errorCallback) {
    await axios({
      method: "patch",
      url: getUrl(`${authConfig.organisationsEndpoint}/${userUniqueId}/${authConfig.micrositeLevel2Endpoint}`),
      headers: getAuthorizationHeaders({
        contentType:authConfig.ORGANISATION_PATCH_SP_LEVEL2_CONTEXTUAL_DATA_BY_ID_REQ_V1,
        accept: authConfig.ORGANISATION_PATCH_SP_LEVEL2_CONTEXTUAL_DATA_BY_ID_RES_V1
      }),
      data: data,
    })
      .then((res) => {
        setMicrositeBasicData(res.data);
      })
      .catch((err) => (errorCallback ? errorCallback(err) : null));
  }

  async function micrositeLevel1Patch(data, userUniqueId, errorCallback) {
    await axios({
      method: "patch",
      url: getUrl(`${authConfig.organisationsEndpoint}/${userUniqueId}/${authConfig.micrositeLevel1Endpoint}`),
      headers: getAuthorizationHeaders({
        contentType:authConfig.ORGANISATION_PATCH_SP_LEVEL1_CONTEXTUAL_DATA_BY_ID_REQ_V1,
        accept: authConfig.ORGANISATION_PATCH_SP_LEVEL1_CONTEXTUAL_DATA_BY_ID_RES_V1
      }),
      data: data,
    })
      .then((res) => {
        setMicrositeBasicData(res.data);
      })
      .catch((err) => (errorCallback ? errorCallback(err) : null));
  }

  async function servicesUpdate(data, userUniqueId, errorCallback) {
    try {
      const response = await axios.patch(
        getUrl(`${authConfig.serviceProfileEndpoint}/${userUniqueId}`),
        data,
        { headers: getAuthorizationHeaders() }
      );
      setUserData(response.data);
      return response.data;
    } catch (err) {
      if (errorCallback) {
        errorCallback(err);
      }
    }
  }

  async function fetchOne(userId) {
    try {
      if (!!userId) {
        const res = await axios.get(
          `${getUrl(authConfig.servicesDataEndpoint)}/${userId}`
        );
        if (res.data) {
          setUserData(res.data);
        }
      }
    } catch (err) {
      console.log("error", err);
    }
  }
  const [statisticsData, setStatisticsData] = useState(null);

  async function statisticsUpdate(data, userUniqueId, errorCallback) {
    try {
      const response = await axios.patch(
        getUrl(`${authConfig.individualStatisticsUpdate}/${userUniqueId}`),
        data,
        { headers: getAuthorizationHeaders() }
      );
      setStatisticsData(response.data);
      return response.data;
    } catch (err) {
      if (errorCallback) {
        errorCallback(err);
      }
    }
  }

  async function fetchStatistics(userId) {
    try {
      if (!!userId) {
        const res = await axios.get(
          `${getUrl(authConfig.statisticsDataEndpoint)}/${userId}`
        );
        if (res.data) {
          setStatisticsData(res.data);
        }
      }
    } catch (err) {
      console.log("error", err);
    }
  }

  const [projectsData, setProjectsData] = useState(null);

  async function fetchUserProjects(userId) {
    try {
      const res = await axios.get(
        `${getUrl(authConfig.projectsDataEndpoint)}/${userId}`
      );
      if (res.data) {
        setProjectsData(res.data);
      }
    } catch (err) {
      console.log("error", err);
    }
  }

  async function fetchProjects(userId,serviceId) {
    try {
      const res = await axios.get(
        `${getUrl(authConfig.serviceProfileEndpoint)}/Organisation/${userId}/PROJECTS/service/${serviceId}`
      );
      if (res.data) {
        setProjectsData(res.data);
      }
    } catch (err) {
      console.log("error", err);
    }
  }

  async function fetchRazorPayId(razorpayPaymentId) {
    try {
      const response = await axios.get(
        `${getUrl(authConfig.razorpayEndpoint)}/${razorpayPaymentId}`
      );
      if (response.data) {
        return response.data; 
      } else {
        console.error("No data received from the Razorpay endpoint.");
        return null;
      }
    } catch (error) {
      console.error("Error fetching Razorpay ID:", error);
      return null;
    }
  }


  async function deleteProject(imageUrl, projectName, userUniqueId, serviceId) {
    try {
      const response = await axios({
        method: "delete",
        url: getUrl(authConfig.documents + "/deleteProject"),
        headers: getAuthorizationHeaders(),
        params: {
          imageURL: imageUrl,
          projectName: projectName,
          userId: userUniqueId,
          serviceNameId: serviceId,
        },
      });
      console.log("Items deleted successfully", response);
      return response;
    } catch (error) {
      console.error("An error occurred", error);
    }
  }

  async function conversationCreate(params, errorCallback, handleSuccess) {
    try {
      const response = await axios({
        method: "post",
        url: getUrl(authConfig.conversationEndpoint),
        headers: getAuthorizationHeaders(),
        data: params,
      });
      setConvsCreated(response.data);
      handleSuccess();
      return response.data;
    } catch (err) {
      if (errorCallback) {
        errorCallback(err);
      }
      return null;
    }
  }
  async function saveUserSp(params) {
    console.log("Payload being sent:", params); // Log the payload

    try {
      await axios({
        method: "post",
        url: getUrl(authConfig.saveUserSpEndpoint),
        headers: getAuthorizationHeaders(),
        data: params,
      });

      return true;
    } catch (err) {
      console.error("Error creating user:", err); // Log the error
      return false;
    }
  }
  async function taskCreate(params, errorCallback, handleSuccess) {
    try {
      const response = await axios({
        method: "post",
        url: getUrl(authConfig.taskEndpoint),
        headers: getAuthorizationHeaders(),
        data: params,
      });

      handleSuccess();
      return response.data;
    } catch (err) {
      if (errorCallback) {
        errorCallback(err);
      }
      return null;
    }
  }

  async function taskUpdate(data, errorCallback, handleSuccess) {
    try {
      const response = await axios.patch(
        getUrl(authConfig.taskEndpoint + "/" + data?.id),
        data,
        { headers: getAuthorizationHeaders() }
      );
      setTaskDataDetails(response.data);
      handleSuccess();
      console.log("Task Data Update Response", response);
      return response.data;
    } catch (err) {
      if (errorCallback) {
        errorCallback(err);
      }
    }
  }
async function createSubscription(params) {
  console.log("Payload being sent:", params); 

  try {
    const response = await axios({
      method: "post",
      url: getUrl(authConfig.createSubscriptionEndpoint),
      headers: getAuthorizationHeaders(),
      data: params,
    });

    console.log("Response from subscription API:", response?.data); 
    setSubscriptionResponseData(response?.data)
    return response.data;  
  } catch (err) {
    console.error("Error sending package data:", err); 
    return null;  
  }
}

async function updateSubscription(params){
  try{
    const response = await axios({
      method : "patch",
      url: getUrl(authConfig.patchSubscription+"/"+params?.id+"/"+params.error),
      headers: getAuthorizationHeaders(),
      data: params,
    })
    return null;
  }
  catch(error){
    console.error("Error Updating failure status", error); 
    return null;
  }
}


async function createPayment(params){
  console.log("Payload being sent:", params); 

  try {
    await axios({
      method: "post",
      url: getUrl(authConfig.createPaymentEndpoint),
      headers: getAuthorizationHeaders(),
      data: params,
    });

    return true;
  } catch (err) {
    console.error("Error sending package data:", err); 
    return false;
  }
}


  const values = {
    user,
    topMenuData,
    quickLinksData,
    leftMenuData,
    loading,
    loginLoad,
    pageLoad,
    entityData,
    roleTypeData,
    roleTypeUser,
    roleData,
    basicProfileGetData,
    listValues,
    listNames,
    roleUser,
    readinessProfileData,
    employeeData,
    profileUser,
    employeeProfile,
    profileEntityData,
    shortFormData,
    usersData,
    allSiteMapDataFromSettings,
    getUsers,
    setRoleUser,
    setRoleData,
    setRoleTypeUser,
    setBasicProfileGetData,
    setListValues,
    setListNames,
    setRoleTypeData,
    setTopMenuData,
    setQuickLinksData,
    setLeftMenuData,
    allCategories,
    allSubCategories,
    masterData,
    masterDataDetails,
    requisitionData,
    requisitionDataDetails,
    specificationData,
    specificationDataDetails,
    snapshotsData,
    snapshotsDataDetails,
    contactGroupsData,
    contactGroupsDataDetails,
    taskData,
    taskDataDetails,
    servicesData,
    servicesDataDetails,
    listNamesData,
    listNamesDataDetails,
    locationsData,
    userPackage,
    setUserPackage,
    setUserPackageData,
    userPackageData,
    subscriptionResponseData,
    setSubscriptionResponseData,
    locationsDataDetails,
    fsiRulesData,
    fsiRulesDataDetails,
    fsiData,
    fsiDataDetails,
    serviceProfileDataDetails,
    roleId,
    rolesDetails,
    setRoleId,
    setRolesDetails,
    workOrderId,
    workOrderDetails,
    setWorkOrderId,
    setWorkOrderDetails,
    invoiceId,
    invoiceDetails,
    setInvoiceId,
    setInvoiceDetails,
    documents,
    documentDetails,
    setDocuments,
    setDocumentDetails,
    individualId,
    individualDetails,
    setIndividualId,
    setIndividualDetails,
    templateId,
    templateDetails,
    setTemplateId,
    setTemplateDetails,
    setMasterData,
    setMasterDataDetails,
    setRequisitionData,
    setRequisitionDataDetails,
    setSpecificationData,
    setSpecificationDataDetails,
    setSnapshotsData,
    setSnapshotsDataDetails,
    setContactGroupsData,
    setContactGroupsDataDetails,
    setTaskData,
    setTaskDataDetails,
    setServicesData,
    setServicesDataDetails,
    setListNamesData,
    setListNamesDataDetails,
    setLocationsData,
    setLocationsDataDetails,
    setFsiRulesData,
    setFsiRulesDataDetails,
    setFsiData,
    setFsiDataDetails,
    setServiceProfileDataDetails,
    setShortFormData,
    setEntityData,
    setEmployeeData,
    setUser,
    setEmployeeProfile,
    setLoading,
    setLoginLoad,
    setPageLoad,
    home: handleHome,
    login: handleLogin,
    loginNew:handleLoginNew,
    fetchProfile,
    logout: handleLogout,
    triggerPasswordReset: handleTriggerForgetPassword,
    resetPassword: handleForgotResetVerification,
    emailVerify: handleEmailVerification,
    updateEntity: patchEntity,
    updateShortForm: patchShortForm,
    updateIsEmpanelled: patchIsEmpanelled,
    updateIsStrategicPartner: patchIsStrategicPartner,
    updateEntityServices: patchEntityServices,
    updateReadiness: patchReadiness,
    getAllMembers: handleGetAllMembers,
    postMasterData: masterDataCreate,
    postListName: listNamesCreate,
    postService: masterDataServiceCreate,
    postLocationZoneMap: locationZoneMapCreate,
    postQuestions: questionCreate,
    mapQuestions: questionMapCreate,
    postSpecification: specificationCreate,
    patchMasterData: masterDataUpdate,
    patchServicesData: serviceDataUpdate,
    patchSpecification: specificationUpdate,
    patchLocation: locationUpdate,
    patchLocationZone: locationZoneUpdate,
    patchListNames: listNamesUpdate,
    patchLabel: labelUpdate,
    postFSIRule: fsiRuleCreate,
    patchServices: servicesUpdate,
    postCalculatedFSI: calculatedFsiCreate,
    patchFSIRule: fsiRuleUpdate,
    patchCalculatedFsi: calculatedFsiUpdate,
    getSocietyProfile,
    getReadinessProfile,
    updateProjectData: projectData,
    updateProfile: handleUpdateProfile,
    getAdminEntityProfile,
    setProfileEntityData,
    setProfileUser,
    addNewMember: addNewUser,
    createRole: createRoleType,
    updateRole: updateRoleType,
    postEmployee: employeeCreate,
    postRequisition: requisitionCreate,
    postOTP:sendOTP,
    postForgotPassword:forgotPassword,
    postResetPassword:resetPassword,
    verifyOTP:patchVerifyOTP,
    resendOTP:patchResendOTP,
    postTemplate:templateCreate,
    putTemplate:templateUpdate,
    patchSiteVisitStatusBookOrCancel:updateSiteVisitStatusBookOrCancel,
    postBroadCastSrToSp: broadCastSrToSpCreate,
    postBroadCastSrToSpFinal:broadCastSrToSpFinalCreate,
    rolePost:createRole,
    rolePatch:patchRole,
    updateWorkOrder:patchWorkOrder,
    patchIndividualPermissions: individualPermissionsPatch,
    postContactGroup: contactGroupCreate,
    patchContactGroup: contactGroupUpdate,
    patchRequisition: requisitionUpdate,
    patchRescheduleSlot:rescheduleSlot,
    patchSiteVisit:updateSiteVisit,
    postRole: roleCreate,
    patchRoles: roleUpdate,
    submitFinalBOQ:uploadFinalBOQ,
    postAssignRole: assignRoleCreate,
    patchEmployee: employeeUpdate,
    patchConfiguration: configurationUpdate,
    signupV2: handleRegister,
    signupV3: handleSignup,
    CHSCreate: allProfilesSocietyCreate,
    SPCreate:SPShortFormCreate,
    uploadDocuments,
    uploadSnapshots,
    uploadFiles,
    postWorkOrder,
    postInvoice,
    putInvoice,
    patchCHSProfile,
    patchWorkOrderUpload,
    uploadPreliminaryQuote,
    updateFiles,
    updateSnapshots,
    uploadProject,
    getAllDocuments,
    documentDelete,
    documentMicrositeDelete,
    snapshotDelete,
    basicProfile,
    allProfilesBasicProfile,
    patchArchitectAdditionalData,
    tempGetArchitectEntityProfile,
    getAllListValuesByListNameId,
    selectDropdown,
    getAllUsersNewTable,
    getFileByLocation,
    getSettingsByType,
    deleteProject,
    deleteMicroSiteProject,
    getBasicProfileData,
    patchMicrosite: micrositeListsPatch,
    micrositeLevel2Patch,
    micrositeBasicData,
    setMicrositeBasicData,
    micrositeGetEndpoint: getMicrositeData,
    userData,
    fetchOne,
    micrositeLevel1Patch,
    fetchUserProjects,
    fetchProjects,
    fetchRazorPayId,
    projectsData,
    conversationPost: conversationCreate,
    userSaveSpPost: saveUserSp,
    createSubscriptionPost: createSubscription,
    updateSubscription : updateSubscription,
    createPaymentPost: createPayment,
    taskPost:taskCreate,
    taskPatch:taskUpdate,

    setConversation,
    setConversationData,
    conversation,
    conversationData,
    fetchStatistics,
    statisticsData,
    statisticsUpdate,
    setBasicProfileAllProfiles,
    basicProfileDataAllProfiles,
    sendWatiMessage,
    sendWatiMessages,
  };

  return (
    <AuthContext.Provider value={values}>
      {children}
      {redirectToLogin ? (
        <SessionExpirationDialog
          open={showWarning}
          onClose={handleCloseDialogRedirectToLogin}
          msg={message}
        />
      ) : (
        <SessionExpirationDialog
          open={showWarning}
          onClose={handleCloseDialog}
          msg={message}
        />
      )}
    </AuthContext.Provider>
  );
};
export { AuthContext, AuthProvider };