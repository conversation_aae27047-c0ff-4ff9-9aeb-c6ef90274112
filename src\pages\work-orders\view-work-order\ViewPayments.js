import { useTheme } from "@emotion/react";
import {
  Divider,
  Grid,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
  useMediaQuery,
} from "@mui/material";
import { useContext } from "react";
import { useForm } from "react-hook-form";
import { AuthContext } from "src/context/AuthContext";
import MUITableCell from "src/pages/SP/MUITableCell";

const innerTableStylings = {
  fontWeight: "bold", // Make text bold
  padding: "6px 16px", // Reduce padding to decrease cell height
};

const tablecellLabelStyle = {
  width: { xs: "50%", sm: "50%", md: "40%" }, // Responsive width adjustment
  padding: "4px", // Ensure padding for better alignment
  textAlign: "right", // Align the text to the left
  verticalAlign: "middle", // Vertically align the text to the middle
  textIndent: {
    lg: "80px",
    md: "80px",
    sm: "100px",
    xs: "0",
  },
};
const tablecellValueStyle = {
  justifyContent: "flex-start",
  alignItems: "center",
  width: { xs: "50%", sm: "50%", md: "60%" }, // Responsive width for small and medium screens
  height: "10px",
};

const field = {
  fontSize: "12.75px",
};

const fieldValueStyle = {
  fontWeight: "bold",
  wordWrap: "break-word",
  whiteSpace: "pre-wrap",
  // color: "#66BB6A", // Sets color for the TableCell
  color: "#108A00",
  fontSize: "14px",
  lineHeight: "1.2",
};

const PaymentSchedule = ({
  PaymentData,
  contractValue,
  gstPercentage,
  paymentMetaData,
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));

  const { listValues } = useContext(AuthContext);

  const {
    control,
    formState: { errors },
  } = useForm();

  return (
    <>
      <Grid container spacing={2} sx={{ marginTop: 2 }}>
        <Grid item xs={12} sm={12} sx={{ margin: 2 }}>
          {PaymentData?.length > 0 ? (
            ""
          ) : (
            <Typography>
              *Note: Please Fill total contract value (Excluding GST)  to create a Payment Schedule
            </Typography>
          )}
        </Grid>
      </Grid>
      <Grid>
        <TableContainer sx={{ padding: "4px 6px" }}>
          <Table>
            <TableBody
              sx={{
                "& .MuiTableCell-root": {
                  p: `${theme.spacing(1.35, 1.125)} !important`,
                },
              }}
            >
              <>
                <TableRow>
                  <MUITableCell sx={tablecellLabelStyle}>
                    <Typography style={field}>
                      {" "}
                      Total Contract Value:
                    </Typography>
                  </MUITableCell>
                  <MUITableCell sx={tablecellValueStyle}>
                    <Typography className="data-field" style={fieldValueStyle}>
                      {contractValue || "N/A"}
                    </Typography>
                  </MUITableCell>
                </TableRow>
                <TableRow>
                  <MUITableCell sx={tablecellLabelStyle}>
                    <Typography style={field}> GST(%):</Typography>
                  </MUITableCell>
                  <MUITableCell sx={tablecellValueStyle}>
                    <Typography className="data-field" style={fieldValueStyle}>
                      {gstPercentage || "N/A"}
                    </Typography>
                  </MUITableCell>
                </TableRow>
                <TableRow>
                  <MUITableCell sx={tablecellLabelStyle}>
                    <Typography style={field}> Recurring:</Typography>
                  </MUITableCell>
                  <MUITableCell sx={tablecellValueStyle}>
                    <Typography className="data-field" style={fieldValueStyle}>
                      {listValues?.find(
                        (item) => item.id === paymentMetaData?.recurring
                      )?.name || " "}
                    </Typography>
                  </MUITableCell>
                </TableRow>
                <TableRow>
                  <MUITableCell sx={tablecellLabelStyle}>
                    <Typography style={field}> Frequency: </Typography>
                  </MUITableCell>
                  <MUITableCell sx={tablecellValueStyle}>
                    <Typography className="data-field" style={fieldValueStyle}>
                      {listValues?.find(
                        (item) => item.id === paymentMetaData?.frequency
                      )?.name || " "}
                    </Typography>
                  </MUITableCell>
                </TableRow>
                <TableRow>
                  <MUITableCell sx={tablecellLabelStyle}>
                    <Typography style={field}>Follow Up Frequency:</Typography>
                  </MUITableCell>
                  <MUITableCell sx={tablecellValueStyle}>
                    <Typography className="data-field" style={fieldValueStyle}>
                      {listValues?.find(
                        (item) => item.id === paymentMetaData?.followUpFrequency
                      )?.name || " "}
                    </Typography>
                  </MUITableCell>
                </TableRow>
                <TableRow>
                  <MUITableCell sx={tablecellLabelStyle}>
                    <Typography style={field}>Houzer Payment:</Typography>
                  </MUITableCell>
                  <MUITableCell sx={tablecellValueStyle}>
                    <Typography className="data-field" style={fieldValueStyle}>
                      {paymentMetaData?.houzerPayment || "N/A"}
                    </Typography>
                  </MUITableCell>
                </TableRow>
                <TableRow>
                  <MUITableCell sx={tablecellLabelStyle}>
                    <Typography style={field}>Renewal Date:</Typography>
                  </MUITableCell>
                  <MUITableCell sx={tablecellValueStyle}>
                    <Typography className="data-field" style={fieldValueStyle}>
                      {paymentMetaData?.renewalDate || ""}
                    </Typography>
                  </MUITableCell>
                </TableRow>
                <TableRow>
                  <MUITableCell sx={tablecellLabelStyle}>
                    <Typography style={field}>Date of Start</Typography>
                  </MUITableCell>
                  <MUITableCell sx={tablecellValueStyle}>
                    <Typography className="data-field" style={fieldValueStyle}>
                      {paymentMetaData?.dateOfStart || ""}
                    </Typography>
                  </MUITableCell>
                </TableRow>
                <TableRow>
                  <MUITableCell sx={tablecellLabelStyle}>
                    <Typography style={field}>Date of Finish:</Typography>
                  </MUITableCell>
                  <MUITableCell sx={tablecellValueStyle}>
                    <Typography className="data-field" style={fieldValueStyle}>
                      {paymentMetaData?.dateOfFinish || ""}
                    </Typography>
                  </MUITableCell>
                </TableRow>
                <TableRow>
                  <MUITableCell sx={tablecellLabelStyle}>
                    <Typography style={field}>Payment Terms:</Typography>
                  </MUITableCell>
                  <MUITableCell sx={tablecellValueStyle}>
                    <Typography className="data-field" style={fieldValueStyle}>
                      {paymentMetaData?.paymentTerms || ""}
                    </Typography>
                  </MUITableCell>
                </TableRow>
                <TableRow>
                  <MUITableCell sx={tablecellLabelStyle}>
                    <Typography style={field}>Remarks:</Typography>
                  </MUITableCell>
                  <MUITableCell sx={tablecellValueStyle}>
                    <Typography className="data-field" style={fieldValueStyle}>
                      {paymentMetaData?.remarks || ""}
                    </Typography>
                  </MUITableCell>
                </TableRow>
              </>
            </TableBody>
          </Table>
        </TableContainer>
      </Grid>
      <Divider />
      <Grid>
        {contractValue && (
          <TableContainer component={Paper}>
            <Table aria-label="payment schedule" size="small">
              <TableHead>
                <TableRow>
                  <TableCell style={innerTableStylings}>Date</TableCell>
                  <TableCell style={innerTableStylings}>
                    Payment Description
                  </TableCell>
                  <TableCell style={innerTableStylings}>
                    Amount Percentage (%)
                  </TableCell>
                  <TableCell style={innerTableStylings}>Amount (₹)</TableCell>
                  <TableCell style={innerTableStylings}>GST Amount</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {PaymentData?.map((row, index) => (
                  <TableRow key={index}>
                    <TableCell style={innerTableStylings}>
                      <Typography>{(row?.date && row.date !== "null") ? row.date : " "}</Typography>
                    </TableCell>
                    <TableCell style={innerTableStylings}>
                      <Typography>
                        {listValues?.find(
                          (item) => item.id === row?.paymentDescription
                        )?.name || "N/A"}
                      </Typography>
                    </TableCell>
                    <TableCell style={innerTableStylings}>
                      <Typography>{row?.amountPercentage || 0}</Typography>
                    </TableCell>
                    <TableCell style={innerTableStylings}>
                      <Typography>{row?.amount || 0}</Typography>
                    </TableCell>
                    <TableCell style={innerTableStylings}>
                      <Typography>{row?.gstAmount || 0}</Typography>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        )}
      </Grid>
    </>
  );
};

export default PaymentSchedule;
