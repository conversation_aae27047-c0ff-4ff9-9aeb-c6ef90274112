import { useTheme } from "@emotion/react";
import {
  Autocomplete,
  Box,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  FormControl,
  Grid,
  IconButton,
  TextField
} from "@mui/material";
import axios from "axios";
import { useContext, useEffect, useState } from "react";
import { Controller, useForm } from "react-hook-form";
import Icon from "src/@core/components/icon";
import { AuthContext } from "src/context/AuthContext";
import { getUrl } from "src/helpers/utils";
import authConfig from "src/configs/auth";
const SocietyMembers = ({
  rowData,
  societyMembers,
  setSocietyMembers,
  open,
  onClose,
  listOfSubCategories,
}) => {
  const theme = useTheme();

  const [designation, setDesignation] = useState(null);

  const { user } = useContext(AuthContext);

  const {
    control,
    handleSubmit,
    setValue,
    reset,
    formState: { errors },
  } = useForm({
    mode: "onChange",
  });

  const handleCloseDialog = () => {
    onClose();
    reset();
    setDesignation(null);
    reset();
    setValue("name", "");
    setValue("contactNumber", "");
    setValue("alternateNumber", "");
    setValue("email", "");
    setValue("fromDate", "");
    setValue("toDate", "");
  };

  useEffect(() => {
    setValue("name", rowData?.name);
    setValue("contactNumber", rowData?.contactNumber);
    setValue("alternateNumber", rowData?.alternateNumber);
    setValue("email", rowData?.email);
    setDesignation(rowData?.designation);
    setValue("fromDate", rowData?.fromDate);
    setValue("toDate", rowData?.toDate);
  }, [rowData]);

  const submit = (data) => {
    const fields = {
      id: crypto.randomUUID(),
      name: data?.name,
      contactNumber: data?.contactNumber,
      alternateNumber: data?.alternateNumber,
      email: data?.email,
      designation: designation,
      fromDate: data?.fromDate,
      toDate: data?.toDate,
      isActive:true
    };
    setSocietyMembers((prevSocietyMembers) => [
      ...(prevSocietyMembers || []),
      fields,
    ]);
    handleCloseDialog();
  };

  const update = (data) => {
    const updatedMembers = societyMembers?.map((member) =>
      member.id === rowData?.id
        ? {
            ...member,
            name: data?.name,
            contactNumber: data?.contactNumber,
            alternateNumber: data?.alternateNumber,
            email: data?.email,
            designation: designation,
            fromDate: data?.fromDate,
            toDate: data?.toDate,
            isActive:rowData?.isActive
          }
        : member
    );

    setSocietyMembers(updatedMembers);
    handleCloseDialog();
  };

  return (
    <>
      <Dialog open={open} onClose={handleCloseDialog} maxWidth="sm" fullWidth>
        <DialogTitle
          sx={{
            position: "relative",
            borderBottom: (theme) => `1px solid ${theme.palette.divider}`,
            p: (theme) => `${theme.spacing(1.75, 4)} !important`,
            display: "flex",
            alignItems: "center",
            justifyContent: { xs: "start" },
            fontSize: { xs: 19, md: 20 },
            margin: "2px 2px 2px 2px",
          }}
          textAlign={"center"}
        >
          <Box
            sx={{
              marginLeft: 1.5,
            }}
          >
            Society Members
          </Box>
          <Box
            sx={{
              position: "absolute",
              top: "4px",
              right: "14px",
              marginRight: 2,
            }}
          >
            <IconButton
              size="small"
              onClick={handleCloseDialog}
              sx={{
                p: "0.2rem",
                borderRadius: 1,
                color: "common.white",
                backgroundColor: "primary.main",
                "&:hover": {
                  backgroundColor: "#66BB6A",
                  transition: "background 0.5s ease, transform 0.5s ease",
                },
              }}
            >
              <Icon icon="tabler:x" fontSize="1rem" />
            </IconButton>
          </Box>
        </DialogTitle>
        <DialogContent>
          <Grid
            container
            alignItems="center"
            justifyContent="center"
            spacing={3}
            sx={{ marginBottom: 2 }}
          >
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <Controller
                  name="name"
                  control={control}
                  rules={{ required: "Society Member Name is required" }}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      value={field.value || ""} 
                      label="Society Member Name"
                      InputLabelProps={{ shrink: true }}
                      size="small"
                      placeholder="Enter Society Member Name"
                      error={Boolean(errors.name)}
                      helperText={errors.name?.message}
                      aria-describedby="validation-basic-name"
                      inputProps={{ maxLength: 50 }}
                    />
                  )}
                />
              </FormControl>
            </Grid>

            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <Controller
                  name="contactNumber"
                  control={control}
                  rules={{ required: "Contact Number is required" }}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      value={field.value || ""} 
                      type="tel"
                      label="Contact Number"
                      size="small"
                      InputLabelProps={{ shrink: true }}
                      placeholder="1234567890"
                      inputProps={{
                        maxLength: 10,
                      }}
                      error={Boolean(errors.contactNumber)}
                      helperText={errors.contactNumber?.message}
                      aria-describedby="validation-basic-contactNumber"
                      onKeyDown={(e) => {
                        if (
                          !/[0-9]/.test(e.key) &&
                          e.key !== "Backspace" &&
                          e.key !== "ArrowLeft" &&
                          e.key !== "ArrowRight" &&
                          e.key !== "Delete" &&
                          e.key !== "Tab"
                        ) {
                          e.preventDefault();
                        }
                      }}
                    />
                  )}
                />
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <Controller
                  name="alternateNumber"
                  control={control}
                  rules={{ required: "Alternate Contact Number is required" }}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      value={field.value || ""} 
                      type="tel"
                      label="Alternate Contact Number"
                      size="small"
                      InputLabelProps={{ shrink: true }}
                      placeholder="1234567890"
                      inputProps={{
                        maxLength: 10,
                      }}
                      error={Boolean(errors.alternateNumber)}
                      helperText={errors.alternateNumber?.message}
                      aria-describedby="validation-basic-alternateNumber"
                      onKeyDown={(e) => {
                        if (
                          !/[0-9]/.test(e.key) &&
                          e.key !== "Backspace" &&
                          e.key !== "ArrowLeft" &&
                          e.key !== "ArrowRight" &&
                          e.key !== "Delete" &&
                          e.key !== "Tab"
                        ) {
                          e.preventDefault();
                        }
                      }}
                    />
                  )}
                />
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <Controller
                  name="email"
                  control={control}
                    rules={{
                    required: "Email is required",
                    pattern: {
                      value: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
                      message: "Enter a valid email address",
                    },
                    minLength: {
                      value: 8,
                      message: "Email must be at least 8 characters long",
                    },
                    maxLength: {
                      value: 100,
                      message: "Email cannot be longer than 100 characters",
                    },
                    validate: async (value) => {
                      if (rowData?.email === value) {
                        return true; // Skip validation if email hasn't changed
                      }
                      // Check if email exists in chsMembers
                      const emailExistsInSP = societyMembers.some(
                        (member) =>
                          member.email.toLowerCase() === value.toLowerCase()
                      );
                      if (emailExistsInSP) {
                        return "Email Already Exist";
                      }
                      try {
                        const res = await axios.post(
                          getUrl(authConfig.individualVerificationAudit) +
                            "/check-email",
                          { email: value }
                        );
                        if (res?.data?.message === "Email Already Exist") {
                          return "Email Already Exist";
                        }
                        return true;
                      } catch (err) {
                        return "Failed to validate email";
                      }
                    },
                  }}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      value={field.value || ""} 
                      label="Email"
                      InputLabelProps={{ shrink: true }}
                      size="small"
                      placeholder="Enter Email"
                      error={Boolean(errors.email)}
                      helperText={errors.email?.message}
                      aria-describedby="validation-basic-email"
                      inputProps={{ maxLength: 50 }}
                    />
                  )}
                />
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6}>
            <Autocomplete
            id="designation"
            options={listOfSubCategories} // Array of { value, key }
            value={
              listOfSubCategories.find((option) => option.value === designation) ||
              null
            } // The currently selected value (e.g. an object with 'value' and 'key')
            onChange={(event, newValue) => {
              // Ensure newValue is an object with 'value' and 'key' properties
              setDesignation(newValue?.value || null);
            }}
            getOptionLabel={(option) => option.key || ""} // Access 'key' for displaying the label
            isOptionEqualToValue={(option, value) => option.value === value} // Compare based on 'value'
            renderInput={(params) => (
              <TextField
                {...params}
                label="Designation"
                variant="outlined"
                size="small"
              />
            )}
          />
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <Controller
                  name="fromDate"
                  control={control}
                  rules={{ required: "From Date is required" }}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      value={field.value || ""} 
                      size="small"
                      label="From Date"
                      type="date"
                      InputLabelProps={{ shrink: true }}
                      error={Boolean(errors.fromDate)}
                      helperText={errors.fromDate?.message}
                      aria-describedby="fromDate"
                    />
                  )}
                />
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <Controller
                  name="toDate"
                  control={control}
                  rules={{ required: "To Date is required" }}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      value={field.value || ""} 
                      size="small"
                      label="To Date"
                      type="date"
                      InputLabelProps={{ shrink: true }}
                      error={Boolean(errors.toDate)}
                      helperText={errors.toDate?.message}
                      aria-describedby="toDate"
                    />
                  )}
                />
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6}></Grid>
          </Grid>
        </DialogContent>
        <DialogActions
          sx={{
            justifyContent: "end",
            borderTop: (theme) => `1px solid ${theme.palette.divider}`,
            p: (theme) => `${theme.spacing(2.5)} !important`,
          }}
        >
          <Button onClick={handleCloseDialog}>Cancel</Button>

          {rowData ? (
            <Button
              onClick={handleSubmit(update)}
              variant="contained"
              sx={{
                marginRight: 3.5,
              }}
            >
              Update
            </Button>
          ) : (
            <Button
              onClick={handleSubmit(submit)}
              variant="contained"
              sx={{
                marginRight: 3.5,
              }}
            >
              Add
            </Button>
          )}
        </DialogActions>
      </Dialog>
    </>
  );
};

export default SocietyMembers;
