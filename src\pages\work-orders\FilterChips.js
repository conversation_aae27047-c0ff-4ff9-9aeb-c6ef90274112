import React from "react";
import { Chip, Box } from "@mui/material";

const FilterChips = ({ selectedFilters, onRemoveFilter,societyOptions,spsList }) => {
  return (
    <Box sx={{ display: "flex", flexWrap: "wrap", mb: 2 }}>
       {selectedFilters?.map((filter) => {
            // Find the corresponding object in convertedArray with the matching value
            const matchedItem = societyOptions.find(
              (item) => item.orgId === filter.value
            );

            const matchSP = spsList.find((item) => item.srId === filter.value);

            // Display the key of matchedItem if the label is "roleFilter", otherwise display the value
            const displayValue =
              filter.label === "Society Name" && matchedItem
                ? matchedItem.societyName
                : filter.label === "Confirmed SP" && matchSP
                ? matchSP.serviceTypeAndCreatedBy
                : filter.value;

            return (
              filter.label && ( // Only render the Chip if label is not null or undefined
                <Chip
                  key={filter.key}
                  label={`${filter.label}: ${displayValue}`}
                  onDelete={() => onRemoveFilter(filter.key)}
                  sx={{ mr: 1, mb: 1 }}
                />
              )
            );
          })}
    </Box>
  );
};

export default FilterChips;