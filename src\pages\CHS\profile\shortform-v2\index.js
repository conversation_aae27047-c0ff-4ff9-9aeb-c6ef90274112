import React from "react";
import {
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  Divider,
  FormHelperText,
  IconButton,
  InputLabel,
  MenuItem,
  Select,
  Table,
  TableBody,
  TableCell,
  Grid,
  Button,
  TableHead,
  TableRow,
  Typography,
  DialogTitle,
  Box,
  InputAdornment
} from "@mui/material";
import Icon from "src/@core/components/icon";
import FormControl from "@mui/material/FormControl";
import { Controller, useForm, useWatch } from "react-hook-form";
import { useAuth } from "src/hooks/useAuth";
import EmailTextField from "src/@core/components/custom-components/EmailTextField";
import NameTextField from "src/@core/components/custom-components/NameTextField";
import TextField from "@mui/material/TextField";
import MobileNumberValidation from "src/@core/components/custom-components/MobileNumberValidation";
import GoogleMapsIconButton from "src/@core/components/custom-components/toastDisplay.js"
import CheckCircleOutlineIcon from "@mui/icons-material/CheckCircleOutline";
import SelectAutoComplete from "src/@core/components/custom-components/SelectAutoComplete";
import { useContext, useEffect, useState, useRef } from "react";
import axios from "axios";
import { getAuthorizationHeaders, getUrl } from "src/helpers/utils";
import authConfig from "src/configs/auth";
import { AuthContext } from "src/context/AuthContext";



const SocietyEnrollmentShortDialog = ({ open, onClose }) => {
  const { getAllListValuesByListNameId } = useContext(AuthContext);
  const [listOfSubCategories, setListOfSubCategories] = useState([])
  const [designation,setDesignation] = useState(null)
  const [anyOther,setAnyOther] = useState(false)
  const [subCategories,setSubCategories] = useState([])
  
  const [leadStatusData, setLeadStatusData] = useState(null);
  const [leadPriorityData, setLeadPriorityData] = useState(null);
  const handleError = (error) => console.error("Error:", error);
  const [leadStatus, setLeadStatus] = useState();
  const [leadPriority, setLeadPriority] = useState();





  const handleLeadPriorityChange = (event) => {
    const selectedId = event.target.value;
    setLeadPriority(selectedId);
  };


  const handleUploadClick = () => {
    setShowUpload(true); 
  };

   



  const auth = useAuth();


  const {
    register,
    handleSubmit,
    setError,
    control,
    clearErrors,
    reset,
    formState: { errors },
  } = useForm({
    defaultValues: {
      firstName: "",
      email: "",
    },
    });

     const [locationsData,setLocationsData] = useState([])
      const [services,setServices] = useState([])



    useEffect(() => {
      if (!!authConfig) {
        getAllListValuesByListNameId(
          authConfig.allServicesListNameId,
          (data) =>
            setServices(
              data?.listValues.map((item) => ({
                value: item.id,
                key: item.listValue,
              }))
            ),
          handleError
        );
      }
      if (!!authConfig) {
        getAllListValuesByListNameId(
          authConfig.locationlistNameId,
          (data) =>
            setLocationsData(
              data?.listValues.map((item) => ({
                value: item.id,
                key: item.listValue,
              }))
            ),
          handleError
        );
      }
      if (!!authConfig) {
        getAllListValuesByListNameId(
          authConfig.leadStatusListNamesId,
          (data) =>
            setLeadStatusData(
              data?.listValues.map((item) => ({
                value: item.id,
                key: item.listValue,
              }))
            ),
          handleError
        );
      }
      if (!!authConfig) {
        getAllListValuesByListNameId(
          authConfig.leadPriorityListNamesId,
          (data) =>
            setLeadPriorityData(
              data?.listValues.map((item) => ({
                value: item.id,
                key: item.listValue,
              }))
            ),
          handleError
        );
      }
      
    }, [authConfig]);


    useEffect(() => {
      axios({
        method: "get",
        url: getUrl(authConfig.selectDropdownNew) + "?selectionType=EMPLOYEES",
        headers: getAuthorizationHeaders(),
      })
        .then((res) => {
          setListOfEmployees(res.data.data);
        })
        .catch((err) => console.log("Employees error", err));
    }, []);

    
    const validateLocationUrl = (value) => {
      const urlPattern = /^https:\/\/(www\.)?(maps\.app\.goo\.gl|google\.com\/maps)\/.+$/;
    if (!urlPattern.test(value)) {
      return 'Please enter a valid Google Maps URL';
    }
    try {
      const url = new URL(value);
      if (url.hostname !== 'maps.app.goo.gl' && url.hostname !== 'google.com') {
        return 'Please enter a valid Google Maps URL';
      }
    } catch (error) {
      return 'Invalid URL format';
    }
    return true;
  };

  const handleLeadStatusChange = (event) => {
    const value = event.target.value;
    setLeadStatus(value);
  };
  const handleDesignationChange = (newValue) => {
    setDesignation(newValue)
    const matchedSubCategory = listOfSubCategories.find(subCategory => subCategory.value === newValue);
    if(matchedSubCategory?.key == 'Any Other'){
      setAnyOther(true)
    }else{
      setAnyOther(false)
    }
  };
 
   useEffect(() => {
     if (!!authConfig) {
       getAllListValuesByListNameId(
         authConfig.ChsDesignation,
         (data) =>
           setListOfSubCategories(
             data?.listValues?.map((item) => ({
               value: item.id,
               key: item.listValue,
             }))
           ),
         handleError
       );
     }
   }, [authConfig]);
   
  const [listOfEmployees, setListOfEmployees] = useState([]);
  const [employeesOptions, setEmployeesOptions] = useState([]);
  useEffect(() => {
    if (listOfEmployees.length) {
      const data = listOfEmployees.map((entry) => ({
        value: entry.id,
        key: entry.name,
      }));
      setEmployeesOptions(data);
    }
  }, [listOfEmployees]);

  const gstOption = useWatch({ control, name: "gstOption" });
  return (
    <Dialog open={open} onClose={onClose} fullScreen maxWidth="sm" >
      <DialogTitle
          sx={{
          position: "sticky",
          top: 0, // Sticks it to the top of the dialog
          zIndex: 10, // Ensures it stays above the content
          backgroundColor: "background.paper",
            borderBottom: (theme) => `1px solid ${theme.palette.divider}`,
            p: (theme) => `${theme.spacing(1.75, 7)} !important`,
            display: "flex",
            alignItems: "center",
            justifyContent:"space-between",
            fontSize: { xs: 19, md: 20 },
            height: "50px", // height
          }}
          textAlign={"center"}
          
        >
            Add New CHS(Short Form)&nbsp;

            <Box sx={{ top: "9px", right: "10px" }}>

            <IconButton
              size="small"
              onClick={onClose}
              sx={{
                // p: "0.438rem",
                borderRadius: 1,
                color: "common.white",
                backgroundColor: "primary.main",
                "&:hover": {
                  backgroundColor: "#66BB6A",
                  transition: "background 0.5s ease, transform 0.5s ease",
                },
              }}
            >
              <Icon icon="tabler:x" fontSize="1rem" />
            </IconButton>
          </Box>
     
         




        </DialogTitle>
        <DialogContent
            sx={{ p: (theme) => `${theme.spacing(10, 7)} !important` }}
          >
            <Grid container spacing={5}>
              {/* Section Header */}
              <Grid
                item
                xs={12}
                sx={{
                  backgroundColor: "#f2f7f2",
                  mt: 4,
                  ml:5,
                  paddingTop: 0,  
                  height: "36px",
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "space-between"
                }}
              >
                <Typography
                  variant="body1"
                  fontWeight={"bold"}
                  sx={{ mt: 0, ml: -3, mb: 5 ,
                    textAlign: "center", // Aligns text in the center
                    
                  }}
                  
                >
                  Society Information
                </Typography>
                <Divider />
              </Grid>

              {/* Input Field for Society Name */}
              <Grid item xl={4} lg={4} xs={12} sm={4}>
                <FormControl fullWidth>
                  <Controller
                    name="societyName"
                    control={control}
                    rules={{ required: true }}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        label="Society Name"
                        InputLabelProps={{ shrink: true }}
                        size="small"
                        placeholder="Enter your Society name"
                        error={Boolean(errors.societyName)}
                        helperText={errors.societyName?.message || ""}
                        aria-describedby="validation-basic-societyName"
                      />
                    )}
                  />
                </FormControl>
              </Grid>



              <Grid item xl={4} lg={4}xs={12} sm={4}>
                <FormControl fullWidth>
                  <Controller
                    name="googleMapLocation"
                    control={control}
                    rules={{ required: true, validate: validateLocationUrl }}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        label="Google location"
                        InputLabelProps={{ shrink: true }}
                        size="small"
                        placeholder="Click on icon to navigate & paste URL here"
                        error={Boolean(errors.googleMapLocation)}
                        helperText={errors.googleMapLocation?.message}
                        aria-describedby="validation-basic-googleMapLocation"
                        InputProps={{
                          endAdornment: (
                            <InputAdornment position="end">
                              <span style={{ position: 'absolute', right: 8, top: 0 }}>
                                <GoogleMapsIconButton />
                              </span>
                              {/* <Link
                                component="a"
                                href={field.value}
                                target="_blank"
                                rel="noopener noreferrer"
                                sx={{
                                  textDecoration: 'none',
                                  '&:hover': {
                                    textDecoration: 'underline',
                                  },
                                }}
                              >
                                {field.value}
                              </Link> */}
                            </InputAdornment>
                          ),
                        }}
                      />
                    )}
                  />
                </FormControl>
              </Grid>


              
              <Grid item lg={4} xl={4} xs={12} sm={4}>
                <FormControl fullWidth>
                  <InputLabel id="registered-label">Registered For</InputLabel>
                  <Controller
                    name="registeredOption"
                    control={control}
                    render={({ field }) => (
                      <Select
                        {...field}
                        size="small"
                        label="Registered For"
                        InputLabelProps={{ shrink: true }}
                        placeholder="Select"
                      >
                        <MenuItem value="RED">RED</MenuItem>
                        <MenuItem value="PRO">PRO</MenuItem>
                      </Select>
                    )}
                  />
                </FormControl>
              </Grid>


              <Grid item xs={12} sm={4}>
            <FormControl fullWidth>
              <InputLabel id="requisition-label">Requisition</InputLabel>
              <Controller
                name="requisitionOption"
                control={control}
                // defaultValue={formData?.requisitionOption || ""}
                render={({ field }) => (
                  <Select
                    {...field}
                    size="small"
                    labelId="requisition-label"
                    label="Requisition"
                    InputLabelProps={{ shrink: true }}
                    placeholder="Select"
                  >
                    <MenuItem value="yes">Yes</MenuItem>
                    <MenuItem value="no">No</MenuItem>
                  </Select>
                )}
              />
            </FormControl>
          </Grid>


          <Grid item xs={12} sm={4} >
                <FormControl fullWidth>
                  <InputLabel id="readiness-label">Readiness</InputLabel>
                  <Controller
                    name="readinessOption"
                    control={control}
                    // defaultValue={formData?.readinessOption || ""}
                    render={({ field }) => (
                      <Select
                        {...field}
                        size="small"
                        labelId="readiness-label"
                        label="Readiness"
                      >
                        <MenuItem value="yes">Yes</MenuItem>
                        <MenuItem value="no">No</MenuItem>
                      </Select>
                    )}
                  />
                </FormControl>
              </Grid>







              {/* address */}
              <Grid item xs={12} lg={30} >
          <FormControl fullWidth>
            <Controller
              name="address"
              control={control}
              rules={{ required: false }}
              
              render={({ field }) => (
                <TextField
                  rows={2}
                  multiline
                  {...field}
                  label="Address"
                  error={Boolean(errors.address)}
                  aria-describedby="Section1-address"
                />
              )}
            />
            {errors.address && (
              <FormHelperText
                sx={{ color: "error.main" }}
                id="Section1-address"
              >
                This field is required
              </FormHelperText>
            )}
          </FormControl>
        </Grid>

       



              <Grid
                item
                xs={12}
                sx={{
                  backgroundColor: "#f2f7f2",
                  mt: 4,
                  ml:5,
                  paddingTop: 0,  
                  height: "36px",
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "space-between"
                }}
              >
                <Typography
                  variant="body1"
                  fontWeight={"bold"}
                  sx={{ mt: 0, ml: -3, mb: 5 ,
                    textAlign: "center", // Aligns text in the center
                    
                  }}
                  
                >
                  Society Member Information
                </Typography>
                <Divider />
              </Grid>


              {/* name and designation */}

              <Grid item xl={4} lg={4} xs={12} sm={4}>
                    <FormControl fullWidth>
                      <Controller
                        name="firstName"
                        control={control}
                        rules={{ required: true }}
                        render={({ field }) => (
                          <NameTextField
                            {...field}
                            label="Name of the Society Member"
                            InputLabelProps={{ shrink: true }}
                            size="small"
                            placeholder="Enter your name"
                            error={Boolean(errors.firstName)}
                            helperText={errors.firstName?.message}
                            aria-describedby="validation-basic-firstName"
                          />
                        )}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item xl={4} lg={4} xs={12} sm={4}>
                  <SelectAutoComplete
                      id={"designation"}
                      label={"Designation"}
                      nameArray={listOfSubCategories}
                      value={designation}
                      onChange={(event) => handleDesignationChange(event.target.value)}
                    />
                    {errors.designation && (
                      <FormHelperText sx={{ color: 'error.main' }} id='validation-designation'>
                        Please select designation
                      </FormHelperText>
                        )}
                  </Grid>
                        {anyOther && (
                            <Grid container spacing={2}>

                          <Grid item xs={12} sm={6}  mt={2}>
                          <FormControl fullWidth>
                            <Controller
                              name="otherDesignation"
                              control={control}
                              rules={{ required: true }}
                              render={({ field }) => (
                                <NameTextField
                                  {...field}
                                  label="Any Other Designation"
                                  InputLabelProps={{ shrink: true }}
                                  size="small"
                                  placeholder="Enter your any other designation"
                                  error={Boolean(errors.otherDesignation)}
                                  helperText={errors.otherDesignation?.message}
                                  aria-describedby="validation-basic-otherDesignation"
                                />
                              )}
                            />
                          </FormControl>
                        </Grid>
                        </Grid>
                        )}


              <Grid item xl={4} lg={4} xs={12} sm={4}>
                  <FormControl fullWidth>
                    <Controller
                      name="mobileNumber"
                      control={control}
                      rules={{ required: true }}
                      render={({ field }) => (
                        <MobileNumberValidation
                          {...field}
                          type="tel"
                          label="Contact Number"
                          size="small"
                          InputLabelProps={{ shrink: true }}
                          error={Boolean(errors.mobileNumber)}
                          helperText={errors.mobileNumber?.message}
                          placeholder="+91 95 155 990 22"
                          aria-describedby="validation-mobileNumber"
                        />
                      )}
                    />
                  </FormControl>
              </Grid>

              {/* alternateNumber */}

              <Grid item xl={4} lg={4} xs={12} sm={4}>
          <FormControl fullWidth>
            <Controller
              name="alternateMobileNumber"
              control={control}
             
              render={({ field }) => (
                <TextField
                  {...field}
                  type="tel"
                  label="Alternate Number"
                  size="small"
                  error={Boolean(errors.alternateMobileNumber)}
                  helperText={errors.alternateNumber?.message}
                  InputLabelProps={{ shrink: true }}
                  placeholder="+91 1234567890"
                  inputProps={{
                    maxLength: field?.value?.startsWith("+91") ? 13 : 10,
                  }}
                />
              )}
            />
          </FormControl>
        </Grid>


        {/* Email Verify */}


        <Grid item xl={4} lg={4} xs={12} sm={4}
         
    >
          <FormControl fullWidth>
            <Controller
              name="individualName"
              control={control}
              rules={{ required: false }}
              // defaultValue={formData?.individualName}
              render={({ field }) => (
                <NameTextField
                  {...field}
                  label="Email address"
                  InputLabelProps={{ shrink: true }}
                  size="small"
                  placeholder="Enter your Email address"
                  // error={Boolean(errors.name)}
                  // helperText={errors.name?.message}
                  aria-describedby="validation-basic-name"
                />
              )}
            />
          </FormControl>
          
        </Grid>


        <Grid item xl={1}>
          <Button sx = {{ padding : "6px" , ml :{
            sm : -3,
            
          }}}
                    variant="contained"
                  >Verify</Button>
          </Grid>


          
          

  

            

         

        <Grid
                item
                xs={12}
                sx={{
                  backgroundColor: "#f2f7f2",
                  mt: 4,
                  ml:5,
                  paddingTop: 0,  
                  height: "36px",
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "space-between"
                }}
              >
                <Typography
                  variant="body1"
                  fontWeight={"bold"}
                  sx={{ mt: 0, ml: -3, mb: 5 ,
                    textAlign: "center", // Aligns text in the center
                    
                  }}
                  
                >
                  Assignment And Status
                </Typography>
                <Divider />
              </Grid>
                  
              <Grid item xs={12} sm={4} lg={4}>
        <FormControl fullWidth>
          <Controller
            name="assignedToUUIDs"
            control={control}
            rules={{ required: false }}
            render={({ field }) => (
              <SelectAutoComplete
                id="assignedTo-select"
                size="small"
                label="Assigned To"
                nameArray={employeesOptions || []}
                value={field.value || []}
                onChange={(e) => {
                  field.onChange(e);
                }}
                error={Boolean(errors.service)}
              />
            )}
          />
        </FormControl>
        {errors.leadPriority && (
          <FormHelperText
            sx={{ color: "error.main" }}
            id="validation-leadPriority"
          >
            {errors.leadPriority?.message}
          </FormHelperText>
        )}
      </Grid>
              


              <Grid item xs={12} sm={4}>
                <FormControl fullWidth>
                <Controller
                  name="status"
                  control={control}
                  size="small"
                 
                  InputLabelProps={{ shrink: true }}
                  render={({ field }) => (
                    <SelectAutoComplete
                      id="status"
                      label="Lead Status"
                      nameArray={leadStatusData}
                      value={leadStatus}
                      onChange={(e) => {
                        field.onChange(e);
                        handleLeadStatusChange(e);
                      }}
                    />
                  )}
                />
              </FormControl>
              </Grid>


              <Grid item xs={12} sm={4}>
              <FormControl fullWidth>
                <Controller
                  name="lead-priority"
                  control={control}
                  size="small"
                
                  render={({ field }) => (
                    <SelectAutoComplete
                      id="lead-priority"
                      label="Lead Priority"
                      nameArray={leadPriorityData}
                      value={leadPriority}
                      onChange={(e) => {
                        field.onChange(e);
                        handleLeadPriorityChange(e);
                      }}
                    />
                  )}
                />
              </FormControl>
            </Grid>



            
          
           


              

          </Grid>
          </DialogContent>


        <DialogActions
          sx={{
            position: "sticky",
            bottom: 0, // Sticks it to the bottom of the dialog
            zIndex: 10, // Ensures it stays above the content
            backgroundColor: "background.paper",
            borderTop: (theme) => `1px solid ${theme.palette.divider}`,
            p: (theme) => `${theme.spacing(1.75, 7)} !important`,
            justifyContent: "flex-end",
          
            justifyContent: "flex-end", // Align all content to the right
     
        
          }}
        >
          <Button
            display="flex"
            justifyContent="center"
            variant="outlined"
            color="primary"
            onClick={onclose}
          >
            Cancel
          </Button>
          <Button
            display="flex"
            justifyContent="Flex-end"
            variant="contained"
            color="primary"
            // onClick={handleSubmit(submit)}
            >
            Save
          </Button>
          </DialogActions>
         
    </Dialog>
  );
};

export default SocietyEnrollmentShortDialog;
