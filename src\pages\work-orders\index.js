import { Card } from "@mui/material";

import { useRBAC } from "src/pages/permission/RBACContext";
import { MENUS, PAGES, PERMISSIONS } from "src/constants";
import { useRouter } from "next/router";
import { useEffect } from "react";
import WorkOrderDetails from "./WorkOrderDetails";
import NavTabsWorkOrders from "src/@core/components/custom-components/NavTabsWorkOrders";

const WorkOrders = () => {
  const { canMenuPageSection, rbacRoles } = useRBAC();
  const router = useRouter();
  const canAccessWorkOrders = (requiredPermission) =>
    canMenuPageSection(MENUS.LEFT,PAGES.PROJECTS ,PAGES.WORK_ORDERS, requiredPermission);

  useEffect(() => {
    if (rbacRoles != null && rbacRoles?.length > 0) {
      if (!canAccessWorkOrders(PERMISSIONS.READ)) {
        router.push("/401");
      }
    }
  }, [rbacRoles]);

  if(canAccessWorkOrders(PERMISSIONS.READ)) {
  return (
    <div>
      {/* <NavTabsWorkOrders
        tabContent1={
          <>
           <WorkOrderDetails/> 
          </>
        }
        tabContent2={
          <>
            <WorkOrderDetails/> 
          </>
        }
        tabContent3={
            <>
             <WorkOrderDetails/>  
            </>
        }
        tabContent4={
            <>
             <WorkOrderDetails/>  
            </>
        }
       
      /> */}
      <WorkOrderDetails/> 
    </div>
  );
} else {
  return null;
}
};

export default WorkOrders;
