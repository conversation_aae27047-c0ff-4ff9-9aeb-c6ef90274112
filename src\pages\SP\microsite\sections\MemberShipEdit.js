import React, { useEffect, useState, useContext } from "react";
import { useF<PERSON>, Controller } from "react-hook-form";
import { useMediaQuery, useTheme, Box } from "@mui/material";
import {
  Button,
  Grid,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  TextField,
  Typography,
  IconButton,
  TableContainer,
} from "@mui/material";
import Icon from "src/@core/components/icon";
import { AuthContext } from "src/context/AuthContext";

const MemberShipEdit = (props) => {
  const { onCancel, data, userData } = props;
  const { user, patchMicrosite } = useContext(AuthContext);

  const {
    control,
    handleSubmit,
    reset,
    setValue,
    unregister,
    getValues,
    formState: { errors },
  } = useForm();

  const initialEntries = data?.memberShipList || [];
  const [entries, setEntries] = useState(initialEntries);
  const [fieldChanged, setFieldChanged] = useState(false);

  const addEntry = () => {
    const currentValues = getValues();
    const currentEntries = currentValues.awards || [];

    setEntries([...currentEntries, { description: "" }]);
  };

  const removeEntry = (index) => {
    const newEntries = entries.filter((_, i) => i !== index);
    setEntries(newEntries);

    unregister(`awards[${index}].description`);

    reset({
      ...getValues(),
      awards: newEntries,
    });
    setFieldChanged(true);
  };

  const onSubmit = async (data) => {
    const awardsData = data?.awards?.filter(
      (entry) => entry.description.trim() !== ""
    ) || [{ description: "" }];

    const userUniqueId =
      userData && userData.id !== undefined ? userData.organisationId : user.orgId;
    await patchMicrosite(
      { memberShipList: awardsData },
      userUniqueId,
      () => {
        console.log("Success memberships.");
      },
      () => {
        console.error("memberships failed");
      }
    );

    onCancel();
    reset();
  };

  const theme = useTheme(); // Access current theme
  const isSmallScreen = useMediaQuery(theme.breakpoints.down("md"));
  const isExtraSmallScreen = useMediaQuery("(max-width:360px)");

  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <Grid
        container
        justifyContent={
          entries.length === 0
            ? { lg: "space-between", md: "space-between", sm: "space-between" }
            : "flex-end"
        }
        flexDirection={
          entries.length === 0
            ? { xs: "column", lg: "row", md: "row", sm: "row" }
            : ""
        }
        alignItems="center"
        sx={{
          mt: { xs: 2, lg: 4 },
          mb: { xs: 2, lg: 2 },
        }}
      >
        {entries.length === 0 && (
          <Typography
            variant="body1"
            style={{
              textAlign: "center",
              flex: 1,
              marginLeft: isExtraSmallScreen ? "" : "4rem",
              marginTop: "5px",
            }}
          >
            Click on ADD to add Membership
          </Typography>
        )}
        <Button
          onClick={addEntry}
          color="primary"
          variant="contained"
          sx={{
            mb: { xs: 2, lg: 2 },
            mt: { xs: 2, lg: 0 },
            alignSelf:
              isExtraSmallScreen || isSmallScreen ? "flex-end" : "auto",
          }}
        >
          Add
        </Button>
      </Grid>
      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            {entries.length > 0 && (
              <TableRow style={{ backgroundColor: "#f2f7f2" }}>
                <TableCell sx={{ padding: "5px" }}>Description</TableCell>
                <TableCell sx={{ padding: "5px" }}>Delete</TableCell>
              </TableRow>
            )}
          </TableHead>
          <TableBody>
            {entries.map((entry, index) => (
              <TableRow key={index}>
                <TableCell>
                  <Controller
                    name={`awards[${index}].description`}
                    control={control}
                    defaultValue={entry.description}
                    rules={{ required: "Description is required" }}
                    render={({ field }) => (
                      <TextField
                      id="description"
                        {...field}
                        label="Description"
                        variant="outlined"
                        size="small"
                        fullWidth
                        error={Boolean(errors?.awards?.[index]?.description)}
                        helperText={
                          errors?.awards?.[index]?.description?.message || ""
                        }
                        sx={{ width: isSmallScreen ? "200px" : "100%" }}
                        onChange={(e) => {
                          field.onChange(e);
                          setFieldChanged(true);
                        }}
                      />
                    )}
                  />
                </TableCell>
                <TableCell>
                  <IconButton onClick={() => removeEntry(index)} color="error">
                    <Icon icon="iconamoon:trash" />
                  </IconButton>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
      <Grid
            container
            justifyContent="center"
            sx={{
              mt: { xs: 4, lg: 4 },
              mb: { xs: 2, lg: 4 },
            }}
          >
            <Button
              size="medium"
              sx={{ mr: 3 }}
               variant="outlined"
                color="primary"
                onClick={() => onCancel()}
            >
              Cancel
            </Button>
            <Button
              size="medium"
              type="submit"
              color="primary"
                variant="contained"
                disabled={!fieldChanged}
              
            >
              Submit
            </Button>
          </Grid>
    </form>
  );
};

export default MemberShipEdit;
