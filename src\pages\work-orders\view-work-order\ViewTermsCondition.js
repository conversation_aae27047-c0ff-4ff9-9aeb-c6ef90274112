import { useTheme } from "@emotion/react";
import {
  Grid,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
  useMediaQuery,
  Divider,
} from "@mui/material";
import { useContext } from "react";
import { AuthContext } from "src/context/AuthContext";

const innerTableStylings = {
  fontWeight: "bold",
  fontSize: "10px", 
};

const ViewTermsAndConditions = ({ terms }) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));
  const { user } = useContext(AuthContext);

  return (
    <>
      {/* Table View */}
      <TableContainer
        component={Paper}
        sx={{
          maxWidth: "100%",
          overflowX: "auto",
        }}
      >
        <Table size="small" sx={{ minWidth: "300px" }}>
          {" "}
          {/* Ensures columns don't collapse */}
          <TableHead>
            <TableRow>
              <TableCell style={innerTableStylings}>Sequence</TableCell>
              <TableCell style={innerTableStylings}>Description</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {terms?.length > 0 ? (
              terms.map((term, idx) => (
                <TableRow key={term?.id}>
                  <TableCell sx={{ padding: "4px" }}>{idx + 1}</TableCell>
                  <TableCell sx={{ padding: "4px" }}>
                    {term?.description}
                  </TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={2} sx={{ textAlign: "center" }}>
                  No data available for Terms & Conditions.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </TableContainer>
    </>
  );
};

export default ViewTermsAndConditions;
