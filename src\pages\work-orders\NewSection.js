import {
    FormControl,
    Grid,
    TextField
} from "@mui/material";
import { useContext, useEffect, useState } from "react";
import { Controller, useForm } from "react-hook-form";
import SelectAutoComplete from "src/@core/components/custom-components/SelectAutoComplete";
import authConfig from "src/configs/auth";
import { AuthContext } from "src/context/AuthContext";


const NewSection = ({newSectionData, setNewSectionData}) => {
 
  const { getAllListValuesByListNameId } = useContext(AuthContext);

  const {
    control,
    setValue,
    formState: { errors },
  } = useForm();


  const [workStatus, setWorkStatus] = useState("");
  const [status, setStatus] = useState("");
  const handleError = (error) => {
    console.error("Payment schedule error:", error);
  };

  const [workStatusList, setWorkStatusList] = useState([]);
  const [statusList, setStatusList] = useState([]);
  useEffect(() => {
    if (!!authConfig) {
      getAllListValuesByListNameId(
        authConfig.workOrderWorkStatusListNameId,
        (data) =>
            setWorkStatusList(
            data?.listValues.map((item) => ({
              value: item.id,
              key: item.listValue,
            }))
          ),
        handleError
      );

      getAllListValuesByListNameId(
        authConfig.workOrderStatusId,
        (data) =>
            setStatusList(
            data?.listValues.map((item) => ({
              value: item.id,
              key: item.listValue,
            }))
          ),
        handleError
      );
    }
  }, [authConfig]);

  useEffect(() => {
    if(newSectionData){
        setWorkStatus(newSectionData?.workStatus);
        setStatus(newSectionData?.workOrderStatus);
        setValue("lastContactedDate",newSectionData?.lastContactDate);
        setValue("nextFollowUpDate",newSectionData?.nextFollowUpDate);
    }
  },[newSectionData])

  return (
    <>
      <Grid container spacing={2} sx={{ marginTop: 2 }}>
        
        <Grid item xs={10} sm={3}>
          <FormControl fullWidth error={Boolean(errors.workStatus)}>
            <Controller
              name="workStatus"
              control={control}
              rules={{ required: false }}
              render={({ field }) => (
                <>
                  <SelectAutoComplete
                    id="workStatus"
                    label="Work Status"
                    nameArray={workStatusList}
                    value={workStatus}
                    onChange={(e) => {
                        const value = e.target.value;
                        field.onChange(value);
                        setWorkStatus(value);
                        setNewSectionData((prev) => ({
                          ...prev,
                          workStatus: value,
                        }));
                    }}
                  />
                  {errors.workStatus && (
                    <FormHelperText sx={{ color: "error.main" }}>
                      {errors.status.message}
                    </FormHelperText>
                  )}
                </>
              )}
            />
          </FormControl>
        </Grid>
        <Grid item xs={10} sm={3}>
          <FormControl fullWidth error={Boolean(errors.status)}>
            <Controller
              name="status"
              control={control}
              rules={{ required: false }}
              render={({ field }) => (
                <>
                  <SelectAutoComplete
                    id="status"
                    label="Status"
                    nameArray={statusList}
                    value={status}
                    onChange={(e) => {
                        const value = e.target.value;
                        field.onChange(value);
                        setStatus(value);
                        setNewSectionData((prev) => ({
                          ...prev,
                          workOrderStatus: value,
                        }));
                    }}
                  />
                  {errors.status && (
                    <FormHelperText sx={{ color: "error.main" }}>
                      {errors.status.message}
                    </FormHelperText>
                  )}
                </>
              )}
            />
          </FormControl>
        </Grid>
      
      
        <Grid item xs={12} sm={3}>
          <FormControl fullWidth>
            <Controller
              name="lastContactedDate"
              control={control}
              rules={{ required: false }}
              render={({ field }) => (
                <TextField
                  {...field}
                  size="small"
                  label="Last Contacted Date"
                  type="date"
                  InputLabelProps={{ shrink: true }}
                  aria-describedby="lastContactedDate"
                  value={field.value}
                  onChange={(e) => {
                  const value = e.target.value;
                  field.onChange(value);
                  setNewSectionData((prev) => ({
                    ...prev,
                    lastContactDate: value,
                  }));
                  }}
                />
              )}
            />
          </FormControl>
        </Grid>
        <Grid item xs={12} sm={3}>
          <FormControl fullWidth>
            <Controller
              name="nextFollowUpDate"
              control={control}
              rules={{ required: false }}
              render={({ field }) => (
                <TextField
                  {...field}
                  size="small"
                  label="Next FollowUp Date"
                  type="date"
                  InputLabelProps={{ shrink: true }}
                  aria-describedby="nextFollowupDate"
                  value={field.value}
                  onChange={(e) => {
                    const value = e.target.value;
                    field.onChange(value);
                    setNewSectionData((prev) => ({
                        ...prev,
                        nextFollowUpDate: value,
                      }));
                  }}
                />
              )}
            />
          </FormControl>
        </Grid>

      </Grid>
     
    </>
  );
};
export default NewSection;
