import ArchitectureIcon from "@mui/icons-material/Architecture";
import AssignmentIcon from "@mui/icons-material/Assignment";
import CheckCircleIcon from "@mui/icons-material/CheckCircle";
import ContactMailIcon from "@mui/icons-material/ContactMail";
import HomeIcon from "@mui/icons-material/Home";
import InfoIcon from "@mui/icons-material/Info";
import MapIcon from "@mui/icons-material/Map";
import PreviewIcon from "@mui/icons-material/Preview";
import {
  Box,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  IconButton,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Typography,
  useMediaQuery,
} from "@mui/material";
import axios from "axios";
import { useContext, useEffect, useState } from "react";
import Icon from "src/@core/components/icon";
import authConfig from "src/configs/auth";
import { AuthContext } from "src/context/AuthContext";
import { getAuthorizationHeaders, getUrl } from "src/helpers/utils";
import PreviewSection from "./LongFormPreview";
import SocietyDetails from "./SocietyDetails";
import LandDetails from "./LandDetails";
import FSIDetails from "./FSIDetails";
import Requirements from "./Requirements";
import SocietyMemberInformation from "./SocietyMemberInformation";
import BusinessInformation from "./BusinessInformation";
import Section7 from "./Section7";
import { useTheme } from "@mui/material/styles";
import ReferenceSection from "./ReferenceSection";
import { fetchIpAddress } from "src/@core/components/custom-components/FetchIpAddress";

const LongFormDialog = ({
  open,
  handleClose,
  currentRow,
  fetchUsers,
  page,
  pageSize,
  roleFilter,
  employeesData,
}) => {
  const { user, patchCHSProfile } = useContext(AuthContext);

  const [dialogMessage, setDialogMessage] = useState("");
  const [dialogSuccess, setDialogSuccess] = useState(false);

  const handleCloseDialog = () => {
    setDialogSuccess(false);
  };

  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));

  const [currentStep, setCurrentStep] = useState(0);
  const [formData, setFormData] = useState({
    societyDetails: {
      name: "",
      googleMapLocation: "",
      enrolledDate: "",
      registeredFor: [],
      requisition: "",
      readiness: "",
      societyAddress: "",
      roadWidth: "",
      grossPlotArea: "",
      authority: "",
      teamMember: "",
      plotCTSNo: "",
      locationId: "",
      zone: "",
      pinCode: "",
    },
    societyMemberInformation: {
      societyMemberName: "",
      societyMemberDesignation: "",
      societyMemberContactNumber: "",
      loginEmail: "",
      alternateNumber: "",
      fromDate: "",
      toDate: "",
      societyCommitteeMemberInformationList: [],
    },
    businessInformation: {
      bankName: "",
      branch: "",
      accountNumber: "",
      ifscCode: "",
      gstNo: "",
      panNo: "",
      stateName: "",
      doYouHaveGstNo: "",
    },
    assignmentAndStatus: {
      assignedTo: "",
      leadPriority: "",
      leadStatus: "",
      remarks: "",
      curatedBy: "",
      curatedOn: "",
    },
    landDetails: {
      builtUpAreaResidential: "",
      builtUpAreaCommercial: "",
      noOfResidence: "",
      noOfCommercial: "",
    },
    fsi: {
      buildingAge: "",
      fsiConsumedFsi: "",
      fsi_AvailableFsi: "",
      fsi_PermissibleFsi: "",
      heightRestriction: "",
      scheme: "",
      dpRestrictions: "",
      litigationsOrEncroachment: "",
    },
    requirements: {
      requirements_ExtraArea: "",
      requirements_Rent: "",
      requirements_Corpus: "",
      notes: "",
      leadGivenTo: "",
    },
    reference: {
      referenceType: "",
      referralName: "",
      referralEmail: "",
      referralNumber: "",
      referralCompanyName: "",
    },
  });

  // Handle updates for individual sections
  const handleFieldUpdate = (sectionId, updatedData) => {
    setFormData((prevData) => {
      return {
        ...prevData,
        [sectionId]: updatedData,
      };
    });
  };

  const hasRedOption = formData.societyDetails?.registeredFor.includes("RED");

  const sections = [
    {
      id: "societyDetails",
      title: "Society Information",
      component: SocietyDetails,
      icon: <HomeIcon />,
    },
    {
      id: "societyMemberInformation",
      title: "Society Member Information",
      component: SocietyMemberInformation,
      icon: <ContactMailIcon />,
    },
    {
      id: "businessInformation",
      title: "Business Information",
      component: BusinessInformation,
      icon: <InfoIcon />,
    },
    {
      id: "assignmentAndStatus",
      title: "Assignment and Status",
      component: Section7,
      icon: <CheckCircleIcon />,
    },
    ...(hasRedOption
      ? [
          {
            id: "landDetails",
            title: "Land Details",
            component: LandDetails,
            icon: <MapIcon />,
          },
          {
            id: "fsi",
            title: "FSI",
            component: FSIDetails,
            icon: <ArchitectureIcon />,
          },
          {
            id: "requirements",
            title: "Requirements",
            component: Requirements,
            icon: <AssignmentIcon />,
          },
        ]
      : []),
    {
      id: "reference",
      title: "Reference",
      component: ReferenceSection,
      icon: <CheckCircleIcon />,
    },
    {
      id: "preview",
      title: "Preview",
      component: PreviewSection,
      icon: <PreviewIcon />,
    },
  ];

  const handleStepChange = (index) => {
    setCurrentStep(index);
  };

  const handleNext = () => {
    if (currentStep < sections?.length - 1) {
      setCurrentStep((prev) => prev + 1);
    }
  };

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep((prev) => prev - 1);
    }
  };

  const handleDialogClose = () => {
    setFormData({
      societyDetails: {
        name: "",
        googleMapLocation: "",
        enrolledDate: "",
        registeredFor: [],
        requisition: "",
        readiness: "",
        societyAddress: "",
        roadWidth: "",
        grossPlotArea: "",
        authority: "",
        teamMember: "",
        plotCTSNo: "",
        locationId: "",
        zone: "",
        pinCode: "",
      },
      societyMemberInformation: {
        societyMemberName: "",
        societyMemberDesignation: "",
        societyMemberContactNumber: "",
        loginEmail: "",
        alternateNumber: "",
        fromDate: "",
        toDate: "",
        societyCommitteeMemberInformationList: [],
      },
      businessInformation: {
        bankName: "",
        branch: "",
        accountNumber: "",
        ifscCode: "",
        gstNo: "",
        panNo: "",
        stateName: "",
        doYouHaveGstNo: "",
      },
      assignmentAndStatus: {
        assignedTo: "",
        leadPriority: "",
        leadStatus: "",
        remarks: "",
        curatedBy: "",
        curatedOn: "",
      },
      landDetails: {
        builtUpAreaResidential: "",
        builtUpAreaCommercial: "",
        noOfResidence: "",
        noOfCommercial: "",
      },
      fsi: {
        buildingAge: "",
        fsiConsumedFsi: "",
        fsi_AvailableFsi: "",
        fsi_PermissibleFsi: "",
        heightRestriction: "",
        scheme: "",
        dpRestrictions: "",
        litigationsOrEncroachment: "",
      },
      requirements: {
        requirements_ExtraArea: "",
        requirements_Rent: "",
        requirements_Corpus: "",
        notes: "",
        leadGivenTo: "",
      },
      reference: {
        referenceType: "",
        referralName: "",
        referralEmail: "",
        referralNumber: "",
        referralCompanyName: "",
      },
    });
    setProfileData({});
    setCurrentStep(0);
    handleClose();
  };
  function extractNonNullFields(formData) {
    const result = {};
    const member = {
      id: profileData?.chsProfileDTO?.userId,
      name: formData?.societyMemberInformation?.societyMemberName,
      contactNumber:
        formData?.societyMemberInformation?.societyMemberContactNumber,
      alternateNumber: formData?.societyMemberInformation?.alternateNumber,
      email: formData?.societyMemberInformation?.loginEmail,
      designation: formData?.societyMemberInformation?.societyMemberDesignation,
      fromDate: formData?.societyMemberInformation?.fromDate,
      toDate: formData?.societyMemberInformation?.toDate,
      isActive: formData?.societyMemberInformation?.isActive,
    };

    const mappings = {
      societyAddress: formData?.societyDetails?.societyAddress,
      societyCommitteeMemberInformationList: Array.isArray(
        formData?.societyMemberInformation
          ?.societyCommitteeMemberInformationList
      )
        ? [
            ...formData.societyMemberInformation
              .societyCommitteeMemberInformationList,
          ]
        : [],
      userId: profileData.chsProfileDTO.userId,
      name: formData?.societyDetails?.name,
      userId: profileData?.chsProfileDTO.userId,
      googleMapLocation: formData?.societyDetails?.googleMapLocation,
      enrolledDate: formData?.societyDetails?.enrolledDate,
      registeredFor: formData?.societyDetails?.registeredFor,
      requisition: formData?.societyDetails?.requisition?.toUpperCase(),
      readiness: formData?.societyDetails?.readiness?.toUpperCase(),
      roadWidth: formData?.societyDetails?.roadWidth,
      grossPlotArea: formData?.societyDetails?.grossPlotArea,
      authority: formData?.societyDetails?.authority,
      teamMember: formData?.societyDetails?.teamMember,
      plotCTSNo: formData?.societyDetails?.plotCTSNo,
      locationId: formData?.societyDetails?.locationId,
      zone: formData?.societyDetails?.zone,
      pinCode: formData?.societyDetails?.pinCode,
      societyMemberName: formData?.societyMemberInformation?.societyMemberName,
      societyMemberDesignation:
        formData?.societyMemberInformation?.societyMemberDesignation,
      societyMemberContactNumber:
        formData?.societyMemberInformation?.societyMemberContactNumber,
      loginEmail: formData?.societyMemberInformation?.loginEmail,
      alternateNumber: formData?.societyMemberInformation?.alternateNumber,
      fromDate: formData?.societyMemberInformation?.fromDate,
      toDate: formData?.societyMemberInformation?.toDate,
      bankName: formData?.businessInformation?.bankName,
      branch: formData?.businessInformation?.branch,
      accountNumber: formData?.businessInformation?.accountNumber,
      ifscCode: formData?.businessInformation?.ifscCode,
      gstNo: formData?.businessInformation?.gstNo,
      panNo: formData?.businessInformation?.panNo,
      stateName: formData?.businessInformation?.stateName,
      doYouHaveGstNo:
        formData?.businessInformation?.doYouHaveGstNo?.toUpperCase(),
      assignedTo: formData?.assignmentAndStatus?.assignedTo,
      leadPriority: formData?.assignmentAndStatus?.leadPriority,
      leadStatus: formData?.assignmentAndStatus?.leadStatus,
      remarks: formData?.assignmentAndStatus?.remarks,
      curatedBy: formData?.assignmentAndStatus?.curatedBy,
      curatedOn: formData?.assignmentAndStatus?.curatedOn,
      builtUpAreaResidential: formData?.landDetails?.builtUpAreaResidential,
      builtUpAreaCommercial: formData?.landDetails?.builtUpAreaCommercial,
      noOfResidence: formData?.landDetails?.noOfResidence,
      noOfCommercial: formData?.landDetails?.noOfCommercial,
      buildingAge: formData?.fsi?.buildingAge,
      fsiConsumedFsi: formData?.fsi?.fsiConsumedFsi,
      fsi_AvailableFsi: formData?.fsi?.fsi_AvailableFsi,
      fsi_PermissibleFsi: formData?.fsi?.fsi_PermissibleFsi,
      heightRestriction: formData?.fsi?.heightRestriction,
      scheme: formData?.fsi?.scheme,
      dpRestrictions: formData?.fsi?.dpRestrictions,
      litigationsOrEncroachment: formData?.fsi?.litigationsOrEncroachment,
      requirements_ExtraArea: formData?.requirements?.requirements_ExtraArea,
      requirements_Rent: formData?.requirements?.requirements_Rent,
      requirements_Corpus: formData?.requirements?.requirements_Corpus,
      notes: formData?.requirements?.notes,
      leadGivenTo: formData?.requirements?.leadGivenTo,
      referenceType: formData?.reference?.referenceType,
      referralName: formData?.reference?.referralName,
      referralNumber: formData?.reference?.referralNumber,
      referralEmail: formData?.reference?.referralEmail,
      referralCompanyName: formData?.reference?.referralCompanyName,
    };

    mappings.societyCommitteeMemberInformationList.push(member);

    for (const [key, value] of Object.entries(mappings)) {
      if (value) {
        result[key] = value;
      }
    }

    return result;
  }

  async function handleSaveProgress() {
    const payload = extractNonNullFields(formData);
    const ipAddress = await fetchIpAddress();
    payload.ipAddress = ipAddress;
    let orgId = currentRow?.organisationId;

    await patchCHSProfile(
      orgId,
      payload,
      () => {
        const message = `Society Profile Progress Saved Successfully`;
        setDialogMessage(message);
        setDialogSuccess(true);
      },
      () => {
        const message = `Failed to save the progress of Society Profile`;
        setDialogMessage(message);
        setDialogSuccess(true);
      }
    );
    fetchUsers(page, pageSize, roleFilter);
    handleDialogClose();
  }

  async function handleSubmit() {
    const payload = extractNonNullFields(formData);

    const ipAddress = await fetchIpAddress();
    payload.ipAddress = ipAddress;
    let orgId = currentRow?.organisationId;

    await patchCHSProfile(
      orgId,
      payload,
      () => {
        const message = `Society Profile Updated Successfully`;
        setDialogMessage(message);
        setDialogSuccess(true);
      },
      () => {
        const message = `Failed to update Society Profile`;
        setDialogMessage(message);
        setDialogSuccess(true);
      }
    );
    fetchUsers(page, pageSize, roleFilter);
    handleDialogClose();
  }

  const [profileData, setProfileData] = useState({});

  useEffect(() => {
    if (!currentRow) {
      // If currentRow is null or undefined, do not make the API call
      return;
    }
    const url =
      getUrl(authConfig.organisationsEndpoint) +
      "/organisation-individual-chs-data/" +
      currentRow?.organisationId;

    axios({
      method: "get",
      url: url,
      headers: getAuthorizationHeaders({
        accept: authConfig.ORGANISATION_META_DATA_GET_BY_ID_V1,
      }),
    })
      .then((res) => {
        setProfileData(res.data);
      })
      .catch((err) => console.log("CHS Data error", err));
  }, [currentRow]);
  useEffect(() => {
    const chsProfileDTO = profileData.chsProfileDTO || {};
    const matchedCommitteeMember =
      profileData.societyCommitteeMemberInformation?.find(
        (member) => member.id === chsProfileDTO?.userId
      );

    const filteredCommitteeMemberList =
      profileData.societyCommitteeMemberInformation?.filter(
        (member) => member.id !== chsProfileDTO?.userId
      );

    setFormData({
      societyDetails: {
        name: chsProfileDTO?.name || null,
        googleMapLocation: chsProfileDTO?.googleMapLocation || null,
        enrolledDate: chsProfileDTO?.enrolledDate || null,
        registeredFor: chsProfileDTO?.registeredFor || [],
        requisition: chsProfileDTO?.requisition?.toLowerCase() || null,
        readiness: chsProfileDTO?.readiness?.toLowerCase() || null,
        societyAddress: chsProfileDTO?.societyAddress || null,
        roadWidth: chsProfileDTO?.roadWidth || null,
        grossPlotArea: chsProfileDTO?.grossPlotArea || null,
        authority: chsProfileDTO?.authority || null,
        teamMember: chsProfileDTO?.teamMember || null,
        plotCTSNo: chsProfileDTO?.plotCTSNo || null,
        locationId: chsProfileDTO?.locationId || null,
        zone: chsProfileDTO?.zone || null,
        pinCode: chsProfileDTO?.pinCode || null,
      },
      societyMemberInformation: {
        societyMemberName: matchedCommitteeMember?.name || null,
        societyMemberDesignation: matchedCommitteeMember?.designation || null,
        societyMemberContactNumber:
          matchedCommitteeMember?.contactNumber || null,
        loginEmail: matchedCommitteeMember?.email || null,
        alternateNumber: matchedCommitteeMember?.alternateNumber || null,
        fromDate: matchedCommitteeMember?.fromDate || null,
        toDate: matchedCommitteeMember?.toDate || null,
        isActive: matchedCommitteeMember?.isActive || false,
        societyCommitteeMemberInformationList:
          filteredCommitteeMemberList || [],
      },
      businessInformation: {
        bankName: chsProfileDTO?.bankName || null,
        branch: chsProfileDTO?.branch || null,
        accountNumber: chsProfileDTO?.accountNumber || null,
        ifscCode: chsProfileDTO?.ifscCode || null,
        gstNo: chsProfileDTO?.gstNo || null,
        panNo: chsProfileDTO?.panNo || null,
        stateName: chsProfileDTO?.stateName || null,
        doYouHaveGstNo: chsProfileDTO?.doYouHaveGstNo?.toLowerCase() || null,
      },
      assignmentAndStatus: {
        assignedTo: chsProfileDTO?.assignedTo || null,
        leadPriority: chsProfileDTO?.leadPriority || null,
        leadStatus: chsProfileDTO?.leadStatus || null,
        remarks: chsProfileDTO?.remarks || null,
        curatedBy: chsProfileDTO?.curatedBy || null,
        curatedOn: chsProfileDTO?.curatedOn || null,
      },
      landDetails: {
        builtUpAreaResidential: chsProfileDTO?.builtUpAreaResidential || null,
        builtUpAreaCommercial: chsProfileDTO?.builtUpAreaCommercial || null,
        noOfResidence: chsProfileDTO?.noOfResidence || null,
        noOfCommercial: chsProfileDTO?.noOfCommercial || null,
      },
      fsi: {
        buildingAge: chsProfileDTO?.buildingAge || null,
        fsiConsumedFsi: chsProfileDTO?.fsiConsumedFsi || null,
        fsi_AvailableFsi: chsProfileDTO?.fsi_AvailableFsi || null,
        fsi_PermissibleFsi: chsProfileDTO?.fsi_PermissibleFsi || null,
        heightRestriction: chsProfileDTO?.heightRestriction || null,
        scheme: chsProfileDTO?.scheme || null,
        dpRestrictions: chsProfileDTO?.dpRestrictions || null,
        litigationsOrEncroachment:
          chsProfileDTO?.litigationsOrEncroachment || null,
      },
      requirements: {
        requirements_ExtraArea: chsProfileDTO?.requirements_ExtraArea || null,
        requirements_Rent: chsProfileDTO?.requirements_Rent || null,
        requirements_Corpus: chsProfileDTO?.requirements_Corpus || null,
        notes: chsProfileDTO?.notes || null,
        leadGivenTo: chsProfileDTO?.leadGivenTo || null,
      },
      reference: {
        referenceType: chsProfileDTO?.referenceType || null,
        referralName: chsProfileDTO?.referralName || null,
        referralNumber: chsProfileDTO?.referralNumber || null,
        referralEmail: chsProfileDTO?.referralEmail || null,
        referralCompanyName: chsProfileDTO?.referralCompanyName || null,
      },
    });
  }, [profileData]);

  const renderSection = () => {
    const ActiveComponent = sections[currentStep].component;
    if (!ActiveComponent) return null;
    const sectionId = sections[currentStep].id;

    return (
      <ActiveComponent
        formData={formData} // Pass the specific section's data
        onUpdate={(updatedData) => handleFieldUpdate(sectionId, updatedData)} // Update the specific section's data
        employeesData={employeesData}
      />
    );
  };

  return (
    <>
      <Dialog fullScreen open={open} onClose={handleDialogClose}>
        <DialogTitle
          sx={{
            p: 2,
            borderBottom: 1,
            borderColor: "divider",
            display: "flex",
            justifyContent: "space-between",
          }}
        >
          <Typography variant="h6">Society Profile</Typography>
          <IconButton
            size="small"
            onClick={handleDialogClose}
            sx={{
              borderRadius: 1,
              color: "common.white",
              backgroundColor: "primary.main",
              "&:hover": {
                backgroundColor: "#66BB6A",
                transition: "background 0.5s ease, transform 0.5s ease",
              },
            }}
          >
            <Icon icon="tabler:x" fontSize="1rem" />
          </IconButton>
        </DialogTitle>
        <DialogContent
          sx={{
            display: "flex",
            flexDirection: isMobile ? "column" : "row",
            height: isMobile ? "auto" : "calc(100% - 100px)",
            overflow: "hidden",
          }}
        >
          {/* Sidebar */}
          <Box
            sx={{
              width: isMobile ? "100%" : 250, // Full width for mobile
              borderRight: isMobile ? "none" : 1,
              borderBottom: isMobile ? 1 : "none", // Bottom border in mobile
              borderColor: "divider",
              overflowY: "auto",
            }}
          >
            <List
              sx={{
                display: "flex",
                flexDirection: isMobile ? "row" : "column", // Horizontal list in mobile
                overflowX: isMobile ? "auto" : "hidden", // Horizontal scroll for mobile
                p: isMobile ? 1 : 0,
                gap: isMobile ? 1 : 0,
              }}
            >
              {sections?.map((section, index) => (
                <ListItem
                  button
                  key={section.id}
                  selected={currentStep === index}
                  onClick={() => handleStepChange(index)}
                  sx={{
                    backgroundColor:
                      currentStep === index ? "#1B5E20" : "inherit",
                    color: currentStep === index ? "#ffffff" : "inherit",
                    "&:hover": {
                      backgroundColor:
                        currentStep === index
                          ? "#0B3D0B"
                          : "rgba(0, 0, 0, 0.04)",
                      color: currentStep === index ? "#ffffff" : "inherit",
                    },
                  }}
                >
                  <ListItemIcon
                    sx={{
                      color: currentStep === index ? "#FFFFFF" : "#757575",
                      backgroundColor:
                        currentStep === index ? "#66BB6A" : "#F5F5F5",
                      borderRadius: "4px",
                      width: "30px",
                      height: "30px",
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center",
                      marginRight: 2,
                      transition:
                        "background-color 0.3s ease, color 0.3s ease, transform 0.3s ease",
                      transform:
                        currentStep === index ? "scale(1.1)" : "scale(1)",
                    }}
                  >
                    {section.icon}
                  </ListItemIcon>

                  <ListItemText
                    primary={
                      <Typography
                        variant="body1"
                        fontWeight={currentStep === index ? "bold" : "normal"}
                      >
                        {section.title}
                      </Typography>
                    }
                  />
                </ListItem>
              ))}
            </List>
          </Box>

          {/* Main Content Area */}
          <Box
            sx={{
              flex: 1,
              display: "flex",
              flexDirection: "column",
              p: 3,
              overflowY: "auto",
            }}
          >
            {/* Section Content */}
            <Box sx={{ flex: 1 }}>{renderSection()}</Box>
          </Box>
        </DialogContent>

        {/* Navigation Buttons */}
        <DialogActions
          sx={{
            display: "flex",
            justifyContent: currentStep === 0 ? "flex-end" : "space-between",
            p: 2,
            borderTop: (theme) => `1px solid ${theme.palette.divider}`,
          }}
        >
          {currentStep !== 0 && (
            <Button
              onClick={handlePrevious}
              variant="outlined"
              color="secondary"
            >
              Previous
            </Button>
          )}

          <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
            {currentStep === sections?.length - 1 ? (
              <></>
            ) : (
              <>
                <Button color="primary" onClick={handleSaveProgress}>
                  Save Progress
                </Button>
              </>
            )}
            {currentStep === sections?.length - 1 ? (
              <Button
                onClick={handleSubmit}
                variant="contained"
                color="primary"
              >
                Submit
              </Button>
            ) : (
              <Button onClick={handleNext} variant="contained" color="primary">
                Next
              </Button>
            )}
          </Box>
        </DialogActions>
      </Dialog>
      <Dialog
        open={dialogSuccess}
        onClose={handleCloseDialog}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
        PaperProps={{
          sx: {
            p: (theme) => `${theme.spacing(2.5)} !important`,
            backgroundColor: (theme) => theme.palette.primary.background,
          },
        }}
      >
        <Box
          sx={{
            width: "100%",
            borderRadius: 1,
            textAlign: "center",
            border: (theme) => `1px solid ${theme.palette.divider}`,
            borderColor: "primary.main",
          }}
        >
          <DialogContent>
            <DialogContentText
              id="alert-dialog-description"
              color="primary.main"
            >
              <div dangerouslySetInnerHTML={{ __html: dialogMessage }} />
            </DialogContentText>
          </DialogContent>
          <DialogActions>
            <Button
              variant="contained"
              onClick={handleCloseDialog}
              sx={{ margin: "auto", width: 100 }}
            >
              Ok
            </Button>
          </DialogActions>
        </Box>
      </Dialog>
    </>
  );
};

export default LongFormDialog;
