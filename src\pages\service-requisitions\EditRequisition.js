import {
  Autocomplete,
  Box,
  Button,
  Card,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  Divider,
  FormControl,
  FormControlLabel,
  FormHelperText,
  Grid,
  IconButton,
  InputLabel,
  Menu,
  MenuItem,
  Radio,
  RadioGroup,
  Select,
  Slider,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TextField,
  Tooltip,
  Typography,
} from "@mui/material";
import { useContext, useEffect, useState } from "react";
import { Controller, useForm } from "react-hook-form";
import CustomAvatar from "src/@core/components/mui/avatar";
import { AuthContext } from "src/context/AuthContext";
import { useAuth } from "src/hooks/useAuth";

import Icon from "src/@core/components/icon";

import { useTheme } from "@emotion/react";
import { DataGrid } from "@mui/x-data-grid";
import axios from "axios";
import "react-datepicker/dist/react-datepicker.css";
import SelectAutoComplete from "src/@core/components/custom-components/SelectAutoComplete";
import SelectProject from "src/@core/components/custom-components/SelectProject";
import authConfig from "src/configs/auth";
import { getAuthorizationHeaders, getUrl } from "src/helpers/utils";
import MUITableCell from "../SP/MUITableCell";
import CommentsDialog from "./CommentsDialog";
import SiteVisitTimings from "./SiteVisitTimings";
import AccordionBasic from "src/@core/components/custom-components/AccordionBasic";
import SpecificationsDialog from "./SpecificationsDialog";

const field = {
  fontSize: "12.75px",
};

const fieldLabelStyle = {
  fontSize: "12.75px",
};

const fieldValueStyle = {
  fontWeight: "bold",
  wordWrap: "break-word",
  whiteSpace: "pre-wrap",
  // color: "#66BB6A", // Sets color for the TableCell
  color: "#108A00",
  fontSize: "13px",
};

const tablecellLabelStyle = {
  width: { xs: "50%", sm: "50%", md: "40%" }, // Responsive width adjustment
  padding: "4px", // Ensure padding for better alignment
  textAlign: "right", // Align the text to the left
  verticalAlign: "middle", // Vertically align the text to the middle
  textIndent: {
    lg: "80px",
    md: "80px",
    sm: "100px",
    xs: "0",
  },
};

const tablecellValueStyle = {
  justifyContent: "flex-start",
  alignItems: "center",
  width: { xs: "50%", sm: "50%", md: "60%" }, // Responsive width for small and medium screens
  height: "10px",
};

const marks = Array.from({ length: 20 }, (_, i) => ({
  value: (i + 1) * 5,
  label: `${(i + 1) * 5}`,
}));


const EditRequisition = ({
  role,
  open,
  onClose,
  formattedData,
  setSpecifications,
  employeesData,
  formData,
  fetchRequisitions,
  referenceData,
  referralNameData,
}) => {
  const theme = useTheme();

  const auth = useAuth();
  const {
    user,
    getAllListValuesByListNameId,
    listValues,
    listNames,
    templateId,
    setTemplateId,
    templateDetails,
    setTemplateDetails,
  } = useContext(AuthContext);
  const {
    register,
    handleSubmit,
    control,
    clearErrors,
    setValue,
    formState: { errors },
  } = useForm();

  const [template, setTemplate] = useState("");
  const [referenceType, setReferenceType] = useState();
  const [referralName, setReferralName] = useState();

  const [budgetValue, setBudgetValue] = useState([]);
  const [budget, setBudget] = useState();
  const [rangeFrom,setRangeFrom] = useState("")
  const [serviceId, setServiceId] = useState();
  const [budgetUnits, setBudgetUnits] = useState([]);
  const [budgetCondition, setBudgetCondition] = useState([]);
  const [currentRow, setCurrentRow] = useState("");

  const [specificationsData, setSpecificationsData] = useState([]);

  const [conversation, setConversation] = useState({});
  const [openMoreInfoDialog, setOpenMoreInfoDialog] = useState(false);

  const handleDialogClose = () => {
    setOpenMoreInfoDialog(false);
  };

  const handleChange = (event, newValue) => {
    setBudgetValue(newValue);
  };

  const [dialogMessage, setDialogMessage] = useState("");
  const [openDialogContent, setOpenDialogContent] = useState(false);

  const [convList, setConvList] = useState([]);

  const [conversations, setConversations] = useState([]);

  const [allServicesList, setAllServicesList] = useState([]);
  const [subServicesList, setSubServicesList] = useState(
    formData?.requisitionData?.subServices
  );

  const [templates, setTemplates] = useState([]);

  const rowsPerPageOptions = [10, 15, 20, 25, 50, 100];
  const [pageSize, setPageSize] = useState(rowsPerPageOptions[0]);
  const [page, setPage] = useState(1);
  const [rowCount, setRowCount] = useState(0);
  const [siteVisitOpen, setSiteVisitOpen] = useState(false);
  const handleSiteVisitDialogOpen = () => setSiteVisitOpen(true);
  const handleSiteVisitDialogClose = () => setSiteVisitOpen(false);
  const [checkedSections, setCheckedSections] = useState({});
  const [siteVisits, setSiteVisits] = useState([]);

  const [service, setService] = useState("");

  function getEmployeeNamesByWorkFor(workForValue) {
    // Step 1: Filter `workAssignments` by `workFor`
    const assignments = formData?.workAssignments?.filter(
      (assignment) => assignment.workFor === workForValue
    );

    // Step 2: Map each `assignedTo` to the corresponding employee name
    return assignments
      ?.map((assignment) => {
        const employee = employeesData?.find(
          (emp) => emp.id === assignment.assignedTo
        );
        return employee ? employee.id : null; // Return name if found, otherwise null
      })
      .filter((name) => name !== null); // Filter out any null values
  }
  const [requisitionDialog, setRequisitionDialog] = useState(false);

  const handleClick = () => {
    setRequisitionDialog(true);
  };

  const handleClickClose = () => {
    setRequisitionDialog(false);
  };

  useEffect(() => {
    setServiceId(formData?.serviceTypeId);
    setReferenceType(formData?.referral?.referenceType);
    setReferralName(formData?.referral?.teamReference);
    setValue(
      "houzerSocietyTeamMember",
      formData?.referral?.houzerSocietyTeamMember
    );

    setValue("status", formData?.status);
    setValue("dataSentDate", formData?.referral?.dataSentDate);
    setValue("curatedBy", formData?.referral?.curatedBy);
    setValue("curatedOn", formData?.referral?.curatedOn);
    setValue(
      "deciMatrixPresentationDate",
      formData?.referral?.deciMatrixPresentationDate
    );
    setValue("societyRemarks", formData?.remarks);
    setConvList(formData?.conversations);
    setValue(
      "requirementDeadLine",
      formData?.requirementDeadLine?.substring(0, 10)
    );

    setValue("priority", formData?.priority);
    setValue("workWithSP", getEmployeeNamesByWorkFor("SP")?.[0]);
    setValue("workWithCHS", getEmployeeNamesByWorkFor("CHS")?.[0]);
    setValue("workForWorkOrder", getEmployeeNamesByWorkFor("WORK_ORDERS")?.[0]);
    setValue("assignedTo", getEmployeeNamesByWorkFor("DRI")?.[0]);

    setSiteVisits(formData?.siteVisitTimings);

    setRangeFrom( formData?.budget?.rangeFrom);
    setValue("rangeTo", formData?.budget?.rangeTo);
    setValue("condition",formData?.budget?.condition)
    setBudget(formData?.budget?.units);

    const serv = formData?.serviceTypeId
      ? listValues.find((item) => item.id === formData?.serviceTypeId)?.name
      : null;

    setService(serv);
    
    setSpecificationsData(formData?.specifications?.sections);
    const sections = formData?.specifications?.sections || [];
    const checkedSections = sections?.reduce((acc, section) => {
      if (section.id) {
        acc[section.id] = section.isChecked === true;
      }
      return acc;
    }, {});
    
    // Set the state or use the object as needed
    setCheckedSections(checkedSections);
     
    if (formData?.serviceTypeId) {
      axios({
        method: "get",
        url:
          getUrl(authConfig.templateEndpoint) +
          "/?serviceTypeId=" +
          formData?.serviceTypeId +
          "&featureType=SPECIFICATIONS",
        headers: getAuthorizationHeaders({
          accept: authConfig.TEMPLATE_GET_NAMES_RES_V1,
        }),
      })
        .then((res) => {
          setTemplates(
            res.data?.templateNames?.map((item) => ({
              value: item.id,
              key: item.name,
            }))
          );
        })
        .catch((err) => console.log("Categories error", err));
    }
  }, [formData]);

  // useEffect(() => {
  //   setSpecificationsData(templateDetails?.sections);
  // }, [templateDetails]);

  const handleSpecifications = () => {
    axios({
      method: "get",
      url:
        getUrl(authConfig.templateEndpoint) +
        "/?serviceTypeId=" +
        formData?.serviceTypeId +
        "&featureType=SPECIFICATIONS",
      headers: getAuthorizationHeaders({
        accept: authConfig.TEMPLATE_GET_NAMES_RES_V1,
      }),
    })
      .then((res) => {
        setTemplateId({
          ...templateId,
          id: res.data?.templateNames[0]?.id,
        });
      })
      .catch((err) => console.log("Categories error", err));
  };

  useEffect(() => {
    setTemplate(templates?.[0]?.value);
  }, [templates]);

  useEffect(() => {
    if (template) {
      setTemplateId({
        ...templateId,
        id: template,
      });
    }
  }, [template]);

  const [menu, setMenu] = useState(null);

  const handleCloseMenuItems = () => {
    setMenu(null);
  };

  const cols = [
    { field: "contactPersonName", headerName: "Name", minWidth: 145, flex: 2 },
    {
      field: "contactNumber",
      headerName: "Contact Number",
      minWidth: 145,
      flex: 2,
    },
    {
      field: "siteVisitDate",
      headerName: "Site Visit Date",
      minWidth: 145,
      flex: 2,
    },
    { field: "startTime", headerName: "Start Time", minWidth: 140, flex: 2 },
    { field: "endTime", headerName: "End Time", minWidth: 140, flex: 2 },
    {
      field: "actions",
      headerName: "Actions",
      sortable: false,
      minWidth: 120,
      flex: 1,
      disableClickEventBubbling: true,
      renderCell: (params) => {
        const onClick = (event) => {
          event.stopPropagation();
          setMenu(event.currentTarget);
          const row = params.row;
          setCurrentRow(row);
        };

        const onClickViewProfile = () => {
          setSiteVisitOpen(true);
          handleCloseMenuItems();
        };
        const onClickDeleteProfile = () => {
          const sv = siteVisits.filter(
            (siteVisit) => siteVisit.id !== currentRow?.id
          );
          setSiteVisits(sv);
          handleCloseMenuItems();
        };

        return (
          <div
            style={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
            }}
          >
            <Tooltip title="Actions">
              <CustomAvatar
                skin="light"
                variant="rounded"
                sx={{
                  mr: { xs: 2, lg: 4 },
                  width: 34,
                  height: 34,
                  cursor: "pointer",
                }}
                onClick={onClick}
              >
                <Icon icon="bi:three-dots-vertical" />
              </CustomAvatar>
            </Tooltip>
            <Menu
              id="actions-menu"
              anchorEl={menu}
              keepMounted
              open={Boolean(menu)}
              onClose={handleCloseMenuItems}
            >
              <MenuItem onClick={onClickViewProfile}>Edit</MenuItem>
              <MenuItem onClick={onClickDeleteProfile}>Delete</MenuItem>
            </Menu>
          </div>
        );
      },
    },
  ];

  const handlePageChange = (direction) => {
    if (direction === page) {
      setPage(page + 1);
    } else {
      setPage(page - 1);
    }
  };

  const handlePageSizeChange = (params) => {
    if (params) {
      setPageSize(params);
      setPage(1);
    }
  };

  const handleCancel = () => {
    setSpecifications([]);
    setSpecificationsData([]);
    setTemplateDetails(null);
    setTemplate(null);
    setConversations([]);
    setCheckedSections({})
    onClose();
  };
  const handleServicesSuccess = (data) => {
    setAllServicesList(data?.listValues);
  };

  const [statusData, setStatusData] = useState(null);

  const handleStatusSuccess = (data) => {
    setStatusData(data?.listValues);
  };

  const [priorityData, setPriorityData] = useState(null);

  const handlePrioritySuccess = (data) => {
    setPriorityData(data?.listValues);
  };

  const handleError = (error) => {
    console.error("society requisition: All Services:", error);
  };

  useEffect(() => {
    if (!!authConfig) {
      getAllListValuesByListNameId(
        authConfig.allServicesListNameId,
        handleServicesSuccess,
        handleError
      );

      getAllListValuesByListNameId(
        authConfig.leadPriorityListNamesId,
        handlePrioritySuccess,
        handleError
      );
      getAllListValuesByListNameId(
        authConfig.statusListNamesId,
        handleStatusSuccess,
        handleError
      );
      getAllListValuesByListNameId(
        authConfig.budgetUnits,
        (data) =>
          setBudgetUnits(
            data?.listValues.map((item) => ({
              value: item.id,
              key: item.listValue,
            }))
          ),
        handleError
      );
      getAllListValuesByListNameId(
        authConfig.budgetCondition,
        (data) =>
          setBudgetCondition(
            data?.listValues.map((item) => ({
              value: item.id,
              key: item.listValue,
            }))
          ),
        handleError
      );
    }
  }, [authConfig]);

  const [isOtherSelected, setIsOtherSelected] = useState(false);

  const handleSuccess = () => {
    const message = `
    <div> 
      <h3>Requisition details updated Successfully.</h3>
    </div>
  `;
    setDialogMessage(message);
    setOpenDialogContent(true);
  };

  const handleFailure = () => {
    const message = `
    <div> 
      <h3> Failed to update requisition details. Please try again later.</h3>
    </div>
  `;
    setDialogMessage(message);
    setOpenDialogContent(true);
  };

  async function submit(data) {

    const currentDate = new Date();
    const currentTime = currentDate.toTimeString()?.split(" ")[0];

    const fields = {
      requirementDeadLine: data?.requirementDeadLine
        ? `${data?.requirementDeadLine}T${currentTime}`
        : null,
      priority: data?.priority,
      budget: {
        rangeFrom: rangeFrom,
        rangeTo:data?.rangeTo,
        units:budget,
        condition:data?.condition
      },
      remarks: data?.societyRemarks,
      specifications: {
        anyOtherServiceProvided: data?.anyOtherService,
        sections: formattedData,
      },
      referral: {
        referenceType: referenceType,
        houzerSocietyTeamMember: data?.houzerSocietyTeamMember,
        teamReference: referralName,
        dataSentDate: data?.dataSentDate === "" ? null : data?.dataSentDate,
        deciMatrixPresentationDate: data?.deciMatrixPresentationDate,
        curatedBy: data?.curatedBy,
        curatedOn: data?.curatedOn,
      },
      status: data?.status,
      siteVisitTimings: siteVisits,
      workAssignments: [
        {
          workFor: "SP",
          assignedTo: data?.workWithSP,
          assignedDate: data?.dataSentDate
            ? `${data.dataSentDate}T${currentTime}`
            : null,
        },
        {
          workFor: "CHS",
          assignedTo: data?.workWithCHS,
          assignedDate: data?.dataSentDate
            ? `${data.dataSentDate}T${currentTime}`
            : null,
        },
        {
          workFor: "WORK_ORDERS",
          assignedTo: data?.workForWorkOrder,
          assignedDate: data?.dataSentDate
            ? `${data.dataSentDate}T${currentTime}`
            : null,
        },
        {
          workFor: "DRI",
          assignedTo: data?.assignedTo,
          assignedDate: data?.dataSentDate
            ? `${data.dataSentDate}T${currentTime}`
            : null,
        },
      ].filter((item) => item.assignedTo != null && item.assignedTo !== ""),
      conversations: convList,
    };

    try {
      const response = await auth.patchRequisition(
        formData?.id,
        fields,
        handleFailure,
        handleSuccess
      );
    } catch (error) {
      console.error("Employee Creation failed:", error);
      handleFailure();
    }
    handleCancel();
  }

  const handleFieldChange = (sectionId, fieldId, value) => {
    setSpecificationsData((prevData) =>
      prevData.map((section) => {
        if (section.id === sectionId) {
          return {
            ...section,
            fields: section.fields.map((field) => {
              if (field.id === fieldId) {
                if (
                  field.componentId === authConfig.textFieldComponentId ||
                  field.componentId === authConfig.textAreaComponentId ||
                  field.componentId === authConfig.numberTextFieldComponentId
                ) {
                  return {
                    ...field,
                    providedTextValue: value,
                  };
                } else {
                  const selectedObjects = Array.isArray(value)
                    ? field.values.filter((item) => value.includes(item.value))
                    : field.values.find((item) => item.value === value);
                  return {
                    ...field,
                    selectedDropdownValues: Array.isArray(selectedObjects)
                      ? selectedObjects
                      : [selectedObjects], // Ensure it's always an array
                  };
                }
              }
              return field;
            }),
          };
        }
        return section;
      })
    );
    setSpecifications((prevData) =>
      prevData.map((section) => {
        if (section.id === sectionId) {
          return {
            ...section,
            fields: section.fields.map((field) => {
              if (field.id === fieldId) {
                if (
                  field.componentId === authConfig.textFieldComponentId ||
                  field.componentId === authConfig.textAreaComponentId ||
                  field.componentId === authConfig.numberTextFieldComponentId
                ) {
                  return {
                    ...field,
                    providedTextValue: value,
                  };
                } else {
                  const selectedObjects = Array.isArray(value)
                    ? field.values.filter((item) => value.includes(item.value))
                    : field.values.find((item) => item.value === value);
                  return {
                    ...field,
                    selectedDropdownValues: Array.isArray(selectedObjects)
                      ? selectedObjects
                      : [selectedObjects], // Ensure it's always an array
                  };
                }
              }
              return field;
            }),
          };
        }
        return section;
      })
    );
  };

  const handleButtonClick = () => {
    setOpenDialogContent(false);
    onClose();
  };

  return (
    <>
      <Dialog fullScreen open={open} onClose={handleCancel}>
        <DialogTitle
          sx={{
            position: "relative",
            borderBottom: (theme) => `1px solid ${theme.palette.divider}`,
            p: (theme) => `${theme.spacing(1.75, 4)} !important`,
            display: "flex",
            alignItems: "center",
            justifyContent: { xs: "start" },
            fontSize: { xs: 19, md: 20 },
            height: "50px", // height
          }}
          textAlign={"center"}
        >
          <Box
            sx={{
              ml: {
                xs: 4,
                sm: 4,
                md: 4,
                lg: 4,
                xl: 4,
              },
            }}
          >
            Edit Service Requisition&nbsp;
          </Box>
          <Box
            sx={{
              position: "absolute",
              top: "9px",
              right: "10px",
              mr: {
                xs: 5.5,
                sm: 5.5,
                md: 5.5,
                lg: 5.5,
                xl: 10.5,
              },
            }}
          >
            <IconButton
              size="small"
              onClick={handleCancel}
              sx={{
                // p: "0.438rem",
                borderRadius: 1,
                color: "common.white",
                backgroundColor: "primary.main",
                "&:hover": {
                  backgroundColor: "#66BB6A",
                  transition: "background 0.5s ease, transform 0.5s ease",
                },
              }}
            >
              <Icon icon="tabler:x" fontSize="1rem" />
            </IconButton>
          </Box>
        </DialogTitle>
        <DialogContent
          sx={{
            position: "relative",
            pt: (theme) => `${theme.spacing(8)} !important`,
            pb: (theme) => `${theme.spacing(5)} !important`,
            px: (theme) => [`${theme.spacing(8)} !important`],
          }}
        >
          {user.organisationCategory !== "SOCIETY" && (
            <Card>
              <Grid
                sx={{
                  backgroundColor: "#f2f7f2",
                  mt: 4,
                  paddingTop: 0,
                  height: "36px",
                  display: "flex",
                  alignItems: "center",
                }}
              >
                <Typography
                  variant="body1"
                  fontWeight={"bold"}
                  sx={{ mt: 0, ml: 2 }}
                >
                  Society Details
                </Typography>
                <Divider />
              </Grid>
              <Divider />
              <TableContainer sx={{ padding: "4px 6px" }} className="tableBody">
                <Table>
                  <TableBody
                    sx={{
                      "& .MuiTableCell-root": {
                        p: `${theme.spacing(1.35, 1.125)} !important`,
                      },
                    }}
                  >
                    <TableRow>
                      <MUITableCell sx={tablecellLabelStyle}>
                        <Typography sx={fieldLabelStyle}>
                          Society name:
                        </Typography>
                      </MUITableCell>
                      <MUITableCell sx={tablecellValueStyle}>
                        <Typography
                          className="data-field"
                          style={fieldValueStyle}
                        >
                          {formData?.initiatingEntity?.orgName}
                        </Typography>
                      </MUITableCell>
                    </TableRow>

                    <TableRow>
                      <MUITableCell sx={tablecellLabelStyle}>
                        <Typography style={fieldLabelStyle}>
                          Society member name:
                        </Typography>
                      </MUITableCell>
                      <MUITableCell sx={tablecellValueStyle}>
                        <Typography
                          className="data-field"
                          style={fieldValueStyle}
                        >
                          {formData?.initiator?.firstName}{" "}
                          {formData?.initiator?.lastName}
                        </Typography>
                      </MUITableCell>
                    </TableRow>

                    <TableRow>
                      <MUITableCell sx={tablecellLabelStyle}>
                        <Typography style={fieldLabelStyle}>Email:</Typography>
                      </MUITableCell>
                      <MUITableCell sx={tablecellValueStyle}>
                        <Typography
                          className="data-field"
                          style={fieldValueStyle}
                        >
                          {formData?.initiator?.email}
                        </Typography>
                      </MUITableCell>
                    </TableRow>

                    <TableRow>
                      <MUITableCell sx={tablecellLabelStyle}>
                        <Typography sx={fieldLabelStyle}>Location:</Typography>
                      </MUITableCell>
                      <MUITableCell sx={tablecellValueStyle}>
                        <Typography
                          className="data-field"
                          style={fieldValueStyle}
                        >
                          {listValues?.find(
                            (item) =>
                              item.id === formData?.initiatingEntity?.location
                          )?.name || ""}
                        </Typography>
                      </MUITableCell>
                    </TableRow>

                    <TableRow>
                      <MUITableCell sx={tablecellLabelStyle}>
                        <Typography sx={fieldLabelStyle}>Zone:</Typography>
                      </MUITableCell>
                      <MUITableCell sx={tablecellValueStyle}>
                        <Typography
                          className="data-field"
                          style={fieldValueStyle}
                        >
                          {formData?.initiatingEntity?.zone}
                        </Typography>
                      </MUITableCell>
                    </TableRow>

                    <TableRow>
                      <MUITableCell sx={tablecellLabelStyle}>
                        <Typography sx={fieldLabelStyle}>Address:</Typography>
                      </MUITableCell>
                      <MUITableCell sx={tablecellValueStyle}>
                        <Typography
                          className="data-field"
                          style={fieldValueStyle}
                        >
                          {formData?.initiatingEntity?.address}
                        </Typography>
                      </MUITableCell>
                    </TableRow>
                  </TableBody>
                </Table>
              </TableContainer>
            </Card>
          )}

          <Card>
            <Grid
              sx={{
                backgroundColor: "#f2f7f2",
                mt: 4,
                paddingTop: 0,
                height: "36px",
                display: "flex",
                alignItems: "center",
              }}
            >
              <Typography
                variant="body1"
                fontWeight={"bold"}
                sx={{ mt: 0, ml: 2 }}
              >
                Requisition Details
              </Typography>
              <Divider />
            </Grid>
            <Divider />

            <Grid container spacing={5} style={{ padding: "16px" }}>
              <Grid
                container
                item
                xs={12}
                sm={5}
                // spacing={2}
                sx={{ mb: 3, mt: 2 }}
              >
                <Grid item>
                  <Typography className="data-field">
                    Service: &nbsp;
                  </Typography>
                </Grid>
                <Grid item>
                  <Typography style={{ fontWeight: "bold" }}>
                    {service}
                  </Typography>
                </Grid>
              </Grid>

              {template && (
                  <Grid item xs={12} md={3} sm={4}>
                    <Button
                      display="flex"
                      justifyContent="center"
                      variant="contained"
                      color="primary"
                      onClick={handleClick}
                    >
                      Add/Edit Specifications
                    </Button>
                  </Grid>
                )}
            </Grid>

            <Grid container spacing={5} style={{ padding: "16px" }}>
              <Grid item xs={12} md={3} sm={3}>
                <FormControl fullWidth>
                  <Controller
                    name="requirementDeadLine"
                    control={control}
                    defaultValue={formData?.requirementDeadLine}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        size="small"
                        label="Requirement DeadLine"
                        type="date"
                        InputLabelProps={{ shrink: true }}
                        aria-describedby="requirementDeadLine"
                        value={field.value}
                        onChange={(e) => {
                          field.onChange(e.target.value);
                        }}
                      />
                    )}
                  />
                </FormControl>
              </Grid>

              <Grid item xs={12} sm={3} md={3}>
                <FormControl fullWidth>
                  <InputLabel id="priorityId"> Priority</InputLabel>
                  <Controller
                    name="priority"
                    control={control}
                    defaultValue={formData?.priority}
                    render={({ field }) => (
                      <Select
                        {...field}
                        labelId="priorityId-label"
                        label="Priority"
                        id="priorityId"
                        size="small"
                      >
                        {priorityData?.map((status) => (
                          <MenuItem key={status.id} value={status.id}>
                            {status.listValue}
                          </MenuItem>
                        ))}
                      </Select>
                    )}
                  />
                </FormControl>
              </Grid>

              <Grid item xs={12} sm={6}>
                <Box display="flex" alignItems="center">
                  <Typography id="budget" gutterBottom>
                    Budget
                  </Typography>
                  <Grid item xs={4} sm={5} lg={2} sx={{ marginLeft: "6px" }}>
                    <FormControl fullWidth>
                      <Controller
                        name="rangeFrom"
                        control={control}
                        rules={{ required: rangeFrom ? false : true }}
                        render={({ field }) => (
                          <TextField
                            {...field}
                            label="from"
                            type="number"
                            InputLabelProps={{ shrink: true }}
                            size="small"
                            value={rangeFrom}
                            onChange={(e) => {
                              field.onChange(e);
                              setRangeFrom(e.target.value)
                            }}
                            error={Boolean(errors.rangeFrom)}
                            helperText={errors.rangeFrom?.message}
                            aria-describedby="validation-basic-rangeFrom"
                          />
                        )}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item xs={4} sm={5} lg={2} sx={{ marginLeft: "6px" }}>
                    <FormControl fullWidth>
                      <Controller
                        name="rangeTo"
                        control={control}
                        rules={{ required: false }}
                        render={({ field }) => (
                          <TextField
                            {...field}
                            label="to"
                            type="number"
                            InputLabelProps={{ shrink: true }}
                            size="small"
                            error={Boolean(errors.rangeTo)}
                            helperText={errors.rangeTo?.message}
                            aria-describedby="validation-basic-rangeTo"
                          />
                        )}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item xs={4} sm={5} lg={3} sx={{ marginLeft: "6px" }}>
                  <FormControl fullWidth error={Boolean(errors.budgetUnits)}>
                      <Controller
                        name="budgetUnits"
                        control={control}
                        rules={{ required: rangeFrom > 0 ? (budget ? false: "Budget Units are required") : false }}  
                        render={({ field }) => (
                          <SelectAutoComplete
                            id="budgetUnits"
                            size="small"
                            label="Units"
                            nameArray={budgetUnits || []}
                            value={budget}
                            onChange={(e) => {
                              field.onChange(e);
                              setBudget(e.target.value)
                            }}
                            error={Boolean(errors.budgetUnits)}
                            helperText={
                              errors.budgetUnits
                                ? errors.budgetUnits.message
                                : ""
                            }
                          />
                        )}
                      />
                      {errors.budgetUnits && (
                        <FormHelperText sx={{ color: "error.main" }}>
                          {errors.budgetUnits.message}
                        </FormHelperText>
                      )}
                    </FormControl>
                  </Grid>
                  <Grid item xs={4} sm={5} lg={3} sx={{ marginLeft: "6px" }}>
                    <FormControl fullWidth>
                      <Controller
                        name="condition"
                        control={control}
                        rules={{ required: false }}
                        render={({ field }) => (
                          <SelectAutoComplete
                            id="budget-condition"
                            size="small"
                            label="Condition"
                            nameArray={budgetCondition || []}
                            value={field.value || []}
                            onChange={(e) => {
                              field.onChange(e);
                            }}
                            error={Boolean(errors.condition)}
                          />
                        )}
                      />
                    </FormControl>
                  </Grid>
                </Box>
              </Grid>

              <Grid item xs={12}>
                <TableContainer>
                  <Table>
                    <TableHead>
                      {specificationsData?.length > 0 && (
                        <TableRow>
                          <TableCell>Specifications</TableCell>
                        </TableRow>
                      )}
                    </TableHead>
                    <TableBody>
                      {specificationsData
                       ?.filter((section) => section.isChecked)
                      ?.map((section) => (
                        <AccordionBasic
                          key={section.id}
                          id={section.id}
                          ariaControls={"basic-schema-1"}
                          heading={
                            listValues.find((item) => item.id === section.id)
                              ?.name
                          }
                          body={
                            <Table>
                              <TableBody>
                                {section.fields?.map((field) => (
                                  <TableRow key={field.id}>
                                    <TableCell>
                                      <Typography>
                                        {
                                          listNames.find(
                                            (item) => item.id === field.labelId
                                          )?.name
                                        }
                                      </Typography>
                                    </TableCell>
                                    <TableCell>
                                      {(() => {
                                        const fieldName = listValues.find(
                                          (item) => item.id === field.labelId
                                        )?.name;
                                        switch (field.componentId) {
                                          case authConfig.radioButtonComponentId:
                                            return (
                                              <FormControl component="fieldset">
                                                <Controller
                                                  name={`selectedOptions.${field.labelId}`}
                                                  control={control}
                                                  render={({
                                                    field: controllerField,
                                                  }) => (
                                                    <RadioGroup
                                                      {...controllerField}
                                                      aria-label="option"
                                                      size="small"
                                                      value={
                                                        controllerField.value ||
                                                        field.selectedDropdownValues?.map(
                                                          (item) => item.value
                                                        ) ||
                                                        []
                                                      }
                                                      onChange={(event) => {
                                                        handleFieldChange(
                                                          section.id,
                                                          field.id,
                                                          event.target.value
                                                        );
                                                        controllerField.onChange(
                                                          event
                                                        );
                                                      }}
                                                      row
                                                    >
                                                      {field.values?.map(
                                                        (value) => (
                                                          <FormControlLabel
                                                            key={value.key}
                                                            value={value.value}
                                                            control={<Radio />}
                                                            label={value.key}
                                                          />
                                                        )
                                                      )}
                                                    </RadioGroup>
                                                  )}
                                                />
                                              </FormControl>
                                            );
                                          case authConfig.multiSelectComponentId:
                                            return (
                                              <FormControl
                                                style={{ width: "500px" }}
                                              >
                                                <InputLabel
                                                  id={field.labelId}
                                                  style={{ zIndex: 0 }}
                                                >
                                                  {fieldName}
                                                </InputLabel>
                                                <Controller
                                                  name={`selectedOptions.${field.labelId}`}
                                                  control={control}
                                                  render={({
                                                    field: controllerField,
                                                  }) => (
                                                    <Select
                                                      multiple
                                                      labelId={field.labelId}
                                                      size="small"
                                                      label={fieldName}
                                                      value={
                                                        Array.isArray(controllerField.value)
                                                          ? controllerField.value
                                                          : field.selectedDropdownValues?.map((item) => item.value) || []
                                                      }
                                                      onChange={(event) => {
                                                        controllerField.onChange(
                                                          event.target.value
                                                        );
                                                        handleFieldChange(
                                                          section.id,
                                                          field.id,
                                                          event.target.value
                                                        );
                                                      }}
                                                      renderValue={(
                                                        selected
                                                      ) => (
                                                        <span>
                                                          {(Array.isArray(selected) ? selected : [])
                                                            .map(
                                                              (selectedValue) =>
                                                                field.values.find((value) => value.value === selectedValue)?.key
                                                            )
                                                            .join(", ")}
                                                        </span>
                                                      )}
                                                    >
                                                      {field.values?.map(
                                                        (value) => (
                                                          <MenuItem
                                                            key={value.value}
                                                            value={value.value}
                                                          >
                                                            {value.key}
                                                          </MenuItem>
                                                        )
                                                      )}
                                                    </Select>
                                                  )}
                                                />
                                              </FormControl>
                                            );
                                          case authConfig.singleSelectComponentId:
                                            return (
                                              <FormControl
                                                style={{ width: "500px" }}
                                              >
                                                <InputLabel
                                                  id={field.labelId}
                                                  style={{ zIndex: 0 }}
                                                >
                                                  {fieldName}
                                                </InputLabel>
                                                <Controller
                                                  name={`selectedOptions.${field.labelId}`}
                                                  control={control}
                                                  render={({
                                                    field: controllerField,
                                                  }) => (
                                                    <Select
                                                      labelId={field.labelId}
                                                      size="small"
                                                      label={fieldName}
                                                      value={
                                                        controllerField.value ||
                                                        field.selectedDropdownValues?.map(
                                                          (item) => item.value
                                                        ) ||
                                                        []
                                                      }
                                                      onChange={(event) => {
                                                        controllerField.onChange(
                                                          event.target.value
                                                        );
                                                        handleFieldChange(
                                                          section.id,
                                                          field.id,
                                                          event.target.value
                                                        );
                                                      }}
                                                    >
                                                      {field.values?.map(
                                                        (value) => (
                                                          <MenuItem
                                                            key={value.value}
                                                            value={value.value}
                                                          >
                                                            {value.key}
                                                          </MenuItem>
                                                        )
                                                      )}
                                                    </Select>
                                                  )}
                                                />
                                              </FormControl>
                                            );
                                          case authConfig.switchComponentId:
                                            return (
                                              <Controller
                                                name={`selectedOptions.${field.labelId}`}
                                                control={control}
                                                render={({
                                                  field: controllerField,
                                                }) => (
                                                  <FormControlLabel
                                                    control={
                                                      <Switch
                                                        {...controllerField}
                                                        checked={
                                                          controllerField.value
                                                        }
                                                        size="small"
                                                        onChange={(event) => {
                                                          controllerField.onChange(
                                                            event
                                                          );
                                                          handleFieldChange(
                                                            section.id,
                                                            field.id,
                                                            event.target.checked
                                                          );
                                                        }}
                                                        name={fieldName}
                                                        inputProps={{
                                                          "aria-label":
                                                            field.labelId,
                                                        }}
                                                      />
                                                    }
                                                    label="Yes"
                                                  />
                                                )}
                                              />
                                            );
                                          case authConfig.numberTextFieldComponentId:
                                            return (
                                              <FormControl
                                                style={{ width: "500px" }}
                                              >
                                                <Controller
                                                  name={`selectedOptions.${field.labelId}`}
                                                  control={control}
                                                  render={({
                                                    field: controllerField,
                                                  }) => (
                                                    <TextField
                                                      {...controllerField}
                                                      size="small"
                                                      type="number"
                                                      variant="outlined"
                                                      value={
                                                        field.providedTextValue ||
                                                        ""
                                                      }
                                                      onChange={(event) => {
                                                        handleFieldChange(
                                                          section.id,
                                                          field.id,
                                                          event.target.value
                                                        );
                                                        controllerField.onChange(
                                                          event
                                                        );
                                                      }}
                                                      fullWidth
                                                      inputProps={{
                                                        "aria-label":
                                                          field.labelId,
                                                      }}
                                                    />
                                                  )}
                                                />
                                              </FormControl>
                                            );
                                          case authConfig.textAreaComponentId:
                                            return (
                                              <FormControl
                                                style={{ width: "500px" }}
                                              >
                                                <Controller
                                                  name={`selectedOptions.${field.labelId}`}
                                                  control={control}
                                                  render={({
                                                    field: controllerField,
                                                  }) => (
                                                    <TextField
                                                      {...controllerField}
                                                      size="small"
                                                      rows={3}
                                                      multiline
                                                      variant="outlined"
                                                      value={
                                                        field.providedTextValue ||
                                                        ""
                                                      }
                                                      onChange={(event) => {
                                                        handleFieldChange(
                                                          section.id,
                                                          field.id,
                                                          event.target.value
                                                        );
                                                        controllerField.onChange(
                                                          event
                                                        );
                                                      }}
                                                      fullWidth
                                                      inputProps={{
                                                        "aria-label":
                                                          field.labelId,
                                                      }}
                                                    />
                                                  )}
                                                />
                                              </FormControl>
                                            );
                                          case authConfig.textFieldComponentId:
                                            return (
                                              <FormControl
                                                style={{ width: "500px" }}
                                              >
                                                <Controller
                                                  name={`selectedOptions.${field.labelId}`}
                                                  control={control}
                                                  render={({
                                                    field: controllerField,
                                                  }) => (
                                                    <TextField
                                                      {...controllerField}
                                                      size="small"
                                                      variant="outlined"
                                                      value={
                                                        field.providedTextValue ||
                                                        ""
                                                      }
                                                      onChange={(event) => {
                                                        handleFieldChange(
                                                          section.id,
                                                          field.id,
                                                          event.target.value
                                                        );
                                                        controllerField.onChange(
                                                          event
                                                        );
                                                      }}
                                                      fullWidth
                                                      inputProps={{
                                                        "aria-label":
                                                          field.labelId,
                                                      }}
                                                    />
                                                  )}
                                                />
                                              </FormControl>
                                            );
                                          default:
                                            return null;
                                        }
                                      })()}
                                    </TableCell>
                                  </TableRow>
                                ))}
                              </TableBody>
                            </Table>
                          }
                          expanded={true}
                        />
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              </Grid>

              <Grid item xs={12}>
                <FormControl fullWidth>
                  <Controller
                    name="societyRemarks"
                    control={control}
                    rules={{ required: false }}
                    defaultValue={formData?.remarks}
                    render={({ field }) => (
                      <TextField
                        rows={3}
                        multiline
                        {...field}
                        label="Society Remarks"
                        InputLabelProps={{ shrink: true }}
                        inputProps={{ maxLength: 1000 }}
                        error={Boolean(errors.societyRemarks)}
                        aria-describedby="societyRemarks"
                      />
                    )}
                  />
                  {errors.societyRemarks && (
                    <FormHelperText
                      sx={{ color: "error.main" }}
                      id="societyRemarks"
                    >
                      This field is required
                    </FormHelperText>
                  )}
                </FormControl>
              </Grid>
            </Grid>
          </Card>

          {user.organisationCategory === "EMPLOYEE" && (
            <Card>
              <Grid
                sx={{
                  backgroundColor: "#f2f7f2",
                  mt: 4,
                  paddingTop: 0,
                  height: "36px",
                  display: "flex",
                  alignItems: "center",
                }}
              >
                <Typography
                  variant="body1"
                  fontWeight={"bold"}
                  sx={{ mt: 0, ml: 2 }}
                >
                  Status and Assignment Details
                </Typography>
                <Divider />
              </Grid>
              <Divider />

              <Grid container spacing={5} style={{ padding: "16px" }}>
                <Grid item xs={12} md={3} sm={4}>
                  <FormControl fullWidth error={Boolean(errors.assignedTo)}>
                    <Controller
                      name="assignedTo"
                      control={control}
                      rules={{ required: false }}
                      render={({ field }) => (
                        <Autocomplete
                          {...field}
                          onChange={(event, newValue) => {
                            field.onChange(newValue ? newValue.id : null);
                          }}
                          value={
                            employeesData
                              ?.map((data) => ({
                                id: data.id,
                                label: data.name,
                              }))
                              .find((emp) => emp.id === field.value) || null
                          }
                          options={
                            employeesData?.map((data) => ({
                              id: data.id,
                              label: data.name,
                            })) || []
                          }
                          getOptionLabel={(option) => option.label || ""}
                          renderInput={(params) => (
                            <TextField
                              {...params}
                              label="Assigned To"
                              error={Boolean(errors.assignedTo)}
                              helperText={
                                errors.assignedTo
                                  ? errors.assignedTo.message
                                  : ""
                              }
                            />
                          )}
                          size="small"
                        />
                      )}
                    />
                  </FormControl>
                </Grid>

                <Grid item xs={12} md={3} sm={4}>
                  <FormControl fullWidth error={Boolean(errors.workWithSP)}>
                    <Controller
                      name="workWithSP"
                      control={control}
                      rules={{ required: false }}
                      render={({ field }) => (
                        <Autocomplete
                          {...field}
                          onChange={(event, newValue) => {
                            field.onChange(newValue ? newValue.id : null);
                          }}
                          value={
                            employeesData
                              ?.map((data) => ({
                                id: data.id,
                                label: data.name,
                              }))
                              .find((emp) => emp.id === field.value) || null
                          }
                          options={
                            employeesData?.map((data) => ({
                              id: data.id,
                              label: data.name,
                            })) || []
                          }
                          getOptionLabel={(option) => option.label || ""}
                          renderInput={(params) => (
                            <TextField
                              {...params}
                              label="Work With SP"
                              error={Boolean(errors.workWithSP)}
                              helperText={
                                errors.workWithSP
                                  ? errors.workWithSP.message
                                  : ""
                              }
                            />
                          )}
                          size="small"
                        />
                      )}
                    />
                  </FormControl>
                </Grid>

                <Grid item xs={12} md={3} sm={4}>
                  <FormControl fullWidth error={Boolean(errors.workWithCHS)}>
                    <Controller
                      name="workWithCHS"
                      control={control}
                      rules={{ required: false }}
                      render={({ field }) => (
                        <Autocomplete
                          {...field}
                          onChange={(event, newValue) => {
                            field.onChange(newValue ? newValue.id : null);
                          }}
                          value={
                            employeesData
                              ?.map((data) => ({
                                id: data.id,
                                label: data.name,
                              }))
                              .find((emp) => emp.id === field.value) || null
                          }
                          options={
                            employeesData?.map((data) => ({
                              id: data.id,
                              label: data.name,
                            })) || []
                          }
                          getOptionLabel={(option) => option.label || ""}
                          renderInput={(params) => (
                            <TextField
                              {...params}
                              label="Work With CHS"
                              error={Boolean(errors.workWithCHS)}
                              helperText={
                                errors.workWithCHS
                                  ? errors.workWithCHS.message
                                  : ""
                              }
                            />
                          )}
                          size="small"
                        />
                      )}
                    />
                  </FormControl>
                </Grid>

                <Grid item xs={12} md={3} sm={4}>
                  <FormControl
                    fullWidth
                    error={Boolean(errors.workForWorkOrder)}
                  >
                    <Controller
                      name="workForWorkOrder"
                      control={control}
                      rules={{ required: false }}
                      render={({ field }) => (
                        <Autocomplete
                          {...field}
                          onChange={(event, newValue) => {
                            field.onChange(newValue ? newValue.id : null);
                          }}
                          value={
                            employeesData
                              ?.map((data) => ({
                                id: data.id,
                                label: data.name,
                              }))
                              .find((emp) => emp.id === field.value) || null
                          }
                          options={
                            employeesData?.map((data) => ({
                              id: data.id,
                              label: data.name,
                            })) || []
                          }
                          getOptionLabel={(option) => option.label || ""}
                          renderInput={(params) => (
                            <TextField
                              {...params}
                              label="Work For Work Order"
                              error={Boolean(errors.workForWorkOrder)}
                              helperText={
                                errors.workForWorkOrder
                                  ? errors.workForWorkOrder.message
                                  : ""
                              }
                            />
                          )}
                          size="small"
                        />
                      )}
                    />
                  </FormControl>
                </Grid>

                <Grid item xs={12} md={3} sm={4}>
                  <FormControl fullWidth error={Boolean(errors.status)}>
                    <Controller
                      name="status"
                      control={control}
                      rules={{ required: false }}
                      render={({ field }) => (
                        <Autocomplete
                          {...field}
                          onChange={(event, newValue) => {
                            field.onChange(newValue ? newValue.id : null);
                          }}
                          value={
                            statusData?.find(
                              (status) => status.id === field.value
                            ) || null
                          }
                          options={statusData || []}
                          getOptionLabel={(option) => option.listValue || ""}
                          renderInput={(params) => (
                            <TextField
                              {...params}
                              label="Status"
                              error={Boolean(errors.status)}
                              helperText={
                                errors.status ? errors.status.message : ""
                              }
                            />
                          )}
                          size="small"
                        />
                      )}
                    />
                  </FormControl>
                </Grid>

                {/* <Grid item xs={12} sm={3}>
                  <FormControl
                    fullWidth
                    error={Boolean(errors.houzerSocietyTeamMember)}
                  >
                    <Controller
                      name="houzerSocietyTeamMember"
                      control={control}
                      rules={{
                        required: false,
                      }}
                      render={({ field }) => (
                        <Autocomplete
                          {...field}
                          onChange={(event, newValue) => {
                            field.onChange(newValue ? newValue.id : null);
                          }}
                          value={
                            employeesData
                              ?.map((data) => ({
                                id: data.id,
                                label: data.name,
                              }))
                              .find((emp) => emp.id === field.value) || null
                          }
                          options={
                            employeesData?.map((data) => ({
                              id: data.id,
                              label: data.name,
                            })) || []
                          }
                          getOptionLabel={(option) => option.label || ""}
                          renderInput={(params) => (
                            <TextField
                              {...params}
                              label="Houzer Society Team Member"
                              error={Boolean(errors.houzerSocietyTeamMember)}
                              helperText={
                                errors.houzerSocietyTeamMember
                                  ? errors.houzerSocietyTeamMember.message
                                  : ""
                              }
                            />
                          )}
                          size="small"
                        />
                      )}
                    />
                  </FormControl>
                </Grid> */}
                <Grid item xs={12} md={3} sm={4}>
                  <FormControl fullWidth error={Boolean(errors.curatedBy)}>
                    <Controller
                      name="curatedBy"
                      control={control}
                      rules={{ required: false }}
                      render={({ field }) => (
                        <Autocomplete
                          {...field}
                          onChange={(event, newValue) => {
                            field.onChange(newValue ? newValue.id : null);
                          }}
                          value={
                            employeesData
                              ?.map((data) => ({
                                id: data.id,
                                label: data.name,
                              }))
                              .find((emp) => emp.id === field.value) || null
                          }
                          options={
                            employeesData?.map((data) => ({
                              id: data.id,
                              label: data.name,
                            })) || []
                          }
                          getOptionLabel={(option) => option.label || ""}
                          renderInput={(params) => (
                            <TextField
                              {...params}
                              label="Curated By"
                              error={Boolean(errors.curatedBy)}
                              helperText={
                                errors.curatedBy ? errors.curatedBy.message : ""
                              }
                            />
                          )}
                          size="small"
                        />
                      )}
                    />
                  </FormControl>
                </Grid>
                <Grid item xs={12} md={3} sm={4}>
                  <FormControl fullWidth>
                    <Controller
                      name="curatedOn"
                      control={control}
                      render={({ field }) => (
                        <TextField
                          {...field}
                          size="small"
                          label="Curated On"
                          type="date"
                          InputLabelProps={{ shrink: true }}
                          aria-describedby="curatedOn"
                          value={field.value}
                          onChange={(e) => {
                            field.onChange(e.target.value);
                          }}
                        />
                      )}
                    />
                  </FormControl>
                </Grid>
                <Grid item xs={12} md={3} sm={4}></Grid>

                <Grid item xs={12} md={3} sm={4}>
                  <FormControl fullWidth>
                    <Controller
                      name="dataSentDate"
                      control={control}
                      render={({ field }) => (
                        <TextField
                          {...field}
                          size="small"
                          label={
                            <span
                              style={{ display: "flex", alignItems: "center" }}
                            >
                              Data Sent Date
                              <Tooltip
                                title="This field is for internal use and should indicate the date when the SR was sent to the Closing Team."
                                arrow
                                placement="top"
                              >
                                <span>
                                  <Icon
                                    icon="mingcute:information-line"
                                    style={{
                                      cursor: "pointer",
                                      marginLeft: 10,
                                      fontSize: 15,
                                    }}
                                  />
                                </span>
                              </Tooltip>
                            </span>
                          }
                          type="date"
                          InputLabelProps={{ shrink: true }}
                          aria-describedby="dataSentDate"
                          value={field.value}
                          onChange={(e) => {
                            field.onChange(e.target.value);
                          }}
                          inputProps={{
                            min: formData?.createdOn?.split("T")[0],
                          }}
                        />
                      )}
                    />
                  </FormControl>
                </Grid>
                <Grid item xs={12} md={3} sm={4}>
                  <FormControl fullWidth>
                    <Controller
                      name="deciMatrixPresentationDate"
                      control={control}
                      render={({ field }) => (
                        <TextField
                          {...field}
                          size="small"
                          label={
                            <span
                              style={{ display: "flex", alignItems: "center" }}
                            >
                              Deci-matrix Presentation Date
                              <Tooltip
                                title="This field indicates the date on which the presentation was made to CHS."
                                arrow
                                placement="top"
                              >
                                <span>
                                  <Icon
                                    icon="mingcute:information-line"
                                    style={{
                                      cursor: "pointer",
                                      marginLeft: 10,
                                      fontSize: 15,
                                    }}
                                  />
                                </span>
                              </Tooltip>
                            </span>
                          }
                          type="date"
                          InputLabelProps={{ shrink: true }}
                          aria-describedby="deciMatrixPresentationDate"
                          value={field.value}
                          onChange={(e) => {
                            field.onChange(e.target.value);
                          }}
                        />
                      )}
                    />
                  </FormControl>
                </Grid>
                <Grid
                  container
                  item
                  xs={12}
                  md={3}
                  sm={4}
                  sx={{ mb: 3, mt: 2 }}
                >
                  <Grid item>
                    <Typography className="data-field">
                      Lead Time Difference: &nbsp;
                    </Typography>
                  </Grid>
                  <Grid item>
                    <Typography style={{ fontWeight: "bold" }}>
                      {formData?.leadTimeDifference}
                      {formData?.leadTimeDifference ? " day(s)" : "N/A"}
                    </Typography>
                  </Grid>
                  <Grid item>
                    <Tooltip
                      title="Lead time difference calculates the difference between the SR Raised Date and the Data Sent Date (in days)."
                      arrow
                      placement="top"
                    >
                      <span>
                        <Icon
                          icon="mingcute:information-line"
                          style={{
                            cursor: "pointer",
                            marginLeft: 5,
                            fontSize: 20,
                          }}
                        />
                      </span>
                    </Tooltip>
                  </Grid>
                </Grid>
                <Grid
                  container
                  item
                  xs={12}
                  md={3}
                  sm={4}
                  sx={{ mb: 3, mt: 2 }}
                >
                  <Grid item>
                    <Typography className="data-field">
                      Requisition Lead Time: &nbsp;
                    </Typography>
                  </Grid>
                  <Grid item>
                    <Typography style={{ fontWeight: "bold" }}>
                      {formData?.requisitionLeadTime}
                      {formData?.requisitionLeadTime ? " day(s)" : "N/A"}
                    </Typography>
                  </Grid>
                  <Grid item>
                    <Tooltip
                      title="Requisition Lead Time represents the difference between the Society Onboarded Date and the Requisition Raised Date (in days)."
                      arrow
                      placement="top"
                    >
                      <span>
                        <Icon
                          icon="mingcute:information-line"
                          style={{
                            cursor: "pointer",
                            marginLeft: 5,
                            fontSize: 20,
                          }}
                        />
                      </span>
                    </Tooltip>
                  </Grid>
                </Grid>
                <Grid item xs={12} md={3} sm={4}>
                  <SelectAutoComplete
                    id={"referenceType"}
                    label={"Select reference type"}
                    nameArray={referenceData}
                    register={register}
                    value={referenceType}
                    defaultValue={referenceType}
                    onChange={(e) => setReferenceType(e.target.value)}
                    error={Boolean(errors.referenceType)}
                  />
                </Grid>

                <Grid item xs={12} md={3} sm={4}>
                  <SelectAutoComplete
                    id={"referralName"}
                    label={"Select Referral Name"}
                    nameArray={referralNameData}
                    register={register}
                    value={referralName}
                    defaultValue={referralName}
                    onChange={(e) => setReferralName(e.target.value)}
                    error={Boolean(errors.referralName)}
                  />
                </Grid>
              </Grid>
            </Card>
          )}

          {user.organisationCategory !== "SERVICE_PROVIDER" && (
            <>
              <Card>
                <Grid
                  sx={{
                    backgroundColor: "#f2f7f2",
                    mt: 4,
                    paddingTop: 0,
                    height: "36px",
                    display: "flex",
                    alignItems: "center",
                  }}
                >
                  <Typography
                    variant="body1"
                    fontWeight={"bold"}
                    sx={{ mt: 0, ml: 2 }}
                  >
                    Site Visit Timings
                  </Typography>
                  <Divider />
                </Grid>
                <Divider />
                <Grid item xs={12} sm={4}>
                  <Box
                    sx={{
                      display: "flex",
                      justifyContent: "flex-start",
                    }}
                  >
                    <div>
                      <Button
                        sx={{ margin: "20px" }}
                        variant="contained"
                        onClick={handleSiteVisitDialogOpen}
                      >
                        Add site visit slot
                      </Button>
                    </div>
                  </Box>
                  {currentRow ? (
                    <SiteVisitTimings
                      open={siteVisitOpen}
                      onClose={handleSiteVisitDialogClose}
                      siteVisits={siteVisits}
                      setSiteVisits={setSiteVisits}
                      rowData={currentRow}
                    />
                  ) : (
                    <SiteVisitTimings
                      open={siteVisitOpen}
                      onClose={handleSiteVisitDialogClose}
                      siteVisits={siteVisits}
                      setSiteVisits={setSiteVisits}
                    />
                  )}
                </Grid>
                <Box style={{ height: "100%", width: "100%" }}>
                  <DataGrid
                    rows={siteVisits || []}
                    columns={cols || []}
                    autoHeight
                    checkboxSelection
                    pagination
                    pageSize={pageSize}
                    page={page - 1}
                    rowsPerPageOptions={rowsPerPageOptions}
                    rowCount={rowCount}
                    paginationMode="server"
                    onPageChange={handlePageChange}
                    onPageSizeChange={handlePageSizeChange}
                    rowHeight={38}
                    headerHeight={38}
                  />
                </Box>
              </Card>
              <Card>
                <Grid
                  sx={{
                    backgroundColor: "#f2f7f2",
                    mt: 4,
                    paddingTop: 0,
                    height: "36px",
                    display: "flex",
                    alignItems: "center",
                  }}
                >
                  <Typography
                    variant="body1"
                    fontWeight={"bold"}
                    sx={{ mt: 0, ml: 2 }}
                  >
                    Conversations
                  </Typography>
                  <Divider />
                </Grid>
                <Divider />
                <Grid
                  container
                  sx={{ margin: "20px" }}
                  justifyContent="flex-start"
                >
                  <CommentsDialog
                    setConvList={setConvList}
                    setConversations={setConversations}
                    conversations={conversations}
                  />
                  {convList?.length > 0 && (
                    <TableContainer sx={{ marginTop: "16px" }}>
                      <Table>
                        <TableHead>
                          <TableRow>
                            {user?.organisationCategory === "EMPLOYEE" && (
                              <>
                                <MUITableCell>Last Contacted Date</MUITableCell>
                                <MUITableCell>Follow Up Date</MUITableCell>
                                <MUITableCell>Follow Up Action</MUITableCell>
                              </>
                            )}
                            <MUITableCell>Conversation</MUITableCell>

                            <MUITableCell>More Info</MUITableCell>
                          </TableRow>
                        </TableHead>
                        <TableBody>
                          {convList?.map((row, index) => (
                            <TableRow key={index}>
                              {user?.organisationCategory === "EMPLOYEE" && (
                                <>
                                  <MUITableCell sx={{ width: "15%" }}>
                                    {row.lastContactedDate?.split("T")[0]}
                                  </MUITableCell>
                                  <MUITableCell sx={{ width: "10%" }}>
                                    {row.followUpDate}
                                  </MUITableCell>
                                  <MUITableCell sx={{ width: "30%" }}>
                                    {row.followUpActions}
                                  </MUITableCell>
                                </>
                              )}

                              <MUITableCell sx={{ width: "30%" }}>
                                {row.message}
                              </MUITableCell>
                              <MUITableCell sx={{ width: "15%" }}>
                                <Tooltip title="More Info">
                                  <CustomAvatar
                                    skin="light"
                                    variant="rounded"
                                    sx={{
                                      width: 28,
                                      height: 28,
                                      cursor: "pointer",
                                    }}
                                    onClick={() => {
                                      setConversation(row);
                                      setOpenMoreInfoDialog(true);
                                    }}
                                  >
                                    <Icon
                                      icon="tabler:info-circle"
                                      fontSize="2.2rem"
                                    />
                                  </CustomAvatar>
                                </Tooltip>
                              </MUITableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </TableContainer>
                  )}
                </Grid>
              </Card>
            </>
          )}
        </DialogContent>
        <DialogActions
          sx={{
            justifyContent: "end",
            borderTop: (theme) => `1px solid ${theme.palette.divider}`,
            p: (theme) => `${theme.spacing(2.5)} !important`,
          }}
        >
          <Button
            display="flex"
            justifyContent="center"
            variant="outlined"
            color="primary"
            onClick={handleCancel}
          >
            Cancel
          </Button>
          <Button
            display="flex"
            justifyContent="Flex-end"
            variant="contained"
            color="primary"
            onClick={handleSubmit(submit)}
            sx={{
              mr: {
                xs: 6,
                sm: 6,
                md: 6,
                lg: 6,
                xl: 10,
              },
            }}
          >
            Save
          </Button>
        </DialogActions>
      </Dialog>
      <Dialog
        open={openMoreInfoDialog}
        onClose={handleDialogClose}
        fullWidth
        scroll="paper"
      >
        <DialogTitle
          sx={{
            position: "relative",
            borderBottom: (theme) => `1px solid ${theme.palette.divider}`,
            p: (theme) => `${theme.spacing(1.5)} !important`,
            display: "flex",
            flexDirection: "column",
            alignItems: "start",
            margin: 3,
            fontSize: {
              xs: "16px",
              md: "20px",
            },
            fontWeight: "bold",
          }}
        >
          Conversation Details
          <Box sx={{ position: "absolute", top: "4px", right: "12px" }}>
            <IconButton
              size="small"
              onClick={handleDialogClose}
              sx={{
                p: "0.438rem",
                borderRadius: 1,
                color: "common.white",
                backgroundColor: "primary.main",
                "&:hover": {
                  backgroundColor: "#66BB6A",
                  transition: "background 0.5s ease, transform 0.5s ease",
                },
              }}
            >
              <Icon icon="tabler:x" fontSize="1rem" />
            </IconButton>
          </Box>
        </DialogTitle>
        <DialogContent maxWidth="lg">
          <TableContainer sx={{ padding: "2px 3px" }} className="tableBody">
            <Table>
              <TableBody
                sx={{
                  "& .MuiTableCell-root": {
                    p: `${theme.spacing(1.35, 1.125)} !important`,
                  },
                }}
              >
                {user?.organisationCategory === "EMPLOYEE" && (
                  <>
                    <TableRow>
                      <MUITableCell>
                        <Typography sx={{ fontWeight: 600 }}>
                          Last Contacted Date
                        </Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography>
                          {conversation.lastContactedDate?.split("T")[0]}
                        </Typography>
                      </MUITableCell>
                    </TableRow>
                    <TableRow>
                      <MUITableCell>
                        <Typography sx={{ fontWeight: 600 }}>
                          Follow Up Date
                        </Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography>{conversation.followUpDate}</Typography>
                      </MUITableCell>
                    </TableRow>
                    <TableRow>
                      <MUITableCell>
                        <Typography sx={{ fontWeight: 600 }}>
                          Follow Up Action
                        </Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography>{conversation.followUpActions}</Typography>
                      </MUITableCell>
                    </TableRow>
                  </>
                )}

                <TableRow>
                  <MUITableCell>
                    <Typography sx={{ fontWeight: 600 }}>
                      Conversation
                    </Typography>
                  </MUITableCell>
                  <MUITableCell>
                    <Typography>{conversation.message}</Typography>
                  </MUITableCell>
                </TableRow>
              </TableBody>
            </Table>
          </TableContainer>
        </DialogContent>
        <DialogActions
          sx={{
            justifyContent: "end",
            borderTop: (theme) => `1px solid ${theme.palette.divider}`,
            p: (theme) => `${theme.spacing(2.5)} !important`,
          }}
        >
          <Button
            size="medium"
            sx={{ mr: 3 }}
            onClick={() => handleDialogClose()}
            variant="outlined"
            color="primary"
          >
            Close
          </Button>
        </DialogActions>
      </Dialog>
      <SpecificationsDialog
        open={requisitionDialog}
        onClose={handleClickClose}
        data={formData?.specifications?.sections?.length === 0? templateDetails?.sections : specificationsData}
        specifications={formattedData}
        setSpecifications={setSpecifications}
        setSpecificationsData={setSpecificationsData}
        checkedSections={checkedSections}
        setCheckedSections={setCheckedSections}
      />
      <Dialog
        open={openDialogContent}
        onClose={handleButtonClick}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
        PaperProps={{
          sx: {
            p: (theme) => `${theme.spacing(2.5)} !important`,
            backgroundColor: (theme) => theme.palette.primary.background,
          },
        }}
      >
        <Box
          sx={{
            width: "100%",
            borderRadius: 1,
            textAlign: "center",
            border: (theme) => `1px solid ${theme.palette.divider}`,
            borderColor: "primary.main",
          }}
        >
          <DialogContent>
            <DialogContentText
              id="alert-dialog-description"
              color="primary.main"
            >
              <div dangerouslySetInnerHTML={{ __html: dialogMessage }} />
            </DialogContentText>
          </DialogContent>
          <DialogActions>
            <Button
              variant="contained"
              onClick={handleButtonClick}
              sx={{ margin: "auto", width: 100 }}
            >
              Okay
            </Button>
          </DialogActions>
        </Box>
      </Dialog>
    </>
  );
};

export default EditRequisition;
