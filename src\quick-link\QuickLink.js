import React, { useState, useEffect, useContext } from "react";
import {
  ClickAwayListener,
  Box,
  Typography,
  Grid,
  IconButton,
  Stack,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  CircularProgress,
  FormControl,
  InputLabel,
  MenuItem,
  Select,
  FormHelperText,
  Table,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  DialogContentText,
} from "@mui/material";
import Icon from "src/@core/components/icon";
import authConfig from "src/configs/auth";
import {
  ExpandMore,
  ExpandLess,
  Close as CloseIcon,
} from "@mui/icons-material";
import QuickLinks from "src/quick-link";
import { getAuthorizationHeaders, getUrl } from "src/helpers/utils";
import SpShortForm from "src/pages/SP/CreateUser";
import ChsShortForm from "src/pages/CHS/CreateUser";
import axios from "axios";
import { useForm, Controller } from "react-hook-form";
import SnapshotEditDialog from "src/pages/leads/snapshots/SnapshotEditDialog";
import { AuthContext } from "src/context/AuthContext";
import SnapshotUploadDialog from "src/@core/components/custom-components/SnapshotUploadDialog";

function QuickLink() {
  const [isOpen, setIsOpen] = useState(true);
  const menuActions = QuickLinks();
  const [expandedCategories, setExpandedCategories] = useState([]);
  const [openDialogSP, setOpenDialogSP] = useState(false);
  const [openDialogCHS, setOpenDialogCHS] = useState(false);
  const [openDialogSnapshot, setOpenDialogSnapshot] = useState(false);
  const [page, setPage] = useState(1);
  const [searchData, setSearchData] = useState({});
  const [loading, setLoading] = useState(false);
  const rowsPerPageOptions = [10, 15, 20, 25, 50, 100];
  const [pageSize, setPageSize] = useState(rowsPerPageOptions[0]);
  const [searchKeyword, setSearchKeyword] = useState("");
  const [initialRowCount, setInitialRowCount] = useState(null);
  const [userList, setUserList] = useState([]);
  const [rowCount, setRowCount] = useState(0);
  const [selectedOption, setSelectedOption] = useState(null);
  const [selectedFiles, setSelectedFiles] = useState([]);
  const [categoryId, setCategoryId] = useState("");
  const [dialogSuccess, setDialogSuccess] = useState(false);
  const [dialogMessage, setDialogMessage] = useState("");

  const [leadStatus, setLeadStatus] = useState("");
  const [leadPriority, setLeadPriority] = useState("");
  const [status, setStatus] = useState("");
  const [assignedTo, setAssignedTo] = useState("");

  const { uploadSnapshots, allCategories, allSubCategories } =
    useContext(AuthContext);

  useEffect(() => {
    if (menuActions?.length > 0) {
      setExpandedCategories(menuActions.map((action) => action.title));
    }
  }, [menuActions]);

  const {
    register,
    handleSubmit,
    control,
    reset,
    clearErrors,
    formState: { errors },
  } = useForm();


  const documentDetails = {
    documentCategory: "240ab5e5-48b3-4897-a1cc-66a98792ef25",
    documentSubCategory: "7bf03e27-502a-45e5-a897-871e59f39073",
    categoryTypeId: categoryId,
    documentFrom: "EMPLOYEE",
    documentTo: "HOUZER",
  };

  const handleSave = async () => {
    setLoading(true);

    const formData = new FormData();
    selectedFiles.forEach((file) => {
      formData.append("files", file);
    });
    formData.append("documentDetails", JSON.stringify(documentDetails));
    formData.append(
      "leadSnapshotResponseDTO",
      JSON.stringify({
        assignedTo: assignedTo,
        status: status,
        leadStatus: leadStatus,
        leadPriority: leadPriority,
      })
    );

    // API call
    await uploadSnapshots(
      formData,
      () => {
        const message = `Successfully Uploaded`;
        setDialogMessage(message);
        setDialogSuccess(true);

        setSelectedFiles([]);
      },
      () => {
        const message = `Failed to upload`;
        setDialogMessage(message);
        setDialogSuccess(true);
      }
    );

    setSelectedFiles([]);
    setLeadPriority("");
    setLeadStatus("");
    setStatus("");
    setAssignedTo("");
    setLoading(false);
    setOpenDialogSnapshot(false);
  };

  const fetchServiceProviders = async (
    currentPage,
    currentPageSize,
    searchKeyword,
    dataSearch
  ) => {
    setLoading(true);

    const url =
      getUrl(authConfig.getAllProfilesEndpointNewTable) +
      "?profileType=SERVICE_PROVIDER";
    const headers = getAuthorizationHeaders();

    const data = {
      page: currentPage,
      pageSize: currentPageSize,
      searchKeyword: searchKeyword,
      serviceTypeUUIDs: dataSearch?.serviceTypeUUIDs ?? null,
      locationUUIDs: dataSearch?.locationUUIDs ?? null,
      leadStatusUUIDs: dataSearch?.leadStatusUUIDs ?? null,
      leadPriorityUUIDs: dataSearch?.leadPriorityUUIDs ?? null,
      isMicroSiteEmpanelled: dataSearch?.isMicroSiteEmpanelled ?? null,
      isListingEmpanelled: dataSearch?.isListingEmpanelled ?? null,
    };

    try {
      const response = await axios({
        method: "post",
        url: url,
        headers: headers,
        data: data,
      });

      if (initialRowCount == null) {
        setInitialRowCount(response.data.rowCount || 0);
      }

      if (response.data) {
        setUserList(response.data?.users || []);
        setRowCount(response.data?.rowCount || 0);
      } else {
        console.error("Unexpected API response format:", response);
      }
    } catch (error) {
      console.error("Error fetching users:", error);
    } finally {
      setLoading(false);
    }
  };

  const fetchUsers = async (currentPage, currentPageSize, searchKeyword) => {
    setLoading(true);
    const url =
      getUrl(authConfig.getAllProfilesEndpointNewTable) +
      "?profileType=SOCIETY";
    const headers = getAuthorizationHeaders();

    const data = {
      page: currentPage,
      pageSize: currentPageSize,
      searchKeyword: searchKeyword,
    };

    console.log(`Fetching users from ${url} with params`, data);

    try {
      const response = await axios({
        method: "post",
        url: url,
        headers: headers,
        data: data,
      });

      if (response.data) {
        setUserList(response.data?.users || []);
        setRowCount(response.data?.rowCount || 0);
      } else {
        console.error("Unexpected API response format:", response);
      }
    } catch (error) {
      console.error("Error fetching users:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleClickAway = () => {
    setIsOpen(false);
  };

  const handleActionClickSP = () => {
    setOpenDialogSP(true);
  };

  const handleActionClickCHS = () => {
    setOpenDialogCHS(true);
  };

  const handleActionClickSnapshot = () => {
    setCategoryId("SOCIETY");
    setOpenDialogSnapshot(true);
  };

  const handleActionClickSPSnapshot = () => {
    setCategoryId("SERVICE_PROVIDER");
    setOpenDialogSnapshot(true);
  };

  const handleDialogCloseSP = () => {
    setOpenDialogSP(false);
  };

  const handleDialogCloseCHS = () => {
    setOpenDialogCHS(false);
  };

  const handleDialogCloseSnapshot = () => {
    setOpenDialogSnapshot(false);
  };

  const handleClose = () => {
    setDialogSuccess(false);
  };

  const handleCategoryToggle = (title) => {
    setExpandedCategories((prevExpandedCategories) => {
      if (prevExpandedCategories.includes(title)) {
        return prevExpandedCategories.filter((menu) => menu !== title);
      } else {
        return [...prevExpandedCategories, title];
      }
    });
  };


  return (
    <>
      {isOpen && menuActions?.length && (
        <ClickAwayListener onClickAway={handleClickAway}>
          <Box
            sx={{
              position: "fixed",
              top: 0,
              left: 0,
              zIndex: 1100,
              background: "white",
              borderRadius: "10px",
              boxShadow: "0 8px 16px rgba(0,0,0,0.1)",
              width: "60%",
              height: "400px",
              overflow: "auto",
              marginLeft: 100,
              marginTop: 12.5,
              "@media (max-width: 600px)": {
                width: "90%",
                marginLeft: "35px",
              },
              "@media (min-width : 601px) and (max-width: 768px)": {
                width: "70%",
                marginLeft: "220px",
              },
              padding: "10px",
              boxSizing: "border-box",
            }}
          >
            <Grid container spacing={2}>
              {menuActions?.map((action, index) => (
                <Grid item xs={12} key={index}>
                  <Grid onClick={() => handleCategoryToggle(action.title)}>
                    <Typography
                      variant="h6"
                      style={{
                        background: "#f2f7f2",
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "space-between",
                        paddingLeft: "15px",
                        cursor: "pointer",
                        transition: "background-color 0.3s",
                        ":hover": {
                          background: "#fff7e6",
                        },
                      }}
                    >
                      {action.title}
                      <IconButton size="small">
                        {expandedCategories.includes(action.title) ? (
                          <ExpandLess />
                        ) : (
                          <ExpandMore />
                        )}
                      </IconButton>
                    </Typography>
                  </Grid>

                  {expandedCategories.includes(action.title) && (
                    <Grid container spacing={2}>
                      {action.path !== null && (
                        <Grid item xs={6} sm={4} lg={3}>
                          <Box
                            sx={{
                              width: "100%",
                              display: "flex",
                              justifyContent: "center",
                              alignItems: "center",
                              height: "80px",
                              margin: "5px",
                              "&:hover": {
                                background: "#f2f7f2",
                                cursor: "pointer",
                              },
                            }}
                          >
                            <Stack direction="column" alignItems="center">
                              <IconButton color="inherit">
                                <Icon fontSize="1.5rem" icon={action.icon} />
                              </IconButton>
                              <Typography variant="caption">
                                {action.title}
                              </Typography>
                            </Stack>
                          </Box>
                        </Grid>
                      )}

                      {action.children && (
                        <>
                          {action.children.map((child, childIndex) => (
                            <Grid item xs={6} sm={4} lg={3} key={childIndex}>
                              <Box
                                sx={{
                                  textAlign: "center",
                                  width: "100%",
                                  height: "80px",
                                  display: "flex",
                                  flexDirection: "column",
                                  justifyContent: "center",
                                  alignItems: "center",
                                  margin: "5px",
                                  "&:hover": {
                                    background: "#f2f7f2",
                                    cursor: "pointer",
                                  },
                                }}
                                onClick={
                                  child.title === "Add New SP"
                                    ? handleActionClickSP
                                    : child.title === "Add New CHS"
                                    ? handleActionClickCHS
                                    : child.title === "Add SP Snapshot"
                                    ? handleActionClickSPSnapshot
                                    : handleActionClickSnapshot
                                }
                              >
                                <IconButton color="inherit">
                                  <Icon fontSize="1.5rem" icon={child.icon} />
                                </IconButton>
                                <Typography variant="caption" component="div">
                                  {child.title}
                                </Typography>
                              </Box>
                            </Grid>
                          ))}
                        </>
                      )}
                    </Grid>
                  )}
                </Grid>
              ))}
            </Grid>
          </Box>
        </ClickAwayListener>
      )}
      {openDialogSP && (
        <SpShortForm
          openDialog={openDialogSP}
          searchData={searchData}
          page={page}
          pageSize={pageSize}
          searchKeyword={searchKeyword}
          fetchServiceProviders={fetchServiceProviders}
          reset={reset}
          handleDialogClose={handleDialogCloseSP}
        />
      )}

      {openDialogCHS && (
        <ChsShortForm
          openDialog={openDialogCHS}
          selectedOption={selectedOption}
          handleDialogClose={handleDialogCloseCHS}
          fetchUsers={fetchUsers}
          reset={reset}
        />
      )}

      {openDialogSnapshot && (
        <SnapshotUploadDialog
          open={openDialogSnapshot}
          onClose={() => handleDialogCloseSnapshot(false)}
          onSave={() => handleSave()}
          selectedFiles={selectedFiles}
          setSelectedFiles={setSelectedFiles}
          isShortForm={false}
          category={categoryId}
          loading={loading}
          leadStatus={leadStatus}
          setLeadStatus={setLeadStatus}
          leadPriority={leadPriority}
          setLeadPriority={setLeadPriority}
          status={status}
          setStatus={setStatus}
          assignedTo={assignedTo}
          setAssignedTo={setAssignedTo}
        />
      )}
      <Dialog
        open={dialogSuccess}
        onClose={handleClose}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
        PaperProps={{
          sx: {
            p: (theme) => `${theme.spacing(2.5)} !important`,
            backgroundColor: (theme) => theme.palette.primary.background,
          },
        }}
      >
        <Box
          sx={{
            width: "100%",
            borderRadius: 1,
            textAlign: "center",
            border: (theme) => `1px solid ${theme.palette.divider}`,
            borderColor: "primary.main",
          }}
        >
          <DialogContent>
            <DialogContentText
              id="alert-dialog-description"
              color="primary.main"
            >
              <div dangerouslySetInnerHTML={{ __html: dialogMessage }} />
            </DialogContentText>
          </DialogContent>
          <DialogActions>
            <Button
              variant="contained"
              onClick={handleClose}
              sx={{ margin: "auto", width: 100 }}
            >
              Ok
            </Button>
          </DialogActions>
        </Box>
      </Dialog>
    </>
  );
}

export default QuickLink;
