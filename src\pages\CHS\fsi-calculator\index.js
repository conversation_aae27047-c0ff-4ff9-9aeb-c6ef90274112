import { useTheme } from "@emotion/react";
import {
  Box,
  <PERSON>ton,
  CardContent,
  CircularProgress,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  Divider,
  FormControl,
  FormHelperText,
  Grid,
  ListItemIcon,
  Menu,
  MenuItem,
  Table,
  TableBody,
  TableContainer,
  TableRow,
  TextField,
  Tooltip,
  Typography,
} from "@mui/material";
import IconButton from "@mui/material/IconButton";
import { DataGrid } from "@mui/x-data-grid";
import axios from "axios";
import { useRouter } from "next/router";
import { useContext, useEffect, useState } from "react";
import { Controller, useForm } from "react-hook-form";
import SelectAutoComplete from "src/@core/components/custom-components/SelectAutoComplete";
import Icon from "src/@core/components/icon";
import CustomAvatar from "src/@core/components/mui/avatar";
import CustomChip from "src/@core/components/mui/chip";
import FallbackSpinner from "src/@core/components/spinner";
import authConfig from "src/configs/auth";
import { MENUS, PAGES, PERMISSIONS } from "src/constants";
import { AuthContext } from "src/context/AuthContext";
import { getAuthorizationHeaders, getUrl } from "src/helpers/utils";
import { useAuth } from "src/hooks/useAuth";
import { useRBAC } from "src/pages/permission/RBACContext";
import MUITableCell from "src/pages/SP/MUITableCell";
import FSIDeleteDialog from "../fsi-calculations/FSIDeleteDialog";
import FSIUpdateDialog from "../fsi-calculations/FSIUpdateDialog";
import EditDialog from "./EditDialog";

const field = {
  fontWeight: 500,
  fontSize: "12.75px",
};

const userStatusObj = {
  true: "Active",
  false: "InActive",
};

const Calculator = () => {
  const auth = useAuth();

  const theme = useTheme();

  const {
    user,
    listValues,
    fsiData,
    setFsiData,
    fsiDataDetails,
    getSocietyProfile,
  } = useContext(AuthContext);

  const [openDialog, setOpenDialog] = useState(false);
  const [organisationData, setOrganisationData] = useState({});

  useEffect(() => {
    axios({
      method: "get",
      url:
        getUrl(authConfig.organisationsEndpoint) +
        "/get-by-orgId/" +
        user?.orgId,
      headers: getAuthorizationHeaders(authConfig.individualListMIMEType),
    })
      .then((res) => {
        setOrganisationData(res.data);
      })
      .catch((err) => console.log("error", err));
  }, [openDialog]);

  const [openUpdateDialog, setOpenUpdateDialog] = useState(false);

  const [viewDialogOpen, setViewDialogOpen] = useState(false);
  const [openDeleteDialog, setOpenDeleteDialog] = useState(false);
  const [openDialogUpdate, setOpenDialogUpdate] = useState(false);
  const [anchorEl, setAnchorEl] = useState(null);
  const rowsPerPageOptions = [10, 15, 20, 25, 50, 100];
  const [userList, setUserList] = useState([]);
  const [loading, setLoading] = useState(true);
  const [pageSize, setPageSize] = useState(rowsPerPageOptions[0]);
  const [page, setPage] = useState(1);
  const [rowCount, setRowCount] = useState(0);
  const [editForm, setEditForm] = useState(false);
  const [calculationDialog, setCalculationDialog] = useState(false);
  const [calculatedData, setCalculatedData] = useState();

  const [condition, setCondition] = useState(true);

  const [currentRow, setCurrentRow] = useState();

  const [searchKeyword, setSearchKeyword] = useState("");

  const [openDialogContent, setOpenDialogContent] = useState(false);
  const [dialogMessage, setDialogMessage] = useState("");

  const [calculateLoad, setCalculateLoad] = useState(false);
  const [saveLoad, setSaveLoad] = useState(false);

  const [authority, setAuthority] = useState("");
  const [roadWidth, setRoadWidth] = useState("");
  const [grossPlotArea, setGrossPlotArea] = useState("");

  const {
    register,
    handleSubmit,
    setError,
    clearErrors,
    control,
    reset,
    setValue,
    formState: { errors },
  } = useForm();

  const mapIsActiveToLabel = (isActive) => {
    return userStatusObj[isActive] || "Unknown";
  };

  const columns = [
    {
      field: "location",
      headerName: "Location",
      flex: 0.2,
      minWidth: 120,
      renderCell: (params) => {
        const location = locationsOptions?.find(
          (item) => item?.value === params?.row?.location
        );
        return (
          <Tooltip title={location?.key}>
            <span>{location?.key || ""}</span>
          </Tooltip>
        );
      },
    },
    {
      field: "roadWidth",
      headerName: "Road Width",
      flex: 0.15,
      minWidth: 100,
    },
    {
      field: "isActive",
      headerName: "Status",
      flex: 0.18,
      minWidth: 120,
      renderCell: ({ row }) => {
        return (
          <CustomChip
            rounded={true}
            skin="light"
            size="small"
            label={mapIsActiveToLabel(row.isActive)}
            color={row.isActive === true ? "success" : "error"}
            sx={{ textTransform: "capitalize" }}
          />
        );
      },
    },
    {
      field: "zonalFSI",
      headerName: "Zonal FSI",
      flex: 0.15,
      minWidth: 90,
    },
    {
      field: "premiumFSI",
      headerName: "Premium FSI",
      flex: 0.15,
      minWidth: 90,
    },
    {
      field: "tdr",
      headerName: "TDR",
      flex: 0.15,
      minWidth: 90,
    },
    {
      field: "fungibleFSI",
      headerName: "FSI",
      flex: 0.15,
      minWidth: 90,
    },
    {
      field: "buildupArea",
      headerName: "Built-up Area",
      flex: 0.15,
      minWidth: 110,
      valueGetter: (params) => {
        const roundedValue = Number(params.value).toFixed(2);
        return roundedValue;
      },
    },
    {
      field: "actions",
      headerName: "Actions",
      flex: 0.15,
      minWidth: 75,
      renderCell: (params) => {
        const handleMenuClick = (params, event) => {
          setAnchorEl(event.currentTarget);
          setFsiData({
            ...fsiData,
            id: params.row.id,
          });
          setCurrentRow(params.row);
        };

        const handleMenuClose = () => {
          setAnchorEl(null);
        };

        const onClickViewProfile = () => {
          setViewDialogOpen(true);
          handleMenuClose(); // Close the menu after clicking
        };
        const onClickToggleStatus = () => {
          if (currentRow?.isActive) {
            setOpenDeleteDialog(true);
          } else {
            setOpenDialogUpdate(true);
          }
          handleMenuClose();
        };

        return (
          <div
            style={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
            }}
          >
            <CustomAvatar
              skin="light"
              variant="rounded"
              sx={{
                mr: { xs: 2, lg: 4 },
                width: 34,
                height: 34,
                cursor: "pointer",
              }}
              onClick={(event) => handleMenuClick(params, event)}
            >
              <Icon icon="bi:three-dots-vertical" />
            </CustomAvatar>
            <Menu
              anchorEl={anchorEl}
              open={Boolean(anchorEl)}
              onClose={handleMenuClose}
            >
              <MenuItem onClick={onClickViewProfile}>
                <Icon icon="iconamoon:edit" style={{ marginRight: 8 }} />
                Edit
              </MenuItem>
              <MenuItem onClick={onClickToggleStatus}>
                <ListItemIcon>
                  <Icon
                    icon={
                      currentRow?.isActive
                        ? "iconamoon:trash"
                        : "tabler:circle-check"
                    }
                  />
                </ListItemIcon>
                {currentRow?.isActive ? "Deactivate" : "Activate"}
              </MenuItem>
            </Menu>
          </div>
        );
      },
    },
  ];

  const isDataViewComplete = [
    'authority',
    'type',
    'location',
    'zone',
    'ward',
    'roadWidth',
    'plotArea'
  ].every(key => organisationData?.[key]);
  const fetchUsers = async (currentPage, currentPageSize, searchKeyword) => {
    const url = getUrl(authConfig.fsiCalculatorSocietyGetALL);

    const headers = getAuthorizationHeaders();

    const data = {
      page: currentPage,
      pageSize: currentPageSize,
      searchKeyword: searchKeyword,
    };

    try {
      const response = await axios({
        method: "post",
        url: url,
        headers: headers,
        data: data,
      });

      if (response.data) {
        setUserList(response.data?.fsiCalculatorResponse);
        setRowCount(response.data?.rowCount || 0);
      } else {
        console.error("Unexpected API response format:", response);
      }
    } catch (error) {
      console.error("Error fetching users:", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchUsers(page, pageSize, searchKeyword);
  }, [page, pageSize, searchKeyword]);

  const handlePageChange = (direction) => {
    if (direction === page) {
      setPage(page + 1);
    } else {
      setPage(page - 1);
    }
  };

  const handlePageSizeChange = (params) => {
    if (params) {
      setPageSize(params);
      setPage(1);
    }
  };

  const [dataView, setDataView] = useState({});

  useEffect(() => {
    reset({ ...dataView });
  }, [dataView, reset]);

  const [locate, setLocate] = useState(null);
  const [zone, setZone] = useState("");

  const [listOfLocations, setListOfLocations] = useState([]);
  const [locationsOptions, setLocationsOptions] = useState([]);

  useEffect(() => {
    axios({
      method: "get",
      url: getUrl(authConfig.selectDropdownNew) + "?selectionType=LOCATIONS",
      headers: getAuthorizationHeaders(),
    })
      .then((res) => {
        setListOfLocations(res.data.data);
      })
      .catch((err) => console.log("Locations error", err));
  }, []);

  useEffect(() => {
    if (!!listOfLocations) {
      let data = [];
      listOfLocations.map((entry) => {
        data.push({ value: entry.id, key: entry.name });
      });
      setLocationsOptions(data);
    }
  }, [listOfLocations]);

  const handleLocationChange = (newValue) => {
    setLocate(newValue);
    const selectedLocation = listOfLocations.find(
      (location) => location.id === newValue
    );
    if (selectedLocation) {
      const zone = selectedLocation.zoneId
        ? listValues.find((item) => item.id === selectedLocation.zoneId)?.name
        : null;
      setZone(zone);
    }
  };

  const [wardNo, setWardNo] = useState(null);
  const [selectedWard, setSelectedWard] = useState({});
  const [listOfWards, setListOfWards] = useState([]);

  const wardOptions = listOfWards?.map((ward) => ({
    value: ward,
    key: ward?.name,
  }));

  const [typeNo, setTypeNo] = useState({});
  const [selectedType, setSelectedType] = useState({});
  const [listOfTypes, setListOfTypes] = useState([]);

  const typeOptions = listOfTypes.map((type) => ({
    value: type,
    key: type?.name,
  }));

  const handleUpdateDialog = () => {
    setOpenUpdateDialog(false);
  };
  useEffect(() => {
    const fetchWards = async () => {
      const data = {
        masterDataType: "WARD_FSI",
      };
      axios({
        method: "post",
        url: getUrl(authConfig.masterDataGetAllEndpoint),
        headers: getAuthorizationHeaders(),
        data: data,
      })
        .then((res) => {
          setListOfWards(res.data?.masterDataResponse);

          const matchingWard = res.data?.masterDataResponse.find(
            (ward) => ward?.name === organisationData?.ward
          );

          // Set the matching location as the defaultValue for the SelectCategory
          if (matchingWard) {
            setWardNo(matchingWard);
          } else {
            setWardNo(null);
          }
        })
        .catch((err) => console.log("error", err));
    };

    fetchWards();

    const fetchTypes = async () => {
      const data = {
        masterDataType: "TYPE_FSI",
      };
      axios({
        method: "post",
        url: getUrl(authConfig.masterDataGetAllEndpoint),
        headers: getAuthorizationHeaders(),
        data: data,
      })
        .then((res) => {
          setListOfTypes(res.data.masterDataResponse);

          const matchingType = res.data?.masterDataResponse.find(
            (type) => type?.name === organisationData?.type
          );

          // Set the matching location as the defaultValue for the SelectCategory
          if (matchingType) {
            setTypeNo(matchingType);
          } else {
            setTypeNo(null);
          }
        })
        .catch((err) => console.log("error", err));
    };

    fetchTypes();

    setZone(organisationData?.zone);

    setLocate(organisationData?.locationId);
  }, [organisationData]);

  const handleWardChange = (newValue) => {
    setSelectedWard(newValue);
  };

  const handleSuccess = () => {
    const message = `
      <div> 
        <h3>FSI Calculated Successfully.</h3>
      </div>
    `;
    setDialogMessage(message);
    setOpenDialogContent(true);
  };

  const handleFailure = () => {
    const message = `
      <div> 
        <h3> Failed to Calculate FSI. Please try again later.</h3>
      </div>
    `;
    setDialogMessage(message);
    setOpenDialogContent(true);
  };

  const handleUpdateProfile = () => {
    const message = `
      <div> 
        <h3> Do you want to update the profile data with this.</h3>
      </div>
    `;
    setDialogMessage(message);
    setOpenUpdateDialog(true);
  };

  async function Calculation(data) {
    setCondition(false);
    setCalculationDialog(true);
    setCalculateLoad(true);
    let fields;
    if (!editForm) {
      // Create Mode

      fields = {
        orgId: organisationData?.orgId,
        societyName: organisationData?.societyName,
        location: organisationData?.locationId,
        ward: organisationData?.ward,
        zone: organisationData?.zone,
        type: organisationData?.type,
        saveInTable: false,
        updateInProfile: false,
        authority: organisationData?.authority,
        roadWidth: organisationData?.roadWidth,
        grossPlotArea: organisationData?.plotArea,
      };
    } else {
      // Update Mode

      fields = {
        orgId: user?.orgId,
        societyName: organisationData?.societyName,
        location: locate,
        zone: zone,
        ward: wardNo?.name,
        type: typeNo?.name,
        saveInTable: false,
        updateInProfile: false,
        authority: data?.authority,
        roadWidth: data?.roadWidth,
        grossPlotArea: data?.grossPlotArea,
      };
    }

    try {
      const response = await auth.postCalculatedFSI(
        fields,
        handleFailure,
        handleSuccess
      );
      setCalculatedData(response);
      setCalculateLoad(false);
    } catch (error) {
      console.error("FSI Rule Creation failed:", error);
      setCalculateLoad(false);
      handleFailure();
    }

    setCondition(true);

    // fetchUsers(page, pageSize, searchKeyword);
  }

  async function SaveCalculation() {
    setOpenUpdateDialog(false);
    setCondition(false);
    setSaveLoad(true);
    let fields;
    fields = {
      orgId: user?.orgId,
      societyName: organisationData?.societyName,
      location: locate,
      zone: zone,
      ward: wardNo?.name,
      type: typeNo?.name,
      saveInTable: true,
      updateInProfile: true,
      authority: authority ? authority : organisationData?.authority,
      roadWidth: roadWidth ? roadWidth : organisationData?.roadWidth,
      grossPlotArea: grossPlotArea ? grossPlotArea : organisationData?.plotArea,
    };

    try {
      const response = await auth.postCalculatedFSI(
        fields,
        handleFailure,
        handleSuccess
      );
      setSaveLoad(false);
    } catch (error) {
      console.error("FSI Rule Creation failed:", error);
      setSaveLoad(false);
      handleFailure();
    }
    fetchUsers(page, pageSize, searchKeyword);
    setOpenDialog(false);
    setEditForm(false);
    setCalculationDialog(false);
    setCondition(true);
    setSelectedWard({});
    setDataView({});
    reset();
  }

  async function SaveCalculate() {
    setOpenUpdateDialog(false);
    setCondition(false);
    setSaveLoad(true);
    let fields;
    fields = {
      orgId: user?.orgId,
      societyName: organisationData?.societyName,
      location: locate,
      zone: zone,
      ward: wardNo?.name,
      type: typeNo?.name,
      saveInTable: true,
      updateInProfile: false,
      authority: authority ? authority : organisationData?.authority,
      roadWidth: roadWidth ? roadWidth : organisationData?.roadWidth,
      grossPlotArea: grossPlotArea ? grossPlotArea : organisationData?.plotArea,
    };

    try {
      const response = await auth.postCalculatedFSI(
        fields,
        handleFailure,
        handleSuccess
      );
      setSaveLoad(false);
    } catch (error) {
      console.error("FSI Rule Creation failed:", error);
      setSaveLoad(false);
      handleFailure();
    }
    fetchUsers(page, pageSize, searchKeyword);
    setOpenDialog(false);
    setEditForm(false);
    setCalculationDialog(false);
    setCondition(true);
    setSelectedWard({});
    setDataView({});
    reset();
  }

  const handleButtonClick = () => {
    setOpenDialogContent(false);
  };

  const handleCalculateClick = () => {
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setSelectedWard({});
    setDataView("");
    setEditForm(false);
    setCalculationDialog(false);
    reset();
    setOpenDialog(false);
    // reset();
  };

  const handleCloseViewDialog = () => {
    setViewDialogOpen(false);
    reset();
  };

  const handleEditClick = () => {
    setEditForm(true); // Updating state variable to true when the button is clicked
  };

  const handleCloseDeleteDialog = () => {
    setOpenDeleteDialog(false);
    fetchUsers(page, pageSize, searchKeyword);
  };

  const handleCloseDialogUpdate = () => {
    setOpenDialogUpdate(false);
    fetchUsers(page, pageSize, searchKeyword);
  };

  const [isActive, setIsActive] = useState(currentRow?.isActive);

  const handleOnChange = (event) => {
    setIsActive(event.target.checked);
  };

  const { canMenuPageSection, rbacRoles } = useRBAC();

  const router = useRouter();

  const canAccessCalculatedFSI = (requiredPermission) =>
    canMenuPageSection(
      MENUS.LEFT,
      PAGES.REDEVELOPMENT,
      PAGES.FSI_CALCULATOR,
      requiredPermission
    );

  useEffect(() => {
    if (rbacRoles != null && rbacRoles?.length > 0) {
      if (!canAccessCalculatedFSI(PERMISSIONS.READ)) {
        router.push("/401");
      }
    }
  }, [rbacRoles]);

  if (canAccessCalculatedFSI(PERMISSIONS.READ)) {
    return (
      <Grid>
        <div>
          <Box
            sx={{
              py: 3,
              px: 6,
              rowGap: 2,
              columnGap: 4,
              display: "flex",
              flexWrap: "wrap",
              alignItems: "center",
              justifyContent: "space-between",
            }}
          >
            <Grid container spacing={3} alignItems="center">
              <Grid item xs={12} sm={4} sx={{ textAlign: "flex-start" }}>
                <Typography variant="h6">List of Calculated FSI's</Typography>
              </Grid>

              <Grid item xs={12} sm={8}>
                <Grid
                  container
                  spacing={2}
                  alignItems="center"
                  justifyContent="flex-end"
                >
                  <Grid item xs={12} sm="auto">
                    <Button variant="contained" onClick={handleCalculateClick}>
                      Calculate New FSI
                    </Button>
                  </Grid>
                </Grid>
              </Grid>
            </Grid>
          </Box>

          <Dialog
            fullWidth
            scroll="paper"
            open={openDialog}
            onClose={handleCloseDialog}
            maxWidth={"md"}
          >
            <DialogTitle
              sx={{
                position: "relative",
                borderBottom: (theme) => `1px solid ${theme.palette.divider}`,
                p: (theme) => `${theme.spacing(1.5, 4)} !important`,
                display: "flex",
                alignItems: "center",
                justifyContent: "start",
                fontSize: { xs: 19, md: 20 },
                height: "50px", // Set fixed height for header
                marginLeft: { xl: 4, lg: 4, md: 4, sm: 4, xs: 4 },
              }}
            >
              FSI Calculator
              <Box sx={{ position: "absolute", top: "9px", right: "26px" }}>
                <IconButton
                  size="small"
                  onClick={handleCloseDialog}
                  sx={{
                    // p: "0.438rem",
                    borderRadius: 1,
                    color: "common.white",
                    backgroundColor: "primary.main",
                    "&:hover": {
                      backgroundColor: "#66BB6A",
                      transition: "background 0.5s ease, transform 0.5s ease",
                    },
                  }}
                >
                  <Icon icon="tabler:x" fontSize="1rem" />
                </IconButton>
              </Box>
            </DialogTitle>
            <DialogContent
              sx={{
                position: "relative",
                pt: (theme) => `${theme.spacing(8)} !important`,
                pb: (theme) => `${theme.spacing(5)} !important`,
                px: (theme) => [`${theme.spacing(8)} !important`],
              }}
            >
              {editForm ? (
                <Grid container spacing={3}>
                  {/* Society Name */}
                  <Grid container item xs={12} sm={6} alignItems="center">
                    <Grid item xs={6}>
                      <Typography style={field}>Society Name:</Typography>
                    </Grid>
                    <Grid item xs={6}>
                      <Typography className="data-field">
                        {organisationData?.societyName}
                      </Typography>
                    </Grid>
                  </Grid>

                  {/* Authority Field */}
                  <Grid item xs={12} sm={6}>
                    <FormControl fullWidth>
                      <Controller
                        name="authority"
                        control={control}
                        rules={{
                          required: {
                            value: true,
                            message: "Authority is required",
                          },
                          pattern: {
                            value: /^[a-zA-Z]*$/,
                            message: "Only alphabets are allowed",
                          },
                          maxLength: {
                            value: 50,
                            message: "Maximum length is 50 characters",
                          },
                        }}
                        defaultValue={organisationData?.authority}
                        value={authority}
                        render={({ field, fieldState, formState }) => {
                          const handleInputChange = (e) => {
                            const inputValue = e.target.value.replace(
                              /\s/g,
                              ""
                            );
                            if (/^[a-zA-Z]*$/.test(inputValue)) {
                              field.onChange(inputValue);
                              setAuthority(inputValue);
                            }
                          };

                          return (
                            <TextField
                              {...field}
                              size="small"
                              label="Authority"
                              InputLabelProps={{ shrink: true }}
                              placeholder="Enter your authority"
                              error={Boolean(fieldState.error)}
                              helperText={fieldState.error?.message}
                              inputProps={{ maxLength: 50 }}
                              onChange={handleInputChange}
                            />
                          );
                        }}
                      />
                    </FormControl>
                  </Grid>

                  {/* Location Dropdown */}
                  <Grid item xs={12} sm={6}>
                    <FormControl
                      fullWidth
                      error={Boolean(errors.selectedLocation)}
                    >
                      <Controller
                        name="selectedLocation"
                        control={control}
                        rules={{
                          required:
                            locate === null ? "Location is required" : false,
                        }}
                        render={({ field }) => (
                          <SelectAutoComplete
                            id="selectedLocation"
                            label="Select Location *"
                            nameArray={locationsOptions}
                            value={locate}
                            onChange={(event) => {
                              field.onChange(event.target?.value);
                              handleLocationChange(event.target.value);
                            }}
                          />
                        )}
                      />
                      {errors.selectedLocation && (
                        <FormHelperText sx={{ color: "error.main" }}>
                          {errors.selectedLocation.message}
                        </FormHelperText>
                      )}
                    </FormControl>
                  </Grid>

                  {/* Zone Field */}
                  <Grid container item xs={12} sm={6} spacing={2}>
                    <Grid item>
                      <Typography className="data-field">Zone:</Typography>
                    </Grid>
                    <Grid item>
                      <Typography style={{ fontWeight: "bold" }}>
                        {zone}
                      </Typography>
                    </Grid>
                  </Grid>

                  <Grid item xs={12} sm={6}>
                    <FormControl fullWidth error={Boolean(errors.type)}>
                      <Controller
                        name="type"
                        control={control}
                        rules={{
                          required:
                            !typeNo || typeNo === null
                              ? "type is required"
                              : false,
                        }}
                        render={({ field }) => (
                          <SelectAutoComplete
                            id="type"
                            label="Select type *"
                            nameArray={typeOptions}
                            value={typeNo}
                            onChange={(event) => {
                              field.onChange(event.target?.value);
                              setTypeNo(event.target.value);
                            }}
                          />
                        )}
                      />
                      {errors.type && (
                        <FormHelperText sx={{ color: "error.main" }}>
                          {errors.type.message}
                        </FormHelperText>
                      )}
                    </FormControl>
                  </Grid>

                  <Grid item xs={12} sm={6}>
                    <FormControl fullWidth error={Boolean(errors.ward)}>
                      <Controller
                        name="ward"
                        control={control}
                        rules={{
                          required:
                            !wardNo || wardNo === null
                              ? "ward is required"
                              : false,
                        }}
                        render={({ field }) => (
                          <SelectAutoComplete
                            id="ward"
                            label="Select ward *"
                            nameArray={wardOptions}
                            value={wardNo}
                            onChange={(event) => {
                              field.onChange(event.target?.value);
                              setWardNo(event.target?.value);
                            }}
                          />
                        )}
                      />
                      {errors.ward && (
                        <FormHelperText sx={{ color: "error.main" }}>
                          {errors.ward.message}
                        </FormHelperText>
                      )}
                    </FormControl>
                  </Grid>

                  {/* Road Width Field */}
                  <Grid item xs={12} sm={6}>
                    <FormControl fullWidth>
                      <Controller
                        name="roadWidth"
                        control={control}
                        rules={{
                          required: {
                            value: true,
                            message: "Road width is required",
                          },
                          validate: (value) =>
                            parseFloat(value) > 0 ||
                            "Road width must be greater than 0",
                          pattern: {
                            value: /^[0-9]*\.?[0-9]*$/,
                            message: "Only numeric values are allowed",
                          },
                        }}
                        defaultValue={organisationData?.roadWidth}
                        value={roadWidth}
                        render={({ field, fieldState, formState }) => {
                          const handleInputChange = (e) => {
                            const inputValue = e.target.value;
                            if (/^[0-9]*\.?[0-9]*$/.test(inputValue)) {
                              field.onChange(inputValue);
                              setRoadWidth(inputValue);
                            }
                          };

                          return (
                            <TextField
                              {...field}
                              size="small"
                              label="Road Width"
                              InputLabelProps={{ shrink: true }}
                              placeholder="Enter road width"
                              error={Boolean(fieldState.error)}
                              helperText={fieldState.error?.message}
                              inputProps={{ maxLength: 10 }}
                              onChange={handleInputChange}
                            />
                          );
                        }}
                      />
                    </FormControl>
                  </Grid>

                  {/* Plot Area Field */}
                  <Grid item xs={12} sm={6}>
                    <FormControl fullWidth>
                      <Controller
                        name="grossPlotArea"
                        control={control}
                        rules={{
                          required: {
                            value: true,
                            message: "Plot area is required",
                          },
                          validate: (value) =>
                            parseFloat(value) > 0 ||
                            "Plot area must be greater than 0",
                          pattern: {
                            value: /^[0-9]*\.?[0-9]*$/,
                            message: "Only numeric values are allowed",
                          },
                        }}
                        defaultValue={organisationData?.plotArea}
                        value={grossPlotArea}
                        render={({ field, fieldState, formState }) => {
                          const handleInputChange = (e) => {
                            const inputValue = e.target.value;
                            if (/^[0-9]*\.?[0-9]*$/.test(inputValue)) {
                              field.onChange(inputValue);
                              setGrossPlotArea(inputValue);
                            }
                          };

                          return (
                            <TextField
                              {...field}
                              size="small"
                              label="Net Plot Area in mts"
                              InputLabelProps={{ shrink: true }}
                              placeholder="Enter Net Plot Area"
                              inputProps={{ maxLength: 10 }}
                              error={Boolean(fieldState.error)}
                              helperText={fieldState.error?.message}
                              onChange={handleInputChange}
                            />
                          );
                        }}
                      />
                    </FormControl>
                  </Grid>
                </Grid>
              ) : (
                <>
                  <Grid container spacing={2}>
                    <Grid
                      item
                      xs={12}
                      style={{
                        display: "flex",
                        justifyContent: "right",
                        marginTop: "-20px",
                      }}
                    >
                      <Tooltip title="Edit">
                        <CustomAvatar
                          skin="light"
                          variant="rounded"
                          sx={{
                            mr: 5,
                            width: 34,
                            height: 34,
                            cursor: condition ? "pointer" : "not-allowed",
                          }}
                          onClick={condition ? handleEditClick : undefined}
                        >
                          <Icon icon="iconamoon:edit" />
                        </CustomAvatar>
                      </Tooltip>
                    </Grid>
                  </Grid>

                  <TableContainer
                    sx={{ padding: "4px 6px" }}
                    className="tableBody"
                  >
                    <Table>
                      <TableBody
                        sx={{
                          "& .MuiTableCell-root": {
                            p: `${theme.spacing(1.35, 1.125)} !important`,
                          },
                        }}
                      >
                        <TableRow>
                          <MUITableCell>
                            <Typography style={field}>Society Name:</Typography>
                          </MUITableCell>
                          <MUITableCell>
                            <Typography className="data-field">
                              {organisationData?.societyName}
                            </Typography>
                          </MUITableCell>
                          <MUITableCell>
                            <Typography style={field}>Authority:</Typography>
                          </MUITableCell>
                          <MUITableCell>
                            <Typography className="data-field">
                              {organisationData?.authority}
                            </Typography>
                          </MUITableCell>
                        </TableRow>
                        <TableRow>
                          <MUITableCell>
                            <Typography style={field}>Location:</Typography>
                          </MUITableCell>
                          <MUITableCell>
                            <Typography className="data-field">
                              {
                                listValues?.find(
                                  (item) =>
                                    item?.id === organisationData?.locationId
                                )?.name
                              }
                            </Typography>
                          </MUITableCell>
                          <MUITableCell>
                            <Typography style={field}>Zone:</Typography>
                          </MUITableCell>
                          <MUITableCell>
                            <Typography className="data-field">
                              {organisationData?.zone}
                            </Typography>
                          </MUITableCell>
                        </TableRow>

                        <TableRow>
                          <MUITableCell>
                            <Typography style={field}>Type:</Typography>
                          </MUITableCell>
                          <MUITableCell>
                            <Typography className="data-field">
                              {organisationData?.type}
                            </Typography>
                          </MUITableCell>
                          <MUITableCell>
                            <Typography style={field}>Ward:</Typography>
                          </MUITableCell>
                          <MUITableCell>
                            <Typography className="data-field">
                              {organisationData?.ward}
                            </Typography>
                          </MUITableCell>
                        </TableRow>

                        <TableRow>
                          <MUITableCell>
                            <Typography style={field}>RoadWidth:</Typography>
                          </MUITableCell>
                          <MUITableCell>
                            <Typography className="data-field">
                              {organisationData?.roadWidth}
                            </Typography>
                          </MUITableCell>
                          <MUITableCell>
                            <Typography style={field}>Plot area:</Typography>
                          </MUITableCell>
                          <MUITableCell>
                            <Typography className="data-field">
                              {organisationData?.plotArea}
                            </Typography>
                          </MUITableCell>
                        </TableRow>
                      </TableBody>
                    </Table>
                  </TableContainer>
                </>
              )}

              {calculationDialog && (
                <Grid container spacing={2}>
                  <Grid
                    item
                    xs={12}
                    sm={12}
                    style={{
                      fontWeight: "bold",
                      marginTop: "15px",
                      marginBottom: "2px",
                    }}
                  >
                    Calculated FSI Details:{" "}
                  </Grid>
                  <Grid item xs={12} sm={10}>
                    <Typography>
                      The maximum FSI under 33(7) / 33(7)B for your Society/
                      Plot is :
                    </Typography>
                  </Grid>
                  <Grid item xs={12} sm={2}>
                    <Typography style={{ fontWeight: "bold" }}>
                      {calculatedData?.fungibleFSI}
                    </Typography>
                  </Grid>
                  <Grid item xs={12} sm={10}>
                    <Typography>
                      {" "}
                      Built Up Area available on your Plot as per FSI (sq mts) :{" "}
                    </Typography>
                  </Grid>
                  <Grid item xs={12} sm={2}>
                    <Typography style={{ fontWeight: "bold" }}>
                      {calculatedData?.buildupArea}
                    </Typography>
                  </Grid>
                </Grid>
              )}
            </DialogContent>
            <DialogActions
              sx={{
                justifyContent: "end",
                borderTop: (theme) => `1px solid ${theme.palette.divider}`,
                p: (theme) => `${theme.spacing(2.5)} !important`,
                height: "50px", // Set fixed height for footer
                marginRight: { xl: 4, lg: 4, md: 4, sm: 4, xs: 4 },
              }}
            >
              {editForm ? (
                <>
                  <Button
                    display="flex"
                    justifyContent="center"
                    variant="contained"
                    color="primary"
                    onClick={handleSubmit(handleUpdateProfile)}
                  >
                    {saveLoad ? (
                      <CircularProgress color="inherit" size={24} />
                    ) : (
                      "Save"
                    )}
                  </Button>

                  <Button
                    display="flex"
                    justifyContent="center"
                    variant="contained"
                    color="primary"
                    onClick={handleSubmit(Calculation)}
                  >
                    {calculateLoad ? (
                      <CircularProgress color="inherit" size={24} />
                    ) : (
                      " Calculate FSI"
                    )}
                  </Button>
                </>
              ) : (
                <>
                  <Button
                    display="flex"
                    justifyContent="center"
                    variant="contained"
                    color="primary"
                    onClick={handleSubmit(SaveCalculation)}
                    disabled={!isDataViewComplete}
                  >
                    {saveLoad ? (
                      <CircularProgress color="inherit" size={24} />
                    ) : (
                      "Save"
                    )}
                  </Button>

                  <Button
                    display="flex"
                    justifyContent="center"
                    variant="contained"
                    color="primary"
                    onClick={handleSubmit(Calculation)}
                    disabled={!isDataViewComplete}
                  >
                    {calculateLoad ? (
                      <CircularProgress color="inherit" size={24} />
                    ) : (
                      " Calculate FSI"
                    )}
                  </Button>
                </>
              )}
            </DialogActions>
          </Dialog>

          <Divider />
          <CardContent>
            <div style={{ height: 480, width: "100%" }}>
              {loading ? (
                <Box
                  display="flex"
                  justifyContent="center"
                  alignItems="center"
                  height="60vh"
                >
                  <FallbackSpinner />
                </Box>
              ) : (
                <DataGrid
                  rows={userList}
                  columns={columns}
                  pagination
                  pageSize={pageSize}
                  page={page - 1}
                  rowsPerPageOptions={rowsPerPageOptions}
                  rowCount={rowCount}
                  paginationMode="server"
                  onPageChange={handlePageChange}
                  onPageSizeChange={handlePageSizeChange}
                  rowHeight={38}
                  headerHeight={38}
                  components={{
                    NoRowsOverlay: () => (
                      <Typography
                        variant="body1"
                        align="center"
                        sx={{ marginTop: "150px" }}
                      >
                        {userList?.length === 0 ? "No Data" : "No Rows"}
                      </Typography>
                    ),
                  }}
                />
              )}
            </div>
          </CardContent>

          <EditDialog
            open={viewDialogOpen}
            onClose={handleCloseViewDialog}
            formData={fsiDataDetails}
            page={page}
            pageSize={pageSize}
            searchKeyword={searchKeyword}
            fetchUsers={fetchUsers}
          />
        </div>
        <FSIDeleteDialog
          open={openDeleteDialog}
          onClose={handleCloseDeleteDialog}
          data={currentRow}
          fetchUsers={fetchUsers}
          page={page}
          pageSize={pageSize}
          searchKeyword={searchKeyword}
        />

        <FSIUpdateDialog
          open={openDialogUpdate}
          onClose={handleCloseDialogUpdate}
          data={currentRow}
          fetchUsers={fetchUsers}
          page={page}
          pageSize={pageSize}
          searchKeyword={searchKeyword}
        />
        <Dialog
          open={openDialogContent}
          onClose={handleButtonClick}
          aria-labelledby="alert-dialog-title"
          aria-describedby="alert-dialog-description"
          PaperProps={{
            sx: {
              p: (theme) => `${theme.spacing(2.5)} !important`,
              backgroundColor: (theme) => theme.palette.primary.background,
            },
          }}
        >
          <Box
            sx={{
              width: "100%",
              borderRadius: 1,
              textAlign: "center",
              border: (theme) => `1px solid ${theme.palette.divider}`,
              borderColor: "primary.main",
            }}
          >
            <DialogContent>
              <DialogContentText
                id="alert-dialog-description"
                color="primary.main"
              >
                <div dangerouslySetInnerHTML={{ __html: dialogMessage }} />
              </DialogContentText>
            </DialogContent>
            <DialogActions>
              <Button
                variant="contained"
                onClick={handleButtonClick}
                sx={{ margin: "auto", width: 100 }}
              >
                Okay
              </Button>
            </DialogActions>
          </Box>
        </Dialog>
        <Dialog
          open={openUpdateDialog}
          onClose={handleUpdateDialog}
          aria-labelledby="alert-dialog-title"
          aria-describedby="alert-dialog-description"
          PaperProps={{
            sx: {
              p: (theme) => `${theme.spacing(2.5)} !important`,
              backgroundColor: (theme) => theme.palette.primary.background,
            },
          }}
        >
          <Box
            sx={{
              width: "100%",
              borderRadius: 1,
              textAlign: "center",
              border: (theme) => `1px solid ${theme.palette.divider}`,
              borderColor: "primary.main",
            }}
          >
            <DialogContent>
              <DialogContentText
                id="alert-dialog-description"
                color="primary.main"
              >
                <div dangerouslySetInnerHTML={{ __html: dialogMessage }} />
              </DialogContentText>
            </DialogContent>
            <DialogActions>
              <Button
                variant="contained"
                onClick={handleSubmit(SaveCalculation)}
                sx={{ margin: "auto", width: 100 }}
              >
                Yes
              </Button>
              <Button
                variant="contained"
                onClick={SaveCalculate}
                sx={{ margin: "auto", width: 100 }}
              >
                No
              </Button>
            </DialogActions>
          </Box>
        </Dialog>
      </Grid>
    );
  } else {
    return null;
  }
};
export default Calculator;
