import { DataGrid } from "@mui/x-data-grid";
import { useContext, useEffect, useState } from "react";
import authConfig from "src/configs/auth";
import { getAuthorizationHeaders, getUrl } from "src/helpers/utils";
import { useAuth } from "src/hooks/useAuth";
import { useRBAC } from "src/pages/permission/RBACContext";
import {
  Button,
  Card,
  CardContent,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Grid,
  IconButton,
  Tooltip,
  Typography,
} from "@mui/material";
import Icon from "src/@core/components/icon";

import CustomAvatar from "src/@core/components/mui/avatar";
import axios from "axios";


import { Box } from "@mui/system";

import { AuthContext } from "src/context/AuthContext";
import Index from "../conversations";

const Society = () => {
  const { user, listValues,taskData,setTaskData,taskDataDetails } = useContext(AuthContext);

  const [isConversationDialogOpen, setConversationDialogOpen] = useState(false);

  const [expanded, setExpanded] = useState(true);

  const handleToggle = () => {
    setExpanded(!expanded);
  };
  // Constants
  const rowsPerPageOptions = [10, 15, 20, 25, 50, 100];
  const [employeesData, setEmployeesData] = useState([]);

  // Define columns
  const columns = [
    {
        field: "name",
        minWidth: 90, 
        headerName: "Name",
        flex: 0.2, // Adjust the flex as needed
        valueGetter: (params) => {
          const { firstName, lastName } = params.row;
          return `${firstName} ${lastName}`;
        },
      },
    // { field: "email", minWidth: 175, headerName: "Email", flex: 0.2 },
    { field: "mobileNumber", minWidth: 125, headerName: "Mobile No", flex: 0.18,valueGetter: (params) => params.row.basicProfile.mobileNumber, },
    // nextConversationDate
    { field: "nextConversationDate", minWidth: 165, headerName: "Follow up Date", flex: 0.16 },
    {
      flex: 0.15,
      minWidth: 120,
      field: "assignedTo",
      headerName: "Assigned To",
      renderCell: (params) => {
        const assignedTo = employeesData.find(item => item.id === params.row.assignedTo);
        return <span>{assignedTo ? assignedTo.name : ""}</span>;
      }
    },
    {
        field: "age",
        headerName: "Age",
        minWidth: 60,
        flex: 0.1,
        valueGetter: params => {
          const nextConversationDate = params.row.nextConversationDate;
          if (nextConversationDate) {
            const today = new Date();
            const nextDate = new Date(nextConversationDate);
            // Extract only date parts
            const todayDate = new Date(today.getFullYear(), today.getMonth(), today.getDate());
            const nextDateOnly = new Date(nextDate.getFullYear(), nextDate.getMonth(), nextDate.getDate());
            // Calculate the difference in milliseconds
            const timeDiff = Math.abs(nextDateOnly.getTime() - todayDate.getTime());
            // Convert milliseconds to days
            const ageInDays = Math.ceil(timeDiff / (1000 * 3600 * 24));
            return ageInDays;
          }
          return "";
        }
      },
    { field: "createdOn", minWidth: 125, headerName: "Created On", flex: 0.13,
    valueGetter: params => {
        const createdOn = params.row.basicProfile.createdOn;
        if (createdOn) {
          const date = new Date(createdOn);
          return date.toISOString().split('T')[0];
        }
        return "";
      }
    },
    { field: "createdBy", minWidth: 125, headerName: "Created By", flex: 0.13,
    renderCell: (params) => {
        const createdBy = employeesData.find(item => item.id === params.row.basicProfile.createdBy);
        return <span>{createdBy ? createdBy.name : ""}</span>;
      }
    },
    {
      flex: 0.077,
      field: "edit",
      headerName: "Actions",
      sortable: false,
      minWidth: 120,
      disableClickEventBubbling: true,
      renderCell: (params) => {
        const onClick = () => {
        //   openEditDialog(params.row);
        openConversationDialog(params.row);
          const row = params.row;
        };

        return (
          <div
            style={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
            }}
          >
            <Tooltip title="Edit or View">
              <CustomAvatar
                skin="light"
                variant="rounded"
                sx={{
                  mr: { xs: 2, lg: 4 },
                  width: 34,
                  height: 34,
                  cursor: "pointer",
                }}
                onClick={onClick}
              >
                <Icon icon="iconamoon:edit" />
              </CustomAvatar>
            </Tooltip>
          </div>
        );
      },
    },
  ];


  const auth = useAuth();
  const [userList, setUserList] = useState([]);
  const [pageSize, setPageSize] = useState(rowsPerPageOptions[0]);
  const [loading, setLoading] = useState(false);
  const [page, setPage] = useState(1);
  const [rowCount, setRowCount] = useState(0);
  const [searchKeyword, setSearchKeyword] = useState("");
  const [currentRow, setCurrentRow] = useState("");

  
  useEffect(() => {
    // Fetch all employees
    axios({
      method: "get",
      url: getUrl(authConfig.selectDropdownNew) + "?selectionType=EMPLOYEES",
      headers: getAuthorizationHeaders(),
    })
      .then((res) => {
        setEmployeesData(res.data.data);
      })
      .catch((err) => console.log("Employees error", err));
  }, []);

  useEffect(() => {
    fetchUsers(page, pageSize, searchKeyword);
  }, [page, pageSize, searchKeyword]);

  const fetchUsers = async (currentPage, currentPageSize, searchKeyword) => {
    setLoading(true);

   // const url = getUrl() 
    const headers = getAuthorizationHeaders();

    const data = {
      page: currentPage,
      pageSize: currentPageSize,
      searchKeyword: searchKeyword,
      serviceProvidersLastSevenDays:true
    };

    try {
      const response = await axios({
        method: "post",
        url: url,
        headers: headers,
        data: data,
      });

      if (response?.data) {
        setUserList(response.data?.conversations || []);
        setRowCount(response.data?.rowCount || 0);
      } else {
        console.error("Unexpected API response format:", response);
      }
    } catch (error) {
      console.error("Error fetching users:", error);
    } finally {
      setLoading(false);
    }
  };

 

  const handlePageChange = (direction) => {
    if (direction === page) {
      setPage(page + 1);
    } else {
      setPage(page - 1);
    }
  };

  const handlePageSizeChange = (params) => {
    if (params) {
      setPageSize(params);
      setPage(1);
    }
  };

  
  const closeConversationDialog = () => {
    setConversationDialogOpen(false);
    fetchUsers();
  };


  const openConversationDialog = (row) => {
    setConversationDialogOpen(true);
    setCurrentRow(row);
  };

  return (
    <>
         <Dialog
          fullScreen
          open={isConversationDialogOpen}
          onClose={closeConversationDialog}
        >

<DialogTitle
     sx={{
        position: "relative",
        borderBottom: (theme) => `1px solid ${theme.palette.divider}`,
        p: (theme) => `${theme.spacing(1.5, 4)} !important`,
        display: "flex",
        alignItems: "center",
        justifyContent: "start" ,
        fontSize: { xs: 19, md: 20 },
      }}
    >
        Conversations
          <Box sx={{ position: "absolute", top: "0px", right: "5px" }}>
          <IconButton
              size="small"
              onClick={closeConversationDialog}
              sx={{
                // p: "0.438rem",
                borderRadius: 1,
                mr:6,
                mt:1,
                color: "text.primary",
                backgroundColor: "action.selected",
                "&:hover": {
                  backgroundColor: (theme) =>
                    `rgba(${theme.palette.customColors.main}, 0.16)`,
                },
              }}
            >
              <Icon icon="tabler:x" fontSize="1rem" />
            </IconButton>
          </Box>
        </DialogTitle>
          
          <DialogContent>
            {employeesData && 
            <>
              <Index currentRow={currentRow} setCurrentRow={setCurrentRow} closeConversationDialog={close} employeeData={employeesData}/>
            </>
            }
            
          </DialogContent>

          <DialogActions sx={{ justifyContent: "center" }}>
           
          </DialogActions>
        </Dialog>
    
          <Box style={{ height: 380, width: "100%" }}>
            <DataGrid
              rows={userList || []}
              columns={columns}
              autoHeight
              pagination
              pageSize={pageSize}
              page={page - 1}
              rowsPerPageOptions={rowsPerPageOptions}
              rowCount={rowCount}
              paginationMode="server"
              onPageChange={handlePageChange}
              onPageSizeChange={handlePageSizeChange}
              getRowId={(row) => row.userId} 
              rowHeight={38}
              headerHeight={38} 
            />
          </Box>
       
    </>
  );
};

export default Society;
