import ArchitectureIcon from "@mui/icons-material/Architecture";
import AssignmentIcon from "@mui/icons-material/Assignment";
import ContactMailIcon from "@mui/icons-material/ContactMail";
import HomeIcon from "@mui/icons-material/Home";
import InfoIcon from "@mui/icons-material/Info";
import MapIcon from "@mui/icons-material/Map";
import PreviewIcon from "@mui/icons-material/Preview";
import {
  Box,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Typography,
  useMediaQuery,
} from "@mui/material";
import { useTheme } from "@mui/material/styles";
import axios from "axios";
import { useCallback, useContext, useEffect, useState } from "react";
import authConfig from "src/configs/auth";
import { AuthContext } from "src/context/AuthContext";
import { getAuthorizationHeaders, getUrl } from "src/helpers/utils";
import dynamic from "next/dynamic";
import { extractNonNullFields } from "src/@core/components/custom-components/ExtractNonNullFields";
import { fetchIpAddress } from "src/@core/components/custom-components/FetchIpAddress";

// Lazy load components
const SocietyDetails = dynamic(() => import("./SocietyDetails"));
const SocietyMemberInformation = dynamic(() =>
  import("./SocietyMemberInformation")
);
const BusinessInformation = dynamic(() => import("./BusinessInformation"));
const LandDetails = dynamic(() => import("./LandDetails"));
const FSIDetails = dynamic(() => import("./FSIDetails"));
const Requirements = dynamic(() => import("./Requirements"));
const PreviewSection = dynamic(() => import("./LongFormPreview"));

const LongFormContent = ({ employeesData, initialFormData }) => {
  const { user, patchCHSProfile } = useContext(AuthContext);
  const [currentStep, setCurrentStep] = useState(0);
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));
  const [dialogMessage, setDialogMessage] = useState("");
  const [dialogSuccess, setDialogSuccess] = useState(false);
  const [formData, setFormData] = useState(initialFormData());

  const hasRedOption = formData.societyDetails?.registeredFor.includes("RED");

  const sections = [
    {
      id: "societyDetails",
      title: "Society Information",
      component: SocietyDetails,
      icon: <HomeIcon />,
    },
    {
      id: "societyMemberInformation",
      title: "Society Member Information",
      component: SocietyMemberInformation,
      icon: <ContactMailIcon />,
    },
    {
      id: "businessInformation",
      title: "Business Information",
      component: BusinessInformation,
      icon: <InfoIcon />,
    },
    ...(hasRedOption
      ? [
          {
            id: "landDetails",
            title: "Land Details",
            component: LandDetails,
            icon: <MapIcon />,
          },
          {
            id: "fsi",
            title: "FSI",
            component: FSIDetails,
            icon: <ArchitectureIcon />,
          },
          {
            id: "requirements",
            title: "Requirements",
            component: Requirements,
            icon: <AssignmentIcon />,
          },
        ]
      : []),
    {
      id: "preview",
      title: "Preview",
      component: PreviewSection,
      icon: <PreviewIcon />,
    },
  ];

  // Handle updates for individual sections
  const handleFieldUpdate = useCallback((sectionId, updatedData) => {
    setFormData((prevData) => ({ ...prevData, [sectionId]: updatedData }));
  }, []);

  const handleStepChange = (index) => {
    setCurrentStep(index);
  };

  const handleNext = () => {
    if (currentStep < sections?.length - 1) {
      setCurrentStep((prev) => prev + 1);
    }
  };

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep((prev) => prev - 1);
    }
  };
  async function handleSaveProgress() {
    const payload = extractNonNullFields(formData, profileData);

    const ipAddress = await fetchIpAddress();
    payload.ipAddress = ipAddress;

    let orgId = user?.orgId;

    await patchCHSProfile(
      orgId,
      payload,
      () => {
        const message = `Society Profile Updated Successfully`;
        setDialogMessage(message);
        setDialogSuccess(true);
      },
      () => {
        const message = `Failed to update Society Profile`;
        setDialogMessage(message);
        setDialogSuccess(true);
      }
    );

    setCurrentStep(0);
  }

  async function handleSubmit() {
    const payload = extractNonNullFields(formData, profileData);

    const ipAddress = await fetchIpAddress();
    payload.ipAddress = ipAddress;

    let orgId = user?.orgId;

    await patchCHSProfile(
      orgId,
      payload,
      () => {
        const message = `Society Profile Updated Successfully`;
        setDialogMessage(message);
        setDialogSuccess(true);
      },
      () => {
        const message = `Failed to update Society Profile`;
        setDialogMessage(message);
        setDialogSuccess(true);
      }
    );

    setCurrentStep(0);
  }

  const [profileData, setProfileData] = useState({});
  useEffect(() => {
    const url =
      getUrl(authConfig.organisationsEndpoint) +
      "/organisation-individual-chs-data/" +
      user?.orgId;

    axios({
      method: "get",
      url: url,
      headers: getAuthorizationHeaders({
        accept: authConfig.ORGANISATION_META_DATA_GET_BY_ID_V1,
      }),
    })
      .then((res) => {
        setProfileData(res.data);
        const chsProfileDTO = res.data?.chsProfileDTO;

        const matchedCommitteeMember =
          res.data.societyCommitteeMemberInformation?.find(
            (member) => member.id === chsProfileDTO?.userId
          );

        const filteredCommitteeMemberList =
          res.data.societyCommitteeMemberInformation?.filter(
            (member) => member.id !== chsProfileDTO?.userId
          );

        setFormData({
          societyDetails: {
            name: chsProfileDTO?.name || "",
            googleMapLocation: chsProfileDTO?.googleMapLocation || "",
            enrolledDate: chsProfileDTO?.enrolledDate || "",
            registeredFor: chsProfileDTO?.registeredFor || [],
            requisition: chsProfileDTO?.requisition?.toLowerCase() || "",
            readiness: chsProfileDTO?.readiness?.toLowerCase() || "",
            societyAddress: chsProfileDTO?.societyAddress || "",
            roadWidth: chsProfileDTO?.roadWidth || "",
            grossPlotArea: chsProfileDTO?.grossPlotArea || "",
            authority: chsProfileDTO?.authority || "",
            teamMember: chsProfileDTO?.teamMember || "",
            plotCTSNo: chsProfileDTO?.plotCTSNo || "",
            locationId: chsProfileDTO?.locationId || "",
            zone: chsProfileDTO?.zone || "",
            pinCode: chsProfileDTO?.pinCode || "",
          },
          societyMemberInformation: {
            societyMemberName: matchedCommitteeMember?.name || "",
            societyMemberDesignation: matchedCommitteeMember?.designation || "",
            societyMemberContactNumber:
              matchedCommitteeMember?.contactNumber || "",
            loginEmail: matchedCommitteeMember?.email || "",
            alternateNumber: matchedCommitteeMember?.alternateNumber || "",
            fromDate: matchedCommitteeMember?.fromDate || "",
            toDate: matchedCommitteeMember?.toDate || "",
            isActive: matchedCommitteeMember?.isActive || false,
            societyCommitteeMemberInformationList:
              filteredCommitteeMemberList || [],
          },
          businessInformation: {
            bankName: chsProfileDTO?.bankName || "",
            branch: chsProfileDTO?.branch || "",
            accountNumber: chsProfileDTO?.accountNumber || "",
            ifscCode: chsProfileDTO?.ifscCode || "",
            gstNo: chsProfileDTO?.gstNo || "",
            panNo: chsProfileDTO?.panNo || "",
            stateName: chsProfileDTO?.stateName || "",
            doYouHaveGstNo:
              res.data?.chsProfileDTO?.doYouHaveGstNo?.toLowerCase() || "",
          },
          landDetails: {
            builtUpAreaResidential: chsProfileDTO?.builtUpAreaResidential || "",
            builtUpAreaCommercial: chsProfileDTO?.builtUpAreaCommercial || "",
            noOfResidence: chsProfileDTO?.noOfResidence || "",
            noOfCommercial: chsProfileDTO?.noOfCommercial || "",
          },
          fsi: {
            buildingAge: chsProfileDTO?.buildingAge || "",
            fsiConsumedFsi: chsProfileDTO?.fsiConsumedFsi || "",
            fsi_AvailableFsi: chsProfileDTO?.fsi_AvailableFsi || "",
            fsi_PermissibleFsi: chsProfileDTO?.fsi_PermissibleFsi || "",
            heightRestriction: chsProfileDTO?.heightRestriction || "",
            scheme: chsProfileDTO?.scheme || "",
            dpRestrictions: chsProfileDTO?.dpRestrictions || "",
            litigationsOrEncroachment:
              chsProfileDTO?.litigationsOrEncroachment || "",
          },
          requirements: {
            requirements_ExtraArea: chsProfileDTO?.requirements_ExtraArea || "",
            requirements_Rent: chsProfileDTO?.requirements_Rent || "",
            requirements_Corpus: chsProfileDTO?.requirements_Corpus || "",
            notes: chsProfileDTO?.notes || "",
            leadGivenTo: chsProfileDTO?.leadGivenTo || "",
          },
        });
      })
      .catch((err) => console.log("CHS Data error", err));
  }, []);

  const renderSection = () => {
    const ActiveComponent = sections[currentStep]?.component;
    if (!ActiveComponent) return null;
    const sectionId = sections[currentStep].id;

    if (Object.keys(profileData)?.length === 0) {
      return null; // or a loading indicator like <Loader />
    }
    return (
      <ActiveComponent
        formData={formData} // Pass the specific section's data
        onUpdate={(updatedData) => handleFieldUpdate(sectionId, updatedData)} // Update the specific section's data
        employeesData={employeesData}
      />
    );
  };

  const handleCloseDialog = () => {
    setDialogSuccess(false);
  };

  return (
    <>
      {/* Parent Container */}
      <Box
        sx={{
          display: "flex",
          flexDirection: "column",
          height: {
            sm: 440, // Height for small screens (tablets)
            md: 480, // Height for medium screens (desktops)
            lg: 600, // Height for large screens
            xl: 600, // Height for extra large screens
          },
          position: "relative",
          overflow: "hidden",
          mt: 2,
        }}
      >
        {/* Content Area */}
        <Box
          sx={{ display: "flex", flex: 1, width: "90vw", overflow: "hidden" }}
        >
          <Box
            sx={{
              borderRight: isMobile ? "none" : 1,
              borderColor: "divider",
              overflowY: "auto",
            }}
          >
            <List
              sx={{
                display: "flex",
                flexDirection: isMobile ? "row" : "column", // Horizontal list in mobile
                overflowX: isMobile ? "auto" : "hidden", // Horizontal scroll for mobile
                p: isMobile ? 1 : 0,
                gap: isMobile ? 1 : 0,
              }}
            >
              {sections?.map((section, index) => (
                <ListItem
                  button
                  key={section.id}
                  selected={currentStep === index}
                  onClick={() => handleStepChange(index)}
                  sx={{
                    backgroundColor:
                      currentStep === index ? "#1B5E20" : "inherit",
                    color: currentStep === index ? "#ffffff" : "inherit",
                    "&:hover": {
                      backgroundColor:
                        currentStep === index
                          ? "#0B3D0B"
                          : "rgba(0, 0, 0, 0.04)",
                      color: currentStep === index ? "#ffffff" : "inherit",
                    },
                  }}
                >
                  <ListItemIcon
                    sx={{
                      color: currentStep === index ? "#FFFFFF" : "#757575",
                      backgroundColor:
                        currentStep === index ? "#66BB6A" : "#F5F5F5",
                      borderRadius: "4px",
                      width: "30px",
                      height: "30px",
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center",
                      marginRight: 2,
                      transition:
                        "background-color 0.3s ease, color 0.3s ease, transform 0.3s ease",
                      transform:
                        currentStep === index ? "scale(1.1)" : "scale(1)",
                    }}
                  >
                    {section.icon}
                  </ListItemIcon>

                  <ListItemText
                    primary={
                      <Typography
                        variant="body1"
                        fontWeight={currentStep === index ? "bold" : "normal"}
                      >
                        {section.title}
                      </Typography>
                    }
                  />
                </ListItem>
              ))}
            </List>
            {isMobile && <Box sx={{ flex: 1 }}>{renderSection()}</Box>}
          </Box>
          {!isMobile && (
            <Box
              sx={{
                flex: 1,
                display: "flex",
                flexDirection: "column",
                p: 3,
                overflow: "auto",
              }}
            >
              {/* Section Content */}
              <Box sx={{ flex: 1 }}>{renderSection()}</Box>
            </Box>
          )}
        </Box>

        {/* Divider and DialogActions */}
        <Box
          sx={{
            borderTop: 1,
            borderColor: "divider",
            display: "flex",
            justifyContent: currentStep === 0 ? "flex-end" : "space-between",
            alignItems: "center",
            p: 2,
            flexShrink: 0,
            gap: 2,
          }}
        >
          {currentStep !== 0 && (
            <Button
              onClick={handlePrevious}
              variant="outlined"
              color="secondary"
            >
              Previous
            </Button>
          )}

          <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
            {currentStep === sections?.length - 1 ? (
              <></>
            ) : (
              <>
                <Button color="primary" onClick={handleSaveProgress}>
                  Save&nbsp;Progress
                </Button>
              </>
            )}
            {currentStep === sections?.length - 1 ? (
              <Button
                onClick={handleSubmit}
                variant="contained"
                color="primary"
              >
                Submit
              </Button>
            ) : (
              <Button onClick={handleNext} variant="contained" color="primary">
                Next
              </Button>
            )}
          </Box>
        </Box>
      </Box>

      <Dialog
        open={dialogSuccess}
        onClose={handleCloseDialog}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
        PaperProps={{
          sx: {
            p: (theme) => `${theme.spacing(2.5)} !important`,
            backgroundColor: (theme) => theme.palette.primary.background,
          },
        }}
      >
        <Box
          sx={{
            width: "100%",
            borderRadius: 1,
            textAlign: "center",
            border: (theme) => `1px solid ${theme.palette.divider}`,
            borderColor: "primary.main",
          }}
        >
          <DialogContent>
            <DialogContentText
              id="alert-dialog-description"
              color="primary.main"
            >
              <div dangerouslySetInnerHTML={{ __html: dialogMessage }} />
            </DialogContentText>
          </DialogContent>
          <DialogActions>
            <Button
              variant="contained"
              onClick={handleCloseDialog}
              sx={{ margin: "auto", width: 100 }}
            >
              Ok
            </Button>
          </DialogActions>
        </Box>
      </Dialog>
    </>
  );
};

export default LongFormContent;
