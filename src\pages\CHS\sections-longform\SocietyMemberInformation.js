import { useContext, useEffect, useRef, useState } from "react";

// ** MUI Imports
import CheckCircleOutlineIcon from "@mui/icons-material/CheckCircleOutline";
import Box from "@mui/material/Box";
import Button from "@mui/material/Button";
import FormControl from "@mui/material/FormControl";
import Grid from "@mui/material/Grid";
import Menu from "@mui/material/Menu";
import MenuItem from "@mui/material/MenuItem";
import TextField from "@mui/material/TextField";
import Tooltip from "@mui/material/Tooltip";
import Typography from "@mui/material/Typography";
import { DataGrid } from "@mui/x-data-grid";
import MobileNumberValidation from "src/@core/components/custom-components/MobileNumberValidation";
import Icon from "src/@core/components/icon";
import CustomAvatar from "src/@core/components/mui/avatar";
// ** Third Party Imports
import { Autocomplete, Divider } from "@mui/material";
import axios from "axios";
import { Controller, useForm } from "react-hook-form";
import NameTextField from "src/@core/components/custom-components/NameTextField";
import authConfig from "src/configs/auth";
import { getAuthorizationHeaders, getUrl } from "src/helpers/utils";
import SocietyMembers from "./SocietyMembers";
import { AuthContext } from "src/context/AuthContext";
import CustomChip from "src/@core/components/mui/chip";
const userStatusObj = {
  true: "Active",
  false: "InActive",
};
const SocietyMemberInformation = ({ formData, onUpdate }) => {
  const {
    control,
    watch,
    formState: { errors },
  } = useForm({
    defaultValues: formData?.societyMemberInformation || {}, // Initialize form fields with existing data
  });

  const { getAllListValuesByListNameId } = useContext(AuthContext);
  const [designation, setDesignation] = useState(
    formData?.societyMemberInformation?.societyMemberDesignation || null
  );

  const rowsPerPageOptions = [10, 15, 20, 25, 50, 100];
  const [pageSize, setPageSize] = useState(rowsPerPageOptions[0]);
  const [page, setPage] = useState(1);
  const [rowCount, setRowCount] = useState(0);
  const [societyMembers, setSocietyMembers] = useState(
    formData?.societyMemberInformation?.societyCommitteeMemberInformationList ||
      []
  );

  const [currentRow, setCurrentRow] = useState(null);
  const [societyMemberOpen, setSocietyMemberOpen] = useState(false);

  // Watch all fields for changes
  const watchedFields = watch();

  // Update formData on any change
  const previousWatchedFields = useRef();

  useEffect(() => {
    // Compare previous watched fields with current watched fields
    const hasWatchedFieldsChanged = JSON.stringify(previousWatchedFields.current) !== JSON.stringify(watchedFields);
    if (hasWatchedFieldsChanged || designation !== previousWatchedFields.current?.designation || societyMembers !== previousWatchedFields.current?.societyMembers) {
      onUpdate({
        ...watchedFields,
        societyMemberDesignation: designation, // Add extra field
        societyCommitteeMemberInformationList: societyMembers, // Add extra field
      });
      previousWatchedFields.current = watchedFields; // Update the ref with the latest value
    }
  }, [watchedFields, designation, societyMembers, onUpdate]);

  const handleSocietyMemberDialogOpen = () => {
    setCurrentRow(null);
    setSocietyMemberOpen(true);
  };
  const handleSocietyMemberDialogClose = () => {
    setSocietyMemberOpen(false);
    setCurrentRow(null);
  };

  const [designationList, setDesignationList] = useState([]);

    const handleError = (error) => {
      console.error("designation get error:", error);
    };
  
    useEffect(() => {
      if (!!authConfig) {
        getAllListValuesByListNameId(
          authConfig.ChsDesignation,
          (data) =>
            setDesignationList(
              data?.listValues?.map((item) => ({
                value: item.id,
                key: item.listValue,
              }))
            ),
          handleError
        );
      }
    }, [authConfig]);

  const handleDesignationChange = (newValue) => {
    setDesignation(newValue);
  };

  const [menu, setMenu] = useState(null);

  const handleCloseMenuItems = () => {
    setMenu(null);
  };

   const mapIsActiveToLabel = (isActive) => {
    return userStatusObj[isActive] || "Unknown";
  };


  const cols = [
    {
      field: "name",
      headerName: "Name",
      minWidth: 105,
      flex: 1,
      renderCell: (params) => (
        <Tooltip title={params.row.name}>
          <span>{params.row.name}</span>
        </Tooltip>
      ),
    },
    {
      field: "contactNumber",
      headerName: "Contact No.",
      minWidth: 105,
      flex: 1,
      renderCell: (params) => (
        <Tooltip title={params.row.contactNumber}>
          <a
            href={`tel:${params.row.contactNumber}`}
            style={{ textDecoration: "none", color: "blue" }}
          >
            {params.row.contactNumber}
          </a>
        </Tooltip>
      ),
    },
    {
      field: "alternateNumber",
      headerName: "Alternate No.",
      minWidth: 105,
      flex: 1,
      renderCell: (params) => (
        <Tooltip title={params.row.alternateNumber}>
          <a
            href={`tel:${params.row.alternateNumber}`}
            style={{ textDecoration: "none", color: "blue" }}
          >
            {params.row.alternateNumber}
          </a>
        </Tooltip>
      ),
    },
    {
      field: "email",
      headerName: "Email",
      minWidth: 110,
      flex: 1,
      renderCell: (params) => (
        <Tooltip title={params.row.email}>
          <a
            href={`mailto:${params.row.email}`}
            style={{ textDecoration: "none", color: "blue" }}
          >
            {params.row.email}
          </a>
        </Tooltip>
      ),
    },
    {
      field: "designation",
      headerName: "Designation",
      minWidth: 95,
      flex: 1,
      renderCell: (params) => {
        const designation = designationList?.find(
          (item) => item?.value === params?.row?.designation
        );
        return (
          <Tooltip title={designation?.key || ""}>
            <span>{designation ? designation?.key : ""}</span>
          </Tooltip>
        );
      },
    },
    {
      field: "fromDate",
      headerName: "From Date",
      minWidth: 80,
      flex: 1,
      renderCell: (params) => (
        <Tooltip title={params.row.fromDate}>
          <span>{params.row.fromDate}</span>
        </Tooltip>
      ),
    },
    {
      field: "toDate",
      headerName: "To Date",
      minWidth: 80,
      flex: 1,
      renderCell: (params) => (
        <Tooltip title={params.row.toDate}>
          <span>{params.row.toDate}</span>
        </Tooltip>
      ),
    },
     {
      field: "isActive",
      headerName: "Is Active",
      flex: 0.13,
      minWidth: 100,
      renderCell: ({ row }) => {
        return (
          <CustomChip
            rounded={true}
            skin="light"
            size="small"
            label={mapIsActiveToLabel(row.isActive)}
            color={row.isActive === true ? "success" : "error"}
            sx={{ textTransform: "capitalize" }}
          />
        );
      },
    },
    {
      field: "actions",
      headerName: "Actions",
      sortable: false,
      minWidth: 80,
      flex: 1,
      disableClickEventBubbling: true,
      renderCell: (params) => {
        const onClick = (event) => {
          event.stopPropagation();
          setMenu(event.currentTarget);
          const row = params.row;
          setCurrentRow(row);
        };

        const onClickViewProfile = () => {
          setSocietyMemberOpen(true);
          handleCloseMenuItems();
        };
        const onClickDeleteProfile = () => { 
           const updatedMembers = societyMembers?.map((member) =>
            member?.id === currentRow?.id
              ? { ...member, isActive: false }
              : member
          );
          setSocietyMembers(updatedMembers);
          handleCloseMenuItems();
        };

        const onClickActivateProfile = () => {
          const updatedMembers = societyMembers?.map((member) =>
            member?.id === currentRow?.id
              ? { ...member, isActive: true }
              : member
          );
         setSocietyMembers(updatedMembers);
          handleCloseMenuItems();
        };

        return (
          <div
            style={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
            }}
          >
            <CustomAvatar
              skin="light"
              variant="rounded"
              sx={{
                mr: { xs: 2, lg: 4 },
                width: 34,
                height: 34,
                cursor: "pointer",
              }}
              onClick={onClick}
            >
              <Icon icon="bi:three-dots-vertical" />
            </CustomAvatar>
            <Menu
              id="actions-menu"
              anchorEl={menu}
              keepMounted
              open={Boolean(menu)}
              onClose={handleCloseMenuItems}
            >
              <MenuItem onClick={onClickViewProfile}>Edit</MenuItem>
               {currentRow?.isActive ? (
                <MenuItem onClick={onClickDeleteProfile}>DeActivate</MenuItem>
              ) : (
                <MenuItem onClick={onClickActivateProfile}>Activate</MenuItem>
              )}
            </Menu>
          </div>
        );
      },
    },
  ];

  const handlePageChange = (direction) => {
    if (direction === page) {
      setPage(page + 1);
    } else {
      setPage(page - 1);
    }
  };

  const handlePageSizeChange = (params) => {
    if (params) {
      setPageSize(params);
      setPage(1);
    }
  };

  return (
    <>
      <Grid
        container
        spacing={3}
        sx={{
          width: "100%",
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          marginTop: "2rem",
        }}
      >
        <Grid item xs={12} sm={6} md={4} lg={4} xl={4} sx={{ width: "100%" }}>
          <FormControl fullWidth>
            <Controller
              name="societyMemberName"
              control={control}
              rules={{ required: "Name is required" }}
              render={({ field }) => (
                <NameTextField
                  {...field}
                  label="Name of the Society Member"
                  InputLabelProps={{ shrink: true }}
                  size="small"
                  placeholder="Enter your name"
                  error={Boolean(errors.societyMemberName)}
                  helperText={errors.societyMemberName?.message}
                />
              )}
            />
          </FormControl>
        </Grid>
        <Grid item xs={12} sm={6} md={4} lg={4} xl={4} sx={{ width: "100%" }}>
          <Autocomplete
            id="societyMemberDesignation"
            options={designationList} // Array of { value, key }
            value={
              designationList?.find((option) => option.value === designation) ||
              null
            } // The currently selected value (e.g. an object with 'value' and 'key')
            onChange={(event, newValue) => {
              // Ensure newValue is an object with 'value' and 'key' properties
              handleDesignationChange(newValue?.value || null);
            }}
            getOptionLabel={(option) => option.key || ""} // Access 'key' for displaying the label
            isOptionEqualToValue={(option, value) => option.value === value} // Compare based on 'value'
            renderInput={(params) => (
              <TextField
                {...params}
                label="Designation"
                variant="outlined"
                size="small"
              />
            )}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={4} lg={4} xl={4} sx={{ width: "100%" }}>
          <FormControl fullWidth>
            <Grid container alignItems="center" wrap="nowrap">
              <Grid item>
                <Typography
                  fontSize="body1"
                  sx={{ fontWeight: "bold", mr: 1, ml: 1 }}
                >
                  Email:
                </Typography>
              </Grid>
              <Grid
                item
                xs
                container
                alignItems="center"
                justifyContent="flex-start"
                sx={{ overflow: "hidden" }}
              >
                <Typography
                  variant="body1"
                  sx={{
                    textOverflow: "ellipsis",
                    overflow: "hidden",
                    whiteSpace: "nowrap",
                  }}
                >
                  {formData?.societyMemberInformation?.loginEmail}
                </Typography>
              </Grid>
              <Grid item>
                <CheckCircleOutlineIcon
                  sx={{ color: "green", marginLeft: 1 }}
                />
              </Grid>
            </Grid>
          </FormControl>
        </Grid>
        <Grid item xs={12} sm={6} md={4} lg={4} xl={4} sx={{ width: "100%" }}>
          <FormControl fullWidth>
            <Controller
              name="societyMemberContactNumber"
              control={control}
              rules={{ required: "Contact number is required" }}
              render={({ field }) => (
                <MobileNumberValidation
                  {...field}
                  value={field.value || ""} 
                  type="tel"
                  label="Contact Number"
                  size="small"
                  InputLabelProps={{ shrink: true }}
                  error={Boolean(errors.societyMemberContactNumber)}
                  helperText={errors.societyMemberContactNumber?.message}
                  placeholder="+91 1234560789"
                />
              )}
            />
          </FormControl>
        </Grid>

        <Grid item xs={12} sm={6} md={4} lg={4} xl={4} sx={{ width: "100%" }}>
          <FormControl fullWidth>
            <Controller
              name="alternateNumber"
              control={control}
              render={({ field }) => (
                <TextField
                  {...field}
                  value={field.value || ""} 
                  type="tel"
                  label="Alternate Number"
                  size="small"
                  error={Boolean(errors.alternateNumber)}
                  helperText={errors.alternateNumber?.message}
                  InputLabelProps={{ shrink: true }}
                  placeholder="+91 1234567890"
                  inputProps={{
                    maxLength: field?.value?.startsWith("+91") ? 13 : 10,
                  }}
                />
              )}
            />
          </FormControl>
        </Grid>
        <Grid item xs={12} sm={6} md={4} lg={4} xl={4} sx={{ width: "100%" }}>
          <FormControl fullWidth>
            <Controller
              name="fromDate"
              control={control}
              rules={{ required: "From Date is required" }}
              render={({ field }) => (
                <TextField
                  {...field}
                  value={field.value || ""} 
                  size="small"
                  label="From Date"
                  type="date"
                  InputLabelProps={{ shrink: true }}
                  error={Boolean(errors.fromDate)}
                  helperText={errors.fromDate?.message}
                  aria-describedby="fromDate"
                />
              )}
            />
          </FormControl>
        </Grid>
        <Grid item xs={12} sm={6} md={4} lg={4} xl={4} sx={{ width: "100%" }}>
          <FormControl fullWidth>
            <Controller
              name="toDate"
              control={control}
              rules={{ required: "To Date is required" }}
              render={({ field }) => (
                <TextField
                  {...field}
                  value={field.value || ""} 
                  size="small"
                  label="To Date"
                  type="date"
                  InputLabelProps={{ shrink: true }}
                  error={Boolean(errors.toDate)}
                  helperText={errors.toDate?.message}
                  aria-describedby="toDate"
                />
              )}
            />
          </FormControl>
        </Grid>
      </Grid>
      <Divider sx={{ my: 2 }} />
      <Grid
        sx={{
          backgroundColor: "#f2f7f2",
          paddingTop: 0,
          height: "36px",
          display: "flex",
          alignItems: "center",
          mt: 2,
          mb: 2,
        }}
      >
        <Typography variant="body1" fontWeight={"bold"} sx={{ mt: 0, ml: 2 }}>
          Society Committee Members:
        </Typography>
      </Grid>

      <Box
        sx={{
          display: "flex",
          justifyContent: "right",
          mr: { xs: 5, sm: 6, md: 8, xl: 23, lg: 30 },
        }}
      >
        <div>
          <Button
            sx={{ margin: "10px" }}
            variant="contained"
            onClick={handleSocietyMemberDialogOpen}
          >
            Add&nbsp;a&nbsp;society&nbsp;member
          </Button>
        </div>
      </Box>
      <Grid item xs={12} sm={4}>
        <>
          {currentRow ? (
            <SocietyMembers
              open={societyMemberOpen}
              onClose={handleSocietyMemberDialogClose}
              societyMembers={societyMembers}
              setSocietyMembers={setSocietyMembers}
              listOfSubCategories={designationList}
              rowData={currentRow}
            />
          ) : (
            <SocietyMembers
              open={societyMemberOpen}
              onClose={handleSocietyMemberDialogClose}
              societyMembers={societyMembers}
              setSocietyMembers={setSocietyMembers}
              listOfSubCategories={designationList}
            />
          )}
        </>
      </Grid>
      {societyMembers?.length > 0 && (
        <div style={{ height: "50%", width: "100%" }}>
          <DataGrid
            rows={societyMembers || []}
            columns={cols}
            autoHeight
            checkboxSelection
            pagination
            pageSize={pageSize}
            page={page - 1}
            rowsPerPageOptions={rowsPerPageOptions}
            rowCount={rowCount}
            paginationMode="server"
            onPageChange={handlePageChange}
            onPageSizeChange={handlePageSizeChange}
            rowHeight={38}
            headerHeight={38}
          />
        </div>
      )}
    </>
  );
};

export default SocietyMemberInformation;
