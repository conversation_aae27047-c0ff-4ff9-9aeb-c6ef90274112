import Typography from "@mui/material/Typography";
import { useContext, useState,useEffect } from "react";
import { getAuthorizationHeaders, getUrl } from "src/helpers/utils";
import axios from "axios";
import authConfig from "src/configs/auth";

// ** Custom Components Imports
import AccordionBasic from "src/@core/components/custom-components/AccordionBasic";

// ** Demo Components Imports
import { useTheme } from "@emotion/react";

// ** Styled Component
import {
  Table,
  TableBody,
  TableContainer,
  TableRow,
  TableCell,
} from "@mui/material";

import MUITableCell from "src/pages/SP/MUITableCell";
import EmployeeDetailsEdit from "./EmployeeDetailsEdit";
import { AuthContext } from "src/context/AuthContext";

const fieldLabelStyle = {
  fontSize: "12.75px",
};

const fieldValueStyle = {
  fontWeight: "bold",
  wordWrap: "break-word",
  whiteSpace: "pre-wrap",
  // color: "#66BB6A", // Sets color for the TableCell
  color:'#108A00',
  fontSize: "13px",
};

const tablecellLabelStyle = {
  width: { xs: "50%", sm: "50%", md: "40%" }, // Responsive width adjustment
  padding: "4px", // Ensure padding for better alignment
  textAlign: "right", // Align the text to the left
  verticalAlign: "middle", // Vertically align the text to the middle
  textIndent: {
    lg: "80px",
    md: "80px",
    sm: "100px",
    xs: "0",
  },
};

const tablecellValueStyle = {
  justifyContent: "flex-start",
  alignItems: "center",
  width: { xs: "50%", sm: "50%", md: "60%" }, // Responsive width for small and medium screens
  height: "10px",
};

const EmployeeDetailsView = ({
  data,
  expanded,
  fetchUsers,
  employeesOptions,
  departmentOptions,
  rolesArray,
}) => {
  const { listValues } = useContext(AuthContext);
  const theme = useTheme();
  
  const [state, setState] = useState("view");

  const viewClick = () => {
    setState("edit");
  };

  const editClick = () => {
    setState("view");
  };

  const workLocationName = data?.employeeMetaData?.employeeData?.workLocation
  ? listValues?.find(
      (item) => item.id === data?.employeeMetaData.employeeData.workLocation
    )?.name
  : null;

  const roleName = data?.rolesId
    ? rolesArray.find((item) => item.value === data?.rolesId)?.key
    : null;

  const designation = data?.designation
    ? listValues?.find((item) => item.id === data?.designation)?.name
    : null;

  const reporting = data?.reportingTo
    ? employeesOptions?.find((item) => item.value === data?.reportingTo)?.key
    : null;

  const department = data?.department
    ? departmentOptions?.find((item) => item.value === data?.department)?.key
    : null;


  const sections = [
    {
      title: "Employee Profile",
      fields: [
        { label: "Employee Id", variable: "systemCode" },
        { label: "First Name", variable: "firstName" },
        { label: "Last Name", variable: "lastName" },
        { label: "Designation", variable: "designation" },
        { label: "Role", variable: "role" },
        { label: "Reporting To", variable: "reportingTo" },
        { label: "Department", variable: "department" },
        { label: "Work Location", variable: "workLocation" },
        { label: "Email", variable: "email" },
        { label: "Mobile Number", variable: "mobileNumber" },
        { label: "Date Of Joining", variable: "fromDate" },
        ...(data?.status === "INACTIVE"
          ? [{ label: "Resigned Date", variable: "toDate" }]
          : []),
      ],
    },
  ];

  return (
    <>
   
            {state === "view" && (
              <TableContainer
                sx={{
                
                    paddingLeft: "0rem",   // Correct, unit provided as a string
                    paddingRight:{
                      xl :-6
                    },

                  cursor:
                    "url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='24' height='24' fill='currentColor'%3E%3Cpath d='M11.423 1A3.577 3.577 0 0 1 15 4.577c0 .27-.108.53-.3.722l-.528.529l-1.971 1.971l-5.059 5.059a3 3 0 0 1-1.533.82l-2.638.528a1 1 0 0 1-1.177-1.177l.528-2.638a3 3 0 0 1 .82-1.533l5.059-5.059l2.5-2.5c.191-.191.451-.299.722-.299m-2.31 4.009l-4.91 4.91a1.5 1.5 0 0 0-.41.766l-.38 1.903l1.902-.38a1.5 1.5 0 0 0 .767-.41l4.91-4.91a2.08 2.08 0 0 0-1.88-1.88m3.098.658a3.6 3.6 0 0 0-1.878-1.879l1.28-1.28c.995.09 1.788.884 1.878 1.88z'/%3E%3C/svg%3E\") 12 12, pointer",
                }}
                onClick={viewClick}
              >
                <Table>
                  {sections?.map((section, sectionIndex) => (
                    <TableBody
                      key={sectionIndex}
                      sx={{
                        "& .MuiTableCell-root": {
                          p: "10.8px 9px !important",
                          wordWrap: "break-word",
                          whiteSpace: "pre-wrap",
                        },
                      }}
                    >
                      <TableRow
                        sx={{
                          backgroundColor: "#f2f7f2",
                          height: "32px !important",
                         
                        }}
                      >
                        <TableCell colSpan={2} sx={{ padding: "4px" }}>
                          <Typography variant="body1" fontWeight="bold">
                            {section?.title}
                          </Typography>
                        </TableCell>
                      </TableRow>
                      {section?.fields?.map((field, fieldIndex) => (
                        <TableRow key={fieldIndex}>
                          <TableCell sx={tablecellLabelStyle}>
                            <Typography sx={fieldLabelStyle}>
                              {field?.label}:
                            </Typography>
                          </TableCell>
                          <TableCell
                            sx={tablecellValueStyle}
                          >
                            <Typography sx={fieldValueStyle}>
                              {field.label === "Designation"
                                ? designation
                                : field.label === "Reporting To"
                                ? reporting
                                : field.label === "Department"
                                ? department
                                : field.label === "Role"
                                ? roleName
                                : field.label === "Work Location"
                                ? workLocationName
                                : field.label === "Date Of Joining"
                                ? data.employeeMetaData.employeeData.fromDate
                                : field.label === "Resigned Date"
                                ? data.employeeMetaData.employeeData.toDate
                                : field.variable
                                    .split(".")
                                    .reduce((acc, key) => acc?.[key], data) ||
                                  ""}
                            </Typography>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  ))}
                </Table>
              </TableContainer>
            )}
            {state === "edit" && (
              <EmployeeDetailsEdit
                onCancel={editClick}
                formData={data}
                fetchUsers={fetchUsers}
                departmentOptions={departmentOptions}
                rolesArray={rolesArray}
              />
            )}
          </>

  );
};

export default EmployeeDetailsView;
