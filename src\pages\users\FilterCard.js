import React, { useState, useEffect } from "react";
import {
  Box,
  Button,
  Grid,
  Typography,
  FormControl,
  MenuItem,
  Select,
  TextField,
  Divider,
  Drawer,
  InputLabel,
  IconButton,
} from "@mui/material";
import { Controller, useForm } from "react-hook-form";
import SelectCategory from "src/@core/components/custom-components/SelectCategory";
import axios from "axios";
import { getAuthorizationHeaders, getUrl } from "src/helpers/utils";
import authConfig from "src/configs/auth";
import { styled } from "@mui/material/styles";
import Icon from "src/@core/components/icon";

const Header = styled(Box)(({ theme }) => ({
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
  position: "relative",
  padding: theme.spacing(3),
  borderBottom: `1px solid ${theme.palette.divider}`,
}));

const FilterCard = ({
  open,
  onClose,
  searchKeyword,
  setSearchKeyword,
  category,
  setCategory,
  handleSearch,
}) => {
  const [emailValue, setEmailValue] = useState(searchKeyword);
  const [userType, setUserType] = useState(null);
  const [userTypeLabel, setUserTypeLabel] = useState("Users");
  const [roleTypeId, setRoleTypeId] = useState("");
  const { register, clearErrors, reset, control } = useForm();

  const handleEmailChange = (event) => setEmailValue(event.target.value);

  const [listOfRoleTypes, setListOfRoleTypes] = useState([]);
  const [roleTypeOptions, setRoleTypeOptions] = useState([]);

  useEffect(() => {
    // Fetch Categories
    axios({
      method: "get",
      url:
        getUrl(authConfig.selectDropdownNew) + "?selectionType=USERS_CATEGORIES",
      headers: getAuthorizationHeaders(),
    })
      .then((res) => setListOfRoleTypes(res.data.data))
      .catch((err) => console.log("Categories error", err));
  }, []);

  useEffect(() => {
    if (!!listOfRoleTypes) {
      let data = listOfRoleTypes.map((entry) => ({
        value: entry.id,
        key: entry.name,
      }));
      setRoleTypeOptions(data);
    }
  }, [listOfRoleTypes]);

  const handleUserTypeChange = (newValue) => {
    const roleType = listOfRoleTypes.filter((entry) => entry.id === newValue);
    setRoleTypeId(newValue);
    setUserType(roleType);
  };

  const handleSearchClick = () => {
    handleSearch(emailValue, userTypeLabel);
    setSearchKeyword(emailValue);
    setCategory(roleTypeId);
    onClose();
  };

  const handleCancel = () => {
    reset({ category: "" });
    setEmailValue("");
    setRoleTypeId("");
    setUserType(null);
    handleSearch("", "");
    onClose();
  };

  const handleClose = () => {
    onClose();
  };

  return (
    <Drawer
      anchor="right"
      open={open}
      onClose={onClose}
      variant="temporary"
      ModalProps={{ keepMounted: true }}
      sx={{ "& .MuiDrawer-paper": { width: { xs: "90%", sm: 500 } } }}
    >
      <Header>
        <Typography variant="h5">Advanced Search</Typography>
        <Box sx={{ position: "absolute", top: "8px", right: "14px" }}>
          <IconButton
            size="small"
            onClick={handleClose}
            sx={{
              borderRadius: 1,
              color: "common.white",
              backgroundColor: "primary.main",
              "&:hover": {
                backgroundColor: "#66BB6A",
                transition: "background 0.5s ease, transform 0.5s ease",
              },
            }}
          >
            <Icon icon="tabler:x" fontSize="1rem" />
          </IconButton>
        </Box>
      </Header>

      <Box
        sx={{
          p: (theme) => theme.spacing(4, 6),
          display: "flex",
          flexDirection: "column",
          justifyContent: "space-between",
          height: "100%",
        }}
      >
        <Grid
          marginTop={2}
          container
          spacing={4}
          marginBottom={5}
          style={{ alignItems: "center", width: "100%" }}
        >
          <Grid item xs={12} sm={12}>
                    <SelectCategory
                      register={register}
                      clearErrors={clearErrors}
                      id={"roleTypeId-select"}
                      label={"Select Category"}
                      name="roleTypeId-select"
                      nameArray={roleTypeOptions}
                      defaultValue={roleTypeId}
                      value={roleTypeId}
                      onChange={(event) =>
                        handleUserTypeChange(event.target.value)
                      }
                      aria-describedby="roleTypeId-select"
                    />
                  </Grid>

          <Grid item xs={12} md={12}>
            <TextField
              fullWidth
              label="Email Id"
              value={emailValue}
              size="small"
              onChange={handleEmailChange}
            />
          </Grid>
        </Grid>
        </Box>

        <Box
          sx={{
            borderTop: (theme) => `1px solid ${theme.palette.divider}`,
            p: (theme) => theme.spacing(2),
            justifyContent: "flex-end",
            display: "flex",
            alignItems: "center",
          }}
        >
          <Button
            variant="contained"
            sx={{ mr: 3 }}
            onClick={handleSearchClick}
          >
            Search
          </Button>
          <Button variant="tonal" onClick={handleCancel}>
            Clear All
          </Button>
        </Box>
    </Drawer>
  );
};

export default FilterCard;
