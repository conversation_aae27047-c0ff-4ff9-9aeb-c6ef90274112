import { useContext, useMemo } from 'react';
import { AuthContext } from 'src/context/AuthContext';
import StaticQuickLinks from 'src/utils/StaticQuickLinks';

const ALLOWED_PERMISSIONS_SET = new Set([1, 3, 5, 7, 15]);
const MODULE_TYPE = "MODULE";
const PAGE_TYPE = "PAGE";
const QUICK_LINKS_NAME = "QuickLinks";

// Preprocess Static QuickLinks for faster lookup
const quickLinksMap = new Map(StaticQuickLinks.map(item => [item?.name, item]));

const QuickLinks = () => {
  const { user } = useContext(AuthContext);

  const permissionsList = useMemo(() => user?.permissionsDTOList || [], [user?.permissionsDTOList]);

  const filterPages = (children, parentName) => {
    const parentStaticItem = quickLinksMap.get(parentName);
    if (!parentStaticItem?.children) return null;

    const filteredPages = children
      .filter(child => child?.type === PAGE_TYPE && ALLOWED_PERMISSIONS_SET.has(child?.permissions))
      .map(child => {
        const staticPageItem = parentStaticItem.children?.find(item => item?.name === child?.name);
        if (!staticPageItem) return null;

        return {
          title: staticPageItem?.title,
          path: staticPageItem?.path,
          icon: staticPageItem?.icon,
          displayNumber: child?.displayNumber || 0,
          permissions: child?.permissions,
          children: child?.children ? filterPages(child.children, child.name) : null,
        };
      })
      .filter(Boolean)
      .sort((a, b) => (a?.displayNumber || 0) - (b?.displayNumber || 0));

    return filteredPages.length > 0 ? filteredPages : null;
  };

  const quickLinksArray = useMemo(() => {
    const quickLinks = permissionsList?.find(item => item?.name === QUICK_LINKS_NAME);
    if (!quickLinks?.children) return [];

    return quickLinks.children
      .filter(child => child?.type === MODULE_TYPE && ALLOWED_PERMISSIONS_SET.has(child?.permissions))
      .map(child => {
        const staticModuleItem = quickLinksMap.get(child?.name);
        if (!staticModuleItem) return null;

        return {
          title: staticModuleItem?.title,
          path: staticModuleItem?.path,
          icon: staticModuleItem?.icon,
          displayNumber: child?.displayNumber || 0,
          permissions: child?.permissions,
          children: child?.children ? filterPages(child.children, child.name) : null,
        };
      })
      .filter(Boolean)
      .sort((a, b) => (a?.displayNumber || 0) - (b?.displayNumber || 0));
  }, [permissionsList]);

  return quickLinksArray;
};

export default QuickLinks;
