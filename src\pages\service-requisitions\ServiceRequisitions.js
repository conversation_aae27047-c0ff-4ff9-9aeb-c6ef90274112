import {
  Box,
  Button,
  CardContent,
  Checkbox,
  Chip,
  Divider,
  FormControlLabel,
  Grid,
  Menu,
  MenuItem,
  Typography,
} from "@mui/material";
import { DataGrid } from "@mui/x-data-grid";
import axios from "axios";
import { useRouter } from "next/router";
import { useContext, useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import Icon from "src/@core/components/icon";
import CustomAvatar from "src/@core/components/mui/avatar";
import FallbackSpinner from "src/@core/components/spinner";
import authConfig from "src/configs/auth";
import { AuthContext } from "src/context/AuthContext";
import { getAuthorizationHeaders, getUrl } from "src/helpers/utils";
import AdvancedSearchNew from "./AdvancedSearchNew";
import DeActivateDialog from "./DeActivateDialog";
import DecilogicReportDialog from "./DecilogicReport";
import SampleRequisition from "./dummyDialog";
import FinalBOQ from "./FinalBOQ";
import RequestForSiteVisit from "./RequestForSiteVisit";
import RequisitionDetails from "./RequisitionDetails";
import RequisitionDetailsDialog from "./RequisitionDetailsDialog";
import useColumns from "./RequisitionsColumns";
import RFQPreliminary from "./RFQPreliminary";
import SelectOrApproveSpDialog from "./SelectOrApproveSpDialog";
import SubmitBoqDialog from "./SubmitBoqDialog";
import SubmitPreliminaryQuoteDialog from "./SubmitPreliminaryQuoteDialog";
import ViewRequisition from "./ViewRequisition";
import WorkOrderDetails from "./WorkOrderDetails";
import toast from "react-hot-toast";
import FilterListIcon from '@mui/icons-material/FilterList';
import { formatBudget } from "../requisitions/formatBudget";

const ServiceRequisitions = ({ role, preliminary, revisedBOQ }) => {
  const {
    user,
    requisitionData,
    requisitionDataDetails,
    setRequisitionData,
    getAllListValuesByListNameId,
    listValues,
  } = useContext(AuthContext);

  const [requisitionsList, setRequisitionsList] = useState([]); // Holds the requisitions data list
  const [openDialog, setOpenDialog] = useState(false); // Manages 'Add Requisition' dialog
  const [openDecilogicDialog, setOpenDecilogicDialog] = useState(false); // Manages 'Add Requisition' dialog
  const handleOpenDecilogicDialog = () => setOpenDecilogicDialog(true); // Opens Decilogic Report dialog
  const router = useRouter();
  const { status } = router.query; // Extract 'status' UUID from query params
  const { zone, assignZone } = router.query;
  const { service, assigned } = router.query;
  const { id } = router.query;
  const { quarter, assignedInQuarter } = router.query;
  const { frequency, assignedTo } = router.query;
  const { currentWeekSRs } = router.query;
  const { seriesName, duration } = router.query;
  const { fromDate,toDate,employee,employeeId} = router.query;

  const statusName = status
    ? listValues?.find((item) => item.id === status)?.name
    : null;

  const zoneName = zone
    ? listValues?.find((item) => item.id === zone)?.name
    : null;

  const serviceName = service
    ? listValues?.find((item) => item.id === service)?.name
    : null;

  const [referenceData, setReferenceData] = useState(null);
  const [referralNameData, setReferralNameData] = useState(null);
  const [priorityData, setPriorityData] = useState([]);
  const [statusData, setStatusData] = useState([]);
   const [budgetUnits, setBudgetUnits] = useState([]);
    const [budgetCondition, setBudgetCondition] = useState([]);
  const [viewProfileDialogOpen, setViewProfileDialogOpen] = useState(false);
  const [
    submitForPreliminaryQuoteDialogOpen,
    setSubmitForPreliminaryQuoteDialogOpen,
  ] = useState(false);
  const [submitForBoqDialogOpen, setSubmitForBoqDialogOpen] = useState(false);
  const [selectOrApproveSPDialogOpen, setSelectOrApproveSPDialogOpen] =
    useState(false);
  const [searchingState, setSearchingState] = useState(false);
  const [addUserOpen, setAddUserOpen] = useState(false);
  const toggleAddUserDrawer = () => setAddUserOpen(!addUserOpen);
  const [searchData, setSearchData] = useState({});

  useEffect(() => {
    reset(searchData);
  }, [searchData]);
  const handleError = (error) => {
    console.error("society requisition: All Services:", error);
  };
  const [servicesList, setServicesList] = useState([]);
  useEffect(() => {
    if (!!authConfig) {
      getAllListValuesByListNameId(
        authConfig.allServicesListNameId,
        (data) =>
          setServicesList(
            data?.listValues?.map((item) => ({
              value: item.id,
              key: item.listValue,
            }))
          ),
        handleError
      );
      getAllListValuesByListNameId(
        authConfig.referenceTypeListNameId,
        (data) =>
          setReferenceData(
            data?.listValues?.map((item) => ({
              value: item.id,
              key: item.listValue,
            }))
          ),
        handleError
      );

      getAllListValuesByListNameId(
        authConfig.referralNameId,
        (data) =>
          setReferralNameData(
            data?.listValues?.map((item) => ({
              value: item.id,
              key: item.listValue,
            }))
          ),
        handleError
      );
      getAllListValuesByListNameId(
        authConfig.leadPriorityListNamesId,
        (data) =>
          setPriorityData(
            data?.listValues?.map((item) => ({
              value: item.id,
              key: item.listValue,
            }))
          ),
        handleError
      );
      getAllListValuesByListNameId(
        authConfig.statusListNamesId,
        (data) =>
          setStatusData(
            data?.listValues?.map((item) => ({
              value: item.id,
              key: item.listValue,
            }))
          ),
        handleError
      );
       getAllListValuesByListNameId(
            authConfig.budgetUnits,
            (data) =>
              setBudgetUnits(
                data?.listValues.map((item) => ({
                  value: item.id,
                  key: item.listValue,
                }))
              ),
            handleError
          );
          getAllListValuesByListNameId(
            authConfig.budgetCondition,
            (data) =>
              setBudgetCondition(
                data?.listValues.map((item) => ({
                  value: item.id,
                  key: item.listValue,
                }))
              ),
            handleError
          );
    }
  }, [authConfig]);

  const [requestSiteVisitDialog, setRequestSiteVisitDialog] = useState(false);
  const [sampleDialog, setSampleDialog] = useState(false);
  const [viewWorkOrderClick, setViewWorkOrderClick] = useState(false);
  const rowsPerPageOptions = [10, 15, 20, 25, 50, 100];
  const [rfqPreliminary, setRfqPreliminary] = useState(false);
  const [finalBOQ, setFinalBOQ] = useState(false);
  const [pageSize, setPageSize] = useState(rowsPerPageOptions[0]);
  const [page, setPage] = useState(1);
  const [currentRow, setCurrentRow] = useState();
  const [rowCount, setRowCount] = useState(0);
  const [initialRowCount, setInitialRowCount] = useState(null);
  const [employeesData, setEmployeesData] = useState([]);
  const [title, setTitle] = useState("");
  const [openDeleteDialog, setOpenDeleteDialog] = useState(false);
  const [loading, setLoading] = useState(true);

  const [selectedFilters, setSelectedFilters] = useState([]);

  const handleApplyFilters = (filters) => {
    setSelectedFilters(filters); // Set filters for chips
    setSearchingState(true); // Trigger search
  };

  // Function to remove a single filter
  const handleRemoveFilter = (key, id) => {
    if (
      (key === "servicesFilter" ||
        key === "priorityFilter" ||
        key === "statusFilter" ||
        key === "assignedToFilter") &&
      id
    ) {
      // Remove the specific ID from the servicesFilter value
      const updatedFilters = selectedFilters
        ?.map((filter) => {
          if (filter.key === key) {
            return {
              ...filter,
              value: filter.value.filter((serviceId) => serviceId !== id),
            };
          }
          return filter;
        })
        .filter((filter) => filter.value?.length > 0); // Remove the filter if no values remain
      setSelectedFilters(updatedFilters);
    } else {
      // Remove the entire filter
      setSelectedFilters(
        selectedFilters.filter((filter) => filter.key !== key)
      );
    }
  };
  // Clear all filters
  const clearAllFilters = () => {
    setSelectedFilters([]);
    setSearchingState(false); // Reset search state if needed
  };

  const clearFilterSR = () => {
    setSelectedFilters((prevFilters) =>
      prevFilters.filter((filter) => filter.key !== "requestDateFrom")
    );

    setSelectedFilters((prevFilters) =>
      prevFilters.filter((filter) => filter.key !== "requestDateTo")
    );

    setSelectedFilters((prevFilters) =>
      prevFilters.filter((filter) => filter.key !== "createdByEmployeeId")
    );

    setSelectedFilters((prevFilters) =>
      prevFilters.filter((filter) => filter.key !== "assignedToEmployeeId")
    );

    // Update the URL to remove the status query parameter
    router.replace(
      {
        // pathname: router.pathname,
        query: {}, // Clear all query parameters
      },
      undefined,
      { shallow: false } // Allow page reload if necessary
    );
  };

  const {
    reset,
    formState: { errors },
  } = useForm();


  const handleCloseSampleDialog = () => {
    setSampleDialog(false);
    fetchRequisitions(page, pageSize, selectedFilters);
  };
  const handleRequestSiteVisitTimings = () => {
    setRequestSiteVisitDialog(false);
    fetchRequisitions(page, pageSize, selectedFilters);
  };

  const handleRfqPreliminaryClose = () => {
    setRfqPreliminary(false);
    fetchRequisitions(page, pageSize, selectedFilters);
  };

  const handleCloseDecilogicDialog = () => {
    setOpenDecilogicDialog(false);
    fetchRequisitions(page, pageSize, selectedFilters);
  };

  const handleFinalBOQ = () => {
    setFinalBOQ(false);
    fetchRequisitions(page, pageSize, selectedFilters);
  };

  const handleCloseDialog = () => {
    reset();
    setOpenDialog(false);
  };

  const [dialogOpen, setDialogOpen] = useState(false);
  const [selectedRequisition, setSelectedRequisition] = useState(null);

  const handleDialogOpen = (row) => {
    setSelectedRequisition(row); // Save the entire row
    setDialogOpen(true);
  };

  const handleDialogClose = () => {
    setDialogOpen(false);
    setSelectedRequisition(null);
  };

  // Fetch all employees data
  useEffect(() => {
    axios({
      method: "get",
      url: getUrl(authConfig.individualEndpoint) + "/employees",
      headers: getAuthorizationHeaders(authConfig.individualEmployeesMIMEType),
    })
      .then((res) => {
        setEmployeesData(res.data);
      })
      .catch((err) => console.log("error", err));
  }, []);

  // getAll api call to fetch all requisitions
  const fetchRequisitions = async (
    currentPage,
    currentPageSize,
    selectedFilters
  ) => {
    let url;
    if (user?.roleId === authConfig.superAdminRoleId) {
      url = getUrl(authConfig.serviceRequisitionsEndpoint + "/admin/all");
    } else {
      url = getUrl(authConfig.serviceRequisitionsEndpoint + "/all");
    }

    let headers;

    if (user?.roleId === authConfig.superAdminRoleId) {
      headers = getAuthorizationHeaders({
        contentType: authConfig.REQUISITION_ADMIN_GETALL_MIMETYPE,
        accept: authConfig.REQUISITION_ADMIN_GETALL_ACCEPT_MIMETYPE,
      });
    } else {
      headers = getAuthorizationHeaders({
        contentType: authConfig.REQUISITION_GETALL_MIMETYPE,
        accept: authConfig.REQUISITION_GETALL_ACCEPT_MIMETYPE,
      });
    }

    const data = {
      page: currentPage,
      pageSize: currentPageSize,
    };

    selectedFilters?.forEach((filter) => {
      const key = filter.key;
      data[key] = filter.value;
    });

    try {
      const response = await axios({
        method: "post",
        url: url,
        headers: headers,
        data: data,
      });

      if (initialRowCount == null) {
        setInitialRowCount(response?.data?.rowCount || 0);
      }

      if (response.data) {
        setRowCount(response.data?.rowCount || 0);
        setRequisitionsList(response?.data?.serviceRequisitionsResponses || []);
      } else {
        console.error("Unexpected API response format:", response);
      }
    } catch (error) {
      console.error("Error fetching users:", error);
    } finally {
      setLoading(false);
    }
  };

  const closeViewProfileDialog = () => {
    setViewProfileDialogOpen(false);
    fetchRequisitions(page, pageSize, selectedFilters);
  };

  const closeSubmitForPreliminaryQuoteDialog = () => {
    setSubmitForPreliminaryQuoteDialogOpen(false);
    fetchRequisitions(page, pageSize, selectedFilters);
  };

  const closeSubmitForBoqDialog = () => {
    setSubmitForBoqDialogOpen(false);
    fetchRequisitions(page, pageSize, selectedFilters);
  };

  const closeSelectOrApproveSpDialog = () => {
    setSelectOrApproveSPDialogOpen(false);
    fetchRequisitions(page, pageSize, selectedFilters);
  };

  const handleCloseDeleteDialog = () => {
    setOpenDeleteDialog(false);
    fetchRequisitions(page, pageSize, selectedFilters);
  };

  useEffect(() => {
    if (status) {
      setSelectedFilters([{ key: "statusFilter", value: [status] }]);
    }
  }, [status]);

  useEffect(() => {
    if (zone) {
      setSelectedFilters((prevFilters) => {
        const newFilters = [...prevFilters];

        newFilters.push({ key: "searchByZoneFilter", value: [zone] });
        newFilters.push({ key: "assignedTo", value: assignZone });

        return newFilters;
      });
    }
  }, [zone]);

  useEffect(() => {
    if (quarter) {
      setSelectedFilters((prevFilters) => {
        const newFilters = [...prevFilters];

        newFilters.push({
          key: "searchByDurationOfRequisition",
          value: quarter,
        });
        newFilters.push({ key: "assignedTo", value: assignedInQuarter });

        return newFilters;
      });
    }
  }, [quarter]);

  useEffect(() => {
    if (currentWeekSRs) {
      setSelectedFilters([
        { key: "currentWeekCreatedServiceRequisitions", value: currentWeekSRs },
      ]);
    }
  }, [currentWeekSRs]);

  useEffect(() => {
    if (frequency) {
      setSelectedFilters((prevFilters) => {
        const newFilters = [...prevFilters];

        if (frequency === "daily") {
          newFilters.push({
            key: "totalServiceRequisitionsCreatedCurrentDay",
            value: true,
          });
        } else if (frequency === "monthly") {
          newFilters.push({
            key: "totalServiceRequisitionsCreatedCurrentMonth",
            value: true,
          });
        } else if (frequency === "yearly") {
          newFilters.push({
            key: "totalServiceRequisitionsCreatedCurrentYear",
            value: true,
          });
        } else if (frequency === "completed") {
          newFilters.push({
            key: "totalCompletedServiceRequisitions",
            value: true,
          });
        }
        newFilters.push({ key: "assignedTo", value: assignedTo });

        return newFilters;
      });
    }
  }, [frequency]);

  useEffect(() => {
    setSelectedFilters((prevFilters) => {
      const newFilters = [...prevFilters];

      if (seriesName) {
        if (seriesName === "assignedTo") {
          newFilters.push({ key: "assignedTo", value: true });
        } else if (seriesName === "createdBy") {
          newFilters.push({ key: "createdBy", value: true });
        }
      }

      if (duration) {
        if (duration === "daily") {
          newFilters.push({
            key: "totalServiceRequisitionsCreatedCurrentDay",
            value: true,
          });
        } else if (duration === "weekly") {
          newFilters.push({ key: "durationOfThisWeek", value: true });
        } else if (duration === "monthly") {
          newFilters.push({ key: "durationOfThisMonth", value: true });
        }
      }

      return newFilters;
    });
  }, [seriesName, duration]);

  useEffect(() => {
    if (fromDate && toDate) {

      setSelectedFilters((prevFilters) => {
        const newFilters = [...prevFilters];
  
        newFilters.push({ key: "requestDateFrom", value: fromDate });
        newFilters.push({ key: "requestDateTo", value: toDate });
        if (employee) {
          newFilters.push({ key: "createdByEmployeeId", value: employee });
        }
        if(employeeId){
          newFilters.push({ key: "assignedToEmployeeId", value: employeeId });
        }
  
        return newFilters;
      });
    }
  }, [fromDate, toDate,employee,employeeId]);

  useEffect(() => {
    if (service) {
      setSelectedFilters((prevFilters) => {
        const newFilters = [...prevFilters];
        newFilters.push({ key: "servicesFilter", value: [service] });
        newFilters.push({ key: "assignedTo", value: assigned });
        return newFilters;
      });
    }
  }, [service]);

  useEffect(() => {
    if (id) {
      setSelectedFilters([{ key: "idFilter", value: id }]);
    }
  }, [id]);

  useEffect(() => {
    fetchRequisitions(page, pageSize, selectedFilters);
  }, [page, pageSize, selectedFilters]);

  const handlePageChange = (direction) => {
    if (direction === page) {
      setPage(page + 1);
    } else {
      setPage(page - 1);
    }
  };

  const handlePageSizeChange = (params) => {
    if (params) {
      setPageSize(params);
      setPage(1);
    }
  };

  const handleCloseWorkOrder = () => {
    setViewWorkOrderClick(false);
  };

  const [anchorEl, setAnchorEl] = useState(null);
  const [menuPosition, setMenuPosition] = useState(null);

  // Handle menu click
  const handleMenuClick = (event, params) => {
    event.stopPropagation();
    setAnchorEl(event.currentTarget); // Set the anchor element to the clicked icon
    setMenuPosition({ mouseX: event.clientX, mouseY: event.clientY }); // Capture mouse click position
    setRequisitionData({
      ...requisitionData,
      id: params.row.id,
    });
    setCurrentRow(params.row); // Save the row details for later actions
  };

  const handleMenuClose = () => {
    setAnchorEl(null); // Close menu
    setMenuPosition(null); // Reset menu position
  };

  const onClickViewProfile = () => {
    setViewProfileDialogOpen(true);
    handleMenuClose(); // Close menu after action
  };

  const onClickToggleStatus = () => {
    setOpenDeleteDialog(true);
    handleMenuClose(); // Close menu after action
  };

  // Function to clear the status filter
  const handleStatusClearFilter = () => {
    // Clear the status filter from selected filters
    setSelectedFilters((prevFilters) =>
      prevFilters.filter((filter) => filter.key !== "statusFilter")
    );

    // Update the URL to remove the status query parameter
    router.replace(
      {
        // pathname: router.pathname,
        query: {}, // Clear all query parameters
      },
      undefined,
      { shallow: false } // Allow page reload if necessary
    );
  };

  const handleZoneClearFilter = () => {
    // Clear the zone filter from selected filters
    setSelectedFilters((prevFilters) =>
      prevFilters.filter((filter) => filter.key !== "searchByZoneFilter")
    );

    setSelectedFilters((prevFilters) =>
      prevFilters.filter((filter) => filter.key !== "assignedTo")
    );

    // Update the URL to remove the zone query parameter
    router.replace(
      {
        // pathname: router.pathname,
        query: {}, // Clear all query parameters
      },
      undefined,
      { shallow: false } // Allow page reload if necessary
    );
  };

  const handleQuarterClearFilter = () => {
    setSelectedFilters((prevFilters) =>
      prevFilters.filter(
        (filter) => filter.key !== "searchByDurationOfRequisition"
      )
    );

    setSelectedFilters((prevFilters) =>
      prevFilters.filter((filter) => filter.key !== "assignedTo")
    );

    // Update the URL to remove the zone query parameter
    router.replace(
      {
        // pathname: router.pathname,
        query: {}, // Clear all query parameters
      },
      undefined,
      { shallow: false } // Allow page reload if necessary
    );
  };

  const handleCurrentWeekClearFilter = () => {
    setSelectedFilters((prevFilters) =>
      prevFilters.filter(
        (filter) => filter.key !== "currentWeekCreatedServiceRequisitions"
      )
    );

    // Update the URL to remove the zone query parameter
    router.replace(
      {
        // pathname: router.pathname,
        query: {}, // Clear all query parameters
      },
      undefined,
      { shallow: false } // Allow page reload if necessary
    );
  };

  const handleFrequencyClearFilter = () => {
    if (frequency === "daily") {
      setSelectedFilters((prevFilters) =>
        prevFilters.filter(
          (filter) => filter.key !== "totalServiceRequisitionsCreatedCurrentDay"
        )
      );
    } else if (frequency === "monthly") {
      setSelectedFilters((prevFilters) =>
        prevFilters.filter(
          (filter) =>
            filter.key !== "totalServiceRequisitionsCreatedCurrentMonth"
        )
      );
    } else if (frequency === "yearly") {
      setSelectedFilters((prevFilters) =>
        prevFilters.filter(
          (filter) =>
            filter.key !== "totalServiceRequisitionsCreatedCurrentYear"
        )
      );
    } else if (frequency === "completed") {
      setSelectedFilters((prevFilters) =>
        prevFilters.filter(
          (filter) => filter.key !== "totalCompletedServiceRequisitions"
        )
      );
    }

    setSelectedFilters((prevFilters) =>
      prevFilters.filter((filter) => filter.key !== "assignedTo")
    );

    // Update the URL to remove the zone query parameter
    router.replace(
      {
        // pathname: router.pathname,
        query: {}, // Clear all query parameters
      },
      undefined,
      { shallow: false } // Allow page reload if necessary
    );
  };

  const clearFilter = () => {
    if (seriesName === "assignedTo") {
      setSelectedFilters((prevFilters) =>
        prevFilters.filter((filter) => filter.key !== "assignedTo")
      );
    } else if (seriesName === "createdBy") {
      setSelectedFilters((prevFilters) =>
        prevFilters.filter((filter) => filter.key !== "createdBy")
      );
    }
    if (duration === "daily") {
      setSelectedFilters((prevFilters) =>
        prevFilters.filter(
          (filter) => filter.key !== "totalServiceRequisitionsCreatedCurrentDay"
        )
      );
    } else if (duration === "weekly") {
      setSelectedFilters((prevFilters) =>
        prevFilters.filter((filter) => filter.key !== "durationOfThisWeek")
      );
    } else if (duration === "monthly") {
      setSelectedFilters((prevFilters) =>
        prevFilters.filter((filter) => filter.key !== "durationOfThisMonth")
      );
    }
    router.replace(
      {
        query: {}, // Clear all query parameters
      },
      undefined,
      { shallow: false } // Allow full page reload
    );
  };

  const handleServiceClearFilter = () => {
    setSelectedFilters((prevFilters) =>
      prevFilters.filter((filter) => filter.key !== "servicesFilter")
    );

    setSelectedFilters((prevFilters) =>
      prevFilters.filter((filter) => filter.key !== "assignedTo")
    );

    router.replace(
      {
        // pathname: router.pathname,
        query: {}, // Clear all query parameters
      },
      undefined,
      { shallow: false } // Allow page reload if necessary
    );
  };

  const handleIdClearFilter = () => {
    // Clear the status filter from selected filters
    setSelectedFilters((prevFilters) =>
      prevFilters.filter((filter) => filter.key !== "idFilter")
    );

    // Update the URL to remove the status query parameter
    router.replace(
      {
        // pathname: router.pathname,
        query: {}, // Clear all query parameters
      },
      undefined,
      { shallow: false } // Allow page reload if necessary
    );
  };
  const [visible, SetVisible] = useState(false);
  const handleVisibleOpen = (e) => {
    SetVisible(e.currentTarget);
  };
  const handleVisibleClose = () => SetVisible(null);

  const [columnVisibility, setColumnVisibility] = useState({
    "Requisition No": true,
    "First Name": false,
    "Last Name": false,
    "Email": false,
    "Mobile Number": false,
    "Location": false,
    "Zone": false,
    "Address": false,
    "Society Name": true,
    "Service Requirement": true,
    "Req Date": true,
    "Assigned To": true,
    "Priority": true,
    "Budget": false,
    "DeadLine": true,
    "Remarks": false,
    "Reference Type": false,
    "Team Reference": false,
    "Work For": false,
    "Assigned Date": false,
    "Is Active": true,
    "Contact No.": false,
    "Contact Person Name": false,
    "Site Visit date": false,
    "Site Visit Start Time": false,
    "Site Visit End Time": false,
    "Actions": true,
  });
  const [checkedCount, setCheckedCount] = useState(0);
  const handleColumnToggle = (field) => {
    if (checkedCount >= 15 && !columnVisibility[field]) {
      toast.error('Limit exceeded. You can only select up to 15 columns.', {
        position: "top-center", // Moves toast to the top
        autoClose: 3000, // Closes after 3 seconds
        hideProgressBar: false,
        closeOnClick: false,
        pauseOnHover: false,
        draggable: false,
      });
      return;
    }

    const newColumnVisibility = { ...columnVisibility, [field]: !columnVisibility[field] };
    const newCheckedCount = Object.values(newColumnVisibility).filter((value) => value).length;
    setColumnVisibility(newColumnVisibility);
    setCheckedCount(newCheckedCount);
  };

  useEffect(() => {
    const count = Object.values(columnVisibility).filter((value) => value).length;
    setCheckedCount(count);
  }, [columnVisibility]);

  const columns = useColumns({
    employeesData,
    handleDialogOpen,
    formatBudget,
  })
    .concat([
      {
        key: "actions",
        field: "actions",
        headerName: "Actions",
        width: 100,
        sortable: false,
        renderCell: (params) => (
          <>
            <CustomAvatar
              skin="light"
              variant="rounded"
              sx={{
                mr: { xs: 2, lg: 4 },
                width: 34,
                height: 34,
                cursor: "pointer",
              }}
              onClick={(event) => handleMenuClick(event, params)}
            >
              <Icon icon="bi:three-dots-vertical" />
            </CustomAvatar>

            <Menu
              anchorEl={anchorEl}
              open={Boolean(anchorEl)}
              onClose={handleMenuClose}
              anchorReference="anchorPosition"
              anchorPosition={
                menuPosition
                  ? { top: menuPosition.mouseY, left: menuPosition.mouseX }
                  : undefined
              }
            >
              {user.organisationCategory === "SERVICE_PROVIDER" ? (
                <MenuItem onClick={onClickViewProfile}>View</MenuItem>
              ) : (
                <MenuItem onClick={onClickViewProfile}>View/Edit</MenuItem>
              )}

              {user.organisationCategory !== "SERVICE_PROVIDER" && (
                <>
                  <MenuItem onClick={onClickToggleStatus}>
                    {currentRow?.isActive ? "Deactivate" : "Activate"}
                  </MenuItem>
                </>
              )}
              {user.organisationCategory === "EMPLOYEE" && (
                <>
                  <MenuItem
                    onClick={() => {
                      setRfqPreliminary(true);
                      handleMenuClose();
                    }}
                  >
                    Assign/Broadcast SR to SP (RFQ Preliminary)
                  </MenuItem>

                  <MenuItem
                    onClick={() => {
                      setFinalBOQ(true);
                      handleMenuClose();
                    }}
                  >
                    Assign/Broadcast SR to SP (RFQ revised/final BOQ)
                  </MenuItem>
                </>
              )}

              {user.organisationCategory !== "SOCIETY" && (
                <MenuItem
                  onClick={() => {
                    setRequestSiteVisitDialog(true);
                    handleMenuClose();
                  }}
                >
                  Request Site Visit
                </MenuItem>
              )}
              {user.organisationCategory !== "SOCIETY" && (
                <>
                  {!revisedBOQ && (
                    <MenuItem
                      onClick={() => {
                        setSubmitForPreliminaryQuoteDialogOpen(true);
                        handleMenuClose();
                      }}
                    >
                      Submit Preliminary Quote
                    </MenuItem>
                  )}
                </>
              )}
              {user.organisationCategory !== "SOCIETY" && (
                <>
                  {!preliminary && (
                    <MenuItem
                      onClick={() => {
                        setSubmitForBoqDialogOpen(true);
                        handleMenuClose();
                      }}
                    >
                      Submit Revised/Final (BOQ) Quote
                    </MenuItem>
                  )}
                </>
              )}
              {user.organisationCategory === "EMPLOYEE" && (
                <MenuItem
                  onClick={() => {
                    handleOpenDecilogicDialog();
                    handleMenuClose();
                  }}
                >
                  Compare BOQ quotations for decilogic report
                </MenuItem>
              )}

              {user.organisationCategory !== "SERVICE_PROVIDER" && (
                <MenuItem
                  onClick={() => {
                    setSelectOrApproveSPDialogOpen(true);
                    handleMenuClose();
                  }}
                >
                  Select/Approve the SP
                </MenuItem>
              )}
            </Menu>
          </>
        ),
      },
    ])
    .filter((column) => columnVisibility[column.headerName]);

  return (
    <>
      <Grid>
        <Box
          sx={{
            py: 3,
            px: 6,
            rowGap: 2,
            columnGap: 4,
            display: "flex",
            flexWrap: "wrap",
            alignItems: "center",
            justifyContent: "space-between",
          }}
        >
          <Grid container spacing={3} alignItems="center">
            <Grid
              item
              xs={12}
              sm={2}
              sx={{ textAlign: "flex-start", display: "flex" }}
            >
              <Typography variant="h6" fontWeight={"600"}>
                Requisitions&nbsp;List
              </Typography>
            </Grid>

            <Grid item xs={12} sm={10}>
              <Grid
                container
                spacing={2}
                alignItems="center"
                justifyContent="flex-end"
              >
                {user.organisationCategory === "EMPLOYEE" && (
                <Grid
                  item
                  xs="auto"
                  sm="auto"
                  sx={{
                    paddingTop: { xs: "15px", sm: "25px" },
                  }}
                >
                  <Button
                    variant="contained"
                    onClick={handleVisibleOpen}
                    startIcon={<FilterListIcon />} 
                    sx={{ ml: 2 }}
                  >
                    Filter Columns
                  </Button>
                </Grid>
                )}
                <Grid
                  item
                  xs="auto"
                  sm="auto"
                  sx={{
                    paddingTop: { xs: "15px", sm: "25px" },
                  }}
                >
                  {/* Filter component */}
                  <AdvancedSearchNew
                    open={addUserOpen}
                    toggle={toggleAddUserDrawer}
                    searchingState={searchingState}
                    setSearchingState={setSearchingState}
                    services={servicesList}
                    referenceData={referenceData}
                    referralNameData={referralNameData}
                    priorityData={priorityData}
                    statusData={statusData}
                    employeesData={employeesData}
                    selectedFilters={selectedFilters}
                    clearAllFilters={clearAllFilters}
                    onApplyFilters={handleApplyFilters}
                    budgetUnits={budgetUnits}
                    budgetCondition={budgetCondition}
                  />
                </Grid>

                <Grid item xs="auto" sm="auto">
                  {user.organisationCategory !== "SERVICE_PROVIDER" && (
                    <Button
                      variant="contained"
                      onClick={() => {
                        setOpenDialog(true);
                      }}
                    >
                      Add Requisition
                    </Button>
                  )}

                  <Menu
                    anchorEl={visible}
                    open={Boolean(visible)}
                    onClose={handleVisibleClose}
                    sx={{
                      height:"80%"
                    }}
                  >
                    {Object.keys(columnVisibility).map((field) => (
                      <MenuItem
                        key={field}
                        onClick={(e) => e.stopPropagation()}
                      >
                        <FormControlLabel
                          control={
                            <Checkbox
                              checked={columnVisibility[field]}
                              onChange={() => handleColumnToggle(field)}
                              disabled={checkedCount >= 16 && !columnVisibility[field]}
                            />
                          }
                          label={field}
                        />
                      </MenuItem>
                    ))}
                  </Menu>
                </Grid>
              </Grid>
            </Grid>
          </Grid>
        </Box>

        <Divider />
        {/*Component for creating a requisition is invoked when the 'Add Requisition' button is clicked*/}

        <RequisitionDetails
          open={openDialog}
          onClose={handleCloseDialog}
          employeesData={employeesData}
          fetchRequisitions={fetchRequisitions}
          referenceData={referenceData}
          setReferenceData={setReferenceData}
          referralNameData={referralNameData}
          setReferralNameData={setReferralNameData}
          pageSR={page}
          pageSizeSR={pageSize}
        />

        <Divider />

        {/*Component for viewing requisition details by clicking on 'View/Edit' 
in the actions menu that appears upon clicking a row in the data grid*/}

        {role ? (
          <ViewRequisition
            data={requisitionDataDetails}
            open={viewProfileDialogOpen}
            onClose={closeViewProfileDialog}
            fetchRequisitions={fetchRequisitions}
            employeesData={employeesData}
            referenceData={referenceData}
            referralNameData={referralNameData}
          />
        ) : (
          <ViewRequisition
            data={requisitionDataDetails}
            open={viewProfileDialogOpen}
            onClose={closeViewProfileDialog}
            fetchRequisitions={fetchRequisitions}
            employeesData={employeesData}
            referenceData={referenceData}
            referralNameData={referralNameData}
          />
        )}

        <SubmitPreliminaryQuoteDialog
          data={requisitionDataDetails}
          open={submitForPreliminaryQuoteDialogOpen}
          onClose={closeSubmitForPreliminaryQuoteDialog}
          fetchRequisitions={fetchRequisitions}
          employeesData={employeesData}
        />

        <SubmitBoqDialog
          formData={requisitionDataDetails}
          open={submitForBoqDialogOpen}
          onClose={closeSubmitForBoqDialog}
          fetchRequisitions={fetchRequisitions}
          employeesData={employeesData}
        />

        <SampleRequisition
          open={sampleDialog}
          onClose={handleCloseSampleDialog}
          title={title}
        />
        <RequestForSiteVisit
          open={requestSiteVisitDialog}
          onClose={handleRequestSiteVisitTimings}
          currentRow={currentRow}
        />

        <RFQPreliminary
          open={rfqPreliminary}
          serviceTypeId={currentRow?.serviceTypeId}
          onClose={handleRfqPreliminaryClose}
          fetchRequisitions={fetchRequisitions}
        />

        <FinalBOQ
          open={finalBOQ}
          data={requisitionDataDetails}
          onClose={handleFinalBOQ}
          fetchRequisitions={fetchRequisitions}
        />

        <SelectOrApproveSpDialog
          data={currentRow}
          open={selectOrApproveSPDialogOpen}
          onClose={closeSelectOrApproveSpDialog}
          fetchRequisitions={fetchRequisitions}
          employeesData={employeesData}
        />

        <CardContent>
          <Box sx={{ display: "flex", flexWrap: "wrap", mb: 2 }}>
            {selectedFilters?.map((filter) => {
              if (filter.label === "Services" && Array.isArray(filter.value)) {
                // If the filter is for services and the value is an array of IDs
                return filter.value?.map((id) => {
                  // Find the corresponding object in servicesList for each ID
                  const matchedItem = servicesList.find(
                    (item) => item.value === id
                  );

                  // Use the key of matchedItem if found, otherwise display the ID itself
                  const displayValue = matchedItem ? matchedItem.key : id;

                  return (
                    <Chip
                      key={`${filter.key}-${id}`} // Ensure unique key for each chip
                      label={`${filter.label}: ${displayValue}`}
                      onDelete={() => handleRemoveFilter(filter.key, id)} // Pass both filter key and ID
                      sx={{ mr: 1, mb: 1 }}
                    />
                  );
                });
              }

              if (filter.label === "Priority" && Array.isArray(filter.value)) {
                // If the filter is for services and the value is an array of IDs
                return filter.value?.map((id) => {
                  // Find the corresponding object in servicesList for each ID
                  const matchedItem = priorityData.find(
                    (item) => item.value === id
                  );

                  // Use the key of matchedItem if found, otherwise display the ID itself
                  const displayValue = matchedItem ? matchedItem.key : id;

                  return (
                    <Chip
                      key={`${filter.key}-${id}`} // Ensure unique key for each chip
                      label={`${filter.label}: ${displayValue}`}
                      onDelete={() => handleRemoveFilter(filter.key, id)} // Pass both filter key and ID
                      sx={{ mr: 1, mb: 1 }}
                    />
                  );
                });
              }

              if (filter.label === "Status" && Array.isArray(filter.value)) {
                // If the filter is for services and the value is an array of IDs
                return filter.value?.map((id) => {
                  // Find the corresponding object in servicesList for each ID
                  const matchedItem = statusData.find(
                    (item) => item.value === id
                  );

                  // Use the key of matchedItem if found, otherwise display the ID itself
                  const displayValue = matchedItem ? matchedItem.key : id;

                  return (
                    <Chip
                      key={`${filter.key}-${id}`} // Ensure unique key for each chip
                      label={`${filter.label}: ${displayValue}`}
                      onDelete={() => handleRemoveFilter(filter.key, id)} // Pass both filter key and ID
                      sx={{ mr: 1, mb: 1 }}
                    />
                  );
                });
              }

              if (
                filter.label === "Assigned To" &&
                Array.isArray(filter.value)
              ) {
                // If the filter is for services and the value is an array of IDs
                return filter.value?.map((id) => {
                  // Find the corresponding object in servicesList for each ID
                  const matchedItem = employeesData.find(
                    (item) => item.id === id
                  );

                  // Use the key of matchedItem if found, otherwise display the ID itself
                  const displayValue = matchedItem ? matchedItem.name : id;

                  return (
                    <Chip
                      key={`${filter.key}-${id}`} // Ensure unique key for each chip
                      label={`${filter.label}: ${displayValue}`}
                      onDelete={() => handleRemoveFilter(filter.key, id)} // Pass both filter key and ID
                      sx={{ mr: 1, mb: 1 }}
                    />
                  );
                });
              }

              if (filter.label === "Reference Type") {
                const matchedItem = referenceData.find(
                  (item) => item.value === filter.value
                );

                // Use the key of matchedItem if found, otherwise display the ID itself
                const displayValue = matchedItem ? matchedItem.key : id;

                return (
                  <Chip
                    key={filter.key} // Ensure unique key for each chip
                    label={`${filter.label}: ${displayValue}`}
                    onDelete={() => handleRemoveFilter(filter.key)} // Pass both filter key and ID
                    sx={{ mr: 1, mb: 1 }}
                  />
                );
              }

              if (filter.label === "Referral Name") {
                const matchedItem = referralNameData.find(
                  (item) => item.value === filter.value
                );

                // Use the key of matchedItem if found, otherwise display the ID itself
                const displayValue = matchedItem ? matchedItem.key : id;

                return (
                  <Chip
                    key={filter.key} // Ensure unique key for each chip
                    label={`${filter.label}: ${displayValue}`}
                    onDelete={() => handleRemoveFilter(filter.key)} // Pass both filter key and ID
                    sx={{ mr: 1, mb: 1 }}
                  />
                );
              }
              if (filter.label === "Budget") {
                const units = budgetUnits.find(
                  (item) => item.value === filter.value.units
                )?.key;
                const condition = budgetCondition.find(item => item.value === filter.value.condition)?.key;


                const displayValue =  `${filter.value.rangeFrom} ${filter.value.rangeTo ? ` - ${filter.value.rangeTo}` : ""} ${units} ${condition ? condition : ""}`;
                return (
                  <Chip
                    key={filter.key} // Ensure unique key for each chip
                    label={`${filter.label}: ${displayValue}`}
                    onDelete={() => handleRemoveFilter(filter.key)} // Pass both filter key and ID
                    sx={{ mr: 1, mb: 1 }}
                  />
                );
              }

              // For other filters, render a single chip
              return (
                filter.label && ( // Only render the Chip if label is not null or undefined
                  <Chip
                    key={filter.key}
                    label={`${filter.label}: ${filter.value}`}
                    onDelete={() => handleRemoveFilter(filter.key)}
                    sx={{ mr: 1, mb: 1 }}
                  />
                )
              );
            })}
          </Box>

          {status && (
            <Chip
              label={
                <>
                  SR's with{" "}
                  <Typography
                    component="span"
                    sx={{ color: "primary.main", fontWeight: "bold" }}
                  >
                    {statusName}
                  </Typography>
                  's status
                </>
              }
              color="primary"
              variant="outlined"
              sx={{ mb: 3 }}
              onDelete={handleStatusClearFilter}
            />
          )}

          {zone && (
            <Chip
              label={
                <>
                  SR's with{" "}
                  <Typography
                    component="span"
                    sx={{ color: "primary.main", fontWeight: "bold" }}
                  >
                    {zoneName}
                  </Typography>
                  's zone
                </>
              }
              color="primary"
              variant="outlined"
              sx={{ mb: 3 }}
              onDelete={handleZoneClearFilter}
            />
          )}

          {quarter && (
            <Chip
              label={
                <>
                  SR's in Quarter{" "}
                  <Typography
                    component="span"
                    sx={{ color: "primary.main", fontWeight: "bold" }}
                  >
                    {quarter}
                  </Typography>
                </>
              }
              color="primary"
              variant="outlined"
              sx={{ mb: 3 }}
              onDelete={handleQuarterClearFilter}
            />
          )}

          {currentWeekSRs && (
            <Chip
              label={<> SR's created this week </>}
              color="primary"
              variant="outlined"
              sx={{ mb: 3 }}
              onDelete={handleCurrentWeekClearFilter}
            />
          )}

          {frequency && (
            <Chip
              label={
                <>
                  SR's{" "}
                  <Typography
                    component="span"
                    sx={{ color: "primary.main", fontWeight: "bold" }}
                  >
                    {(() => {
                      if (frequency === "daily") return " created today";
                      if (frequency === "monthly")
                        return " created by this month";
                      if (frequency === "yearly")
                        return " created by this Year";
                      if (frequency === "completed") return " Completed";
                    })()}
                  </Typography>
                </>
              }
              color="primary"
              variant="outlined"
              sx={{ mb: 3 }}
              onDelete={handleFrequencyClearFilter}
            />
          )}

          {(seriesName || duration) && (
            <Chip
              label={
                <>
                  SR's{" "}
                  <Typography
                    component="span"
                    sx={{ fontWeight: "bold", color: "primary.main" }}
                  >
                    {(() => {
                      if (seriesName === "assignedTo") return " Assigned to me";
                      if (seriesName === "createdBy") return " Created by me";
                    })()}
                    {(() => {
                      if (duration === "daily") return " today";
                      if (duration === "weekly") return " this week";
                      if (duration === "monthly") return " this month";
                      return " till now";
                    })()}
                  </Typography>{" "}
                </>
              }
              color="primary"
              variant="outlined"
              sx={{ mb: 3 }}
              onDelete={clearFilter}
            />
          )}

          {service && (
            <Chip
              label={
                <>
                  SR's with{" "}
                  <Typography
                    component="span"
                    sx={{ color: "primary.main", fontWeight: "bold" }}
                  >
                    {serviceName}
                  </Typography>
                  {""} service
                </>
              }
              color="primary"
              variant="outlined"
              sx={{ mb: 3 }}
              onDelete={handleServiceClearFilter}
            />
          )}

          {(fromDate && toDate) && (
                 <Chip
                 label={
                   <>
                     <Typography
                       component="span"
                       sx={{ fontWeight: 'bold', color: 'primary.main' }}
                     >

                      
                       {(() => {
                      
                        if (employee) return `Requisitions raised  by ${
                          employeesData?.find((item) => item.id === employee)?.name ||
                          ""
                        }`;
                        if (employeeId) return `Requisitions assigned to ${
                          employeesData?.find((item) => item.id === employeeId)?.name ||
                          ""
                        }`;
                      else return `Total Service Requisitions from ${fromDate} to ${toDate}`
                      
                    })()}
                      
                     </Typography>{' '}
                   </>
                 }
                 color="primary"
                 variant="outlined"
                 sx={{ mb: 3 }}
                 onDelete={clearFilterSR}
               />      
                )}

          {id && (
            <Chip
              label={<>Recently Assigned SR</>}
              color="primary"
              variant="outlined"
              sx={{ mb: 3 }}
              onDelete={handleIdClearFilter}
            />
          )}

          <div style={{ height: 380, width: "100%" }}>
            {loading ? (
              <Box
                display="flex"
                justifyContent="center"
                alignItems="center"
                height="60vh"
              >
                <FallbackSpinner />
              </Box>
            ) : (
              <>
                <DataGrid
                  rows={requisitionsList}
                  columns={columns}
                  pagination
                  pageSize={pageSize}
                  page={page - 1}
                  rowsPerPageOptions={rowsPerPageOptions}
                  rowCount={rowCount}
                  paginationMode="server"
                  onPageChange={handlePageChange}
                  onPageSizeChange={handlePageSizeChange}
                  rowHeight={38}
                  headerHeight={38}
                  components={{
                    NoRowsOverlay: () => (
                      <Typography
                        variant="body1"
                        align="center"
                        sx={{ marginTop: "120px" }}
                      >
                        {requisitionsList?.length === 0 ? "No Data" : "No Rows"}
                      </Typography>
                    ),
                  }}
                />
              </>
            )}
          </div>
        </CardContent>
      </Grid>

      <DecilogicReportDialog
        open={openDecilogicDialog}
        onClose={handleCloseDecilogicDialog}
        rowData={currentRow}
      />

      <DeActivateDialog
        open={openDeleteDialog}
        onClose={handleCloseDeleteDialog}
        data={currentRow}
      />

      {viewWorkOrderClick ? (
        <>
          <WorkOrderDetails
            open={viewWorkOrderClick}
            handleCloseWorkOrder={handleCloseWorkOrder}
          />
        </>
      ) : (
        ""
      )}

      <>
        <RequisitionDetailsDialog
          open={dialogOpen}
          onClose={() => setDialogOpen(false)}
          selectedRequisition={selectedRequisition}
          formatBudget={formatBudget}
        />
      </>
    </>
  );
};

export default ServiceRequisitions;
