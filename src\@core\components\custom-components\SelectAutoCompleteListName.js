import { CircularProgress, DialogContentText } from "@mui/material";
import Box from "@mui/material/Box";
import Button from "@mui/material/Button";
import Dialog from "@mui/material/Dialog";
import DialogActions from "@mui/material/DialogActions";
import DialogContent from "@mui/material/DialogContent";
import DialogTitle from "@mui/material/DialogTitle";
import FormControl from "@mui/material/FormControl";
import Grid from "@mui/material/Grid";
import IconButton from "@mui/material/IconButton";
import MenuItem from "@mui/material/MenuItem";
import TextField from "@mui/material/TextField";
import { alpha, styled } from "@mui/material/styles";
import { useState } from "react";
import { Controller, useForm } from "react-hook-form";
import CustomAutocomplete from "src/@core/components/custom-components/CustomAutoComplete";
import CustomTextField from "src/@core/components/custom-components/CustomTextField";
import Icon from "src/@core/components/icon";
import { useAuth } from "src/hooks/useAuth";

// Styled component for custom select item
const CustomSelectItem = styled(MenuItem)(({ theme }) => ({
  color: theme.palette.success.main,
  backgroundColor: "transparent !important",
  "&:hover": {
    color: `${theme.palette.success.main} !important`,
    backgroundColor: `${alpha(theme.palette.success.main, 0.1)} !important`,
  },
  "&.Mui-focusVisible": {
    backgroundColor: `${alpha(theme.palette.success.main, 0.2)} !important`,
  },
  "&.Mui-selected": {
    color: `${theme.palette.success.contrastText} !important`,
    backgroundColor: `${theme.palette.success.main} !important`,
    "&.Mui-focusVisible": {
      backgroundColor: `${theme.palette.success.dark} !important`,
    },
  },
}));

const SelectAutoCompleteListName = (props) => {
  const { control, reset } = useForm();
  const {
    id,
    label,
    nameArray,
    value,
    onChange,
    setValuesUpdate,
    setAddSection,
  } = props;

  const [selectedValue, setSelectedValue] = useState(value || null);
  const [isDropdownOpen, setDropdownOpen] = useState(false);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [newOption, setNewOption] = useState("");

  const [dialogMessage, setDialogMessage] = useState("");
  const [openDialogContent, setOpenDialogContent] = useState(false);
  const [loading,setLoading] = useState(false)

  // Handle option selection
  const handleOptionSelect = (event, option) => {
    if (option?.value === "add-new") {
      setDialogOpen(true); // Open dialog for "Add New"
      setAddSection(true);
    } else {
      const selectedValue = option ? option.value : null;
      setSelectedValue(selectedValue);
      if (onChange) {
        onChange({ target: { value: selectedValue } });
      }
    }
    setDropdownOpen(false);
  };

  const auth = useAuth();

  const handleSuccess = () => {
    const message = `
    <div> 
      <h3>label added Successfully.</h3>
    </div>
  `;
    setDialogMessage(message);
    setOpenDialogContent(true);
    reset();
  };

  const handleFailure = (err) => {
    let message;
    if (err.response.status == 400) {
      message = `
    <div>
      <h3>Label already exists!</h3>
    </div>
  `;
    } else {
      message = `
    <div> 
      <h3> Failed to Add Label. Please try again later.</h3>
    </div>
  `;
    }
    setDialogMessage(message);
    setOpenDialogContent(true);
  };

  // Add the new option to the list
  async function handleAddNew(data) {
    setValuesUpdate(false);
    setLoading(true)
    const fields = {
      name: newOption.trim(),
    };


    try {
      const response = await auth.postListName(
        fields,
        handleFailure,
        handleSuccess
      );
    } catch (error) {
      console.error("Service Data Creation failed:", error);
      handleFailure();
    }

    setAddSection(false);
    setLoading(false)
    setValuesUpdate(true);
    setNewOption("");
  }

  const handleButtonClick = () => {
    setOpenDialogContent(false);
    setDialogOpen(false);
  };

  return (
    <Box sx={{ position: "relative", zIndex: 1 }}>
      <CustomAutocomplete
        autoHighlight
        id={id}
        options={[{ key: "Add New", value: "add-new" }, ...nameArray]} // Move "Add New" to the top
        getOptionLabel={(option) => option.key || ""}
        value={
          nameArray.find((option) => option.value === selectedValue) || null
        }
        onChange={handleOptionSelect}
        open={isDropdownOpen}
        onOpen={() => setDropdownOpen(true)}
        onClose={() => setDropdownOpen(false)}
        renderOption={(props, option) => (
          <Box
            component="li"
            {...props}
            onClick={(event) => handleOptionSelect(event, option)}
          >
            {option.value === "add-new" ? (
              <CustomSelectItem component="li" {...props}>
                <Icon icon="tabler:circle-plus" />
                Add New
              </CustomSelectItem>
            ) : (
              option.key
            )}
          </Box>
        )}
        renderInput={(params) => (
          <CustomTextField
            {...params}
            size="small"
            // label={label}
            placeholder={`${label}`}
            InputLabelProps={{
              ...params.InputLabelProps,
              shrink: true,
            }}
          />
        )}
      />
      {/* Dialog for adding a new option */}
      <Dialog open={dialogOpen} onClose={() => setDialogOpen(false)} fullWidth>
        <DialogTitle
          sx={{
            position: "relative",
            borderBottom: (theme) => `1px solid ${theme.palette.divider}`,
            p: (theme) => `${theme.spacing(1.5)} !important`,
            display: "flex",
            flexDirection: "row",
            alignItems: "center",
            justifyContent: "space-between",
          }}
        >
          Add New
          <IconButton
            size="small"
            onClick={() => setDialogOpen(false)}
            sx={{
              // p: "0.438rem",
              borderRadius: 1,
              color: "common.white",
              backgroundColor: "primary.main",
              "&:hover": {
                backgroundColor: "#66BB6A",
                transition: "background 0.5s ease, transform 0.5s ease",
              },
            }}
          >
            <Icon icon="tabler:x" fontSize="1rem" />
          </IconButton>
        </DialogTitle>
        <DialogContent
          sx={{
            position: "relative",
            p: (theme) => `${theme.spacing(10, 8)} !important`,
          }}
        >
          <Grid item xs={12} sm={6}>
            <FormControl fullWidth sx={{ mt: 2 }}>
              <Controller
                name="name"
                control={control}
                rules={{ required: "This field is required" }}
                render={({ field }) => (
                  <TextField
                    {...field}
                    id="name"
                    label="Name"
                    size="small"
                    fullWidth
                    autoFocus
                    margin="dense"
                    value={newOption}
                    onChange={(e) => setNewOption(e.target.value)}
                    InputLabelProps={{
                      shrink: true,
                    }}
                  />
                )}
              />
            </FormControl>
          </Grid>
        </DialogContent>
        <DialogActions
          sx={{
            justifyContent: "end",
            borderTop: (theme) => `1px solid ${theme.palette.divider}`,
            p: (theme) => `${theme.spacing(2.5)} !important`,
            height: "50px",
          }}
        >
          <Button onClick={() => setDialogOpen(false)}>Cancel</Button>
          <Button onClick={handleAddNew} variant="contained" color="primary">
             {loading ? <CircularProgress color="inherit" size={24} /> : "Add"}
          </Button>
        </DialogActions>
      </Dialog>

      <Dialog
        open={openDialogContent}
        onClose={handleButtonClick}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
        PaperProps={{
          sx: {
            p: (theme) => `${theme.spacing(2.5)} !important`,
            backgroundColor: (theme) => theme.palette.primary.background,
          },
        }}
      >
        <Box
          sx={{
            width: "100%",
            borderRadius: 1,
            textAlign: "center",
            border: (theme) => `1px solid ${theme.palette.divider}`,
            borderColor: "primary.main",
          }}
        >
          <DialogContent>
            <DialogContentText
              id="alert-dialog-description"
              color="primary.main"
            >
              <div dangerouslySetInnerHTML={{ __html: dialogMessage }} />
            </DialogContentText>
          </DialogContent>
          <DialogActions>
            <Button
              variant="contained"
              onClick={handleButtonClick}
              sx={{ margin: "auto", width: 100 }}
            >
              Okay
            </Button>
          </DialogActions>
        </Box>
      </Dialog>
    </Box>
  );
};

export default SelectAutoCompleteListName;
