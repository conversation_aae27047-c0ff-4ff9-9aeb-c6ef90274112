import { DataGrid } from "@mui/x-data-grid";
import { useContext, useEffect, useState } from "react";
import authConfig from "src/configs/auth";
import { getAuthorizationHeaders, getUrl } from "src/helpers/utils";
import { useAuth } from "src/hooks/useAuth";
import { useRBAC } from "src/pages/permission/RBACContext";
import {
  Card,
  CardContent,
  Dialog,
  DialogContent,
  DialogTitle,
  Divider,
  FormControl,
  Grid,
  Button,
  IconButton,
  InputAdornment,
  Link,
  Menu,
  MenuItem,
  Chip,
  TextField,
  Tooltip,
  Typography,
} from "@mui/material";
import Icon from "src/@core/components/icon";
import CustomAvatar from "src/@core/components/mui/avatar";
import axios from "axios";
import { Controller, useForm } from "react-hook-form";
import { Box } from "@mui/system";
import SearchIcon from "@mui/icons-material/Search";
import { AuthContext } from "src/context/AuthContext";
import CloseExpandIcons from "src/@core/components/custom-components/CloseExpandIcons";
import Index from "src/pages/conversations";
import FallbackSpinner from "src/@core/components/spinner";
import IndexCHS from "src/pages/conversations/index-chs";
import AdvancedSearchCHS from "../AdvancedSearchCHS";
import AdvancedSearchConversations from "./AdvanceSearchConversations";
import { MENUS, PAGES, PERMISSIONS } from "src/constants";
import { useRouter } from "next/router";

const CallConversations = () => {
  const { user, listValues } = useContext(AuthContext);

  const [expanded, setExpanded] = useState(true);

  const handleToggle = () => {
    setExpanded(!expanded);
  };
  // Constants
  const rowsPerPageOptions = [10, 15, 20, 25, 50, 100];
  const [employeeData, setEmployeesData] = useState([]);
  const [currentRow, setCurrentRow] = useState("");
  const [isConversationDialogOpen, setConversationDialogOpen] = useState(false);
  const [addUserOpen, setAddUserOpen] = useState(false);
  const toggleAddUserDrawer = () => setAddUserOpen(!addUserOpen);
  const [initialRowCount, setInitialRowCount] = useState(null);

  const [searchData, setSearchData] = useState({});
  const [menu, setMenu] = useState(null);

  const [searchingState, setSearchingState] = useState(false);

  const [showAllServices, setShowAllServices] = useState(false);
  const [showAllLocations, setShowAllLocations] = useState(false);
  const [showAllLeadStatus, setShowAllLeadStatus] = useState(false);
  const [showAllLeadPriority, setShowAllLeadPriority] = useState(false);
  const [showAllAssignedTo, setShowAllAssignedTo] = useState(false);
  const [showAllPortalsRegistered, setShowAllPortalsRegistered] =
    useState(false);

  const [showAllOutcome, setShowAllOutcome] = useState(false);
  const [showAllTarget, setShowAllTarget] = useState(false);
  const [showAllShallRemind, setShowAllShallRemind] = useState(false);
  const [showAllType, setShowAllType] = useState(false);

  const [matchedOutcome, setMatchedOutcome] = useState([]);
  const [matchedTarget, setMatchedTarget] = useState([]);
  const [matchedShallRemind, setMatchedShallRemind] = useState([]);
  const [matchedType, setMatchedType] = useState([]);

  const [matchedLocations, setMatchedLocations] = useState([]);
  const [matchedServices, setMatchedServices] = useState([]);
  const [matchedLeadStatus, setMatchedLeadStatus] = useState([]);
  const [matchedLeadPriority, setMatchedLeadPriority] = useState([]);
  const [matchedAssignedTo, setMatchedAssignedTo] = useState([]);
  const [matchedPortalsRegistered, setPortalsRegistered] = useState([]);
  // const handleCloseDialog = () => {
  const handleCloseMenuItems = () => {
    setMenu(null);
  };

  // Define columns
  const columns = [
    {
      field: "fullName",
      minWidth: 120,
      headerName: "Name",
      flex: 0.3, // Adjust the flex as needed
      renderCell: (params) => (
        <Tooltip title={`${params.row.fullName}`}>
          <span>{params.value}</span>
        </Tooltip>
      ),
    },
    {
      field: "orgName",
      minWidth: 120,
      headerName: "Society Name",
      flex: 0.3,
      renderCell: (params) => (
        <Tooltip title={`${params.row.orgName}`}>
          <span>{params.value}</span>
        </Tooltip>
      ),
    },
    {
      field: "email",
      minWidth: 115,
      headerName: "Email",
      flex: 0.2,
      renderCell: (params) => {
        const email = params?.value;

        return (
          <Tooltip title={email}>
            <Link
              href={`mailto:${email}`}
              target="_blank"
              rel="noopener noreferrer"
              sx={{
                color: "#6666ff",
                textDecoration: "underline",
                cursor: "pointer",
              }}
            >
              {email}
            </Link>
          </Tooltip>
        );
      },
    },
    {
      field: "mobileNumber",
      minWidth: 125,
      headerName: "Mobile No",
      flex: 0.14,
      valueGetter: (params) => params.row.basicProfile?.mobileNumber,
      renderCell: (params) => {
        const mobileNumber = params?.value;

        return (
          <Tooltip title={mobileNumber}>
            <Link
              href={`tel:${mobileNumber}`}
              target="_blank"
              rel="noopener noreferrer"
              sx={{
                color: "#6666ff",
                textDecoration: "underline",
                cursor: "pointer",
              }}
            >
              {mobileNumber}
            </Link>
          </Tooltip>
        );
      },
    },
    {
      flex: 0.15,
      minWidth: 120,
      field: "assignedTo",
      headerName: "Assigned To",
      renderCell: (params) => {
        const assignedTo = employeeData.find(
          (item) => item.id === params.row.assignedTo
        );
        return <span>{assignedTo ? assignedTo.name : ""}</span>;
      },
    },
    {
      flex: 0.2,
      minWidth: 120,
      field: "nextConversationDate",
      headerName: "Next Conversation Date",
    },
    {
      flex: 0.2,
      minWidth: 115,
      field: "remarks",
      headerName: "Remarks",
      renderCell: (params) => {
        const remarks = params.value; // Fallback message if no remarks are present
        return (
          <Tooltip title={remarks}>
            <span>{remarks}</span>
          </Tooltip>
        );
      },
    },
    {
      flex: 0.077,
      field: "edit",
      headerName: "Actions",
      sortable: false,
      minWidth: 120,
      disableClickEventBubbling: true,
      renderCell: (params) => {
        const handleClickMenu = (event) => {
          event.stopPropagation();
          setMenu(event.currentTarget);
          const row = params.row;
          setCurrentRow(row);
        };

        const onClickEdit = () => {
          setConversationDialogOpen(true);
          handleCloseMenuItems();
        };

        return (
          <div
            style={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
            }}
          >
            <CustomAvatar
              skin="light"
              variant="rounded"
              sx={{
                mr: { xs: 2, lg: 4 },
                width: 34,
                height: 34,
                cursor: "pointer",
              }}
              onClick={handleClickMenu}
            >
              <Icon icon="bi:three-dots-vertical" />
            </CustomAvatar>
            <Menu
              id="actions-menu"
              anchorEl={menu}
              keepMounted
              open={Boolean(menu)}
              onClose={handleCloseMenuItems}
            >
              <MenuItem onClick={onClickEdit}>Edit</MenuItem>
            </Menu>
          </div>
        );
      },
    },
  ];

  // Use States
  const {
    register,
    handleSubmit,
    setError,
    control,
    reset,
    clearErrors,
    formState: { errors },
  } = useForm();

  const auth = useAuth();
  const [userList, setUserList] = useState([]);
  const [pageSize, setPageSize] = useState(rowsPerPageOptions[0]);
  const [loading, setLoading] = useState(true);
  const [page, setPage] = useState(1);
  const [rowCount, setRowCount] = useState(0);
  const [keyword, setKeyword] = useState("");
  const [searchKeyword, setSearchKeyword] = useState("");
  const router = useRouter();

  const { fromDate, toDate, employee } = router.query;

  // Define getRowId function to extract userId as the row id
  const getRowId = (row) => row.userId;

  const openConversationDialog = (row) => {
    setConversationDialogOpen(true);
    setCurrentRow(row);
  };

  const closeConversationDialog = () => {
    setConversationDialogOpen(false);
    fetchUsers(page, pageSize, searchKeyword);
  };

  // Static user list data
  const staticUserList = [
    {
      userId: 1,
      firstName: "Alice",
      lastName: "Williams",
      email: "<EMAIL>",
      basicProfile: {
        mobileNumber: "1234567890",
        assignedTo: "Sri",
        leadStatus: "Closed",
        remarks: "Interested",
      },
    },
  ];

  useEffect(() => {

    // Fetch all employees
    axios({
      method: "get",
      url: getUrl(authConfig.selectDropdownNew) + "?selectionType=EMPLOYEES",
      headers: getAuthorizationHeaders(),
    })
      .then((res) => {
        setEmployeesData(res.data.data);
      })
      .catch((err) => console.log("Employees error", err));
  }, []);

  useEffect(() => {
    fetchUsers(page, pageSize, searchKeyword, searchData);
  }, [page, pageSize, searchKeyword, searchData]);

  const fetchUsers = async (
    currentPage,
    currentPageSize,
    searchKeyword,
    dataSearch
  ) => {
    const url = getUrl(authConfig.getAllConversationsCHS);
    const headers = getAuthorizationHeaders();

    const data = {
      page: currentPage,
      pageSize: currentPageSize,
      searchKeyword: searchKeyword,
      serviceProvidersLastSevenDays: false,
      serviceTypeUUIDs:
        dataSearch?.serviceTypeUUIDs?.map((services) => services.value) || [],
      locationUUIDs:
        dataSearch?.locationUUIDs?.map((location) => location.value) || [],
      leadStatusUUIDs:
        dataSearch?.leadStatusUUIDs?.map((leadStatus) => leadStatus.value) ||
        [],
      leadPriorityUUIDs:
        dataSearch?.leadPriorityUUIDs?.map(
          (leadPriority) => leadPriority.value
        ) || [],
      outcomeUUIDs:
        dataSearch?.outcomeUUIDs?.map((outcome) => outcome.value) || [],
      targetUUIDs: dataSearch?.targetUUIDs?.map((target) => target.value) || [],
      shallRemindUUIDs:
        dataSearch?.shallRemindUUIDs?.map((shallRemind) => shallRemind.value) ||
        [],
      typeUUIDs: dataSearch?.typeUUIDs?.map((type) => type.value) || [],
      fromDate: dataSearch?.fromDate || null,
      toDate: dataSearch?.toDate || null,
      assignedToEmployeeId: dataSearch?.assignedToEmployeeId || null,
    };

    try {
      const response = await axios({
        method: "post",
        url: url,
        headers: headers,
        data: data,
      });
      if (initialRowCount == null) {
        setInitialRowCount(response.data.rowCount || 0);
      }
      if (response.data) {
        setUserList(response.data?.conversations || []);
        setRowCount(response.data?.rowCount || 0);
      } else {
        console.error("Unexpected API response format:", response);
      }
    } catch (error) {
      console.error("Error fetching users:", error);
    } finally {
      setLoading(false);
    }
  };

  const handlePageChange = (direction) => {
    if (direction === page) {
      setPage(page + 1);
    } else {
      setPage(page - 1);
    }
  };

  const handlePageSizeChange = (params) => {
    if (params) {
      setPageSize(params);
      setPage(1);
    }
  };

  const clearFilter = () => {
    router.replace(
      {
        query: {}, // Clear all query parameters
      },
      undefined,
      { shallow: false } // Allow full page reload
    );

    setSearchData(() => ({
      fromDate: null,
      toDate: null,
      assignedToEmployeeId: null,
    }));
  };

  useEffect(() => {
    reset(searchData);
  }, [searchData]);

  useEffect(() => {
    const locationUUIDsKeys =
      searchData?.locationUUIDs?.map((item) => item.key) || [];
    setMatchedLocations(locationUUIDsKeys);
    const serviceTypeUUIDsKeys =
      searchData?.serviceTypeUUIDs?.map((item) => item.key) || [];
    setMatchedServices(serviceTypeUUIDsKeys);

    const leadStatusUUIDsKeys =
      searchData?.leadStatusUUIDs?.map((item) => item.key) || [];
    setMatchedLeadStatus(leadStatusUUIDsKeys);

    const leadPriorityUUIDsKeys =
      searchData?.leadPriorityUUIDs?.map((item) => item.key) || [];
    setMatchedLeadPriority(leadPriorityUUIDsKeys);

    const assignedToUUIDsKeys =
      searchData?.assignedToUUIDs?.map((item) => item.key) || [];
    setMatchedAssignedTo(assignedToUUIDsKeys);

    const portalsRegisteredUUIDsKeys =
      searchData?.portalsRegisteredUUIDs?.map((item) => item.key) || [];
    setPortalsRegistered(portalsRegisteredUUIDsKeys);

    const outcomeUUIDsKeys =
      searchData?.outcomeUUIDs?.map((item) => item.key) || [];
    setMatchedOutcome(outcomeUUIDsKeys);

    const targetUUIDsKeys =
      searchData?.targetUUIDs?.map((item) => item.key) || [];
    setMatchedTarget(targetUUIDsKeys);

    const shallRemindUUIDsKeys =
      searchData?.shallRemindUUIDs?.map((item) => item.key) || [];
    setMatchedShallRemind(shallRemindUUIDsKeys);

    const typeUUIDsKeys = searchData?.typeUUIDs?.map((item) => item.key) || [];
    setMatchedType(typeUUIDsKeys);
  }, [searchData, listValues]);

  useEffect(() => {
    if (fromDate && toDate) {
      setSearchData((prev) => {
        const updatedData = { ...prev };

        updatedData.fromDate = fromDate;
        updatedData.toDate = toDate;

        if (employee) {
          updatedData.assignedToEmployeeId = employee;
        }
        return updatedData;
      });
    }
  }, [fromDate, toDate, employee]);

  const handleRemoveFilter = (filterType, value = null) => {
    const updatedData = { ...searchData };

    if (typeof updatedData[filterType] === "boolean") {
      updatedData[filterType] = false;
    } else if (typeof updatedData[filterType] === "string") {
      updatedData[filterType] = "";
    } else {
      if (value !== null) {
        updatedData[filterType] = updatedData[filterType].filter(
          (item) => item.key !== value
        );
      }
    }

    setSearchData(updatedData);
    //fetchUsers(page, pageSize, searchKeyword, updatedData);
  };

  const getVisibleChips = (dataArray, showAll) => {
    return showAll ? dataArray : dataArray.slice(0, 5);
  };
  const renderFilterSection = (
    label,
    data,
    matchedItems,
    showAll,
    setShowAll,
    handleRemoveFilter,
    UUIDs
  ) => {
    if (!data || data.length === 0) {
      return null;
    }

    const commonStyle = {
      fontSize: "12px",
      height: "24px",
      "& .MuiChip-label": {
        padding: "0 8px",
      },
      "& .MuiSvgIcon-root": {
        fontSize: "16px",
      },
    };

    const { canMenuPageSection, rbacRoles } = useRBAC();

  

    const canAccessCHSConversations = (requiredPermission) =>
      canMenuPageSection(
        MENUS.LEFT,
        PAGES.ENGAGE,
        PAGES.CHS_CONVERSATIONS,
        requiredPermission
      );

    useEffect(() => {
      if (rbacRoles != null && rbacRoles.length > 0) {
        if (!canAccessCHSConversations(PERMISSIONS.READ)) {
          router.push("/401");
        }
      }
    }, [rbacRoles]);
    if (canAccessCHSConversations(PERMISSIONS.READ)) {
      return (
        <Box display="flex" flexDirection="column" gap={1}>
          <Box display="flex" flexWrap="wrap" gap={1} sx={{ marginTop: "6px" }}>
            {/* Label */}
            <Typography
              variant="subtitle1"
              sx={{
                fontSize: "12px",
                fontWeight: "bold",
                height: "24px",
                "& .MuiChip-label": {
                  padding: "0 8px",
                },
                "& .MuiSvgIcon-root": {
                  fontSize: "16px",
                },
              }}
            >
              {label}:
            </Typography>
            {/* Chips */}
            {getVisibleChips(matchedItems, showAll).map((item, index) => (
              <Chip
                key={index}
                label={item}
                onDelete={() => handleRemoveFilter(UUIDs, item)}
                variant="outlined"
                sx={commonStyle}
              />
            ))}
            {/* View All / View Less Button */}
            {matchedItems.length > 5 && (
              <Button
                size="small"
                onClick={() => setShowAll(!showAll)}
                sx={commonStyle}
              >
                {showAll ? "View Less ↑" : "View All ↓"}
              </Button>
            )}
          </Box>
        </Box>
      );
    } else {
      return null;
    }
  };


  return (
    <>
      <Dialog
        fullScreen
        open={isConversationDialogOpen}
        onClose={closeConversationDialog}
      >
        <DialogTitle
          sx={{
            position: "relative",
            borderBottom: (theme) => `1px solid ${theme.palette.divider}`,
            p: (theme) => `${theme.spacing(1.5)} !important`,
            display: "flex",
            flexDirection: "row", // Change to row for horizontal alignment
            alignItems: "center",
            justifyContent: "space-between", // Changed to evenly distribute space
            height: "50px", // height
          }}
          textAlign="center"
          fontSize="20px !important"
          fontWeight="bold"
        >
          <div style={{ flex: 1, textAlign: "left" }}>
            <Typography
              variant="h6"
              fontWeight="bold"
              sx={{
                fontSize: "18px",
                marginLeft: { xl: 4, lg: 4, md: 4, sm: 4, xs: 4 },
              }}
            >
              Conversation Details
            </Typography>
          </div>
          <div
            style={{
              display: "flex",
              alignItems: "center",
              justifyContent: "flex-end",
              gap: 2,
            }}
          >
            {/* <CloseExpandIcons
                expanded={expanded}
                onToggle={handleToggle}
                sx={{ mt: 4 }}
              /> */}
            <IconButton
              size="small"
              onClick={closeConversationDialog}
              sx={{
                borderRadius: 1,
                mt: 3,
                mb: 3,
                mr: 5,
                color: "common.white",
                backgroundColor: "primary.main",
                "&:hover": {
                  backgroundColor: "#66BB6A",
                  transition: "background 0.5s ease, transform 0.5s ease",
                },
              }}
            >
              <Icon icon="tabler:x" fontSize="1rem" />
            </IconButton>
          </div>
        </DialogTitle>

        <DialogContent>
          {employeeData && (
            <>
              <IndexCHS
                closeConversationDialog={close}
                employeeData={employeeData}
                currentRow={currentRow}
              />
            </>
          )}
        </DialogContent>
      </Dialog>
      <div>
        <Box
          sx={{
            py: 3,
            px: 6,
            rowGap: 2,
            columnGap: 4,
            display: "flex",
            flexWrap: "wrap",
            alignItems: "center",
            justifyContent: "space-between",
          }}
        >
          <Grid container spacing={3} alignItems="center">
            <Grid item xs={12} sm={12}>
              <Grid
                container
                spacing={2}
                alignItems="center"
                justifyContent="flex-end"
              >
                <Grid item xs={12} sm={4} sx={{ textAlign: "flex-start" }}>
                  <Typography variant="h6" fontWeight={"600"}>
                    List of CHS Conversations
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={8}>
                  <Grid
                    container
                    spacing={2}
                    alignItems="center"
                    justifyContent="flex-end"
                  >
                    <Grid
                      item
                      // xs={3}
                      // sm="auto"
                      sx={{
                        paddingTop: { xs: "15px", sm: "25px" },
                        mr: "6px",
                        ml: "6px",
                        marginTop: { xs: "0.5rem", sm: "0rem" },
                      }}
                    >
                      {/* <AdvancedSearchConversations
                        open={addUserOpen}
                        toggle={toggleAddUserDrawer}
                        searchKeyword={searchKeyword}
                        searchData={searchData}
                        setSearchKeyword={setSearchKeyword}
                        setSearchData={setSearchData}
                        fetchUsers={fetchUsers}
                        page={page}
                        pageSize={pageSize}
                        searchingState={searchingState}
                        setSearchingState= {setSearchingState}
                      /> */}
                    </Grid>
                  </Grid>
                </Grid>
              </Grid>
            </Grid>
          </Grid>
        </Box>
        <Divider />

        <CardContent>
          <div style={{ fontSize: "13px", marginBottom: "12px" }}>
            {searchingState &&
              (searchKeyword ||
              searchData?.serviceTypeUUIDs?.length > 0 ||
              searchData?.locationUUIDs?.length > 0 ||
              searchData?.leadStatusUUIDs?.length > 0 ||
              searchData?.leadPriorityUUIDs?.length > 0 ||
              searchData?.assignedToUUIDs?.length > 0 ||
              searchData?.outcomeUUIDs?.length > 0 ||
              searchData?.targetUUIDs?.length > 0 ||
              searchData?.typeUUIDs?.length > 0 ||
              searchData?.shallRemindUUIDs?.length > 0 ||
              searchData?.searchKeywordByComments ||
              searchData?.searchKeywordByEmail ||
              searchData?.searchKeywordByCompanyName ||
              searchData?.searchKeywordByMobile ? (
                <div style={{ marginBottom: "16px" }}>
                  Showing {rowCount} of {initialRowCount} Results |
                  <>
                    {searchKeyword !== "" && (
                      <Chip
                        label={
                          <span>
                            <span style={{ fontWeight: "bold" }}>Name:</span>{" "}
                            {searchKeyword}
                          </span>
                        }
                        onDelete={() => setSearchKeyword("")}
                        variant="outlined"
                        sx={{
                          fontSize: "12px",
                          height: "24px",
                          "& .MuiChip-label": {
                            padding: "0 8px",
                          },
                          "& .MuiSvgIcon-root": {
                            fontSize: "16px",
                          },
                        }}
                      />
                    )}
                    {searchData?.searchKeywordByEmail && (
                      <Chip
                        label={
                          <span>
                            <span style={{ fontWeight: "bold" }}>Email:</span>{" "}
                            {searchData?.searchKeywordByEmail}
                          </span>
                        }
                        onDelete={() =>
                          handleRemoveFilter("searchKeywordByEmail")
                        }
                        variant="outlined"
                        sx={{
                          fontSize: "12px",
                          height: "24px",
                          "& .MuiChip-label": {
                            padding: "0 8px",
                          },
                          "& .MuiSvgIcon-root": {
                            fontSize: "16px",
                          },
                        }}
                      />
                    )}
                    {searchData?.searchKeywordBySocietyName && (
                      <Chip
                        label={
                          <span>
                            <span style={{ fontWeight: "bold" }}>
                              Society Name:
                            </span>{" "}
                            {searchData?.searchKeywordBySocietyName}
                          </span>
                        }
                        onDelete={() =>
                          handleRemoveFilter("searchKeywordBySocietyName")
                        }
                        variant="outlined"
                        sx={{
                          fontSize: "12px",
                          height: "24px",
                          "& .MuiChip-label": {
                            padding: "0 8px",
                          },
                          "& .MuiSvgIcon-root": {
                            fontSize: "16px",
                          },
                        }}
                      />
                    )}
                    {searchData?.searchKeywordByCompanyName && (
                      <Chip
                        label={
                          <span>
                            <span style={{ fontWeight: "bold" }}>
                              Company Name:
                            </span>{" "}
                            {searchData?.searchKeywordByCompanyName}
                          </span>
                        }
                        onDelete={() =>
                          handleRemoveFilter("searchKeywordByCompanyName")
                        }
                        variant="outlined"
                        sx={{
                          fontSize: "12px",
                          height: "24px",
                          "& .MuiChip-label": {
                            padding: "0 8px",
                          },
                          "& .MuiSvgIcon-root": {
                            fontSize: "16px",
                          },
                        }}
                      />
                    )}
                    {searchData?.searchKeywordByMobile && (
                      <Chip
                        label={
                          <span>
                            <span style={{ fontWeight: "bold" }}>Mobile:</span>{" "}
                            {searchData?.searchKeywordByMobile}
                          </span>
                        }
                        onDelete={() =>
                          handleRemoveFilter("searchKeywordByMobile")
                        }
                        variant="outlined"
                        sx={{
                          fontSize: "12px",
                          height: "24px",
                          "& .MuiChip-label": {
                            padding: "0 8px",
                          },
                          "& .MuiSvgIcon-root": {
                            fontSize: "16px",
                          },
                        }}
                      />
                    )}
                    {searchData?.searchKeywordByComments && (
                      <Chip
                        label={
                          <span>
                            <span style={{ fontWeight: "bold" }}>
                              Comments:
                            </span>{" "}
                            {searchData?.searchKeywordByComments}
                          </span>
                        }
                        onDelete={() =>
                          handleRemoveFilter("searchKeywordByComments")
                        }
                        variant="outlined"
                        sx={{
                          fontSize: "12px",
                          height: "24px",
                          "& .MuiChip-label": {
                            padding: "0 8px",
                          },
                          "& .MuiSvgIcon-root": {
                            fontSize: "16px",
                          },
                        }}
                      />
                    )}

                    {renderFilterSection(
                      "Service",
                      searchData?.serviceTypeUUIDs,
                      matchedServices,
                      showAllServices,
                      setShowAllServices,
                      handleRemoveFilter,
                      "serviceTypeUUIDs"
                    )}
                    {renderFilterSection(
                      "Location",
                      searchData?.locationUUIDs,
                      matchedLocations,
                      showAllLocations,
                      setShowAllLocations,
                      handleRemoveFilter,
                      "locationUUIDs"
                    )}
                    {renderFilterSection(
                      "Lead Status",
                      searchData?.leadStatusUUIDs,
                      matchedLeadStatus,
                      showAllLeadStatus,
                      setShowAllLeadStatus,
                      handleRemoveFilter,
                      "leadStatusUUIDs"
                    )}
                    {renderFilterSection(
                      "Lead Priority",
                      searchData?.leadPriorityUUIDs,
                      matchedLeadPriority,
                      showAllLeadPriority,
                      setShowAllLeadPriority,
                      handleRemoveFilter,
                      "leadPriorityUUIDs"
                    )}
                    {renderFilterSection(
                      "Assigned To",
                      searchData?.assignedToUUIDs,
                      matchedAssignedTo,
                      showAllAssignedTo,
                      setShowAllAssignedTo,
                      handleRemoveFilter,
                      "assignedToUUIDs"
                    )}
                    {renderFilterSection(
                      "Portals Registered",
                      searchData?.portalsRegisteredUUIDs,
                      matchedPortalsRegistered,
                      showAllPortalsRegistered,
                      setShowAllPortalsRegistered,
                      handleRemoveFilter,
                      "portalsRegisteredUUIDs"
                    )}
                    {renderFilterSection(
                      "Outcome",
                      searchData?.outcomeUUIDs,
                      matchedOutcome,
                      showAllOutcome,
                      setShowAllOutcome,
                      handleRemoveFilter,
                      "outcomeUUIDs"
                    )}
                    {renderFilterSection(
                      "Target",
                      searchData?.targetUUIDs,
                      matchedTarget,
                      showAllTarget,
                      setShowAllTarget,
                      handleRemoveFilter,
                      "targetUUIDs"
                    )}
                    {renderFilterSection(
                      "Shall Remind",
                      searchData?.shallRemindUUIDs,
                      matchedShallRemind,
                      showAllShallRemind,
                      setShowAllShallRemind,
                      handleRemoveFilter,
                      "shallRemindUUIDs"
                    )}
                    {renderFilterSection(
                      "Type",
                      searchData?.typeUUIDs,
                      matchedType,
                      showAllType,
                      setShowAllType,
                      handleRemoveFilter,
                      "typeUUIDs"
                    )}
                  </>
                </div>
              ) : (
                setSearchingState(false)
              ))}
            {fromDate && toDate && (
              <Chip
                label={
                  <>
                    <Typography
                      component="span"
                      sx={{ fontWeight: "bold", color: "primary.main" }}
                    >
                      {(() => {
                        if (employee)
                          return `Conversations with Societies  by ${
                            employeeData?.find((item) => item.id === employee)
                              ?.name || ""
                          }`;
                        else return "Total Conversations with Societies";
                      })()}
                    </Typography>{" "}
                  </>
                }
                color="primary"
                variant="outlined"
                sx={{ mb: 3 }}
                onDelete={clearFilter}
              />
            )}
          </div>

          <div style={{ height: 380, width: "100%" }}>
            {loading ? (
              <Box
                display="flex"
                justifyContent="center"
                alignItems="center"
                height="60vh"
              >
                <FallbackSpinner />
              </Box>
            ) : (
              <DataGrid
                rows={userList || []}
                columns={columns}
                pagination
                pageSize={pageSize}
                page={page - 1}
                rowsPerPageOptions={rowsPerPageOptions}
                rowCount={rowCount}
                paginationMode="server"
                onPageChange={handlePageChange}
                onPageSizeChange={handlePageSizeChange}
                getRowId={getRowId} // Specify getRowId function
                rowHeight={38}
                headerHeight={38}
                components={{
                  NoRowsOverlay: () => (
                    <Typography
                      variant="body1"
                      align="center"
                      sx={{ marginTop: "120px" }}
                    >
                      {userList?.length === 0 ? "No Data" : "No Rows"}
                    </Typography>
                  ),
                }}
              />
            )}
          </div>
        </CardContent>
      </div>
    </>
  );
};
export default CallConversations;
