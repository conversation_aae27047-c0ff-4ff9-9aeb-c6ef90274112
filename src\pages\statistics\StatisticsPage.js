import React, { useContext, useState, useEffect } from "react";
import {
  TableRow,
  TableCell,
  FormControlLabel,
  Checkbox,
  TextField,
  Grid,
  Button,
  Typography,
  TableContainer,
  Paper,
  Snackbar,
  Alert,
} from "@mui/material";
import { useForm } from "react-hook-form";
import { AuthContext } from "src/context/AuthContext";

function StatisticsPage({
  data,
  onCancel,
  userDataAllProfile,
  serviceId,
  finalUser,
}) {

  const { control } = useForm();
  const [selectedOptions, setSelectedOptions] = useState({});
  const [checkedCount, setCheckedCount] = useState(0);
  const [categoryValues, setCategoryValues] = useState({});
  const [errors, setErrors] = useState({});
  const [openSnackbar, setOpenSnackbar] = useState(false);
  const { user, micrositeStatistics, statisticsUpdate, fetchStatistics } =
    useContext(AuthContext);

  const userUniqueId = userDataAllProfile?.id != null ? userDataAllProfile?.id : user?.id;

  const handleChange = (categoryId, event) => {
    if (event.target.checked && checkedCount === 4) {
      return;
    }

    setSelectedOptions((prevOptions) => {
      const newOptions = {
        ...prevOptions,
        [categoryId]: event.target.checked,
      };

      const newCount = Object.values(newOptions).filter(Boolean).length;
      setCheckedCount(newCount);

      return newOptions;
    });
  };

  const handleTextChange = (categoryId, value) => {
    setCategoryValues((prevValues) => ({
      ...prevValues,
      [categoryId]: value,
    }));
  };

  useEffect(() => {
    if (finalUser && finalUser.metadata && finalUser.metadata.listNames) {
      const initialCategoryValues = {};
      const initialSelectedOptions = {};
      let initialCheckedCount = 0;

      data.forEach((category) => {
        const foundStatistic = finalUser.metadata.listNames.find(
          (statistic) => statistic.listNameId === category.id
        );
        if (foundStatistic) {
          initialCategoryValues[category.id] = foundStatistic.otherValue;
          initialSelectedOptions[category.id] = foundStatistic.isChecked;
          if (foundStatistic.isChecked) {
            initialCheckedCount += 1;
          }
        }
      });

      setCategoryValues(initialCategoryValues);
      setSelectedOptions(initialSelectedOptions);
      setCheckedCount(initialCheckedCount);
    }
  }, [finalUser, data]);

  const handleSave = async () => {
    const newErrors = {};
    let hasErrors = false;

    data.forEach((category) => {
      if (selectedOptions[category.id] && !categoryValues[category.id]) {
        newErrors[category.id] = `Value for ${category.name} is required`;
        hasErrors = true;
      }
    });

    setErrors(newErrors);

    if (hasErrors) {
      return;
    }

    const formData = data.map((category) => ({
      listNameId: category.id,
      listValues: null,
      otherValue: categoryValues[category.id],
      isChecked: selectedOptions[category.id] || false, // Include isChecked field
    }));

    const individualId = finalUser.user_id || finalUser.createdBy;

    const payload = {
      id: finalUser.id,
      individualId:finalUser.user_id || finalUser.createdBy,
      metadata: {
        listNames: formData,
      },
      isActive: true,
      serviceNameId: finalUser.serviceNameId,
      userServicesDataGroup: finalUser.userServicesDataGroup,
    };

    try {
      await statisticsUpdate(payload,individualId, () => {
        console.error("Statistics data Details failed");
      });
      fetchStatistics( individualId || user?.id);
      setOpenSnackbar(true); // Show snackbar on successful save
      setTimeout(() => {
        setOpenSnackbar(false);
        onCancel();
      }, 2000);
    } catch (error) {
      console.error("Error occurred:", error);
    }
  };

  const handleSnackbarClose = () => {
    setSnackbarOpen(false);
  };

  return (
    <>
      <TableContainer component={Paper}>
      <TableRow>
          <TableCell colSpan={2}>
            <Grid container spacing={2} justifyContent="flex-start">
              <Grid item>
                <Typography>Select any 4 Questions before answering</Typography>
              </Grid>
          </Grid>
        </TableCell>
      </TableRow>
      {data?.map((category) => (
          category?.isActive && (
          <TableRow key={category.id}>
            <TableCell>
              <FormControlLabel
                control={
                  <Checkbox
                  id="checkbox"
                    checked={selectedOptions[category.id] || false}
                    onChange={(event) => handleChange(category.id, event)}
                    disabled={checkedCount === 4 && !selectedOptions[category.id]}
                  />
                }
                sx={{minWidth:'250px'}}
                label={category.name}
              />
            </TableCell>
            <TableCell>
              <TextField
              id="textfield"
                size="small"
                sx={{minWidth:'150px'}}
                value={categoryValues[category.id] || ""}
                onChange={(e) => handleTextChange(category.id, e.target.value)}
                label={`Value for ${category.name}`}
                error={!!errors[category.id]}
                helperText={errors[category.id] || ""}
                disabled={!selectedOptions[category.id]}
                required={selectedOptions[category.id]}
                inputProps={{ maxLength: 25 }} 
              />
            </TableCell>
          </TableRow>
          )
        ))}
      </TableContainer>
      <Grid
        container
        justifyContent="center"
        sx={{
          mt: { xs: 4, lg: 4 },
          mb: { xs: 2, lg: 4 },
        }}
      >
        
        <Button
        id="cancelButton"
          size="medium"
          sx={{ mr: 3 }}
          type="submit"
          color="primary"
          onClick={() => onCancel()}              
        >
          Cancel
        </Button>
        <Button
        id="saveButton"
          size="medium"
          variant="contained"
          color="primary"
          onClick={handleSave}
        >
          Save
        </Button>
      </Grid>
      <Snackbar
        open={openSnackbar}
        autoHideDuration={2000}
        onClose={() => setOpenSnackbar(false)}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert color="success" sx={{ width: '100%', backgroundColor: 'green', color: 'white' }}>
          Answers successfully saved!
        </Alert>
      </Snackbar>
    </>
  );
}

export default StatisticsPage;
