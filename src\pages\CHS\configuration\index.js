import {
  <PERSON>,
  <PERSON><PERSON>,
  Card,
  CardContent,
  CircularProgress,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  Typography
} from "@mui/material";
import Grid from "@mui/material/Grid";
import axios from "axios";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import SelectAutoComplete from "src/@core/components/custom-components/SelectAutoComplete";
import authConfig from "src/configs/auth";
import { getAuthorizationHeaders, getUrl } from "src/helpers/utils";
import { useAuth } from "src/hooks/useAuth";
import { useRBAC } from "src/pages/permission/RBACContext";
import { MENUS, PAGES, PERMISSIONS } from "src/constants";
import { useRouter } from "next/router";

  const CHSConfiguration = () => {

  
    const auth = useAuth();

    const [listOfEmployees, setListOfEmployees] = useState([]);
    const [employeesOptions, setEmployeesOptions] = useState([]);

    const [employeeId, setEmployeeId] = useState("");

    const [clickSave,setClickSave] = useState(false)

    const [loading,setLoading] = useState(false)
    const [dialogMessage, setDialogMessage] = useState("");
    const [openDialogContent, setOpenDialogContent] = useState(false);

    const {
        register,
        handleSubmit,
        clearErrors,
        control,
        reset,
        setValue,
        formState: { errors },
      } = useForm();



      useEffect(() => {
    
        axios({
          method: "get",
          url:
          getUrl(authConfig.settings) + "?settingsType=DEFAULT_SOCIETIES_CONFIGURATION",
          headers: getAuthorizationHeaders(),
        }).then((res) => {
          setEmployeeId(res.data.configurationDTO?.configuration?.defaultAssignedToId);
        });
      
    }, []);


    const handleSuccess = () => {
      const message = `
      <div> 
        <h3> Configuration Updated Successfully.</h3>
      </div>
    `;
      setDialogMessage(message);
      setLoading(false)
      setOpenDialogContent(true);
    };


    const handleFailure = () => {
      const message = `
      <div> 
        <h3> Failed to Update Configuration. Please try again later.</h3>
      </div>
    `;
      setDialogMessage(message);
      setLoading(false)
      setOpenDialogContent(true);
    };


    useEffect(() => {
        // Fetch employees
        axios({
          method: "get",
          url: getUrl(authConfig.selectDropdownNew) + "?selectionType=EMPLOYEES",
          headers: getAuthorizationHeaders(),
        })
          .then((res) => {
            setListOfEmployees(res.data.data);
          })
          .catch((err) => console.log("Employees error", err));
      }, []);

    useEffect(() => {
        if (!!listOfEmployees) {
          let data = [];
          listOfEmployees.map((entry) => {
            data.push({ value: entry.id, key: entry.name });
          });
          setEmployeesOptions(data);
        }
      }, [listOfEmployees]);

      async function submit(data) {
        setClickSave(false)
        setLoading(true)

        const fields = {
          settingsType:"DEFAULT_SOCIETIES_CONFIGURATION",
          configurationDTO: {
            configuration: {
              defaultAssignedToId: employeeId
            }
          }
        }

        const response = await auth.patchConfiguration(fields, () => {
          console.error(" Configuration Details failed");
        });

        if(response){
          handleSuccess();
        }else{
          handleFailure();
        }

      }

      const handleClose = () => setOpenDialogContent(false);

      const handleEmployeeChange = (newValue) => {
        setEmployeeId(newValue);
        setClickSave(true);
      };


      const { canMenuPageSection, rbacRoles } = useRBAC();


      const router = useRouter();
      const canAccessSocietyCHSConfig= (requiredPermission) =>
        canMenuPageSection(MENUS.TOP, PAGES.SOCIETY_CHS, PAGES.CONFIG_CHS , requiredPermission);
    
      useEffect(() => {
        if (rbacRoles != null && rbacRoles.length > 0) {
          if (!canAccessSocietyCHSConfig(PERMISSIONS.READ)) {
            router.push("/401");
          }
        }
      }, [rbacRoles]);
    
    
    if(canAccessSocietyCHSConfig(PERMISSIONS.READ)) { 
    
    return (
      <>
        <Card>
        <Box
              sx={{
                py: 3,
                px: 6,
                rowGap: 2,
                columnGap: 4,
                display: "flex",
                flexWrap: "wrap",
                alignItems: "center",
                justifyContent: "space-between",
              }}
            >
              <Grid container spacing={3} alignItems="center">
                <Grid item xs={12} sx={{ textAlign: "flex-start" }}>
                  <Typography variant="h6" fontWeight={"500"}>Default Configuration for Societies</Typography>
                  <Typography variant="body2" fontWeight={"300"}>A Society from the sign-up and readiness is assigned to this employee by default.</Typography>
                </Grid>
              </Grid>
            </Box>
            <CardContent>
                <Grid container spacing={3}>
                    <Grid item xs={12} sm={4}>
                    <SelectAutoComplete
                  id="employeeId"
                  label="Assigned To"
                  name="employeeId"
                  nameArray={employeesOptions}
                  value={employeeId}
                  onChange={(event) => {
                    handleEmployeeChange(event.target.value);
                  }}
                  aria-describedby="employeeId-select"
                />
                    </Grid>
                    <Grid item xs={12} sm={4}>
                    <Button
                        display="flex"
                        justifyContent="center"
                        variant="contained"
                        color="primary"
                        onClick={handleSubmit(submit)}
                    >
                             {loading ? <CircularProgress color="inherit" size={24} /> : "Save"}
                    </Button>
                    </Grid>
                    {clickSave && 
                      <Grid item xs={12}>
                        <Typography variant="body2" fontWeight={"300"}>click on save to apply your changes</Typography>
                      </Grid>
                    }
                </Grid>
            </CardContent>
        </Card>
        <Dialog
        open={openDialogContent}
        onClose={handleClose}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
        PaperProps={{
          sx: {
            p: (theme) => `${theme.spacing(2.5)} !important`,
            backgroundColor: (theme) => theme.palette.primary.background,
          },
        }}
      >
        <Box
          sx={{
            width: "100%",
            borderRadius: 1,
            textAlign: "center",
            border: (theme) => `1px solid ${theme.palette.divider}`,
            borderColor: "primary.main",
          }}
        >
          <DialogContent>
            <DialogContentText
              id="alert-dialog-description"
              color="primary.main"
            >
              <div dangerouslySetInnerHTML={{ __html: dialogMessage }} />
            </DialogContentText>
          </DialogContent>
          <DialogActions>
            <Button
              variant="contained"
              onClick={handleClose}
              sx={{ margin: "auto", width: 100 }}
            >
              Okay
            </Button>
          </DialogActions>
        </Box>
      </Dialog>
      </>
    );
  }
  else {
    return null;
  }
  };
  
  export default CHSConfiguration;
  