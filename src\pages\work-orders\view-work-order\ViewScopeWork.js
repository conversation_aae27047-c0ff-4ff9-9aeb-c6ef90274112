import { useTheme } from "@emotion/react";
import {
  ExpandLess as ExpandLessIcon,
  ExpandMore as ExpandMoreIcon,
} from "@mui/icons-material";
import {
  Collapse,
  IconButton,
  List,
  ListItem,
  ListItemText,
  Typography,
  useMediaQuery,
} from "@mui/material";
import Paper from "@mui/material/Paper";
import { Box } from "@mui/system";
import { useState } from "react";

const ViewScopeOfWork = ({ scopeData }) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));

  const [expanded, setExpanded] = useState({});

  const handleToggleExpand = (index) => {
    setExpanded((prev) => ({ ...prev, [index]: !prev[index] }));
  };

  if (!scopeData || scopeData?.length === 0) {
    return (
      <Box sx={{ textAlign: "center", mt: 2 }}>
        <Typography
         variant="body1" color="text.secondary">
          Please update the scope of work or information.
        </Typography>
      </Box>
    );
  }

  return (
    <List>
      {scopeData?.map((item, index) => (
        <Paper
          key={index}
          sx={{ mb: 0.5, boxShadow: "0px 1px 3px rgba(0, 0, 0, 0.2)" }}
        >
          {/* Main Point */}
          <ListItem alignItems="flex-start" dense>
            <ListItemText
              primary={
                <Typography
                  variant="subtitle1"
                  fontWeight="bold"
                  sx={{
                    fontSize: isMobile ? "0.75rem" : "0.8rem",
                    wordBreak: "break-word",
                  }}
                >
                  {`${index + 1}. ${item.name}`}
                </Typography>
              }
            />
            <IconButton
              edge="end"
              aria-label="expand"
              onClick={() => handleToggleExpand(index)}
              size="small"
            >
              {expanded[index] ? (
                <ExpandLessIcon fontSize="small" />
              ) : (
                <ExpandMoreIcon fontSize="small" />
              )}
            </IconButton>
          </ListItem>

          {/* Sub-Points */}
          <Collapse in={expanded[index]} timeout="auto" unmountOnExit>
            <List component="div" disablePadding sx={{ pl: 4 }}>
              {item.subPoints?.map((subItem, subIndex) => (
                <ListItem key={subIndex} dense>
                  <ListItemText
                    primary={
                      <Typography
                        variant="body1"
                        sx={{
                          fontSize: isMobile ? "0.73rem" : "0.75rem",
                          wordBreak: "break-word",
                        }}
                      >
                        {`${index + 1}.${subIndex + 1} ${subItem?.name}`}
                      </Typography>
                    }
                  />
                </ListItem>
              ))}
            </List>
          </Collapse>
        </Paper>
      ))}
    </List>
  );
};

export default ViewScopeOfWork;
