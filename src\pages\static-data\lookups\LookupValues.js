import {
  Box,
  Card,
  CardContent,
  DialogContentText,
  Divider,
  Menu,
  MenuItem,
  InputAdornment,
  Tooltip,
} from "@mui/material";
import Button from "@mui/material/Button";
import Dialog from "@mui/material/Dialog";
import DialogActions from "@mui/material/DialogActions";
import DialogContent from "@mui/material/DialogContent";
import DialogTitle from "@mui/material/DialogTitle";
import FormControl from "@mui/material/FormControl";
import Grid from "@mui/material/Grid";
import IconButton from "@mui/material/IconButton";
import TextField from "@mui/material/TextField";
import Typography from "@mui/material/Typography";
import { DataGrid } from "@mui/x-data-grid";
import axios from "axios";
import { useContext, useEffect, useState } from "react";
import { Controller, useForm } from "react-hook-form";
import Icon from "src/@core/components/icon";
import CustomAvatar from "src/@core/components/mui/avatar";
import authConfig from "src/configs/auth";
import { AuthContext } from "src/context/AuthContext";
import { getAuthorizationHeaders, getUrl } from "src/helpers/utils";
import { useAuth } from "src/hooks/useAuth";
import CustomChip from "src/@core/components/mui/chip";
import SearchIcon from "@mui/icons-material/Search";
import { format } from "date-fns";
import DeleteListValueDialog from "./DeleteListValueDialog";
import LookupValuesView from "./LookupValuesView";
import ActivateListValueDialog from "./ActivateListValueDialog";
import SelectAutoComplete from "src/@core/components/custom-components/SelectAutoComplete";
import FallbackSpinner from "src/@core/components/spinner";

const userStatusObj = {
  true: "Active",
  false: "InActive",
};

const LookUpValues = () => {
  const [namesList, setNamesList] = useState([]);
  const [openDeleteDialog, setOpenDeleteDialog] = useState(false);
  const [openActivateDialog, setOpenActivateDialog] = useState(false);
  const [openDialogContent, setOpenDialogContent] = useState(false);

  const [openDialog, setOpenDialog] = useState(false);
  const [openCreateDialog, setOpenCreateDialog] = useState(false);
  const auth = useAuth();

  const [keyword, setKeyword] = useState("");
  const [searchKeyword, setSearchKeyword] = useState("");
  const [expanded, setExpanded] = useState(true);
  const rowsPerPageOptions = [10, 15, 20, 25, 50, 100];
  const [pageSize, setPageSize] = useState(rowsPerPageOptions[0]);
  const [page, setPage] = useState(1);
  const [currentRow, setCurrentRow] = useState();
  const [rowCount, setRowCount] = useState(0);
  const [dialogMessage, setDialogMessage] = useState("");

  const [loading, setLoading] = useState(true);

  const { servicesData, setServicesData, servicesDataDetails, listNames } =
    useContext(AuthContext);

  const [menu, setMenu] = useState(null);

  const handleCloseMenuItems = () => {
    setMenu(null);
  };

  const {
    register,
    handleSubmit,
    setError,
    clearErrors,
    control,
    reset,
    formState: { errors },
  } = useForm({
    defaultValues: {
      name: "",
    },
  });

  async function submit(data) {
    const fields = {
      name: data?.name.trim(),
      listNamesId: listNameId,
    };
    try {
      const response = await auth.postService(fields, handleFailure);
      if (response) {
        handleSuccess();
      }
      reset();
    } catch (error) {
      console.error("Service Data Creation failed:", error);
      handleFailure();
    }

    handleCloseCreateDialog();
  }

  const handleSuccess = () => {
    const message = `
      <div> 
        <h3> LookUp Value added Successfully.</h3>
      </div>
    `;
    setDialogMessage(message);
    setOpenDialogContent(true);
  };

  const handleFailure = (err) => {
    let message;
    if (err.response.data.message.includes("existing entry in List Values")) {
      message = `
      <div> 
        <h3> LookUp value already Exists.</h3>
      </div>
    `;
    } else {
      message = `
      <div> 
        <h3> Failed to Add LookUp value. Please try again later.</h3>
      </div>
    `;
    }
    setDialogMessage(message);
    setOpenDialogContent(true);
  };

  const handleButtonClick = () => {
    setOpenDialogContent(false);
  };

  const handleOpenDialog = () => {
    setOpenCreateDialog(true);
  };

  const handleCloseDialog = () => {
    reset();
    setOpenDialog(false);
    fetchValues(page, pageSize, searchKeyword);
  };

  const handleCloseCreateDialog = () => {
    reset();
    setListNameId("");
    setOpenCreateDialog(false);
    fetchValues(page, pageSize, searchKeyword);
  };

  const handleCloseActivateDialog = () => {
    setOpenActivateDialog(false);
    fetchValues(page, pageSize, searchKeyword);
  };

  const [listNameId, setListNameId] = useState("");
  const [listOfListNames, setListOfListNames] = useState([]);
  const [listNamesOptions, setListNamesOptions] = useState([]);

  useEffect(() => {
    axios({
      method: "get",
      url: getUrl(authConfig.selectDropdownNew) + "?selectionType=LIST_NAME",
      headers: getAuthorizationHeaders(),
    })
      .then((res) => {
        setListOfListNames(res.data.data);
      })
      .catch((err) => console.log("Locations error", err));
  }, []);

  useEffect(() => {
    if (!!listOfListNames) {
      let data = [];
      listOfListNames.map((entry) => {
        data.push({ value: entry.id, key: entry.name });
      });
      setListNamesOptions(data);
    }
  }, [listOfListNames]);

  const fetchValues = async (currentPage, currentPageSize, searchKeyword) => {
    const url = getUrl(authConfig.listValuesGetAll);

    const headers = getAuthorizationHeaders();

    const data = {
      page: currentPage,
      pageSize: currentPageSize,
      searchKeyword: searchKeyword,
    };

    try {
      const response = await axios({
        method: "post",
        url: url,
        headers: headers,
        data: data,
      });

      if (response.data) {
        setNamesList(response.data?.listValuesResponse);
        setRowCount(response.data?.rowCount || 0);
      } else {
        console.error("Unexpected API response format:", response);
      }
    } catch (error) {
      console.error("Error fetching users:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleCloseDeleteDialog = () => {
    setOpenDeleteDialog(false);
    fetchValues(page, pageSize, searchKeyword);
  };

  useEffect(() => {
    fetchValues(page, pageSize, searchKeyword);
  }, [page, pageSize, searchKeyword]);

  const handlePageChange = (direction) => {
    if (direction === page) {
      setPage(page + 1);
    } else {
      setPage(page - 1);
    }
  };

  const handlePageSizeChange = (params) => {
    if (params) {
      setPageSize(params);
      setPage(1);
    }
  };

  const mapIsActiveToLabel = (isActive) => {
    return userStatusObj[isActive] || "Unknown";
  };

  const columns = [
    {
      field: "listNamesId",
      headerName: "List Name",
      flex: 0.11,
      minWidth: 120,
      renderCell: (params) => {
        const listName = listNames?.find((item) => item.id === params.value);
        return <span>{listName ? listName.name : "Unknown"}</span>;
      },
    },
    { field: "name", headerName: "List Value", flex: 0.11, minWidth: 120 },
    {
      field: "isActive",
      headerName: "Status",
      flex: 0.11,
      minWidth: 100,
      renderCell: ({ row }) => {
        return (
          <CustomChip
            rounded={true}
            skin="light"
            size="small"
            label={mapIsActiveToLabel(row.isActive)}
            color={row.isActive === true ? "success" : "error"}
            sx={{ textTransform: "capitalize" }}
          />
        );
      },
    },
    {
      field: "createdOn",
      headerName: "Created on",
      flex: 0.11,
      minWidth: 100,
      renderCell: ({ row }) => {
        return format(new Date(row.createdOn), "dd-MM-yyyy");
      },
    },
    {
      field: "updatedOn",
      headerName: "Updated on",
      flex: 0.11,
      minWidth: 100,
      renderCell: ({ row }) => {
        return format(new Date(row.updatedOn), "dd-MM-yyyy");
      },
    },
    {
      flex: 0.05,
      field: "actions",
      headerName: "Actions",
      sortable: false,
      minWidth: 95,
      disableClickEventBubbling: true,
      renderCell: (params) => {
        const handleClickMenu = (event) => {
          event.stopPropagation();
          setMenu(event.currentTarget);
          const row = params.row;
          setCurrentRow(row);
          setServicesData({
            ...servicesData,
            id: row.id,
          });
        };

        const onClickViewProfile = () => {
          setOpenDialog(true);
          handleCloseMenuItems();
        };
        const onClickDeleteProfile = () => {
          setOpenDeleteDialog(true);
          handleCloseMenuItems();
        };

        const onClickActivateProfile = () => {
          setOpenActivateDialog(true);
          handleCloseMenuItems();
        };

        return (
          <div
            style={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
            }}
          >
            <CustomAvatar
              skin="light"
              variant="rounded"
              sx={{
                mr: { xs: 2, lg: 4 },
                width: 34,
                height: 34,
                cursor: "pointer",
              }}
              onClick={handleClickMenu}
            >
              <Icon icon="bi:three-dots-vertical" />
            </CustomAvatar>
            <Menu
              id="actions-menu"
              anchorEl={menu}
              keepMounted
              open={Boolean(menu)}
              onClose={handleCloseMenuItems}
            >
              <MenuItem onClick={onClickViewProfile}>Edit</MenuItem>
              {currentRow?.isActive ? (
                <MenuItem onClick={onClickDeleteProfile}>DeActivate</MenuItem>
              ) : (
                <MenuItem onClick={onClickActivateProfile}>Activate</MenuItem>
              )}
            </Menu>
          </div>
        );
      },
    },
  ];

  return (
    <>
      <Grid>
        <Box
          sx={{
            py: 3,
            px: 6,
            rowGap: 2,
            columnGap: 4,
            display: "flex",
            flexWrap: "wrap",
            alignItems: "center",
            justifyContent: "space-between",
          }}
        >
          <Grid container spacing={3} alignItems="center">
            <Grid item xs={12} sm={4} sx={{ textAlign: "flex-start" }}>
              <Typography variant="h6">{"List of LookUp Values"}</Typography>
            </Grid>

            <Grid item xs={12} sm={8}>
              <Grid
                container
                spacing={2}
                alignItems="center"
                justifyContent="flex-end"
              >
                <Grid item xs={12} sm="auto">
                  <FormControl>
                    <Controller
                      name="mainSearch"
                      control={control}
                      render={({ field: { onChange } }) => (
                        <TextField
                          id="mainSearch"
                          placeholder="Search"
                          value={keyword}
                          onChange={(e) => {
                            onChange(e.target.value);
                            setKeyword(e.target.value);
                            setSearchKeyword(e.target.value);
                          }}
                          onKeyDown={(e) => {
                            if (e.key === "Enter") {
                              setSearchKeyword(keyword);
                              fetchValues(page, pageSize, searchKeyword);
                            }
                          }}
                          sx={{
                            "& .MuiInputBase-root": {
                              height: "40px",
                            },
                          }}
                          InputProps={{
                            endAdornment: (
                              <InputAdornment position="start">
                                <SearchIcon
                                  sx={{
                                    cursor: "pointer",
                                    marginRight: "-15px",
                                  }}
                                  onClick={() => {
                                    setSearchKeyword(keyword);
                                    fetchValues(page, pageSize, searchKeyword);
                                  }}
                                />{" "}
                              </InputAdornment>
                            ),
                          }}
                        />
                      )}
                    />
                  </FormControl>
                </Grid>
                <Grid item xs="auto" sm="auto">
                  <Button variant="contained" onClick={handleOpenDialog}>
                    Add LookUp Value
                  </Button>
                </Grid>
              </Grid>
            </Grid>
          </Grid>
        </Box>
        <Divider />
        <Dialog
          open={openCreateDialog}
          onClose={handleCloseCreateDialog}
          fullWidth
          maxWidth="xs"
          scroll="paper"
        >
          <DialogTitle
            sx={{
              position: "relative",
              borderBottom: (theme) => `1px solid ${theme.palette.divider}`,
              p: (theme) => `${theme.spacing(1.75, 4)} !important`,
              display: "flex",
              alignItems: "center",
              justifyContent: { xs: "start" },
              fontSize: { xs: 19, md: 20 },
              height: "50px", // height
              marginLeft: { xl: 3, lg: 3, md: 3, sm: 3, xs: 3 },
            }}
            textAlign={"center"}
          >
            Add New List Value
            <Box
              sx={{
                position: "absolute",
                top: "9px",
                right: "14px",
                marginRight: { xl: 4, lg: 4, md: 4, sm: 4.3, xs: 4.3 },
              }}
            >
              <IconButton
                size="small"
                onClick={handleCloseCreateDialog}
                sx={{
                  // p: "0.438rem",
                  borderRadius: 1,
                  color: "common.white",
                  backgroundColor: "primary.main",
                  "&:hover": {
                    backgroundColor: "#66BB6A",
                    transition: "background 0.5s ease, transform 0.5s ease",
                  },
                }}
              >
                <Icon icon="tabler:x" fontSize="1rem" />
              </IconButton>
            </Box>
          </DialogTitle>
          <DialogContent
            sx={{
              position: "relative",
              pt: (theme) => `${theme.spacing(8)} !important`,
              pb: (theme) => `${theme.spacing(5)} !important`,
              px: (theme) => [`${theme.spacing(8)} !important`],
            }}
          >
            <Grid container spacing={3}>
              <Grid item xs={12} xl={12}>
                <SelectAutoComplete
                  id={"listNamesId"}
                  label={"Select lookup name"}
                  nameArray={listNamesOptions}
                  register={register}
                  value={listNameId}
                  defaultValue={listNameId}
                  onChange={(e) => {
                    setListNameId(e.target.value);
                  }}
                  error={Boolean(errors.listNamesId)}
                />
              </Grid>
              <Grid item xs={12} xl={12}>
                <FormControl fullWidth>
                  <Controller
                    name="name"
                    control={control}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        size="small"
                        label=" List Value"
                        InputLabelProps={{ shrink: true }}
                        placeholder="Enter your Lookup Value"
                        error={Boolean(errors.name)}
                        helperText={errors.name?.message}
                        aria-describedby="Section1-name"
                      />
                    )}
                  />
                </FormControl>
              </Grid>
            </Grid>
          </DialogContent>
          <DialogActions
            sx={{
              justifyContent: "end",
              borderTop: (theme) => `1px solid ${theme.palette.divider}`,
              p: (theme) => `${theme.spacing(2.5)} !important`,
              height: "50px", // height
              marginRight: { xl: 5.3, lg: 5.5, md: 5.5, sm: 5.5, xs: 5.5 },
            }}
          >
            <Button
              display="flex"
              justifyContent="center"
              variant="outlined"
              color="primary"
              onClick={() => {
                handleCloseCreateDialog();
              }}
            >
              Cancel
            </Button>

            <Button
              display="flex"
              justifyContent="center"
              variant="contained"
              color="primary"
              onClick={handleSubmit(submit)}
            >
              Save
            </Button>
          </DialogActions>
        </Dialog>
        <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth={"md"}>
          <DialogTitle
            sx={{
              position: "relative",
              borderBottom: (theme) => `1px solid ${theme.palette.divider}`,
              p: (theme) => `${theme.spacing(1.75, 4)} !important`,
              display: "flex",
              alignItems: "center",
              justifyContent: "start",
              fontSize: { xs: 13, md: 18 },
              height: "50px", // height
            }}
            textAlign={"center"}
          >
            Edit LookUp Value
            <Box sx={{ position: "absolute", top: "9px", right: "12px" }}>
              <IconButton
                size="small"
                onClick={handleCloseDialog}
                sx={{
                  // p: "0.438rem",
                  borderRadius: 1,
                  color: "common.white",
                  backgroundColor: "primary.main",
                  "&:hover": {
                    backgroundColor: "#66BB6A",
                    transition: "background 0.5s ease, transform 0.5s ease",
                  },
                }}
              >
                <Icon icon="tabler:x" fontSize="1rem" />
              </IconButton>
            </Box>
          </DialogTitle>
          <DialogContent
            sx={{
              position: "relative",
              pt: (theme) => `${theme.spacing(8)} !important`,
              pb: (theme) => `${theme.spacing(5)} !important`,
              px: (theme) => [`${theme.spacing(8)} !important`],
            }}
          >
            <LookupValuesView
              data={servicesDataDetails}
              expanded={expanded}
              onCancel={handleCloseDialog}
              fetchUsers={fetchValues}
            />
          </DialogContent>
          <DialogActions
            sx={{
              justifyContent: "end",
              borderTop: (theme) => `1px solid ${theme.palette.divider}`,
              p: (theme) => `${theme.spacing(2.5)} !important`,
              height: "50px", // height
            }}
          >
            <Button
              display="flex"
              justifyContent="center"
              variant="outlined"
              color="primary"
              onClick={handleCloseDialog}
            >
              Close
            </Button>
          </DialogActions>
        </Dialog>

        <CardContent>
          <div style={{ height: 430, width: "100%" }}>
            {loading ? (
              <Box
                display="flex"
                justifyContent="center"
                alignItems="center"
                height="60vh"
              >
                <FallbackSpinner />
              </Box>
            ) : (
              <DataGrid
                rows={namesList}
                columns={columns}
                pagination
                pageSize={pageSize}
                page={page - 1}
                rowsPerPageOptions={rowsPerPageOptions}
                rowCount={rowCount}
                paginationMode="server"
                onPageChange={handlePageChange}
                onPageSizeChange={handlePageSizeChange}
                rowHeight={38}
                headerHeight={38}
              />
            )}
          </div>
        </CardContent>

        <DeleteListValueDialog
          open={openDeleteDialog}
          onClose={handleCloseDeleteDialog}
          data={currentRow}
        />
        <ActivateListValueDialog
          open={openActivateDialog}
          onClose={handleCloseActivateDialog}
          data={currentRow}
        />
      </Grid>

      <Dialog
        open={openDialogContent}
        onClose={handleButtonClick}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
        PaperProps={{
          sx: {
            p: (theme) => `${theme.spacing(2.5)} !important`,
            backgroundColor: (theme) => theme.palette.primary.background,
          },
        }}
      >
        <Box
          sx={{
            width: "100%",
            borderRadius: 1,
            textAlign: "center",
            border: (theme) => `1px solid ${theme.palette.divider}`,
            borderColor: "primary.main",
          }}
        >
          <DialogContent>
            <DialogContentText
              id="alert-dialog-description"
              color="primary.main"
            >
              <div dangerouslySetInnerHTML={{ __html: dialogMessage }} />
            </DialogContentText>
          </DialogContent>
          <DialogActions>
            <Button
              variant="contained"
              onClick={handleButtonClick}
              sx={{ margin: "auto", width: 100 }}
            >
              Okay
            </Button>
          </DialogActions>
        </Box>
      </Dialog>
    </>
  );
};

export default LookUpValues;
