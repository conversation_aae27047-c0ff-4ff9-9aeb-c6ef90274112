import PhoneAndroidIcon from "@mui/icons-material/PhoneAndroid";
import {
  <PERSON><PERSON>,
  <PERSON>,
  Button,
  CircularProgress,
  Container,
  Divider,
  <PERSON><PERSON>k<PERSON>,
  TextField,
  Typography,
} from "@mui/material";
import { useContext, useEffect, useState } from "react";
// ** Layout Import
import { styled } from "@mui/material/styles";
import { Controller, useForm } from "react-hook-form";
import Icon from "src/@core/components/icon";
import BlankLayout from "src/@core/layouts/BlankLayout";
import authConfig from "src/configs/auth";
// ** Next Imports
import Link from "next/link";
import { fetchIpAddress } from "src/@core/components/custom-components/FetchIpAddress";
import { useAuth } from "src/hooks/useAuth";
import Verification from "./verification";

import { useRouter } from "next/router";
import OrganisationDetails from "./OrganisationDetails";
import GoogleLoginDialog from "../register-old-two/GoogleLoginDialog";
import { AuthContext } from "src/context/AuthContext";
import FallbackSpinner from "src/@core/components/spinner";
import PrivacyPolicyDialog from "./PrivacyPolicyDialog";
import TermsAndConditionsDialog from "./TermsAndConditionsDialog";


const LinkStyled = styled(Link)(({ theme }) => ({
  fontSize: { xs: "0.7rem", lg: "0.9rem" },
  textDecoration: "none",
  color: theme.palette.primary.main,
}));

const SignupPage = () => {
  const {
    control,
    register,
    handleSubmit,
    formState: { errors },
  } = useForm();
  const auth = useAuth();

  const { getAllListValuesByListNameId,pageLoad } = useContext(AuthContext);

  const router = useRouter();
  const { role } = router.query;
  const [email, setEmail] = useState("");
  const [showPwd, setShowPwd] = useState(false);
  const [sendOTP, setSendOTP] = useState(false);
  const [showDetails, setShowDetails] = useState(false);
  const [sendOTPFail, setSendOTPFail] = useState(false);
  const [showToast, setShowToast] = useState(false); // State for toast visibility
  const [toastMessage, setToastMessage] = useState("");
  const [countdown, setCountdown] = useState(30);
  const [googleLogin, setGoogleLogin] = useState(false);
  const [selectedRole, setSelectedRole] = useState("");
  const [companyType, setCompanyType] = useState("");
  const [organisationName, setOrganisationName] = useState("");

  const [openPolicyDialog, setOpenPolicyDialog] = useState(false);
  const [openTermsDialog, setOpenTermsDialog] = useState(false);
  const handleOpenPolicyDialog = () => {
    setOpenPolicyDialog(true);
  };
  const handleClosePolicyDialog = () => {
    setOpenPolicyDialog(false);
  };
  const handleOpenTermsDialog = () => {
    setOpenTermsDialog(true);
  }
  const handleCloseTermsDialog = () => {
    setOpenTermsDialog(false);
  }

  const [companyTypeOptions, setCompanyTypeOptions] = useState([]);

  const handleError = (error) => {
    console.error("Register: Company Type:", error);
  };
  useEffect(() => {
    if (!!authConfig) {
      getAllListValuesByListNameId(
        authConfig.companyType,
        (data) =>
          setCompanyTypeOptions(
            data?.listValues?.map((item) => ({
              value: item.id,
              key: item.listValue,
            }))
          ),
        handleError
      );
    }
  }, [authConfig]);

  useEffect(() => {
    if (role === "society") {
      setSelectedRole("SOCIETY");
    } else {
      setSelectedRole("SERVICE_PROVIDER");
    }
  }, [role]);

  const url = authConfig.googleAuthUrl;

  const googleUrl = `${url}&role=${selectedRole}&organisationName=${organisationName}&companyType=${companyType}`;

  const handleFailure = () => {
    setSendOTPFail(true);
    setToastMessage("Failed to Send OTP. Please try again later.");
    setShowToast(true);
  };

  async function handleEmailVerification() {
    setSendOTP(true);
    const ipAddress = await fetchIpAddress();
    const data = {
      contactType: "EMAIL",
      contactValue: email,
      ipAddress: ipAddress,
    };

    try {
      const response = await auth.postOTP(data, handleFailure);
      if (response) {
        setShowPwd(true);
      }
      if (response.isVerified) {
        setShowDetails(true);
      } else {
        setShowDetails(false);
      }
      if (response.otpSent) {
        setToastMessage(
          "Email has been sent successfully! Please check your inbox (or spam folder) for confirmation."
        );
        setShowToast(true);
      }
    } catch (error) {
      console.error("Employee Creation failed:", error);
      handleFailure();
    }
    setSendOTP(false);
  }

  const handleToastClose = () => {
    setShowToast(false);
    setToastMessage("");
  };

  const handleSocialLogin = () => {
    setGoogleLogin(true);
  };

  const handleCloseGoogleLogin = () => {
    if (!organisationName) {
      setError("name", {
        type: "manual",
        message: "This field is required",
      });
      return; // Do not proceed further if validation fails
    }
    router.push(googleUrl);
    setGoogleLogin(false);
  };
  return pageLoad ? (
    <FallbackSpinner />
  ) : (
    <>
      {showPwd ? (
        <>
          {showDetails ? (
            <OrganisationDetails
              email={email}
              setShowPwd={setShowPwd}
            />
          ) : (
            <Verification
              email={email}
              setShowPwd={setShowPwd}
              setShowDetails={setShowDetails}
              showDetails={showDetails}
              countdown={countdown}
              setCountdown={setCountdown}
            />
          )}
        </>
      ) : (
        <Box
          sx={{
            display: { xs: "flex", sm: "block" },
            justifyContent: { xs: "center" },
            alignItems: { xs: "center" },
            height: { xs: "100vh" },
            transform: {
              xs: "scale(0.9)",
              sm: "none",
            },
          }}
        >
          <Container
            maxWidth="xs"
            sx={{
              height: "90vh",
              display: "flex",
              flexDirection: "column",
              alignItems: "center",
              justifyContent: "space-between",
              boxShadow: 3,
              p: 8,
              borderRadius: 6,
              bgcolor: "white",
              position: "absolute",
              top: "50%",
              left: "50%",
              transform: "translate(-50%, -50%)",
              overflowY: "auto",
            }}
          >
            <>
              <Box
                sx={{
                  width: "80%",
                  display: "flex",
                  flexDirection: "column",
                  flex: 1,
                  justifyContent: "top",
                }}
              >
                <Typography
                  variant="h5"
                  fontWeight="450"
                  sx={{ ml: 0, mb: 1.5 }}
                >
                  Sign Up{" "}
                  <Typography component="span" variant="body1" fontWeight="450">
                    {role === "service-provider"
                      ? "as a Service Provider"
                      : "as a Society"}
                  </Typography>
                </Typography>

                <Typography
                  variant="h6"
                  fontWeight="500"
                  sx={{
                    fontSize: { xs: "15px", lg: "20px", sm: "18px" },
                    mt: 2,
                    ml: -2,
                    display: "flex",
                    alignItems: "center",
                  }}
                >
                  <a href={authConfig.guestURL + "home"}>
                    <img
                      src="/images/houzer-logo.webp"
                      alt="Houzer Logo"
                      style={{ width: "50px", height: "45px" }} // Increased size
                    />
                  </a>
                  Welcome to Houzer! 👋🏻
                </Typography>
                <Typography
                  sx={{ mb: 3, fontSize: "0.7rem", color: "#a9a9a9" }}
                >
                  By continuing, you agree to our{" "}
                  <span passHref onClick={handleOpenTermsDialog}>
                    <Typography
                      component="a"
                      sx={{
                        textDecoration: "none !important",
                        fontSize: { xs: "0.6rem", lg: "0.7rem" },
                        cursor: "pointer",
                        fontWeight: 600,
                        color: "blue", // Apply primary color
                        margin: 0, // Ensure no default margin
                        padding: 0, // Ensure no default padding
                      }}
                    >
                      <u>Terms & Conditions</u>
                    </Typography>
                  </span>{" "}
                  and
                  <br />
                  acknowledge that you understand the{" "}
                  <span passHref onClick={handleOpenPolicyDialog}>
                    <Typography
                      component="a"
                      sx={{
                        textDecoration: "none !important",
                        fontSize: { xs: "0.6rem", lg: "0.7rem" },
                        cursor: "pointer",
                        fontWeight: 600,
                        color: "blue", // Apply primary color
                        margin: 0, // Ensure no default margin
                        padding: 0, // Ensure no default padding
                      }}
                    >
                      <u>Privacy Policy</u>
                    </Typography>
                  </span>
                  .
                </Typography>

                <Box
                  display="flex"
                  flexDirection="column"
                  gap={2}
                  alignItems="center"
                >
                  {/* <Button
                    startIcon={<PhoneAndroidIcon />}
                    variant="outlined"
                    fullWidth
                    sx={{
                      bgcolor: "white",
                      color: "black",
                      border: "1px solid #d3d3d3",
                      display: "flex",
                      justifyContent: "space-between",
                      alignItems: "center",
                      paddingLeft: "16px",
                      paddingRight: "16px",
                      fontWeight: "450",
                      "&:hover": {
                        bgcolor: "white",
                        color: "black",
                        border: "1px solid #d3d3d3", 
                        boxShadow: "none",
                      },
                    }}
                  >
                    <span style={{ flex: 1, textAlign: "center" }}>
                      Continue With Mobile Number
                    </span>
                  </Button> */}
                  <Button
                    variant="outlined"
                    startIcon={<Icon icon="logos:google-icon" fontSize={20} />}
                    fullWidth
                    sx={{
                      bgcolor: "white",
                      color: "black",
                      border: "1px solid #d3d3d3",
                      display: "flex", // Enable flex layout
                      justifyContent: "space-between", // Ensure the icon is at the start and text is centered
                      alignItems: "center", // Vertically align the icon and text
                      paddingLeft: "16px", // Optional: Add some padding to move icon slightly from the left
                      paddingRight: "16px", // Optional: Ensure there's padding to the right
                      fontWeight: "450",
                      "&:hover": {
                        bgcolor: "white", // Prevent background color change
                        color: "black", // Prevent text color change
                        border: "1px solid #d3d3d3", // Keep the same border
                        boxShadow: "none", // Remove hover shadow effect
                      },
                    }}
                    onClick={handleSocialLogin}
                  >
                    <span style={{ flex: 1, textAlign: "center" }}>
                      Continue With Google
                    </span>
                  </Button>
                </Box>

                <Divider
                  sx={{
                    mt: 4,
                    mb: 4,
                    fontSize: { xs: "0.6rem", lg: "0.8rem" }, // Larger font size
                  }}
                >
                  OR
                </Divider>
                <Controller
                  name="email"
                  control={control}
                  rules={{
                    required: "Email is required",
                    pattern: {
                      value: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
                      message: "Enter a valid email address",
                    },
                    minLength: {
                      value: 8,
                      message: "Email must be at least 8 characters long",
                    },
                    maxLength: {
                      value: 100,
                      message: "Email cannot be longer than 100 characters",
                    },
                  }}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      label="Email*"
                      size="small"
                      InputLabelProps={{
                        shrink: true,
                        sx: { fontSize: "1rem" },
                      }}
                      helperText={errors.email?.message}
                      error={Boolean(errors.email)}
                      placeholder="<EMAIL>"
                      fullWidth
                      inputProps={{
                        minLength: 8,
                        maxLength: 100,
                      }}
                      onChange={(event) => {
                        if (event.target.value.length <= 100) {
                          field.onChange(event);
                          setEmail(event.target.value);
                        }
                      }}
                    />
                  )}
                />

                <Box
                  sx={{
                    display: "flex",
                    flexWrap: "wrap",
                    mt: 3,
                  }}
                >
                  <Typography
                    sx={{
                      color: "text.secondary",
                      mr: 2,
                      fontSize: { xs: "0.7rem", lg: "0.9rem" },
                    }}
                  >
                    Already have an account?{" "}
                  </Typography>
                  <Typography
                    sx={{
                      color: "text.secondary",
                      fontSize: { xs: "0.7rem", lg: "0.9rem" },
                    }}
                  >
                    <LinkStyled
                      href="/login"
                      sx={{
                        fontWeight: 500,
                        fontSize: { xs: "0.7rem", lg: "0.9rem" },
                      }}
                    >
                      Login
                    </LinkStyled>
                  </Typography>
                </Box>
              </Box>
              <Box
                sx={{
                  width: "80%",
                  py: 2,
                  textAlign: "center",
                  mt: "auto",
                }}
              >
                <Button
                  variant="contained"
                  color="primary"
                  sx={{ mt: 4, width: "100%" }}
                  onClick={handleSubmit(handleEmailVerification)}
                >
                  {sendOTP ? (
                    <CircularProgress color="inherit" size={24} />
                  ) : (
                    "Continue"
                  )}
                </Button>
              </Box>
            </>
          </Container>
        </Box>
      )}
      <GoogleLoginDialog
        open={googleLogin}
        close={() => setGoogleLogin(false)}
        handleClose={handleCloseGoogleLogin}
        control={control}
        selectedRole={selectedRole}
        organisationName={organisationName}
        setOrganisationName={setOrganisationName}
        errors={errors}
        register={register}
        companyTypeOptions={companyTypeOptions}
        companyType={companyType}
        setCompanyType={setCompanyType}
      />
      <Snackbar
        open={showToast}
        autoHideDuration={5000} // Toast will be visible for 5 seconds
        onClose={handleToastClose}
        anchorOrigin={{ vertical: "top", horizontal: "right" }} // Position of the toast
      >
        <Alert
          onClose={handleToastClose}
          severity={sendOTPFail ? "error" : "success"}
          sx={{
            color: "black",
            padding: "4px 8px", // Reduce padding to make it smaller
            fontSize: "0.875rem", // Adjust font size for a more compact look
            borderRadius: "12px", // Optional: you can adjust the border radius
            border: "0.5px solid #ccc", // Optional: set a border or remove it completely
          }}
        >
          {toastMessage}
        </Alert>
      </Snackbar>

      <PrivacyPolicyDialog
        open={openPolicyDialog}
        onClose={handleClosePolicyDialog}
      />
      <TermsAndConditionsDialog
        open={openTermsDialog}
        onClose={handleCloseTermsDialog}
      />
    </>
  );
};

SignupPage.getLayout = (page) => <BlankLayout>{page}</BlankLayout>;
SignupPage.guestGuard = true;

export default SignupPage;
