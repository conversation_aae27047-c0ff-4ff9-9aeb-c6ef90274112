import { useTheme } from "@emotion/react";
import {
    FormControl,
    Grid,
    Table,
    TableBody,
    TableCell,
    TableContainer,
    TableRow,
    Typography,
} from "@mui/material";
import { useContext, useEffect } from "react";
import { Controller, useForm } from "react-hook-form";
import SelectAutoComplete from "src/@core/components/custom-components/SelectAutoComplete";
import SelectClearAutoComplete from "src/@core/components/custom-components/SelectClearAutoComplete";
import { AuthContext } from "src/context/AuthContext";
import MUITableCell from "../SP/MUITableCell";

const tablecellLabelStyle = {
  width: { xs: "50%", sm: "50%", md: "40%" }, // Responsive width adjustment
  padding: "4px", // Ensure padding for better alignment
  textAlign: "right", // Align the text to the left
  verticalAlign: "middle", // Vertically align the text to the middle
  textIndent: {
    lg: "80px",
    md: "80px",
    sm: "100px",
    xs: "0",
  },
};
const tablecellValueStyle = {
  justifyContent: "flex-start",
  alignItems: "center",
  width: { xs: "50%", sm: "50%", md: "60%" }, // Responsive width for small and medium screens
  height: "10px",
};

const field = {
  fontSize: "12.75px",
};

const fieldValueStyle = {
  fontWeight: "bold",
  wordWrap: "break-word",
  whiteSpace: "pre-wrap",
  // color: "#66BB6A", // Sets color for the TableCell
  color: "#108A00",
  fontSize: "14px",
  lineHeight: "1.2",
};

const SRInformation = ({
  societyOptions,
  serviceRequirement,
  setServiceRequirement,
  societySelected,
  setSocietySelected,
  serviceRequirements,
  data
}) => {
  const theme = useTheme();

  const {
    requisitionData,
    requisitionDataDetails,
    setRequisitionData,
    listValues,
  } = useContext(AuthContext);

  const {
    control,
    formState: { errors },
  } = useForm();

  const handleSocietyChange = (event) => {
    setServiceRequirement("");
    setSocietySelected(event.target.value);
  };

  useEffect(() => {
    if (serviceRequirement) {
      setRequisitionData({
        ...requisitionData,
        id: serviceRequirement,
      });
    }
  }, [serviceRequirement]);

  return (
    <>
      <TableContainer sx={{ padding: "4px 6px" }}>
        <Table>
          <TableBody
            sx={{
              "& .MuiTableCell-root": {
                p: `${theme.spacing(1.35, 1.125)} !important`,
              },
            }}
          >
            {!data || Object.keys(data)?.length === 0
              ? (
                <TableRow>
                <TableCell colSpan={2}>
                  <Grid container spacing={2}>
                    <Grid item xs={10} sm={6}>
                      <FormControl fullWidth>
                        <Controller
                          name="selectedSociety"
                          control={control}
                          defaultValue=""
                          render={({ field }) => (
                            <SelectAutoComplete
                              id="society"
                              label="Society Name"
                              value={societySelected}
                              nameArray={societyOptions?.map((option) => ({
                                value: option.orgId,
                                key: option.societyName,
                              }))}
                              onChange={(event) => {
                                field.onChange(event.target.value);
                                handleSocietyChange(event);
                              }}
                            />
                          )}
                        />
                      </FormControl>
                    </Grid>
                    {societySelected && (
                      <Grid item xs={10} sm={6}>
                        <FormControl fullWidth>
                          <Controller
                            name="serviceRequirement"
                            control={control}
                            defaultValue=""
                            render={({ field }) => (
                              <SelectClearAutoComplete
                                id="serviceRequirement"
                                label="Service Requirement"
                                value={serviceRequirement}
                                nameArray={serviceRequirements?.map((option) => ({
                                  value: option.srId,
                                  key: option.serviceTypeAndCreatedBy,
                                }))}
                                onChange={(event) => {
                                  field.onChange(event.target.value);
                                  setServiceRequirement(event.target.value);
                                }}
                              />
                            )}
                          />
                        </FormControl>
                      </Grid>
                    )}
                  </Grid>
                </TableCell>
              </TableRow>
              )
              : (
                <>
                <TableRow>
                  <MUITableCell sx={tablecellLabelStyle}>
                    <Typography style={field}>
                      Society:
                    </Typography>
                  </MUITableCell>
                  <MUITableCell sx={tablecellValueStyle}>
                    <Typography className="data-field" style={fieldValueStyle}>
                      {data?.societyName}
                    </Typography>
                  </MUITableCell>
                </TableRow>
                <TableRow>
                  <MUITableCell sx={tablecellLabelStyle}>
                    <Typography style={field}>
                      Service Requirement:
                    </Typography>
                  </MUITableCell>
                  <MUITableCell sx={tablecellValueStyle}>
                    <Typography className="data-field" style={fieldValueStyle}>
                      {serviceRequirements?.find(
                        (item) =>
                          item.srId === serviceRequirement
                      )?.serviceTypeAndCreatedBy || "-"}
                    </Typography>
                  </MUITableCell>
                </TableRow>
                </>
              )}
           

            {societySelected && serviceRequirement && (
              <>
                <TableRow>
                  <MUITableCell sx={tablecellLabelStyle}>
                    <Typography style={field}>
                      Confirmed Service Provider:
                    </Typography>
                  </MUITableCell>
                  <MUITableCell sx={tablecellValueStyle}>
                    <Typography className="data-field" style={fieldValueStyle}>
                      {requisitionDataDetails?.confirmedL1SpOrgName}
                    </Typography>
                  </MUITableCell>
                </TableRow>

                <TableRow>
                  <MUITableCell sx={tablecellLabelStyle}>
                    <Typography style={field}>
                      Confirmed Service Provider Date:
                    </Typography>
                  </MUITableCell>
                  <MUITableCell sx={tablecellValueStyle}>
                    <Typography className="data-field" style={fieldValueStyle}>
                      {requisitionDataDetails?.confirmedSpDate?.split("T")[0]}
                    </Typography>
                  </MUITableCell>
                </TableRow>

                <TableRow>
                  <MUITableCell sx={tablecellLabelStyle}>
                    <Typography style={field}>Service Type:</Typography>
                  </MUITableCell>
                  <MUITableCell sx={tablecellValueStyle}>
                    <Typography className="data-field" style={fieldValueStyle}>
                      {listValues?.find(
                        (item) =>
                          item.id === requisitionDataDetails?.serviceTypeId
                      )?.name || "-"}
                    </Typography>
                  </MUITableCell>
                </TableRow>
                <TableRow>
                  <MUITableCell sx={tablecellLabelStyle}>
                    <Typography style={field}>
                      Service Requisition No:
                    </Typography>
                  </MUITableCell>
                  <MUITableCell sx={tablecellValueStyle}>
                    <Typography className="data-field" style={fieldValueStyle}>
                      {requisitionDataDetails?.systemCode}
                    </Typography>
                  </MUITableCell>
                </TableRow>
                <TableRow>
                  <MUITableCell sx={tablecellLabelStyle}>
                    <Typography style={field}>Priority:</Typography>
                  </MUITableCell>
                  <MUITableCell sx={tablecellValueStyle}>
                    <Typography className="data-field" style={fieldValueStyle}>
                      {listValues?.find(
                        (item) => item.id === requisitionDataDetails?.priority
                      )?.name || "-"}
                    </Typography>
                  </MUITableCell>
                </TableRow>
                <TableRow>
                  <MUITableCell sx={tablecellLabelStyle}>
                    <Typography style={field}>Budget:</Typography>
                  </MUITableCell>
                  <MUITableCell sx={tablecellValueStyle}>
                    <Typography className="data-field" style={fieldValueStyle}>
                      {requisitionDataDetails?.budget?.rangeFrom} {" "}
                              {requisitionDataDetails?.budget?.rangeTo && ("-")} {" "}
                              {requisitionDataDetails?.budget?.rangeTo}{" "}
                              {
                                listValues?.find(
                                  (item) => item.id === requisitionDataDetails?.budget?.units
                                )?.name
                              }{" "}
                              {
                                listValues?.find(
                                  (item) => item.id === requisitionDataDetails?.budget?.condition
                                )?.name
                              }
                    </Typography>
                  </MUITableCell>
                </TableRow>
                <TableRow>
                  <MUITableCell sx={tablecellLabelStyle}>
                    <Typography style={field}>Requirement Deadline:</Typography>
                  </MUITableCell>
                  <MUITableCell sx={tablecellValueStyle}>
                    <Typography className="data-field" style={fieldValueStyle}>
                      {
                        requisitionDataDetails?.requirementDeadLine?.split(
                          "T"
                        )[0]
                      }
                    </Typography>
                  </MUITableCell>
                </TableRow>
                <TableRow>
                  <MUITableCell sx={tablecellLabelStyle}>
                    <Typography style={field}>Society Remarks:</Typography>
                  </MUITableCell>
                  <MUITableCell sx={tablecellValueStyle}>
                    <Typography className="data-field" style={fieldValueStyle}>
                      {requisitionDataDetails?.remarks}
                    </Typography>
                  </MUITableCell>
                </TableRow>
              </>
            )}
          </TableBody>
        </Table>
      </TableContainer>
    </>
  );
};

export default SRInformation;