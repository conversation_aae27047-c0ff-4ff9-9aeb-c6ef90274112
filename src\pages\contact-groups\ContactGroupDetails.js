import { useState, useEffect, useContext, useRef } from "react";
import {
  Box,
  Grid,
  Button,
  Typography,
  Divider,
  IconButton,
  Card,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  TextField,
  FormHelperText,
  DialogContentText,
  FormControlLabel,
  Checkbox,
  InputAdornment,
  Tooltip,
  ListItemText,
  Link,
} from "@mui/material";
import { AuthContext } from "src/context/AuthContext";
import { Controller, useForm } from "react-hook-form";
import { useAuth } from "src/hooks/useAuth";
import CustomAvatar from "src/@core/components/mui/avatar";
import SearchIcon from "@mui/icons-material/Search";

import Icon from "src/@core/components/icon";
import MultiSelectAutoComplete from "src/@core/components/custom-components/MultiSelectAutoComplete";
import { getUrl, getAuthorizationHeaders } from "src/helpers/utils";
import "react-datepicker/dist/react-datepicker.css";
import authConfig from "src/configs/auth";
import axios from "axios";
import { DataGrid } from "@mui/x-data-grid";
import toast, { Toaster } from "react-hot-toast";
import SelectAutoComplete from "src/@core/components/custom-components/SelectAutoComplete";

const field = {
  fontWeight: 500,
  fontSize: "12.75px",
};

const ContactGroupDetails = ({
  open,
  onClose,
  categoriesData,
  employeesData,
  fetchContactGroups,
}) => {
  const auth = useAuth();
  const { getAllListValuesByListNameId } = useContext(AuthContext);
  const {
    handleSubmit,
    control,
    setValue,
    reset,
    formState: { errors },
  } = useForm();

  const currentToast = useRef(null); // To keep track of the current toast

  const [dialogMessage, setDialogMessage] = useState("");
  const [openDialogContent, setOpenDialogContent] = useState(false);

  const [selectedRows, setSelectedRows] = useState([]);
  const [allServicesList, setAllServicesList] = useState([]);
  const [locationsList, setLocationsList] = useState([]);
  const [leadStatusList, setLeadStatusList] = useState([]);
  const [leadPriorityList, setLeadPriorityList] = useState([]);

  const [selectedUsers, setSelectedUsers] = useState([]);

  const [selectedCategory, setSelectedCategory] = useState("");
  const [designationsData, setDesignationsData] = useState(null);
  const [locationsData, setLocationsData] = useState(null);

  const [isStrategicPartner, setIsStrategicPartner] = useState(false);
  const [isListingEmpanelled, setIsListingEmpanelled] = useState(false);
  const [isMicroSiteEmpanelled, setIsMicroSiteEmpanelled] = useState(false);
  const rowsPerPageOptions = [10, 15, 20, 25, 50, 100];

  const [pageSize, setPageSize] = useState(rowsPerPageOptions[0]);
  const [page, setPage] = useState(1);
  const [rowCount, setRowCount] = useState(0);
  const [keyword, setKeyword] = useState("");
  const [searchKeyword, setSearchKeyword] = useState("");
  const [societyKeyword, setSocietyKeyword] = useState("");
  const [societySearch, setSocietySearch] = useState("");
  const [selectedServices, setSelectedServices] = useState([]);
  const [selectedLocations, setSelectedLocations] = useState([]);
  const [spSelectedLeadStatus, setSpSelectedLeadStatus] = useState([]);
  const [spSelectedLeadPriority, setSpSelectedLeadPriority] = useState([]);
  const [chsSelectedLeadStatus, setChsSelectedLeadStatus] = useState([]);
  const [chsSelectedLeadPriority, setChsSelectedLeadPriority] = useState([]);
  const [selectedAssignments, setSelectedAssignments] = useState([]);
  const [selectedDesignations, setSelectedDesignations] = useState([]);
  const [categoryId, setCategoryId] = useState("");
  const [chsLocations, setChsLocations] = useState([]);
  const [chsAssignments, setChsAssignments] = useState([]);
  const [employeeLocations, setEmployeeLocations] = useState([]);
  const [employeeAssignments, setEmployeeAssignments] = useState([]);
  const [finalList, setFinalList] = useState(false);

  const [usersData, setUserData] = useState([]);

  const onDelete = (id) => {
    setSelectedUsers((prevUsers) => prevUsers.filter((user) => user.id !== id));
  };

  const columns = [
    {
      field: "category",
      minWidth: 100,
      headerName: "Contact Type",
      flex: 0.2,
      renderCell: (params) => {
        const category = params?.row?.contactType
          ?.replace(/_/g, " ")
          ?.toLowerCase()
          ?.replace(/\b\w/g, (char) => char.toUpperCase());
        return <span>{category}</span>;
      },
    },
    {
      field: "name",
      minWidth: 100,
      headerName: "Name",
      flex: 0.2,
      valueGetter: (params) => {
        const { firstName, lastName, orgName } = params?.row;
      
        if (categoryId === "EMPLOYEE") {
          return lastName ? `${firstName} ${lastName}` : firstName;
        } else {
          return orgName;
        }
      }
    },
    {
      field: "email",
      minWidth: 100,
      headerName: "Email",
      flex: 0.4,
      renderCell: (params) => {
        const email = params?.value;

        return (
          <Link
            href={`mailto:${email}`}
            target="_blank"
            rel="noopener noreferrer"
            sx={{ color: "#6666ff" }}
          >
            {email}
          </Link>
        );
      },
    },

    {
      field: "mobileNumber",
      headerName: "Mobile Number",
      flex: 0.13,
      minWidth: 100,
      renderCell: (params) => {
        // Ensure params.value is neither null nor undefined
        const mobileNumber = params && params.value ? params.value : "";

        // If mobileNumber is empty or invalid, return nothing
        if (
          mobileNumber === "" ||
          mobileNumber === null ||
          mobileNumber === undefined
        ) {
          return null; // Handle cases with no valid mobile number
        }

        return mobileNumber.length > 9 ? (
          <Tooltip title={mobileNumber}>
            <Link
              href={`tel:${mobileNumber}`}
              target="_blank"
              rel="noopener noreferrer"
              sx={{ color: "#6666ff" }}
            >
              <span>{mobileNumber}</span>
            </Link>
          </Tooltip>
        ) : (
          <Link
            href={`tel:${mobileNumber}`}
            target="_blank"
            rel="noopener noreferrer"
            sx={{ color: "#6666ff" }}
          >
            {mobileNumber}
          </Link>
        );
      },
    },
  ];

  const cols = [
    {
      field: "category",
      minWidth: 100,
      headerName: "Contact Type",
      flex: 0.2,
      renderCell: (params) => {
        const category = params?.row?.contactType
          ?.replace(/_/g, " ")
          ?.toLowerCase()
          ?.replace(/\b\w/g, (char) => char.toUpperCase());
        return <span>{category}</span>;
      },
    },
    {
      field: "name",
      minWidth: 100,
      headerName: "Name",
      flex: 0.2,
      valueGetter: (params) => {
        const { firstName, lastName, orgName } = params?.row;
      
        if (categoryId === "EMPLOYEE") {
          return lastName ? `${firstName} ${lastName}` : firstName;
        } else {
          return orgName;
        }
      }
    },
    {
      field: "email",
      minWidth: 100,
      headerName: "Email",
      flex: 0.2,
      renderCell: (params) => {
        const email = params?.value;

        return (
          <Link
            href={`mailto:${email}`}
            target="_blank"
            rel="noopener noreferrer"
            sx={{ color: "#6666ff" }}
          >
            {email}
          </Link>
        );
      },
    },
    {
      field: "mobileNumber",
      headerName: "Mobile Number",
      flex: 0.13,
      minWidth: 100,
      renderCell: (params) => {
        const mobileNumber = params?.value;

        return mobileNumber?.length > 9 ? (
          <Tooltip title={mobileNumber}>
            <Link
              href={`tel:${mobileNumber}`}
              target="_blank"
              rel="noopener noreferrer"
              sx={{ color: "#6666ff" }}
            >
              <span>{mobileNumber}</span>
            </Link>
          </Tooltip>
        ) : (
          <Link
            href={`tel:${mobileNumber}`}
            target="_blank"
            rel="noopener noreferrer"
            sx={{ color: "#6666ff" }}
          >
            {mobileNumber}
          </Link>
        );
      },
    },
    {
      field: "actions",
      headerName: "Actions",
      flex: 0.09,
      minWidth: 100,
      renderCell: (params) => {
        const onClickDelete = () => {
          const id = params?.row?.id;
          onDelete(id);
        };
        return (
          <div
            style={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
            }}
          >
            <Tooltip title="Delete">
              <CustomAvatar
                skin="light"
                variant="rounded"
                sx={{ mr: 5, width: 34, height: 34, cursor: "pointer" }}
                onClick={onClickDelete}
              >
                <Icon icon="iconamoon:trash" />
              </CustomAvatar>
            </Tooltip>
          </div>
        );
      },
    },
  ];

  const handleStrategicPartner = (event) => {
    setIsStrategicPartner(event.target.checked);
  };

  const handleListingEmpanelled = (event) => {
    setIsListingEmpanelled(event.target.checked);
  };

  const handleMicrositeEmpanelled = (event) => {
    setIsMicroSiteEmpanelled(event.target.checked);
  };

  const handleCancel = () => {
    onClose();
    setFinalList(false);
    setSelectedCategory("");
    setCategoryId("");
    setSearchKeyword("");
    setSocietyKeyword("");
    setKeyword("");
    setIsListingEmpanelled(false);
    setIsMicroSiteEmpanelled(false);
    setIsStrategicPartner(false);
    setSelectedUsers([]);
    setSelectedDesignations([])
    reset({
      contactGroupName: "",
      categoryId: "",
      services: "",
      locations: "",
      assignedTo: "",
      leadStatus: "",
      leadPriority: "",
      empLocations: "",
      empAssignedTo: "",
      designation: "",
      chsLocations: "",
      chsAssignedTo: "",
      chsLeadStatus: "",
      chsLeadPriority: "",
    });
  };

  const handleError = (error) => {
    console.error("society requisition: All Services:", error);
  };

  useEffect(() => {
    if (!!authConfig) {
      getAllListValuesByListNameId(
        authConfig.allServicesListNameId,
        (data) =>
          setAllServicesList(
            data?.listValues.map((item) => ({
              value: item.id,
              key: item.listValue,
            }))
          ),
        handleError
      );
    }
    if (!!authConfig) {
      getAllListValuesByListNameId(
        authConfig.locationlistNameId,
        (data) =>
          setLocationsList(
            data?.listValues.map((item) => ({
              value: item.id,
              key: item.listValue,
            }))
          ),
        handleError
      );
    }
    if (!!authConfig) {
      getAllListValuesByListNameId(
        authConfig.leadStatusListNamesId,
        (data) =>
          setLeadStatusList(
            data?.listValues.map((item) => ({
              value: item.id,
              key: item.listValue,
            }))
          ),
        handleError
      );
    }
    if (!!authConfig) {
      getAllListValuesByListNameId(
        authConfig.leadPriorityListNamesId,
        (data) =>
          setLeadPriorityList(
            data?.listValues.map((item) => ({
              value: item.id,
              key: item.listValue,
            }))
          ),
        handleError
      );
    }
    if (!!authConfig) {
      getAllListValuesByListNameId(
        authConfig.workLocation,
        (data) =>
          setLocationsData(
            data?.listValues.map((item) => ({
              value: item.id,
              key: item.listValue,
            }))
          ),
        handleError
      );
    }
    if (!!authConfig) {
      getAllListValuesByListNameId(
        authConfig.designationId,
        (data) =>
          setDesignationsData(
            data?.listValues.map((item) => ({
              value: item.id,
              key: item.listValue,
            }))
          ),
        handleError
      );
    }
  }, [authConfig]);

  const fetchUsers = async (
    currentPage,
    currentPageSize,
    searchKeyword,
    categoryId,
    selectedServices,
    isStrategicPartner,
    isListingEmpanelled,
    isMicroSiteEmpanelled,
    selectedLocations,
    selectedAssignments,
    spSelectedLeadStatus,
    spSelectedLeadPriority,
    selectedDesignations,
    employeeLocations,
    employeeAssignments,
    chsAssignments,
    chsLocations,
    chsSelectedLeadStatus,
    chsSelectedLeadPriority,
    societySearch
  ) => {
    const url = getUrl(authConfig.allUsersForContacts);

    const headers = getAuthorizationHeaders({
      contentType: authConfig.INDIVIDUAL_GET_ALL_CONTACT_GROUP_REQ_V1,
      accept: authConfig.INDIVIDUAL_GET_ALL_CONTACT_GROUP_RES_V1,
    });

    let data = {
      page: currentPage,
      pageSize: currentPageSize,
      category: categoryId,
    };

    if (categoryId === "SERVICE_PROVIDER") {
      Object.assign(data, {
        searchByOrgName:searchKeyword,
        serviceTypeIds: selectedServices?.map((service) => service.value),
        strategicPartner:isStrategicPartner,
        listingEmpanelled:isListingEmpanelled,
        microSiteEmpanelled:isMicroSiteEmpanelled,
        location: selectedLocations?.map((location) => location.value),
        assignedTo: selectedAssignments?.map((assignment) => assignment.value),
        leadStatus: spSelectedLeadStatus?.map((status) => status.value),
        leadPriority: spSelectedLeadPriority?.map((priority) => priority.value),
      });
    } else if (categoryId === "EMPLOYEE") {
      Object.assign(data, {
        location: employeeLocations?.map((location) => location.value),
        designation: selectedDesignations?.map(
          (designation) => designation.value
        ),
      });
    } else if (categoryId === "SOCIETY") {
      Object.assign(data, {
        searchByOrgName:societySearch,
        location: chsLocations?.map((location) => location.value),
        assignedTo: chsAssignments?.map((assignment) => assignment.value),
        leadStatus: chsSelectedLeadStatus?.map((status) => status.value),
        leadPriority: chsSelectedLeadPriority?.map(
          (priority) => priority.value
        ),
      });
    }

    try {
      const response = await axios({
        method: "post",
        url: url,
        headers: headers,
        data: data,
      });

      if (response.data) {
        setUserData(response.data?.individuals || []);
        setRowCount(response.data?.rowCount || 0);
      } else {
        console.error("Unexpected API response format:", response);
        handleFailure();
      }
    } catch (error) {
      console.error("Error fetching users:", error);
    }
  };

  useEffect(() => {
    fetchUsers(
      page,
      pageSize,
      searchKeyword,
      categoryId,
      selectedServices,
      isStrategicPartner,
      isListingEmpanelled,
      isMicroSiteEmpanelled,
      selectedLocations,
      selectedAssignments,
      spSelectedLeadStatus,
      spSelectedLeadPriority,
      selectedDesignations,
      employeeLocations,
      employeeAssignments,
      chsAssignments,
      chsLocations,
      chsSelectedLeadStatus,
      chsSelectedLeadPriority,
      societySearch
    );
  }, [
    page,
    pageSize,
    searchKeyword,
    categoryId,
    selectedServices,
    isStrategicPartner,
    isListingEmpanelled,
    isMicroSiteEmpanelled,
    selectedLocations,
    selectedAssignments,
    selectedDesignations,
    employeeLocations,
    employeeAssignments,
    chsAssignments,
    chsLocations,
    chsSelectedLeadStatus,
    chsSelectedLeadPriority,
    societySearch,
    spSelectedLeadStatus,
    spSelectedLeadPriority,
  ]);

  const handleSuccess = () => {
    const message = `
    <div> 
      <h3>Contact Group Created Successfully.</h3>
    </div>
  `;
    setDialogMessage(message);
    setOpenDialogContent(true);
  };

  const handleFailure = () => {
    const message = `
    <div> 
      <h3> Failed to Create Group. Please try again later.</h3>
    </div>
  `;
    setDialogMessage(message);
    setOpenDialogContent(true);
  };

  const [currentToastId, setCurrentToastId] = useState(null);

  const onFormInvalid = (toastError) => {
    if (currentToastId !== null) {
      toast.dismiss(currentToastId);
    }

    Object.keys(toastError).forEach((error) => {
      if (toastError[error].message.length > 0) {
        const toastId = toast.error(toastError[error].message, {
          duration: 4000, // Set the duration you prefer
          onClose: () => {
            setCurrentToastId(null); // Reset currentToastId when the toast closes
          },
        });
        setCurrentToastId(toastId);
      }
    });
  };

  const isApiCalling = useRef(false);

  async function submit(data) {
    if (isApiCalling.current) {
      // API call is already in progress, return early
      return;
    }

    if (selectedUsers.length < 1) {
      toast.error(
        "At least one contact should be in final list to create a group",
        {
          style: { zIndex: 9999 }, // Adjust the zIndex to ensure it appears above the dialog
        }
      );
      return;
    }
    isApiCalling.current = true;
    const fields = {
      name: data?.contactGroupName,
      contactList: selectedUsers,
      searchFilter: {
        sp: {
          service: selectedServices?.map((services) => services.value),
          listing: isListingEmpanelled,
          microsite: isMicroSiteEmpanelled,
          houzerStrategic: isStrategicPartner,
          search: searchKeyword,
          location: selectedLocations.map((locations) => locations.value),
          assignedTo: selectedAssignments.map((assigned) => assigned.value),
          leadStatus: spSelectedLeadStatus.map(
            (leadStatus) => leadStatus.value
          ),
          leadPriority: spSelectedLeadPriority.map(
            (leadPriority) => leadPriority.value
          ),
        },
        chs: {
          search: societyKeyword,
          location: chsLocations.map((location) => location.value),
          assignedTo: chsAssignments.map((assignment) => assignment.value),
          leadStatus: chsSelectedLeadStatus.map(
            (leadStatus) => leadStatus.value
          ),
          leadPriority: chsSelectedLeadPriority.map(
            (leadPriority) => leadPriority.value
          ),
        },
        employee: {
          location: employeeLocations.map((location) => location.value),
          assignedTo: employeeAssignments.map((assignment) => assignment.value),
          designation: selectedDesignations.map(
            (designation) => designation.value
          ),
        },
      },
    };

    try {
      const response = await auth.postContactGroup(
        fields,
        handleFailure,
        handleSuccess
      );
    } catch (error) {
      console.error("Employee Creation failed:", error);
      handleFailure();
    } finally {
      isApiCalling.current = false;
    }
    fetchContactGroups();
    handleCancel();
  }

  const handleButtonClick = () => {
    setOpenDialogContent(false);
  };

  const handleCategoryChange = (event) => {
    const selectedId = event.target.value;
    setCategoryId(event.target.value);
    const selectedCat = categoriesData.find(
      (cat) => cat.category === selectedId
    );
    if (selectedCat) {
      setSelectedCategory(selectedCat.category);
    }
  };

  const handlePageChange = (direction) => {
    if (direction === page) {
      setPage(page + 1);
    } else {
      setPage(page - 1);
    }
  };

  const handlePageSizeChange = (params) => {
    if (params) {
      setPageSize(params);
      setPage(1);
    }
  };

  const handleServiceChange = (event) => {
    const value = event.target.value;
    setSelectedServices(value);
  };

  const handleLocationChange = (event) => {
    const value = event.target.value;
    setSelectedLocations(value);
  };

  const handleSPLeadStatusChange = (event) => {
    const value = event.target.value;
    setSpSelectedLeadStatus(value);
  };

  const handleSPLeadPriorityChange = (event) => {
    const value = event.target.value;
    setSpSelectedLeadPriority(value);
  };

  const handleCHSLeadStatusChange = (event) => {
    const value = event.target.value;
    setChsSelectedLeadStatus(value);
  };

  const handleCHSLeadPriorityChange = (event) => {
    const value = event.target.value;
    setChsSelectedLeadPriority(value);
  };

  const handleEmployeeLocationChange = (event) => {
    const value = event.target.value;
    setEmployeeLocations(value);
  };

  const handleEmployeeAssignmentsChange = (event) => {
    setEmployeeAssignments(event.target.value);
  };

  const handleAssignedToChange = (event) => {
    const value = event.target.value;
    setSelectedAssignments(value);
  };

  const handleDesignationChange = (event) => {
    const value = event.target.value;
    setSelectedDesignations(value);
  };

  const handleCHSLocationChange = (event) => {
    const value = event.target.value;
    setChsLocations(value);
  };

  const handleCHSAssignmentsChange = (event) => {
    const value = event.target.value;
    setChsAssignments(value);
  };

  useEffect(() => {
    if (selectedRows.length > 0) {
      setFinalList(true);
    } else {
      setFinalList(false);
    }
  }, [selectedRows]);

  const handleFinalListClick = () => {
    const selectedData = usersData.filter((row) =>
      selectedRows.includes(row.id)
    );

    setSelectedUsers((prevSelectedUsers) => {
      const newSelectedData = selectedData.filter(
        (row) => !prevSelectedUsers.some((selected) => selected.id === row.id)
      );
      return [...prevSelectedUsers, ...newSelectedData];
    });
  };

  return (
    <>
      <Dialog fullScreen open={open} onClose={handleCancel}>
        <DialogTitle
          sx={{
            position: "relative",
            borderBottom: (theme) => `1px solid ${theme.palette.divider}`,
            p: (theme) => `${theme.spacing(1.75, 4)} !important`,
            display: "flex",
            alignItems: "center",
            justifyContent: { xs: "start" },
            fontSize: { xs: 19, md: 20 },
            height: "50px", // Set fixed height for header
            marginLeft: { xl: 4, lg: 4, md: 4, sm: 4, xs: 4 },
          }}
          textAlign={"center"}
        >
          Create a Contact Group
          <Box
            sx={{
              position: "absolute",
              top: "9px",
              right: "10px",
              marginRight: { xl: 10, lg: 6, md: 6, sm: 5.5, xs: 5.7 },
            }}
          >
            <IconButton
              size="small"
              onClick={handleCancel}
              sx={{
                // p: "0.438rem",
                borderRadius: 1,
                color: "common.white",
                backgroundColor: "primary.main", // Set background color to green
                "&:hover": {
                  backgroundColor: (theme) =>
                    `rgba(${theme.palette.customColors.main}, 0.16)`,
                },
              }}
            >
              <Icon icon="tabler:x" fontSize="1rem" />
            </IconButton>
          </Box>
        </DialogTitle>
        <DialogContent
          sx={{
            position: "relative",
            pt: (theme) => `${theme.spacing(8)} !important`,
            pb: (theme) => `${theme.spacing(5)} !important`,
            px: (theme) => [`${theme.spacing(8)} !important`],
          }}
        >
          <Card>
            <Grid
              sx={{
                backgroundColor: "#f2f7f2",
                mt: 4,
                paddingTop: 0,
                height: "36px",
                display: "flex",
                alignItems: "center",
              }}
            >
              <Typography
                variant="body1"
                fontWeight={"bold"}
                sx={{ mt: 0, ml: 2 }}
              >
                Contact Group name
              </Typography>
              <Divider />
            </Grid>
            <Divider />

            <Grid container spacing={5} style={{ padding: "16px" }}>
              <Grid item xs={12} sm={4}>
                <FormControl fullWidth>
                  <Controller
                    name="contactGroupName"
                    control={control}
                    rules={{ required: "Contact Group Name is required" }}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        label="Contact Group Name"
                        InputLabelProps={{ shrink: true }}
                        size="small"
                        placeholder="Enter your Contact Group Name "
                        error={Boolean(errors.contactGroupName)}
                        helperText={errors.contactGroupName?.message}
                        aria-describedby="validation-contactGroupName"
                      />
                    )}
                  />
                </FormControl>
              </Grid>
            </Grid>
          </Card>
          <Card>
            <Grid
              sx={{
                backgroundColor: "#f2f7f2",
                mt: 4,
                paddingTop: 0,
                height: "36px",
                display: "flex",
                alignItems: "center",
              }}
            >
              <Typography
                variant="body1"
                fontWeight={"bold"}
                sx={{ mt: 0, ml: 2 }}
              >
                Contacts filter criteria
              </Typography>
              <Divider />
            </Grid>
            <Divider />

            <Grid container spacing={5} style={{ padding: "16px" }}>
              <Grid item xs={12} sm={3}>
                <FormControl fullWidth error={Boolean(errors.categoryId)}>
                  <Controller
                    name="categoryId"
                    control={control}
                    rules={{
                      required: false,
                    }}
                    render={({ field }) => (
                      <>
                        <SelectAutoComplete
                          id="categoryId"
                          label="Category Type"
                          nameArray={categoriesData.map((type) => ({
                            key: type.category
                              .replace(/_/g, " ") // Replace underscores with spaces
                              .toLowerCase() // Convert to lowercase to standardize
                              .replace(/\b\w/g, (char) => char.toUpperCase()), // Capitalize first letter
                            value: type.category,
                          }))}
                          value={categoryId}
                          onChange={(e) => {
                            field.onChange(e.target.value);
                            handleCategoryChange(e); // Update dependent state
                          }}
                        />
                        {errors.categoryId && (
                          <FormHelperText sx={{ color: "error.main" }}>
                            {errors.categoryId.message}
                          </FormHelperText>
                        )}
                      </>
                    )}
                  />
                </FormControl>
              </Grid>
              {selectedCategory == "SERVICE_PROVIDER" && (
                <>
                  <Grid item xs={12} sm={3}>
                    <FormControl fullWidth error={Boolean(errors.services)}>
                      <Controller
                        name="services"
                        control={control}
                        render={({ field }) => (
                          <MultiSelectAutoComplete
                            id="services"
                            label="Select Services"
                            nameArray={allServicesList}
                            value={field.value || []}
                            onChange={(e) => {
                              handleServiceChange(e);
                              field.onChange(e.target.value);
                            }}
                            error={Boolean(errors.services)}
                          />
                        )}
                      />
                    </FormControl>
                  </Grid>

                  <Grid item xs={12} sm={3}>
                    <FormControl fullWidth>
                      <Controller
                        name="mainSearch"
                        control={control}
                        render={({ field: { onChange } }) => (
                          <TextField
                            size="small"
                            id="mainSearch"
                            placeholder="Search by company name"
                            value={keyword}
                            onChange={(e) => {
                              onChange(e.target.value);
                              setKeyword(e.target.value);
                              setSearchKeyword(e.target.value);
                            }}
                            onKeyDown={(e) => {
                              if (e.key === "Enter") {
                                setSearchKeyword(keyword);
                              }
                            }}
                            sx={{
                              "& .MuiInputBase-root": {
                                fontSize: "0.9rem",
                                borderRadius: "5px",
                                backgroundColor: "white",
                              },
                            }}
                            InputProps={{
                              endAdornment: (
                                <InputAdornment position="start">
                                  <SearchIcon
                                    sx={{
                                      cursor: "pointer",
                                    }}
                                    onClick={() => {
                                      setSearchKeyword(keyword);
                                    }}
                                  />
                                </InputAdornment>
                              ),
                            }}
                          />
                        )}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item lg={3} md={2}></Grid>
                  <Grid item xs={12} sm={3}>
                    <FormControl fullWidth error={Boolean(errors.location)}>
                      <Controller
                        name="location"
                        control={control}
                        rules={{ required: false }}
                        render={({ field }) => (
                          <MultiSelectAutoComplete
                            id="location"
                            size="small"
                            label="Select Locations"
                            nameArray={locationsList}
                            value={field.value || []}
                            onChange={(event) => {
                              field.onChange(event.target.value);
                              handleLocationChange(event);
                            }}
                            error={Boolean(errors.location)}
                          />
                        )}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item lg={3} md={2}>
                    <FormControlLabel
                      control={
                        <Checkbox
                          checked={isListingEmpanelled}
                          onChange={handleListingEmpanelled}
                          name="listingCheckbox"
                          color="primary"
                        />
                      }
                      label="Is Listing Empanelled"
                    />
                  </Grid>
                  <Grid item lg={3} md={2}>
                    <FormControlLabel
                      control={
                        <Checkbox
                          checked={isMicroSiteEmpanelled}
                          onChange={handleMicrositeEmpanelled}
                          name="micrositeCheckbox"
                          color="primary"
                        />
                      }
                      label="Is Microsite Empanelled"
                    />
                  </Grid>
                  <Grid item lg={3} md={2}>
                    <FormControlLabel
                      control={
                        <Checkbox
                          checked={isStrategicPartner}
                          onChange={handleStrategicPartner}
                          name="strategicPartnerCheckbox"
                          color="primary"
                        />
                      }
                      label="Houzer Strategic Partner"
                    />
                  </Grid>
                  <Grid item xs={12} sm={3}>
                    <FormControl fullWidth>
                      <Controller
                        name="leadStatus"
                        control={control}
                        rules={{ required: false }}
                        render={({ field }) => (
                          <MultiSelectAutoComplete
                            size="small"
                            id="leadStatus"
                            label="Lead Status"
                            nameArray={leadStatusList}
                            value={field.value || []}
                            onChange={(event) => {
                              field.onChange(event.target.value);
                              handleSPLeadStatusChange(event);
                            }}
                            error={Boolean(errors.leadStatus)}
                          />
                        )}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item xs={12} sm={3}>
                    <FormControl fullWidth>
                      <Controller
                        name="leadPriority"
                        control={control}
                        rules={{ required: false }}
                        render={({ field }) => (
                          <MultiSelectAutoComplete
                            size="small"
                            id="leadPriority"
                            label="Lead Priority"
                            nameArray={leadPriorityList}
                            value={field.value || []}
                            onChange={(event) => {
                              field.onChange(event.target.value);
                              handleSPLeadPriorityChange(event);
                            }}
                            error={Boolean(errors.leadPriority)}
                          />
                        )}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item xs={12} sm={3}>
                    <FormControl fullWidth>
                      <Controller
                        name="assignedTo"
                        control={control}
                        rules={{ required: false }}
                        render={({ field }) => (
                          <MultiSelectAutoComplete
                            size="small"
                            id="assignedTo"
                            label="Assigned To"
                            nameArray={employeesData.map((emp) => ({
                              value: emp.id,
                              key: emp.name,
                            }))}
                            value={field.value || []}
                            onChange={(event) => {
                              field.onChange(event.target.value);
                              handleAssignedToChange(event);
                            }}
                            error={Boolean(errors.leadPriority)}
                          />
                        )}
                      />
                    </FormControl>
                  </Grid>
                </>
              )}

              {selectedCategory == "EMPLOYEE" && (
                <>
                  <Grid item xs={12} sm={3}>
                    <FormControl fullWidth>
                      <Controller
                        name="location"
                        control={control}
                        rules={{ required: false }}
                        render={({ field }) => (
                          <MultiSelectAutoComplete
                            size="small"
                            id="location"
                            label="Select Locations"
                            nameArray={locationsData}
                            value={field.value || []}
                            onChange={(event) => {
                              field.onChange(event.target.value);
                              handleEmployeeLocationChange(event);
                            }}
                            error={Boolean(errors.locations)}
                          />
                        )}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item xs={12} sm={3}>
                    <FormControl fullWidth>
                      <Controller
                        name="designation"
                        control={control}
                        rules={{ required: false }}
                        render={({ field }) => (
                          <MultiSelectAutoComplete
                            size="small"
                            id="designation"
                            label=" Select Designation"
                            nameArray={designationsData}
                            value={field.value || []}
                            onChange={(event) => {
                              field.onChange(event.target.value);
                              handleDesignationChange(event);
                            }}
                            error={Boolean(errors.designation)}
                          />
                        )}
                      />
                    </FormControl>
                  </Grid> 
                </>
              )}
              {selectedCategory == "SOCIETY" && (
                <>
                  <Grid item xs={12} sm={3}>
                    <FormControl fullWidth>
                      <Controller
                        name="search"
                        control={control}
                        render={({ field: { onChange } }) => (
                          <TextField
                            size="small"
                            id="search"
                            placeholder="Search by Society name"
                            value={societyKeyword}
                            onChange={(e) => {
                              onChange(e.target.value);
                              setSocietyKeyword(e.target.value);
                              setSocietySearch(e.target.value);
                            }}
                            onKeyDown={(e) => {
                              if (e.key === "Enter") {
                                setSocietySearch(keyword);
                              }
                            }}
                            sx={{
                              "& .MuiInputBase-root": {
                                fontSize: "0.9rem",
                                borderRadius: "5px",
                                backgroundColor: "white",
                              },
                            }}
                            InputProps={{
                              endAdornment: (
                                <InputAdornment position="start">
                                  <SearchIcon
                                    sx={{
                                      cursor: "pointer",
                                    }}
                                    onClick={() => {
                                      setSocietySearch(keyword);
                                    }}
                                  />
                                </InputAdornment>
                              ),
                            }}
                          />
                        )}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item xs={12} sm={3}>
                    <FormControl fullWidth error={Boolean(errors.services)}>
                      <Controller
                        name="location"
                        control={control}
                        rules={{ required: false }}
                        render={({ field }) => (
                          <MultiSelectAutoComplete
                            id="location"
                            size="small"
                            label="Select Locations"
                            nameArray={locationsList}
                            value={field.value || []}
                            onChange={(event) => {
                              field.onChange(event.target.value);
                              handleCHSLocationChange(event);
                            }}
                            error={Boolean(errors.location)}
                          />
                        )}
                      />
                    </FormControl>
                  </Grid>
                  <Grid itm xs={12} sm={3}></Grid>
                  <Grid item xs={12} sm={3}>
                    <FormControl fullWidth>
                      <Controller
                        name="leadStatus"
                        control={control}
                        rules={{ required: false }}
                        render={({ field }) => (
                          <MultiSelectAutoComplete
                            size="small"
                            id="leadStatus"
                            label="Lead Status"
                            nameArray={leadStatusList}
                            value={field.value || []}
                            onChange={(event) => {
                              field.onChange(event.target.value);
                              handleCHSLeadStatusChange(event);
                            }}
                            error={Boolean(errors.serivcesProvided)}
                          />
                        )}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item xs={12} sm={3}>
                    <FormControl fullWidth>
                      <Controller
                        name="leadPriority"
                        control={control}
                        rules={{ required: false }}
                        render={({ field }) => (
                          <MultiSelectAutoComplete
                            size="small"
                            id="leadPriority"
                            label="Lead Priority"
                            nameArray={leadPriorityList}
                            value={field.value || []}
                            onChange={(event) => {
                              field.onChange(event.target.value);
                              handleCHSLeadPriorityChange(event);
                            }}
                            error={Boolean(errors.leadPriority)}
                          />
                        )}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item xs={12} sm={3}>
                    <FormControl fullWidth>
                      <Controller
                        name="assignedTo"
                        control={control}
                        rules={{ required: false }}
                        render={({ field }) => (
                          <MultiSelectAutoComplete
                            size="small"
                            id="assignedTo"
                            label="Assigned To"
                            nameArray={employeesData.map((emp) => ({
                              value: emp.id,
                              key: emp.name,
                            }))}
                            value={field.value || []}
                            onChange={(event) => {
                              field.onChange(event.target.value);
                              handleCHSAssignmentsChange(event);
                            }}
                            error={Boolean(errors.leadPriority)}
                          />
                        )}
                      />
                    </FormControl>
                  </Grid>
                </>
              )}
            </Grid>
          </Card>
          <Card>
            <Grid
              sx={{
                backgroundColor: "#f2f7f2",
                mt: 4,
                paddingTop: 0,
                height: "36px",
                display: "flex",
                alignItems: "center",
              }}
            >
              <Typography
                variant="body1"
                fontWeight={"bold"}
                sx={{ mt: 0, ml: 2 }}
              >
                Contacts
              </Typography>
              <Divider />
            </Grid>
            <Divider />

            <div style={{ height: 380, width: "100%" }}>
              <DataGrid
                rows={usersData}
                columns={columns}
                checkboxSelection
                pagination
                pageSize={pageSize}
                page={page - 1}
                rowsPerPageOptions={rowsPerPageOptions}
                rowCount={rowCount}
                paginationMode="server"
                onPageChange={handlePageChange}
                onPageSizeChange={handlePageSizeChange}
                //onSelectionModelChange={handleSelection}
                rowHeight={38}
                headerHeight={38}
                onSelectionModelChange={(newSelection) => {
                  setSelectedRows(newSelection);
                }}
              />
            </div>
            <div
              style={{
                display: "flex",
                justifyContent: "center",
                marginTop: "20px",
                marginBottom: "20px",
              }}
            >
              <Tooltip
                title={!finalList ? "Select at least one row to enable" : ""}
              >
                <span>
                  <Button
                    disabled={!finalList}
                    variant="contained"
                    onClick={handleFinalListClick}
                  >
                    Add to Final List
                  </Button>
                </span>
              </Tooltip>
            </div>
          </Card>
          <div style={{ height: 380, width: "100%" }}>
            <DataGrid
              rows={selectedUsers}
              columns={cols}
              rowsPerPageOptions={rowsPerPageOptions}
              rowCount={selectedUsers?.length}
              rowHeight={38}
              headerHeight={38}
            />
          </div>
        </DialogContent>
        <DialogActions
          sx={{
            justifyContent: "end",
            borderTop: (theme) => `1px solid ${theme.palette.divider}`,
            p: (theme) => `${theme.spacing(2.5)} !important`,
            height: "50px", // Set fixed height for footer
          }}
        >
          <Box
            sx={{
              marginRight: { xl: 10, lg: 6, md: 6, sm: 5.5, xs: 6 }, // Correct marginRight syntax
              display: "flex", // Ensure the Box is a flex container
              gap: 2,
            }}
          >
            <Button
              display="flex"
              variant="outlined"
              color="primary"
              onClick={handleCancel}
            >
              Cancel
            </Button>
            <Button
              display="flex"
              justifyContent="center"
              variant="contained"
              color="primary"
              onClick={handleSubmit(submit, onFormInvalid)}
            >
              Create
            </Button>
          </Box>
        </DialogActions>
        <Toaster position="top-right" />
      </Dialog>

      <Dialog
        open={openDialogContent}
        onClose={handleButtonClick}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
        PaperProps={{
          sx: {
            p: (theme) => `${theme.spacing(2.5)} !important`,
            backgroundColor: "white",
          },
        }}
      >
        <Box
          sx={{
            width: "100%",
            borderRadius: 1,
            textAlign: "center",
            border: (theme) => `1px solid ${theme.palette.divider}`,
            borderColor: "primary.main",
          }}
        >
          <DialogContent>
            <DialogContentText
              id="alert-dialog-description"
              color="primary.main"
            >
              <div dangerouslySetInnerHTML={{ __html: dialogMessage }} />
            </DialogContentText>
          </DialogContent>
          <DialogActions>
            <Button
              variant="contained"
              onClick={handleButtonClick}
              sx={{ margin: "auto", width: 100 }}
            >
              Okay
            </Button>
          </DialogActions>
        </Box>
      </Dialog>
    </>
  );
};

export default ContactGroupDetails;
