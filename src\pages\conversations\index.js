// ** MUI Imports
import Grid from "@mui/material/Grid";
import Typography from "@mui/material/Typography";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";

// ** Custom Components Imports
import { useContext } from "react";
import PageHeader from "src/@core/components/page-header";
import { AuthContext } from "src/context/AuthContext";
import { getAuthorizationHeaders, getUrl } from "src/helpers/utils";



// ** Demo
import {
  Card,
  CardContent,
  TableCell
} from "@mui/material";
import { styled } from "@mui/material/styles";
import DatePickerWrapper from "src/@core/styles/libs/react-datepicker";


import axios from "axios";

import authConfig from "src/configs/auth";
import ServiceConversationDetails from "./sections/ServiceConversationDetails";
import ServiceInfo from "./sections/ServiceInfo";
import ServiceProviderConversation from "./sections/ServiceProviderConversation";
const MUITableCell = styled(TableCell)(({ theme }) => ({
  borderBottom: 0,
  paddingLeft: "0 !important",
  paddingRight: "0 !important",
  "&:not(:last-child)": {
    paddingRight: `${theme.spacing(2)} !important`,
  },
}));

const Index = (currentRow, setCurrentRow, employeeData) => {
  const {
    register,
    control,
    setValue,
    handleSubmit,
    formState: { errors },
  } = useForm();

  const {
    getAllListValuesByListNameId,
    setBasicProfileAllProfiles,
    basicProfileAllProfiles,
  } = useContext(AuthContext);

  const [conversationTypeData, setConversationTypeData] = useState(null);
  const [outcomeConversationData, setOutcomeConversationData] = useState(null);
  const [targetData, setTargetData] = useState(null);
  const [shallRemindData, setShallRemindData] = useState(null);

  const [openConversationDialog, setOpenConversationDialog] = useState(false);

  const handleOpenConversationDialog = () => {
    setOpenConversationDialog(true);
  };

  const handleError = (error) => {
    console.error("Basic profile: All Services:", error);
  };

  useEffect(() => {
    if (!!authConfig) {
      getAllListValuesByListNameId(
        authConfig.shallRemind,
        (data) =>
          setShallRemindData(
            data?.listValues.map((item) => ({
              value: item.id,
              key: item.listValue,
            }))
          ),
        handleError
      );

      getAllListValuesByListNameId(
        authConfig.target,
        (data) =>
          setTargetData(
            data?.listValues.map((item) => ({
              value: item.id,
              key: item.listValue,
            }))
          ),
        handleError
      );

      getAllListValuesByListNameId(
        authConfig.outcomeConversation,
        (data) =>
          setOutcomeConversationData(
            data?.listValues.map((item) => ({
              value: item.id,
              key: item.listValue,
            }))
          ),
        handleError
      );

      getAllListValuesByListNameId(
        authConfig.conversationType,
        (data) =>
          setConversationTypeData(
            data?.listValues.map((item) => ({
              value: item.id,
              key: item.listValue,
            }))
          ),
        handleError
      );
    }
  }, [authConfig]);

  const [conversationList, setConversationList] = useState([]);
  const [rowCount, setRowCount] = useState(0);

  const fetchUsers = async (currentPage, currentPageSize, searchKeyword) => {
    const url = getUrl(
      authConfig.getAllConversationsByUserId +
        "/" +
        (currentRow?.currentRow?.userId || currentRow?.currentRow?.id)
    );
    const headers = getAuthorizationHeaders();

    const data = {
      page: currentPage,
      pageSize: currentPageSize,
      searchKeyword: searchKeyword,
    };

    try {
      const response = await axios({
        method: "post",
        url: url,
        headers: headers,
        data: data,
      });

      if (response.data) {
        console.log("Edhena.??")
        setConversationList(response.data?.conversations || []);
        setRowCount(response.data?.rowCount || 0);
      } else {
        console.error("Unexpected API response format:", response);
      }
    } catch (error) {
      console.error("Error fetching users:", error);
    } finally {
    }
  };

  useEffect(() => {
    if (!!currentRow && !!currentRow?.currentRow) {
      fetchUsers();
    }
  }, []);

  useEffect(() => {
    basicProfileAllProfiles;
  }, [setBasicProfileAllProfiles]);

 
    return (
      <div>
        <>
          <style>
            {`
           .tableBody:hover {
               background-color: #f6f6f7;
               cursor: pointer
           }
       `}
          </style>
          <DatePickerWrapper>
            <Grid container spacing={6} className="match-height">
              <Grid item xs={12}>
                <Grid
                  container
                  spacing={2}
                  sx={{
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "space-between",
                  }}
                >
                  <Grid item xs={8}>
                    <PageHeader
                      subtitle={<Typography variant="h6"></Typography>}
                    />
                  </Grid>

                  <Grid item xs={12}>
                    <ServiceInfo
                      data={currentRow?.currentRow?.basicProfileData || currentRow?.currentRow }
                      expanded={currentRow?.expanded}
                      employeeData={currentRow?.employeeData}
                      basicProfile={currentRow?.currentRow?.basicProfile}
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <Card sx={{ width: "100%" }}>
                      <CardContent>
                        <Grid container spacing={1}>
                          <Grid
                            item
                            xs={12}
                            sx={{
                              display: "flex",
                              justifyContent: "flex-end",
                              mt: 4,
                              mb: 2,
                            }}
                          >
                            <ServiceProviderConversation
                              userId={
                                currentRow?.currentRow?.userId ||
                                currentRow?.currentRow?.id
                              }
                              data={
                                currentRow?.currentRow?.basicProfile ||
                                currentRow?.currentRow?.basicProfileData || currentRow?.currentRow
                              }
                              openConversationDialog={openConversationDialog}
                              setOpenConversationDialog={
                                setOpenConversationDialog
                              }
                              handleOpenConversationDialog={
                                handleOpenConversationDialog
                              }
                              outcomeConversationData={outcomeConversationData}
                              targetData={targetData}
                              shallRemindData={shallRemindData}
                              conversationTypeData={conversationTypeData}
                              fetchUsers={fetchUsers}
                            />
                          </Grid>
                          <Grid item xs={12}>
                            <ServiceConversationDetails
                              expanded={currentRow?.expanded}
                              conversationList={conversationList}
                              rowCount={rowCount}
                            />
                          </Grid>
                        </Grid>
                      </CardContent>
                    </Card>
                  </Grid>
                </Grid>
              </Grid>
            </Grid>
          </DatePickerWrapper>
        </>
      </div>
    );
  
};

export default Index;
