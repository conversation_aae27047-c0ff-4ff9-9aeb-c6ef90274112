import Card from "@mui/material/Card";
import Table from "@mui/material/Table";
import TableRow from "@mui/material/TableRow";
import TableHead from "@mui/material/TableHead";
import TableBody from "@mui/material/TableBody";
import TableCell from "@mui/material/TableCell";
import CardHeader from "@mui/material/CardHeader";
import CardContent from "@mui/material/CardContent";
import TableContainer from "@mui/material/TableContainer";
import Chip from "@mui/material/Chip";
import {
  Box,
  Button,
  CardActions,
  Divider,
  Grid,
  Typography,
} from "@mui/material";

const ViewMode = ({
  data,
  handleSubmit,
  isEditMode,
  setIsEditMode,
  tabContent,
}) => {
  const title = tabContent.name + " Services";

  if (data?.length > 0) {
    return (
      <Card>
        {/* Sticky Header */}
        <Grid
          sx={{
            position: "sticky",
            top: 0, // Stick to the top
            zIndex: 2, // Higher than the content to stay on top
            backgroundColor: "#ffffff", // Match the background with your theme
          }}
        >
          <CardHeader
            title={<span style={{ fontWeight: "bold" }}>{title}</span>}
            sx={{
              color: "black",
              lineHeight: 1.8,
              mt: 2,
              fontSize: "1.7rem",
              fontWeight: "bold !important",
              paddingTop: "5px",
              paddingBottom: "1px",
              borderRadius: "5px",
              display: "flex", // Use flex to center content
              justifyContent: "center", // Center horizontally
              textAlign: "center", // Use block to allow textAlign to work
            }}
          />
          <Box
            sx={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
              padding: "0 16px",
            }}
          >
            <CardHeader
              title="Select the options for the service you provide."
              sx={{ flex: 1 }}
            />
            <CardActions sx={{ justifyContent: "flex-end", flex: 1 }}>
              <Button
                variant="contained"
                onClick={() => setIsEditMode(!isEditMode)}
                sx={{ marginTop: "15px" }}
              >
                {isEditMode ? "Cancel" : "Edit"}
              </Button>
              {isEditMode && (
                <Button onClick={handleSubmit} sx={{ marginTop: "15px" }}>
                  Save
                </Button>
              )}
            </CardActions>
          </Box>
          <Divider />
        </Grid>

        {/* Scrollable Content */}
        <CardContent
          sx={{
            maxHeight: "calc(100vh - 200px)", // Adjust height accordingly
            overflowY: "auto", // Enable vertical scrolling
          }}
        >
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Category</TableCell>
                  <TableCell>Selected Options</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {data
                  ?.slice()
                  ?.sort((a, b) => a.listSequence - b.listSequence)
                  ?.map((category) => (
                    <TableRow key={category.name}>
                      <TableCell>{category.name}</TableCell>
                      <TableCell>
                        {category.values.length > 0
                          ? category.values.map((value) => (
                              <Chip
                                key={value.id}
                                label={value.name}
                                style={{
                                  marginRight: "4px",
                                  marginTop: "10px",
                                }}
                              />
                            ))
                          : category.otherValue && (
                              <Chip
                                label={category.otherValue}
                                style={{
                                  marginRight: "4px",
                                  marginTop: "10px",
                                }}
                              />
                            )}
                      </TableCell>
                    </TableRow>
                  ))}
              </TableBody>
            </Table>
          </TableContainer>
        </CardContent>
      </Card>
    );
  } else {
    return (
      <Card
        sx={{
          height: "75vh",
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
        }}
      >
        <Typography
          sx={{
            fontSize: "1.1rem", // You can adjust this value as needed
          }}
        >
          There are currently no selectable options for {tabContent.name} Service
        </Typography>
      </Card>
    );
  }
};

export default ViewMode;
