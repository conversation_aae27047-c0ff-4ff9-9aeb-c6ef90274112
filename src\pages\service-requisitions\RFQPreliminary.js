import { useState, useEffect, useContext, useRef, Fragment } from "react";
import {
  Box,
  Grid,
  Button,
  Typography,
  Divider,
  IconButton,
  Card,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  TextField,
  FormHelperText,
  DialogContentText,
  FormControlLabel,
  Checkbox,
  InputAdornment,
  Tooltip,
  ListItemText,
  TableContainer,
  Table,
  CardHeader,
  Link,
  TableBody,
  TableRow,
} from "@mui/material";
import { AuthContext } from "src/context/AuthContext";
import { Controller, useForm } from "react-hook-form";
import { useAuth } from "src/hooks/useAuth";
import CustomAvatar from "src/@core/components/mui/avatar";
import SearchIcon from "@mui/icons-material/Search";
import DeleteIcon from "@mui/icons-material/Delete";

import Icon from "src/@core/components/icon";
import MultiSelectAutoComplete from "src/@core/components/custom-components/MultiSelectAutoComplete";
import { getUrl, getAuthorizationHeaders } from "src/helpers/utils";
import "react-datepicker/dist/react-datepicker.css";
import authConfig from "src/configs/auth";
import axios from "axios";
import { DataGrid } from "@mui/x-data-grid";
import toast, { Toaster } from "react-hot-toast";
import AccordionBasic from "src/@core/components/custom-components/AccordionBasic";
import { useTheme } from "@emotion/react";
import MUITableCell from "../SP/MUITableCell";

const field = {
  fontSize: "12.75px",
};
const fieldValueStyle = {
  fontWeight: "bold",
  wordWrap: "break-word",
  whiteSpace: "pre-wrap",
  // color: "#66BB6A", // Sets color for the TableCell
  color: "#108A00",
  fontSize: "14px",
  lineHeight: "1.2",
};
const tablecellLabelStyle = {
  width: { xs: "50%", sm: "50%", md: "40%" }, // Responsive width adjustment
  padding: "4px", // Ensure padding for better alignment
  textAlign: "right", // Align the text to the left
  verticalAlign: "middle", // Vertically align the text to the middle
  textIndent: {
    lg: "80px",
    md: "80px",
    sm: "100px",
    xs: "0",
  },
};
const tablecellValueStyle = {
  justifyContent: "flex-start",
  alignItems: "center",
  width: { xs: "50%", sm: "50%", md: "60%" }, // Responsive width for small and medium screens
  height: "10px",
};

const RFQPreliminary = ({
  open,
  serviceTypeId,
  onClose,
  fetchRequisitions,
}) => {
  const auth = useAuth();

  const {
    user,
    getAllListValuesByListNameId,
    listValues,
    listNames,
    requisitionDataDetails,
  } = useContext(AuthContext);
  const {
    handleSubmit,
    control,
    setValue,
    reset,
    formState: { errors },
  } = useForm();
  const currentToast = useRef(null); // To keep track of the current toast

  const [dialogMessage, setDialogMessage] = useState("");
  const [openDialogContent, setOpenDialogContent] = useState(false);

  const [selectedRows, setSelectedRows] = useState([]);
  const [allServicesList, setAllServicesList] = useState([]);
  const [locationsList, setLocationsList] = useState([]);
  const [leadStatusList, setLeadStatusList] = useState([]);
  const [leadPriorityList, setLeadPriorityList] = useState([]);

  const [selectedUsers, setSelectedUsers] = useState([]);

  const [selectedCategory, setSelectedCategory] = useState("");
  const [designationsData, setDesignationsData] = useState(null);
  const [locationsData, setLocationsData] = useState(null);

  const rowsPerPageOptions = [10, 15, 20, 25, 50, 100];

  const [pageSize, setPageSize] = useState(rowsPerPageOptions[0]);
  const [page, setPage] = useState(1);
  const [rowCount, setRowCount] = useState(0);

  const [isStrategicPartner, setIsStrategicPartner] = useState(false);
  const [isListingEmpanelled, setIsListingEmpanelled] = useState(false);
  const [isMicroSiteEmpanelled, setIsMicroSiteEmpanelled] = useState(false);
  const [companyNameFilter, setCompanyNameFilter] = useState("");
  const [locationFilter, setLocationFilter] = useState([]);
  const [leadStatusFilter, setLeadStatusFilter] = useState([]);
  const [leadPriorityFilter, setLeadPriorityFilter] = useState([]);
  const [assignedToFilter, setAssignedToFilter] = useState([]);
  const isApiCalling = useRef(false);

  const [keyword, setKeyword] = useState("");
  const [societyKeyword, setSocietyKeyword] = useState("");
  const [societySearch, setSocietySearch] = useState("");
  const [selectedServices, setSelectedServices] = useState([]);

  const [chsSelectedLeadStatus, setChsSelectedLeadStatus] = useState([]);
  const [chsSelectedLeadPriority, setChsSelectedLeadPriority] = useState([]);

  const [selectedDesignations, setSelectedDesignations] = useState([]);
  const [finalList, setFinalList] = useState(false);

  const [serviceProvidersData, setServiceProvidersData] = useState([]);

  const [employeesData, setEmployeesData] = useState([]);

  const [selectedRowIds, setSelectedRowIds] = useState([]);

  // Handle deletion of selected rows
  const handleDelete = () => {
    // Filter out the rows from selectedUsers based on UUIDs in selectedRows
    setSelectedUsers((prevUsers) =>
      prevUsers.filter((user) => !selectedRows.includes(user.id))
    );

    setSelectedRows([]); // Clear selected rows after deletion
  };

  useEffect(() => {
    // Fetch all employees
    axios({
      method: "get",
      url: getUrl(authConfig.selectDropdownNew) + "?selectionType=EMPLOYEES",
      headers: getAuthorizationHeaders(),
    })
      .then((res) => {
        setEmployeesData(res.data.data);
      })
      .catch((err) => console.log("Employees error", err));
  }, []);

  const onDelete = (id) => {
    setSelectedUsers((prevUsers) => prevUsers.filter((user) => user.id !== id));
  };

  const columns = [
    {
      field: "companyName",
      minWidth: 200,
      headerName: "Company Name",
      flex: 0.4,
    },
    {
      field: "name",
      minWidth: 100,
      headerName: "Name",
      flex: 0.2,
    },
    {
      field: "email",
      minWidth: 100,
      headerName: "Email",
      flex: 0.2,
      renderCell: (params) => {
        const email = params?.value;
        return (
          <Link
            href={`mailto:${email}`}
            target="_blank"
            rel="noopener noreferrer"
            sx={{ color: "#6666ff" }}
          >
            {email}
          </Link>
        );
      },
    },
    {
      field: "mobileNumber",
      headerName: "Mobile Number",
      flex: 0.13,
      minWidth: 100,
      renderCell: (params) => {
        const mobileNumber = params?.value;

        return mobileNumber?.length > 9 ? (
          <Tooltip title={mobileNumber}>
            <Link
              href={`tel:${mobileNumber}`}
              target="_blank"
              rel="noopener noreferrer"
              sx={{ color: "#6666ff" }}
            >
              <span>{mobileNumber}</span>
            </Link>
          </Tooltip>
        ) : (
          <Link
            href={`tel:${mobileNumber}`}
            target="_blank"
            rel="noopener noreferrer"
            sx={{ color: "#6666ff" }}
          >
            {mobileNumber}
          </Link>
        );
      },
    },
  ];

  const cols = [
    {
      field: "companyName",
      minWidth: 100,
      headerName: "Company Name",
      flex: 0.4,
    },
    {
      field: "name",
      minWidth: 100,
      headerName: "Name",
      flex: 0.2,
    },
    {
      field: "email",
      minWidth: 100,
      headerName: "Email",
      flex: 0.2,
      renderCell: (params) => {
        const email = params?.value;
        return (
          <Link
            href={`mailto:${email}`}
            target="_blank"
            rel="noopener noreferrer"
            sx={{ color: "#6666ff" }}
          >
            {email}
          </Link>
        );
      },
    },
    {
      field: "mobileNumber",
      headerName: "Mobile Number",
      flex: 0.13,
      minWidth: 100,
      renderCell: (params) => {
        const mobileNumber = params?.value;

        return mobileNumber?.length > 9 ? (
          <Tooltip title={mobileNumber}>
            <Link
              href={`tel:${mobileNumber}`}
              target="_blank"
              rel="noopener noreferrer"
              sx={{ color: "#6666ff" }}
            >
              <span>{mobileNumber}</span>
            </Link>
          </Tooltip>
        ) : (
          <Link
            href={`tel:${mobileNumber}`}
            target="_blank"
            rel="noopener noreferrer"
            sx={{ color: "#6666ff" }}
          >
            {mobileNumber}
          </Link>
        );
      },
    },
    {
      field: "actions",
      headerName: "Actions",
      flex: 0.09,
      minWidth: 100,
      renderCell: (params) => {
        const onClickDelete = () => {
          const id = params.row.id;
          onDelete(id);
        };
        return (
          <div
            style={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
            }}
          >
            <Tooltip title="Delete">
              <CustomAvatar
                skin="light"
                variant="rounded"
                sx={{ mr: 5, width: 34, height: 34, cursor: "pointer" }}
                onClick={onClickDelete}
              >
                <Icon icon="iconamoon:trash" />
              </CustomAvatar>
            </Tooltip>
          </div>
        );
      },
    },
  ];

  const handleCancel = () => {
    onClose();
    setFinalList(false);
    setSelectedCategory("");
    setCompanyNameFilter("");
    setSocietyKeyword("");
    setKeyword("");
    setIsListingEmpanelled(false);
    setIsMicroSiteEmpanelled(false);
    setIsStrategicPartner(false);
    setSelectedUsers([]);
    reset({
      services: "",
      locations: "",
      assignedTo: "",
      leadStatus: "",
      leadPriority: "",
    });
  };

  const handleError = (error) => {
    console.error("society requisition: All Services:", error);
  };

  const [specifications, setSpecifications] = useState([]);

  useEffect(() => {
    if (!!authConfig) {
      getAllListValuesByListNameId(
        authConfig.allServicesListNameId,
        (data) =>
          setAllServicesList(
            data?.listValues.map((item) => ({
              value: item.id,
              key: item.listValue,
            }))
          ),
        handleError
      );
    }
    if (!!authConfig) {
      getAllListValuesByListNameId(
        authConfig.locationlistNameId,
        (data) =>
          setLocationsList(
            data?.listValues.map((item) => ({
              value: item.id,
              key: item.listValue,
            }))
          ),
        handleError
      );
    }
    if (!!authConfig) {
      getAllListValuesByListNameId(
        authConfig.leadStatusListNamesId,
        (data) =>
          setLeadStatusList(
            data?.listValues.map((item) => ({
              value: item.id,
              key: item.listValue,
            }))
          ),
        handleError
      );
    }
    if (!!authConfig) {
      getAllListValuesByListNameId(
        authConfig.leadPriorityListNamesId,
        (data) =>
          setLeadPriorityList(
            data?.listValues.map((item) => ({
              value: item.id,
              key: item.listValue,
            }))
          ),
        handleError
      );
    }
    if (!!authConfig) {
      getAllListValuesByListNameId(
        authConfig.workLocation,
        (data) =>
          setLocationsData(
            data?.listValues.map((item) => ({
              value: item.id,
              key: item.listValue,
            }))
          ),
        handleError
      );
    }
    if (!!authConfig) {
      getAllListValuesByListNameId(
        authConfig.designationId,
        (data) =>
          setDesignationsData(
            data?.listValues.map((item) => ({
              value: item.id,
              key: item.listValue,
            }))
          ),
        handleError
      );
    }
  }, [authConfig]);

  useEffect(() => {
    if (serviceTypeId) {
      fetchServiceProviders({
        currentPage: page,
        currentPageSize: pageSize,
        companyNameFilter,
        isStrategicPartner,
        isListingEmpanelled,
        isMicroSiteEmpanelled,
        locationFilter,
        assignedToFilter,
        leadStatusFilter,
        leadPriorityFilter,
      });
    }
  }, [
    serviceTypeId,
    page,
    pageSize,
    companyNameFilter, // Updated
    isStrategicPartner,
    isListingEmpanelled,
    isMicroSiteEmpanelled,
    locationFilter, // Updated
    assignedToFilter, // Updated
    leadStatusFilter, // Updated
    leadPriorityFilter, // Updated
  ]);

  const fetchServiceProviders = async ({
    currentPage,
    currentPageSize,
    companyNameFilter, // Updated
    isStrategicPartner,
    isListingEmpanelled,
    isMicroSiteEmpanelled,
    locationFilter, // Updated
    assignedToFilter, // Updated
    leadStatusFilter, // Updated
    leadPriorityFilter, // Updated
  }) => {
    if (!serviceTypeId || user.organisationCategory !== "EMPLOYEE") {
      return;
    }

    const url = getUrl(
      authConfig.getAllServiceProvidersFromOrganization + "/" + serviceTypeId
    );

    const headers = getAuthorizationHeaders({
      contentType: authConfig.orgMimeReqType,
      accept: authConfig.orgMimeResType,
    });

    const data = {
      page: currentPage,
      pageSize: currentPageSize,
      companyNameFilter: companyNameFilter || "", // Updated
      isStrategicPartner,
      isListingEmpanelled,
      isMicroSiteEmpanelled,
      locationFilter: locationFilter?.map((loc) => loc.value) || [], // Updated
      assignedToFilter: assignedToFilter?.map((assign) => assign.value) || [], // Updated
      leadStatusFilter: leadStatusFilter?.map((status) => status.value) || [], // Updated
      leadPriorityFilter:
        leadPriorityFilter?.map((priority) => priority.value) || [], // Updated
    };

    try {
      const response = await axios.post(url, data, { headers });

      if (response?.data?.broadcastRequisitionResponseList) {
        setServiceProvidersData(response.data.broadcastRequisitionResponseList);
        setRowCount(response.data.rowCount || 0);
      } else {
        console.error("Unexpected API response format:", response);
        handleFailure();
      }
    } catch (error) {
      console.error("Error fetching service providers:", error);
    }
  };

  const handleSuccess = () => {
    const message = `
    <div> 
      <h3>Broadcasted SR to Service Provider(s) Successfully.</h3>
    </div>
  `;
    setDialogMessage(message);
    setOpenDialogContent(true);
  };

  const handleSelectAtLeastOne = () => {
    const message = `
    <div> 
      <h3>Please Select At least one Company to BroadCast.</h3>
    </div>
  `;
    setDialogMessage(message);
    setOpenDialogContent(true);
  };

  const handleFailure = () => {
    const message = `
    <div> 
      <h3> Failed to Broadcast SR. Please try again later.</h3>
    </div>
  `;
    setDialogMessage(message);
    setOpenDialogContent(true);
  };

  const [currentToastId, setCurrentToastId] = useState(null);

  const onFormInvalid = (toastError) => {
    if (currentToastId !== null) {
      toast.dismiss(currentToastId);
    }

    Object.keys(toastError).forEach((error) => {
      if (toastError[error].message.length > 0) {
        const toastId = toast.error(toastError[error].message, {
          duration: 4000, // Set the duration you prefer
          onClose: () => {
            setCurrentToastId(null); // Reset currentToastId when the toast closes
          },
        });
        setCurrentToastId(toastId);
      }
    });
  };

  const [isButtonDisabled, setIsButtonDisabled] = useState(false); // State for button disabled

  async function submit() {
    setIsButtonDisabled(true); // Disable the button when clicked

    if (!selectedUsers || !requisitionDataDetails?.id) {
      console.error(
        "Missing data for selectedUsers or requisitionDataDetails."
      );
      setIsButtonDisabled(false); // Re-enable the button if there's an error
      return;
    }

    const type = "PRELIMINARY";

    try {
      const payload = selectedUsers
        .filter((entry) => entry?.organisationId)
        .map((entry) => ({
          serviceRequisitionId: requisitionDataDetails.id,
          organisationId: entry.organisationId,
        }));

      if (payload.length === 0) {
        handleSelectAtLeastOne();
        setIsButtonDisabled(false); // Re-enable the button if no payload
        return;
      }

      const response = await auth.postBroadCastSrToSp(
        payload,
        type,
        handleFailure,
        handleSuccess
      );
    } catch (error) {
      console.error("Broadcast SR to SP failed:", error);
      handleFailure();
    } finally {
      isApiCalling.current = false;
      setIsButtonDisabled(false); // Re-enable the button after operation
    }

    fetchRequisitions();
    handleCancel();
  }

  useEffect(() => {
    setSpecifications(requisitionDataDetails?.specifications?.sections);
  }, [requisitionDataDetails]);

  const handleButtonClick = () => {
    setOpenDialogContent(false);
  };

  const handlePageChange = (direction) => {
    if (direction === page) {
      setPage(page + 1);
    } else {
      setPage(page - 1);
    }
  };

  const handlePageSizeChange = (params) => {
    if (params) {
      setPageSize(params);
      setPage(1);
    }
  };


  const handleLocationChange = (event) => {
    const value = event.target.value;
    setLocationFilter(value);
  };

  const handleSPLeadStatusChange = (event) => {
    const value = event.target.value;
    setLeadStatusFilter(value);
  };

  const handleSPLeadPriorityChange = (event) => {
    const value = event.target.value;
    setLeadPriorityFilter(value);
  };

  const handleAssignedToChange = (event) => {
    const value = event.target.value;
    setAssignedToFilter(value);
  };

  const serviceType = requisitionDataDetails?.serviceTypeId
    ? listValues?.find(
        (item) => item.id === requisitionDataDetails?.serviceTypeId
      )?.name
    : null;

  const priorityName = requisitionDataDetails?.priority
    ? listValues?.find((item) => item.id === requisitionDataDetails?.priority)
        ?.name
    : null;

  useEffect(() => {
    if (selectedRows.length > 0) {
      setFinalList(true);
    } else {
      setFinalList(false);
    }
  }, [selectedRows]);

  const handleFinalListClick = () => {
    const selectedData = serviceProvidersData.filter((row) =>
      selectedRows.includes(row.id)
    );

    setSelectedUsers((prevSelectedUsers) => {
      const newSelectedData = selectedData.filter(
        (row) => !prevSelectedUsers.some((selected) => selected.id === row.id)
      );
      return [...prevSelectedUsers, ...newSelectedData];
    });
  };
  const theme = useTheme();

  return (
    <>
      <Dialog fullScreen open={open} onClose={handleCancel}>
        <DialogTitle
          sx={{
            position: "relative",
            borderBottom: (theme) => `1px solid ${theme.palette.divider}`,
            p: (theme) => `${theme.spacing(1.75, 4)} !important`,
            display: "flex",
            alignItems: "center",
            justifyContent: { xs: "start" },
            fontSize: { xs: 15, md: 20 },
            height: "50px", // height
          }}
          textAlign={"center"}
        >
          <Box
            sx={{
              display: "flex",
              alignItems: "center",
              ml: {
                xs: 3,
                xl: 5,
                lg: 5,
                sm: 5,
                md: 5,
              },

              width: { xs: "70%" },
              height: { xs: "60px" },
            }}
          >
            Assign/Broadcast SR to SP (RFQ Preliminary)
          </Box>
          <Box
            sx={{
              position: "absolute",
              top: "9px",
              right: "10px",
              mr: {
                xs: 6,
                sm: 6,
                md: 6,
                lg: 6,
                xl: 10,
              },
            }}
          >
            <IconButton
              size="small"
              onClick={handleCancel}
              sx={{
                // p: "0.438rem",
                borderRadius: 1,
                color: "common.white",
                backgroundColor: "primary.main",
                "&:hover": {
                  backgroundColor: (theme) =>
                    `rgba(${theme.palette.customColors.main}, 0.16)`,
                },
              }}
            >
              <Icon icon="tabler:x" fontSize="1rem" />
            </IconButton>
          </Box>
        </DialogTitle>
        <DialogContent
          style={{
            overflowY: "scroll",
          }}
          sx={{
            position: "relative",
            pt: (theme) => `${theme.spacing(8)} !important`,
            pb: (theme) => `${theme.spacing(5)} !important`,
            px: (theme) => [`${theme.spacing(8)} !important`],
          }}
        >
          <>
            <Card>
              <AccordionBasic
                id={"panel-header-2"}
                ariaControls={"panel-content-2"}
                heading={"Requisition Details"}
                body={
                  <>
                    <TableContainer
                      sx={{ padding: "4px 6px", paddingTop: "0px !important" }}
                      className="tableBody"
                    >
                      <Table>
                        <TableBody
                          sx={{
                            "& .MuiTableCell-root": {
                              p: `${theme.spacing(1.35, 1.125)} !important`,
                            },
                          }}
                        >
                          <TableRow>
                            <MUITableCell sx={tablecellLabelStyle}>
                              <Typography style={field}>
                                Service Requisition No:
                              </Typography>
                            </MUITableCell>
                            <MUITableCell sx={tablecellValueStyle}>
                              <Typography
                                className="data-field"
                                style={fieldValueStyle}
                              >
                                {requisitionDataDetails?.systemCode}
                              </Typography>
                            </MUITableCell>
                          </TableRow>
                          <TableRow>
                            <MUITableCell sx={tablecellLabelStyle}>
                              <Typography style={field}>
                                Society Name:
                              </Typography>
                            </MUITableCell>
                            <MUITableCell sx={tablecellValueStyle}>
                              <Typography
                                className="data-field"
                                style={fieldValueStyle}
                              >
                                {
                                  requisitionDataDetails?.initiatingEntity
                                    ?.orgName
                                }
                              </Typography>
                            </MUITableCell>
                          </TableRow>
                          <TableRow>
                            <MUITableCell sx={tablecellLabelStyle}>
                              <Typography style={field}>
                                Service Type:
                              </Typography>
                            </MUITableCell>
                            <MUITableCell sx={tablecellValueStyle}>
                              <Typography
                                className="data-field"
                                style={fieldValueStyle}
                              >
                                {serviceType}
                              </Typography>
                            </MUITableCell>
                          </TableRow>

                          {requisitionDataDetails?.specifications
                            ?.anyOtherServiceProvided && (
                            <TableRow>
                              <MUITableCell
                                sx={{
                                  textAlign: "right",
                                  width: "50%",
                                  paddingRight: theme.spacing(4),
                                }}
                              >
                                <Typography style={field}>
                                  Other Services:
                                </Typography>
                              </MUITableCell>
                              <MUITableCell
                                sx={{
                                  textAlign: "left",
                                  width: "50%",
                                  paddingLeft: theme.spacing(2),
                                }}
                              >
                                <Typography
                                  className="data-field"
                                  style={fieldValueStyle}
                                >
                                  {
                                    requisitionDataDetails?.specifications
                                      ?.anyOtherServiceProvided
                                  }
                                </Typography>
                              </MUITableCell>
                            </TableRow>
                          )}

                          <TableRow>
                            <MUITableCell sx={tablecellLabelStyle}>
                              <Typography style={field}>Priority:</Typography>
                            </MUITableCell>
                            <MUITableCell sx={tablecellValueStyle}>
                              <Typography
                                className="data-field"
                                style={fieldValueStyle}
                              >
                                {priorityName}
                              </Typography>
                            </MUITableCell>
                          </TableRow>

                          <TableRow>
                            <MUITableCell sx={tablecellLabelStyle}>
                              <Typography style={field}>Budget:</Typography>
                            </MUITableCell>
                            <MUITableCell sx={tablecellValueStyle}>
                              <Typography
                                className="data-field"
                                style={fieldValueStyle}
                              >
                                {requisitionDataDetails?.budget?.rangeFrom}{" "}
                                {requisitionDataDetails?.budget?.rangeTo && "-"}{" "}
                                {requisitionDataDetails?.budget?.rangeTo}{" "}
                                {
                                  listValues?.find(
                                    (item) =>
                                      item.id ===
                                      requisitionDataDetails?.budget?.units
                                  )?.name
                                }{" "}
                                {
                                  listValues?.find(
                                    (item) =>
                                      item.id ===
                                      requisitionDataDetails?.budget?.condition
                                  )?.name
                                }
                              </Typography>
                            </MUITableCell>
                          </TableRow>

                          <TableRow>
                            <MUITableCell sx={tablecellLabelStyle}>
                              <Typography style={field}>
                                Requirement Dead Line:
                              </Typography>
                            </MUITableCell>
                            <MUITableCell sx={tablecellValueStyle}>
                              <Typography
                                className="data-field"
                                style={fieldValueStyle}
                              >
                                {requisitionDataDetails?.requirementDeadLine}
                              </Typography>
                            </MUITableCell>
                          </TableRow>

                          <TableRow>
                            <MUITableCell sx={tablecellLabelStyle}>
                              <Typography style={field}>
                                Society Remarks:
                              </Typography>
                            </MUITableCell>
                            <MUITableCell sx={tablecellValueStyle}>
                              <Typography
                                className="data-field"
                                style={fieldValueStyle}
                              >
                                {requisitionDataDetails?.remarks}
                              </Typography>
                            </MUITableCell>
                          </TableRow>

                          <TableRow>
                            <MUITableCell sx={tablecellLabelStyle}>
                              <Typography style={fieldValueStyle}>
                                Specifications
                              </Typography>
                            </MUITableCell>
                            <MUITableCell sx={tablecellValueStyle}>
                              <Typography
                                className="data-field"
                                style={fieldValueStyle}
                              ></Typography>
                            </MUITableCell>
                          </TableRow>

                          {specifications
                            ?.slice()
                            ?.sort((a, b) => a.listSequence - b.listSequence)
                            ?.map((section) => (
                              <Fragment key={section.id}>
                                {/* Display section ID */}
                                <TableRow>
                                  <MUITableCell sx={tablecellLabelStyle}>
                                    <Typography
                                      variant="body2"
                                      fontWeight={"bold"}
                                      sx={{ mt: 0, ml: 2 }}
                                    >
                                      {
                                        listValues?.find(
                                          (item) => item.id === section.id
                                        )?.name
                                      }
                                    </Typography>
                                  </MUITableCell>
                                  <MUITableCell sx={tablecellValueStyle}>
                                    <Typography
                                      className="data-field"
                                      style={fieldValueStyle}
                                    ></Typography>
                                  </MUITableCell>
                                </TableRow>

                                {section.fields?.map((field) => (
                                  <TableRow key={field.id}>
                                    <MUITableCell sx={tablecellLabelStyle}>
                                      <Typography style={field}>
                                        {
                                          listNames?.find(
                                            (item) => item.id === field.labelId
                                          )?.name
                                        }
                                        {":"}
                                      </Typography>
                                    </MUITableCell>
                                    <MUITableCell sx={tablecellValueStyle}>
                                      <Typography
                                        className="data-field"
                                        style={fieldValueStyle}
                                      >
                                        {
                                          field.componentId ===
                                            authConfig.textFieldComponentId ||
                                          field.componentId ===
                                            authConfig.textAreaComponentId ||
                                          field.componentId ===
                                            authConfig.numberTextFieldComponentId
                                            ? field.providedTextValue // Show providedTextValue if the componentId matches
                                            : field.selectedDropdownValues
                                                ?.map((item) => item.key)
                                                .join(", ") // Show the keys as a comma-separated string
                                        }
                                      </Typography>
                                    </MUITableCell>
                                  </TableRow>
                                ))}
                              </Fragment>
                            ))}
                        </TableBody>
                      </Table>
                    </TableContainer>
                  </>
                }
                expanded={false}
              />
            </Card>
          </>
          {/* Filter Criteria */}
          <Card>
            <AccordionBasic
              id={"panel-header-2"}
              ariaControls={"panel-content-2"}
              heading={"Filter Criteria"}
              body={
                <>
                  <Grid container spacing={4} sx={{ mt: 2 }}>
                    {/* SP Filter Criteria Fields */}
                    <Grid item xs={12} sm={3} sx={{ mt: 2 }}>
                      <FormControl fullWidth>
                        <Controller
                          name="services"
                          control={control}
                          render={({ field }) => (
                            <Grid container alignItems="center" spacing={2}>
                              <Grid item xs={4} sm={4}>
                                <Typography
                                  variant="body1"
                                  //style={{ fontSize: "14px" }}
                                >
                                  Service Type:
                                </Typography>
                              </Grid>
                              <Grid item xs={8} sm={4}>
                                <Typography
                                  variant="body1"
                                  style={{ fontWeight: 500 }}
                                >
                                  {listValues?.find(
                                    (item) => item.id === serviceTypeId
                                  )?.name || null}
                                </Typography>
                              </Grid>
                            </Grid>
                          )}
                        />
                      </FormControl>
                    </Grid>

                    <Grid item xs={12} sm={3}>
                      <FormControl fullWidth>
                        <Controller
                          name="mainSearch"
                          control={control}
                          render={({ field: { onChange } }) => (
                            <TextField
                              size="small"
                              id="mainSearch"
                              placeholder="Search by company name"
                              value={keyword}
                              onChange={(e) => {
                                onChange(e.target.value);
                                setKeyword(e.target.value);
                                setCompanyNameFilter(e.target.value);
                              }}
                              onKeyDown={(e) => {
                                if (e.key === "Enter") {
                                  setCompanyNameFilter(keyword);
                                }
                              }}
                              sx={{
                                "& .MuiInputBase-root": {
                                  fontSize: "0.9rem",
                                  borderRadius: "5px",
                                  backgroundColor: "white",
                                },
                              }}
                              InputProps={{
                                endAdornment: (
                                  <InputAdornment position="start">
                                    <SearchIcon
                                      sx={{
                                        cursor: "pointer",
                                      }}
                                      onClick={() => {
                                        setCompanyNameFilter(keyword);
                                      }}
                                    />
                                  </InputAdornment>
                                ),
                              }}
                            />
                          )}
                        />
                      </FormControl>
                    </Grid>

                    <Grid item xs={12} sm={3}>
                      <FormControl fullWidth error={Boolean(errors.location)}>
                        <Controller
                          name="location"
                          control={control}
                          rules={{ required: false }}
                          render={({ field }) => (
                            <MultiSelectAutoComplete
                              id="location"
                              size="small"
                              label=" Location"
                              nameArray={locationsList}
                              value={field.value || []}
                              onChange={(event) => {
                                field.onChange(event.target.value);
                                handleLocationChange(event);
                              }}
                              error={Boolean(errors.location)}
                            />
                          )}
                        />
                      </FormControl>
                    </Grid>

                    <Grid item xs={12} sm={3}>
                      <FormControl fullWidth>
                        <Controller
                          name="assignedTo"
                          control={control}
                          rules={{ required: false }}
                          render={({ field }) => (
                            <MultiSelectAutoComplete
                              size="small"
                              id="assignedTo"
                              label="Assigned To"
                              nameArray={employeesData.map((emp) => ({
                                value: emp.id,
                                key: emp.name,
                              }))}
                              value={field.value || []}
                              onChange={(event) => {
                                field.onChange(event.target.value);
                                handleAssignedToChange(event);
                              }}
                              error={Boolean(errors.leadPriority)}
                            />
                          )}
                        />
                      </FormControl>
                    </Grid>

                    <Grid item xs={12} sm={3}>
                      <FormControl
                        fullWidth
                        error={Boolean(errors.empanelledOptions)}
                      >
                        <Controller
                          name="Subscribed To"
                          control={control}
                          rules={{ required: false }}
                          render={({ field }) => (
                            <MultiSelectAutoComplete
                              id="empanelled-options"
                              size="small"
                              label="Subscribed To"
                              nameArray={[
                                {
                                  key: "Listing",
                                  value: "isListingEmpanelled",
                                },
                                {
                                  key: "Microsite",
                                  value: "isMicroSiteEmpanelled",
                                },
                                {
                                  key: "Houzer Strategic Partner",
                                  value: "isStrategicPartner",
                                },
                              ]}
                              value={field.value || []}
                              onChange={(event) => {
                                field.onChange(event.target.value);
                              }}
                              error={Boolean(errors.empanelledOptions)}
                            />
                          )}
                        />
                      </FormControl>
                    </Grid>

                    <Grid item xs={12} sm={3}>
                      <FormControl fullWidth>
                        <Controller
                          name="leadStatus"
                          control={control}
                          rules={{ required: false }}
                          render={({ field }) => (
                            <MultiSelectAutoComplete
                              size="small"
                              id="leadStatus"
                              label="Lead Status"
                              nameArray={leadStatusList}
                              value={field.value || []}
                              onChange={(event) => {
                                field.onChange(event.target.value);
                                handleSPLeadStatusChange(event);
                              }}
                              error={Boolean(errors.leadStatus)}
                            />
                          )}
                        />
                      </FormControl>
                    </Grid>

                    <Grid item xs={12} sm={3}>
                      <FormControl fullWidth>
                        <Controller
                          name="leadPriority"
                          control={control}
                          rules={{ required: false }}
                          render={({ field }) => (
                            <MultiSelectAutoComplete
                              size="small"
                              id="leadPriority"
                              label="Lead Priority"
                              nameArray={leadPriorityList}
                              value={field.value || []}
                              onChange={(event) => {
                                field.onChange(event.target.value);
                                handleSPLeadPriorityChange(event);
                              }}
                              error={Boolean(errors.leadPriority)}
                            />
                          )}
                        />
                      </FormControl>
                    </Grid>
                  </Grid>
                </>
              }
            />
          </Card>

          {/* List of SPs */}
          <Card>
            <AccordionBasic
              id={"panel-header-2"}
              ariaControls={"panel-content-2"}
              heading={"List of Service Providers "}
              body={
                <>
                  <Grid container spacing={4} sx={{ mt: 2 }}>
                    <div
                      style={{ height: 380, width: "100%", marginTop: "10px" }}
                    >
                      <DataGrid
                        rows={serviceProvidersData}
                        columns={columns}
                        checkboxSelection
                        pagination
                        pageSize={pageSize}
                        page={page - 1}
                        rowsPerPageOptions={rowsPerPageOptions}
                        rowCount={rowCount}
                        paginationMode="server"
                        onPageChange={handlePageChange}
                        onPageSizeChange={handlePageSizeChange}
                        //onSelectionModelChange={handleSelection}
                        rowHeight={38}
                        headerHeight={38}
                        onSelectionModelChange={(newSelection) => {
                          setSelectedRows(newSelection);
                        }}
                      />
                    </div>
                  </Grid>
                </>
              }
            />
          </Card>

          {/* Final List of SPs */}
          <Card>
            <div
              style={{
                display: "flex",
                alignItems: "center",
                justifyContent: "space-between",
              }}
            >
              <CardHeader
                title={
                  <span style={{ fontSize: "14px", marginLeft: -7 }}>
                    Final List of SPs
                  </span>
                } // Adjust font size here
              />

              <div
                style={{
                  display: "flex",
                  justifyContent: "flex-end",
                  flexGrow: 1,
                }}
              >
                {selectedRowIds.length > 1 && (
                  <Tooltip title="Bulk Delete">
                    <IconButton onClick={handleDelete}>
                      <DeleteIcon color="error" sx={{ mr: 2 }} />
                    </IconButton>
                  </Tooltip>
                )}

                <Tooltip
                  title={
                    !finalList
                      ? "To enable, select at least one SP from the above table"
                      : ""
                  }
                >
                  <span>
                    <Button
                      disabled={!finalList}
                      variant="contained"
                      justifyContent="center"
                      onClick={handleFinalListClick}
                      sx={{ mr: 2 }}
                    >
                      Add to Final List
                    </Button>
                  </span>
                </Tooltip>
              </div>
            </div>
            <div style={{ height: 380, width: "100%" }}>
              <DataGrid
                rows={selectedUsers}
                columns={cols}
                checkboxSelection
                onSelectionModelChange={(newSelection) => {
                  setSelectedRowIds(newSelection); // Set the selected rows by their IDs
                }}
                selectionModel={selectedRowIds} // Keep track of selected rows
                rowsPerPageOptions={rowsPerPageOptions}
                rowCount={selectedUsers?.length}
                rowHeight={38}
                headerHeight={38}
              />
            </div>
          </Card>
        </DialogContent>
        <DialogActions
          sx={{
            justifyContent: "end",
            borderTop: (theme) => `1px solid ${theme.palette.divider}`,
            p: (theme) => `${theme.spacing(1.75, 4)} !important`,
            height: "50px", // height
          }}
        >
          <Button
            display="flex"
            variant="outlined"
            color="primary"
            onClick={handleCancel}
            sx={{
              padding: {
                xs: "5px 4px",
                xl: "8px 14px",
                lg: "8px 10px",
                md: "8px 10px",
                sm: "8px 10px",
              },
            }}
          >
            Cancel
          </Button>
          <Button
            display="flex"
            variant="contained"
            color="primary"
            onClick={handleSubmit(submit, onFormInvalid)}
            disabled={
              isButtonDisabled ||
              !selectedUsers ||
              selectedUsers.length === 0 ||
              !requisitionDataDetails?.id ||
              isApiCalling.current
            }
          >
            Broadcast RFQ Preliminary
          </Button>
        </DialogActions>
        <Toaster position="top-right" />
      </Dialog>

      <Dialog
        open={openDialogContent}
        onClose={handleButtonClick}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
        PaperProps={{
          sx: {
            p: (theme) => `${theme.spacing(2.5)} !important`,
            backgroundColor: "white",
          },
        }}
      >
        <Box
          sx={{
            width: "100%",
            borderRadius: 1,
            textAlign: "center",
            border: (theme) => `1px solid ${theme.palette.divider}`,
            borderColor: "primary.main",
          }}
        >
          <DialogContent>
            <DialogContentText
              id="alert-dialog-description"
              color="primary.main"
            >
              <div dangerouslySetInnerHTML={{ __html: dialogMessage }} />
            </DialogContentText>
          </DialogContent>
          <DialogActions>
            <Button
              variant="contained"
              onClick={handleButtonClick}
              sx={{ margin: "auto", width: 100 }}
            >
              Okay
            </Button>
          </DialogActions>
        </Box>
      </Dialog>
    </>
  );
};

export default RFQPreliminary;
