
import {
    <PERSON><PERSON>,
    <PERSON>,
    <PERSON><PERSON>,
    Card,
    Dialog,
    DialogActions,
    DialogContent,
    DialogContentText,
    DialogTitle,
    Divider,
    FormControl,
    FormHelperText,
    Grid,
    IconButton,
    Snackbar,
    Table,
    TableBody,
    TableCell,
    TableContainer,
    TableHead,
    TableRow,
    TextField,
    Typography,
} from "@mui/material";
import axios from "axios";
import { useContext, useEffect, useRef, useState } from "react";
import { Controller, useForm } from "react-hook-form";
import AutoCompleteWithoutLabel from "src/@core/components/custom-components/AutoCompleteWithoutLabel";
import MultiSelectAutoCompleteCustom from "src/@core/components/custom-components/MultiSelectAutoCompleteCustom";
import SelectAutoComplete from "src/@core/components/custom-components/SelectAutoComplete";
import SelectAutoCompleteListName from "src/@core/components/custom-components/SelectAutoCompleteListName";
import Icon from "src/@core/components/icon";
import authConfig from "src/configs/auth";
import { AuthContext } from "src/context/AuthContext";
import { getAuthorizationHeaders, getUrl } from "src/helpers/utils";
import { useAuth } from "src/hooks/useAuth";
import { useRBAC } from "src/pages/permission/RBACContext";
import PreviewDialog from "./PreviewDialog";
import SectionsDialog from "./SectionsDialog";
  
  const SpecificationCreate = ({
    open,
    onClose,
    data,
    fetchTemplates,
    page,
    pageSize,
    selectedFilters,
    templateName,
    setTemplateName,
    service,
    setService,
  }) => {
    const { can } = useRBAC();
  
    const { listValues, listNames, setListNames, getAllListValuesByListNameId,setTemplateId,templateId } =
      useContext(AuthContext);
  
    const auth = useAuth();
  
    const {
      control,
      setValue,
      handleSubmit,
      formState: { errors },
    } = useForm();
  
    const [allServicesList, setAllServicesList] = useState([]);
    const [allUiComponentsId, setAllUiComponentsId] = useState([]);
  
    const [sections, setSections] = useState([]);
    const [sectionOpen, setSectionOpen] = useState(false);
    const [allSectionsId, setAllSectionsId] = useState([]);
    const [sectionsUpdate, setSectionsUpdate] = useState(false);
    const [saveSections, setSaveSections] = useState(false);
  
    const [labelsUpdate, setLabelsUpdate] = useState(false);
  
    const [labelId, setLabelId] = useState();
  
    const [openPreview, setOpenPreview] = useState(false);
  
    const handleSectionCancel = () => {
      setSectionOpen(false);
    };
  
    const [dialogMessage, setDialogMessage] = useState("");
    const [openDialogContent, setOpenDialogContent] = useState(false);
  
    const [sampleData, setSampleData] = useState([]);
  
    const transformSections = (data) => {
      return data?.map((section) => ({
        sectionId: section?.id,
        subSections: section?.subSections?.map((subSection) => subSection?.id),
      }));
    };
  
    useEffect(() => {
      setService(data?.serviceTypeId);
      setTemplateName(data?.name);
      setValue("description", data?.description);
      setSampleData(data?.sections);
      const transformedSectionData = transformSections(data?.sections);
      setSections(transformedSectionData);
    }, [data]);
  
    useEffect(() => {
      // Ensure sections is an array or fallback to an empty array
      const transformed = Array.isArray(sections)
        ? sections?.map((section, sectionIndex) => ({
            id: section.sectionId,
            displayOrder: sectionIndex + 1,
            fields: [
              {
                id: crypto.randomUUID(),
                labelId: "",
                componentId: "",
                displayOrder: 1,
                values: [],
              },
            ],
            subSections: section.subSections?.map(
              (subsectionId, subsectionIndex) => ({
                id: subsectionId,
                displayOrder: subsectionIndex + 1,
                fields: [
                  {
                    id: crypto.randomUUID(),
                    labelId: "",
                    componentId: "",
                    displayOrder: 1,
                    values: [],
                  },
                ],
              })
            ),
          }))
        : []; // Fallback to empty array if sections is not an array
  
      if (!data || Object.keys(data).length === 0) {
        setSampleData(transformed);
      } else {
        setSampleData((prevSampleData) => {
          const existingData = Array.isArray(prevSampleData)
            ? prevSampleData
            : [];
  

          const updatedData = transformed?.map((newSection) => {
            const existingSection = existingData.find(
              (existingSection) => existingSection.id === newSection.id
            );
  
            if (existingSection) {
              // Synchronize subSections
              const updatedSubSections = existingSection.subSections.filter(
                (existingSub) =>
                  newSection.subSections.some(
                    (newSub) => newSub.id === existingSub.id
                  )
              );
  
              // Add new subSections
              newSection.subSections.forEach((newSub) => {
                if (
                  !updatedSubSections.some(
                    (updatedSub) => updatedSub.id === newSub.id
                  )
                ) {
                  updatedSubSections.push(newSub);
                }
              });
  
              return {
                ...existingSection,
                subSections: updatedSubSections,
              };
            }
  
            // If the section is new, include it directly
            return newSection;
          });
  
          return updatedData;
        });
      }
    }, [sections]);
  
    const [listNamesOptions, setListNamesOptions] = useState([]);
    const [listOfListNames, setListOfListNames] = useState([]);
  
    useEffect(() => {
      axios({
        method: "get",
        url: getUrl(authConfig.selectDropdownNew) + "?selectionType=LIST_NAME",
        headers: getAuthorizationHeaders(),
      })
        .then((res) => {
          setListOfListNames(res.data.data);
        })
        .catch((err) => console.log("Locations error", err));
    }, [labelsUpdate]);
  
    // Convert list names to options
    useEffect(() => {
      if (listOfListNames) {
        const data = listOfListNames?.map((entry) => ({
          value: entry.id,
          key: entry.name,
        }));
        setListNamesOptions(data);
      }
    }, [listOfListNames]);
  
    useEffect(() => {
      if (authConfig) {
        getAllListValuesByListNameId(
          authConfig.allSectionsId,
          (data) =>
            setAllSectionsId(
              data?.listValues?.map((section) => ({
                value: section.id,
                key: section.listValue,
              }))
            ),
          (error) => console.error("Error fetching sections:", error)
        );    
      }
    }, [authConfig, sectionsUpdate]);
  
    const [dropdownOptions, setDropdownOptions] = useState({});
  
    const handleDropdownOpen = async (fieldId) => {
      if (fieldId) {
        getAllListValuesByListNameId(
          fieldId,
          (data) => {
            // Success callback: Update dropdownOptions with fetched data
            setDropdownOptions((prev) => ({
              ...prev,
              [fieldId]: data?.listValues?.map((section) => ({
                value: section.id,
                key: section.listValue,
              })),
            }));
          },
          (error) => console.error("Error fetching sections:", error)
        );
      }
    };
  
    const [selectedRow, setSelectedRow] = useState(null);
  
    useEffect(() => {
      if (authConfig) {
        getAllListValuesByListNameId(
          authConfig.allServicesListNameId,
          (data) =>
            setAllServicesList(
              data?.listValues?.map((service) => ({
                value: service.id,
                key: service.listValue,
              }))
            ),
          (error) => console.error("Error fetching services:", error)
        );
  
        getAllListValuesByListNameId(
          authConfig.uiComponentsId,
          (data) =>
            setAllUiComponentsId(
              data?.listValues?.map((item) => ({
                value: item.id,
                key: item.listValue,
              }))
            ),
          (error) => console.error("Error fetching UI components:", error)
        );
      }
    }, [authConfig]);
  
    useEffect(() => {
      const serviceName =
        listValues?.find((item) => item.id === service)?.name || "Unknown";
  
      const fetchSectionsByServiceName = (serviceName) => {
        // Find the object that matches the serviceName
        const matchedService = data.find(
          (item) => item.serviceType === serviceName
        );
  
        // If a match is found, update the state with its sections array
        if (matchedService) {
          setSelectedRow(matchedService);
        } else {
          setSelectedRow([]); // Clear sections if no match is found
        }
      };
  
      // fetchSectionsByServiceName(serviceName);
    }, [service]);
  
    const handleClose = () => {
      setSections([]);
      setSampleData([]);
      setService("");
      setLabelId("");
      onClose();
    };
  
    const handlePreview = () => {
      setOpenPreview(true);
    };
  
    const handleClosePreview = () => {
      setOpenPreview(false);
    };
  
    const [fieldValues, setFieldValues] = useState({});
  
    const [showToast, setShowToast] = useState(false); // State for toast visibility
    const [toastMessage, setToastMessage] = useState("");
  
    const handleInputChange = (
      sectionIndex,
      subsectionId,
      fieldId,
      field,
      value
    ) => {
      const newSampleData = [...sampleData];
  
      // Find the subsection within the selected section
      const subsectionIndex = newSampleData[sectionIndex].subSections.findIndex(
        (subsection) => subsection.id === subsectionId
      );
  
      // If subsection is found
      if (subsectionIndex !== -1) {
        // Find the field within the selected subsection
        const fieldIndex = newSampleData[sectionIndex].subSections[
          subsectionIndex
        ].fields.findIndex((item) => item.id === fieldId);
  
        // If field is found, update the specified field's value
        if (fieldIndex !== -1) {
          newSampleData[sectionIndex].subSections[subsectionIndex].fields[
            fieldIndex
          ][field] = value;
          setSampleData(newSampleData); // Update the state with the modified data
        }
      }
    };
  
    const fetchFieldValues = (listNameId, fieldId) => {
      // Call `getAllListValuesByListNameId` with the provided callbacks
      getAllListValuesByListNameId(
        listNameId,
        (data) => {
          const newValues = data?.listValues?.map((section) => ({
            value: section.id,
            key: section.listValue,
          }));
          setFieldValues((prev) => ({
            ...prev,
            [fieldId]: newValues, // Update the values for the specific field
          }));
        },
        (error) =>
          console.error(`Error fetching values for field ${fieldId}:`, error)
      );
    };
  
    const handleFieldChange = (sectionIndex, fieldId, field, value) => {
      const newSampleData = [...sampleData];
  
      // Find the field within the fields array of the selected section
      const fieldIndex = newSampleData[sectionIndex].fields.findIndex(
        (item) => item.id === fieldId
      );
  
      // If field is found, update the specified field's value
      if (fieldIndex !== -1) {
        newSampleData[sectionIndex].fields[fieldIndex][field] = value;
        setSampleData(newSampleData); // Update the state with the modified data
      }
  
      if (field === "labelId") {
        fetchFieldValues(value, fieldId);
      }
    };
  
    const handleDeleteFieldRow = (sectionIndex, fieldId) => {
      const newSampleData = [...sampleData]; // Create a copy of the current data
  
      // Find the index of the field with the matching fieldId
      const fieldIndex = newSampleData[sectionIndex].fields.findIndex(
        (field) => field.id === fieldId
      );
  
      // Remove the field if it exists
      if (fieldIndex !== -1) {
        newSampleData[sectionIndex].fields.splice(fieldIndex, 1);
  
        // Update displayOrder for the remaining fields
        for (
          let i = fieldIndex;
          i < newSampleData[sectionIndex].fields.length;
          i++
        ) {
          newSampleData[sectionIndex].fields[i].displayOrder = i + 1; // Reassign displayOrder in correct order
        }
  
        // Update the state with the modified data
        setSampleData(newSampleData);
      }
    };
  
    const handleAddFieldRow = (sectionIndex, fieldId) => {
      const newSampleData = [...sampleData]; // Create a copy of the current data
  
      // Find the index of the field with the matching fieldId
      const fieldIndex = newSampleData[sectionIndex].fields.findIndex(
        (field) => field.id === fieldId
      );
  
      if (fieldIndex !== -1) {
        // Get the last sequence number to maintain proper displayOrder
        const lastSequence =
          newSampleData[sectionIndex].fields[fieldIndex].displayOrder;
  
        // Insert the new field right after the found index
        newSampleData[sectionIndex].fields.splice(fieldIndex + 1, 0, {
          id: crypto.randomUUID(), // Generate a new UUID
          labelId: "",
          componentId: "",
          displayOrder: lastSequence + 1, // New field's sequence
          values: [],
        });
  
        // Update displayOrder for all fields after the inserted field
        for (
          let i = fieldIndex + 2;
          i < newSampleData[sectionIndex].fields.length;
          i++
        ) {
          newSampleData[sectionIndex].fields[i].displayOrder = i + 1;
        }
  
        // Update the state with the modified data
        setSampleData(newSampleData);
      }
    };
  
    const handleDeleteRow = (sectionIndex, subsectionId, fieldId) => {
      const newSampleData = [...sampleData]; // Create a copy of the current data
  
      // Find the subsection by subsectionId
      const subsectionIndex = newSampleData[sectionIndex].subSections.findIndex(
        (subsection) => subsection.id === subsectionId
      );
  
      if (subsectionIndex !== -1) {
        // Find the index of the field with the matching fieldId
        const fieldIndex = newSampleData[sectionIndex].subSections[
          subsectionIndex
        ].fields.findIndex((field) => field.id === fieldId);
  
        // Remove the field if it exists
        if (fieldIndex !== -1) {
          newSampleData[sectionIndex].subSections[subsectionIndex].fields.splice(
            fieldIndex,
            1
          );
  
          // Update displayOrder for the remaining fields
          for (
            let i = fieldIndex;
            i <
            newSampleData[sectionIndex].subSections[subsectionIndex].fields
              .length;
            i++
          ) {
            newSampleData[sectionIndex].subSections[subsectionIndex].fields[
              i
            ].displayOrder = i + 1; // Reassign displayOrder in correct order
          }
        }
  
        // Update the state with the modified data
        setSampleData(newSampleData);
      }
    };
  
    const handleAddRow = (sectionIndex, subsectionId, fieldId) => {
      const newSampleData = [...sampleData];
  
      // Find the subsection by subsectionId
      const subsectionIndex = newSampleData[sectionIndex].subSections.findIndex(
        (subsection) => subsection.id === subsectionId
      );
  
      if (subsectionIndex !== -1) {
        // Find the index of the field with the matching fieldId
        const fieldIndex = newSampleData[sectionIndex].subSections[
          subsectionIndex
        ].fields.findIndex((field) => field.id === fieldId);
  
        if (fieldIndex !== -1) {
          // Get the last sequence number to maintain proper displayOrder
          const lastSequence =
            newSampleData[sectionIndex].subSections[subsectionIndex].fields[
              fieldIndex
            ].displayOrder;
  
          // Insert the new field right after the found index
          newSampleData[sectionIndex].subSections[subsectionIndex].fields.splice(
            fieldIndex + 1,
            0,
            {
              id: crypto.randomUUID(), // Generate a new UUID
              labelId: "",
              componentId: "",
              displayOrder: lastSequence + 1, // New field's sequence
              values: [],
            }
          );
  
          // Update displayOrder for all fields after the inserted field
          for (
            let i = fieldIndex + 2;
            i <
            newSampleData[sectionIndex].subSections[subsectionIndex].fields
              .length;
            i++
          ) {
            newSampleData[sectionIndex].subSections[subsectionIndex].fields[
              i
            ].displayOrder = i + 1;
          }
  
          // Update the state with the modified data
          setSampleData(newSampleData);
        }
      }
    };
  
    const transformData = (sampleData) => {
      return sampleData?.map((section) => ({
        id: section.id,
        displayOrder: section.displayOrder,
        fields: section.fields
          .filter((field) => field.labelId) // Filter out fields with empty labelId
          ?.map((field) => ({
            id:field.id,
            values: field.values,
            labelId: field.labelId,
            componentId: field.componentId,
            displayOrder: field.displayOrder,
          })),
        subSections: section.subSections?.map((subsection) => ({
          id: subsection.id,
          displayOrder: subsection.displayOrder,
          fields: subsection.fields
            .filter((subField) => subField.labelId) // Filter out subFields with empty labelId
            ?.map((subField) => ({
              id:subField.id,
              values: subField.values,
              labelId: subField.labelId,
              componentId: subField.componentId,
              displayOrder: subField.displayOrder,
            })),
        })),
      }));
    };
  
    const cleanSubSections = (data) => {
      if (!Array.isArray(data)) {
        console.error("Invalid data: expected an array");
        return [];
      }
  

      return data?.map((section) => {
        // Clean subSections by removing those with empty fields
        const cleanedSubSections =
          section.subSections?.filter(
            (subSection) =>
              Array.isArray(subSection.fields) && subSection.fields.length > 0
          ) || [];
  
        return {
          ...section,
          subSections: cleanedSubSections, // Update subSections with cleaned array
        };
      });
    };
  
    const cleanSections = (data) => {
      if (!Array.isArray(data)) {
        console.error("Invalid data: expected an array");
        return [];
      }
  
      return data.filter((section) => {
        const hasFields =
          Array.isArray(section.fields) && section.fields.length > 0;
        const hasSubSections =
          Array.isArray(section.subSections) && section.subSections.length > 0;
  
        return hasFields || hasSubSections; // Keep the section if it has either fields or subSections
      });
    };
  
    const cleanSampleData = (data) => {
      // Step 1: Clean subSections
      const cleanedWithSubSections = cleanSubSections(data);
  
      // Step 2: Clean sections (remove sections where both fields and subSections are empty)
      const cleanedSections = cleanSections(cleanedWithSubSections);
  
      return cleanedSections;
    };
  
    const handleSuccess = () => {
      let message;
      {
        !data || Object.keys(data).length === 0
          ? (message = `
          <div> 
            <h3>Template created Successfully.</h3>
          </div>
        `)
          : (message = `
          <div> 
            <h3>Template updated Successfully.</h3>
          </div>
        `);
      }
  
      setDialogMessage(message);
      setOpenDialogContent(true);
    };
  
    const handleFailure = (err) => {
      let message;
    const data = err.response?.data;

    if (!data || Object.keys(data).length === 0) {
        message = `
        <div> 
          <h3>Failed to create template. Please try again later.</h3>
        </div>
      `;
    } else if (data.message?.includes("already exists")) {
        message = `
        <div> 
          <h3>${data.message}</h3>
        </div>
      `;
    } else {
        message = `
        <div> 
          <h3>Failed to update template. Please try again later.</h3>
        </div>
      `;
    }
      setDialogMessage(message);
      setOpenDialogContent(true);
    };
  
    const handleButtonClick = () => {
      setOpenDialogContent(false);
    };

  
    const isApiCalling = useRef(false);
    async function submit(data) {
      if (isApiCalling.current) {
        // API call is already in progress, return early
        return;
      }
      const transformedData = transformData(sampleData);
      const updatedSampleData = cleanSampleData(transformedData);
  
      if (updatedSampleData.length === 0) {
        setToastMessage("Sections data is Mandatory with labels!");
        setShowToast(true);
        return; // Exit the function early
      }
  
      const fields = {
        serviceTypeId: service,
        templateName: data?.name,
        description: data?.description,
        featureName: "SPECIFICATIONS",
        sections: updatedSampleData,
      };
  
      isApiCalling.current = true;
  
      try {
        const response = await auth.postTemplate(
          fields,
          handleFailure,
          handleSuccess
        );
      } catch (error) {
        console.error("Employee Creation failed:", error);
        handleFailure(err);
      } finally {
        isApiCalling.current = false;
      }
  
      fetchTemplates(page, pageSize, selectedFilters);
      handleClose();
    }
  
    const [addLabel, setAddLabel] = useState(false);
    useEffect(() => {
      axios({
        method: "post",
        url: getUrl(authConfig.allListNamesValues) + "?selectType=LIST_NAMES",
      })
        .then((res) => {
          setListNames(res.data.data);
          window.localStorage.setItem(
            authConfig.listValues,
            JSON.stringify(res.data.data)
          );
        })
        .catch((err) => console.log("List values error", err));
    }, [addLabel]);
  
    async function update(updateData) {
      if (isApiCalling.current) {
        // API call is already in progress, return early
        return;
      }
      const transformedData = transformData(sampleData);
      const updatedSampleData = cleanSampleData(transformedData);
  
      if (updatedSampleData.length === 0) {
        setToastMessage("Sections data is Mandatory with labels!");
        setShowToast(true);
        return; // Exit the function early
      }
  
      const fields = {
        serviceTypeId: service,
        templateName: templateName,
        description: updateData?.description,
        featureName: "SPECIFICATIONS",
        sections: transformedData,
      };
  
      isApiCalling.current = true;
  
      try {
        const response = await auth.putTemplate(
          data?.id,
          fields,
          handleFailure,
          handleSuccess
        );
      } catch (error) {
        console.error("Employee Creation failed:", error);
        handleFailure(err);
      } finally {
        isApiCalling.current = false;
      }
      setTemplateId({
        ...templateId,
        id: data?.id,
      });
    }
  
    const handleToastClose = () => {
      setShowToast(false);
      setToastMessage("");
    };

    return (
      <>
        <Dialog open={open} onClose={handleClose} fullScreen>
          <DialogTitle
            sx={{
              position: "relative",
              borderBottom: (theme) => `1px solid ${theme.palette.divider}`,
              p: (theme) => `${theme.spacing(1.75, 4)} !important`,
              display: "flex",
              alignItems: "center",
              justifyContent: "start",
              fontSize: { xs: 19, md: 20 },
              height: "50px",
              marginLeft:{xl:2,lg:2,md:2,sm:2,xs:2},
            }}
            textAlign={"center"}
          >
            {!data?.id
              ? "Add Specifications template details"
              : "Update Specifications template Details"}
            <Box sx={{ position: "absolute", top: "9px", right: "18px" }}>
              <IconButton
                size="small"
                onClick={handleClose}
                sx={{
                  // p: "0.438rem",
                  borderRadius: 1,
                  mr:1.8,
                  color: "common.white",
                  backgroundColor: "primary.main",
                  "&:hover": {
                    backgroundColor: "#66BB6A",
                    transition: "background 0.5s ease, transform 0.5s ease",
                  },
                }}
              >
                <Icon icon="tabler:x" fontSize="1rem" />
              </IconButton>
            </Box>
          </DialogTitle>
          <DialogContent>
            <>
              <Grid container spacing={3}>
                <Grid item xs={12} sm={6} sx={{ mt: 2 }}>
                  <FormControl fullWidth error={Boolean(errors.serviceType)}>
                    <Controller
                      name="serviceType"
                      control={control}
                      rules={{
                        required:
                          !data || Object.keys(data).length === 0
                            ? "Service Type is required"
                            : false,
                      }}
                      render={({ field }) => (
                        <SelectAutoComplete
                          id="serviceType"
                          label="Select Service Type"
                          nameArray={allServicesList}
                          value={service}
                          onChange={(e) => {
                            setService(e.target.value);
                            field.onChange(e.target.value);
                          }}
                        />
                      )}
                    />
                    {errors.serviceType && (
                      <FormHelperText sx={{ color: "error.main" }}>
                        {errors.serviceType.message}
                      </FormHelperText>
                    )}
                  </FormControl>
                </Grid>
                <Grid item xs={12} sm={6} sx={{ mt: 2 }}>
                  <FormControl fullWidth>
                    <Controller
                      name="name"
                      control={control}
                      rules={{
                        required:
                          !data || Object.keys(data).length === 0
                            ? "Template name is required"
                            : false,
                      }}
                      render={({ field }) => (
                        <TextField
                          {...field}
                          label="Template Name"
                          InputLabelProps={{ shrink: true }}
                          size="small"
                          placeholder="Enter Template name"
                          value={templateName}
                          onChange={(e) => {
                            setTemplateName(e.target.value); // Update state on change
                            field.onChange(e); // Update react-hook-form field value
                          }}
                          error={Boolean(errors.name)}
                          helperText={errors.name?.message}
                          aria-describedby="validation-name"
                          inputProps={{ maxLength: 50 }}
                        />
                      )}
                    />
                  </FormControl>
                </Grid>
                <Grid item xs={12} sm={12} sx={{ mt: 2 }}>
                  <FormControl fullWidth>
                    <Controller
                      name="description"
                      control={control}
                      rules={{ required: "Description is required" }}
                      render={({ field }) => (
                        <TextField
                          rows={3}
                          multiline
                          {...field}
                          label="Description"
                          InputLabelProps={{ shrink: true }}
                          placeholder="describe about template"
                          error={Boolean(errors.description)}
                          helperText={errors.description?.message}
                          aria-describedby="validation-description"
                          inputProps={{ maxLength: 1000 }}
                        />
                      )}
                    />
                  </FormControl>
                </Grid>
                <Grid item xs={12} sm={6} sx={{ mt: 2 }}>
                  <Button
                    size="medium"
                    variant="contained"
                    color="primary"
                    onClick={() => setSectionOpen(true)}
                  >
                    Add/Edit Sections
                  </Button>
                </Grid>
  
                {sampleData?.map((section, sectionIndex) => (
                  <Grid item xs={12} sm={12} sx={{ mt: 2 }} key={sectionIndex}>
                    <Card style={{ marginBottom: "16px" }}>
                      <Grid
                        sx={{
                          backgroundColor: "#f2f7f2",
                          mt: 4,
                          paddingTop: 0,
                          height: "36px",
                          display: "flex",
                          alignItems: "center",
                        }}
                      >
                        <Typography
                          variant="body1"
                          fontWeight={"bold"}
                          sx={{ mt: 0, ml: 2 }}
                        >
                          {listValues.find((item) => item.id === section.id)
                            ?.name || "Unknown"}
                        </Typography>
                        <Divider />
                      </Grid>
                      <Divider />
  
                        <TableContainer>
                          <Table sx={{ minWidth: 480 }} size="small">
                            {section.fields.length > 0 && (
                              <TableHead>
                                <TableRow>
                                  <TableCell
                                    sx={{
                                      fontWeight: "bold",
                                      borderBottom:
                                        "2px solid rgba(224, 224, 224, 1)",
                                      width: "35%",
                                    }}
                                  >
                                    Label
                                  </TableCell>
                                  <TableCell
                                    sx={{
                                      fontWeight: "bold",
                                      borderBottom:
                                        "2px solid rgba(224, 224, 224, 1)",
                                      width: "35%",
                                    }}
                                  >
                                    Component
                                  </TableCell>
                                  <TableCell
                                    sx={{
                                      fontWeight: "bold",
                                      borderBottom:
                                        "2px solid rgba(224, 224, 224, 1)",
                                      width: "35%",
                                    }}
                                  >
                                    Values
                                  </TableCell>
                                  <TableCell
                                    sx={{
                                      fontWeight: "bold",
                                      borderBottom:
                                        "2px solid rgba(224, 224, 224, 1)",
                                      width: "20%",
                                    }}
                                  >
                                    displayOrder
                                  </TableCell>
                                  <TableCell
                                    sx={{
                                      fontWeight: "bold",
                                      borderBottom:
                                        "2px solid rgba(224, 224, 224, 1)",
                                      width: "15%",
                                    }}
                                  >
                                    Actions
                                  </TableCell>
                                </TableRow>
                              </TableHead>
                            )}
                            <TableBody>
                              {section.fields?.map((row) => (
                                <TableRow
                                  key={row.id}
                                  sx={{
                                    borderBottom:
                                      "1px solid rgba(224, 224, 224, 1)",
                                  }}
                                >
                                  <TableCell
                                    sx={{
                                      borderBottom:
                                        "1px solid rgba(224, 224, 224, 1)",
                                      width: "35%",
                                    }}
                                  >
                                    <FormControl
                                      fullWidth
                                      error={Boolean(errors.listNamesId)}
                                    >
                                      <Controller
                                        name="label"
                                        control={control}
                                        rules={{ required: false }}
                                        render={({ field }) => (
                                          <SelectAutoCompleteListName
                                            id="label"
                                            nameArray={listNamesOptions}
                                            value={row.labelId}
                                            setAddSection={setAddLabel}
                                            label="label"
                                            setValuesUpdate={setLabelsUpdate}
                                            onChange={(e) => {
                                              field.onChange(e.target.value);
                                              handleFieldChange(
                                                sectionIndex,
                                                row.id,
                                                "labelId",
                                                e.target.value
                                              );
                                              setLabelId(e.target.value);
                                            }}
                                          />
                                        )}
                                      />
                                      {errors.listNamesId && (
                                        <FormHelperText
                                          sx={{ color: "error.main" }}
                                        >
                                          {errors.listNamesId.message}
                                        </FormHelperText>
                                      )}
                                    </FormControl>
                                  </TableCell>
                                  <TableCell
                                    sx={{
                                      borderBottom:
                                        "1px solid rgba(224, 224, 224, 1)",
                                      width: "35%",
                                    }}
                                  >
                                    <AutoCompleteWithoutLabel
                                      id="uiComponentsId"
                                      label="UI Component"
                                      nameArray={allUiComponentsId}
                                      value={row.componentId}
                                      onChange={(e, newValue) =>{
                                        handleFieldChange(
                                          sectionIndex, // sectionIndex
                                          row.id, // fieldId
                                          "componentId", // field name
                                          e.target.value // new value from AutoComplete
                                        )
                                      }}
                                      error={Boolean(errors.uiComponentsId)}
                                    />
                                  </TableCell>
                                  <TableCell
                                    sx={{
                                      borderBottom:
                                        "1px solid rgba(224, 224, 224, 1)",
                                      width: "35%",
                                    }}
                                  >
                                    {(row.labelId && !(row.componentId === authConfig.textAreaComponentId || row.componentId === authConfig.numberTextFieldComponentId || row.componentId === authConfig.textFieldComponentId)) && (
                                        <MultiSelectAutoCompleteCustom
                                        id="values"
                                        label="Values"
                                        nameArray={dropdownOptions[row.labelId] || []}
                                        value={row.values}
                                        onChange={(e, newValue) =>
                                          handleFieldChange(
                                            sectionIndex,
                                            row.id,
                                            "values",
                                            e.target.value
                                          )
                                        }
                                        labelId={row.labelId}
                                        handleValuesDropdownOpen={
                                          handleDropdownOpen
                                        }
                                        error={Boolean(errors.listValuesId)}
                                        onClick={() => handleDropdownOpen(row.labelId)}
                                      />
                                    )}
                                    
                                  </TableCell>
                                  <TableCell
                                    sx={{
                                      borderBottom:
                                        "1px solid rgba(224, 224, 224, 1)",
                                      width: "20%",
                                    }}
                                  >
                                    <TextField
                                      value={row.displayOrder}
                                      size="small"
                                      fullWidth
                                      InputProps={{ readOnly: true }} // Makes the field read-only
                                    />
                                  </TableCell>
                                  <TableCell
                                    sx={{
                                      borderBottom:
                                        "1px solid rgba(224, 224, 224, 1)",
                                      width: "15%",
                                    }}
                                  >
                                    <>
                                      {section.fields.length > 1 && (
                                        <IconButton
                                          sx={{ p: 0, width: 26, height: 26 }}
                                          size="small"
                                          color="error"
                                          onClick={() =>
                                            handleDeleteFieldRow(
                                              sectionIndex,
                                              row.id
                                            )
                                          }
                                        >
                                          <Icon icon="tabler:trash" />
                                        </IconButton>
                                      )}
                                      <IconButton
                                        sx={{ p: 0, width: 26, height: 26 }}
                                        size="small"
                                        color="success"
                                        onClick={() =>
                                          handleAddFieldRow(sectionIndex, row.id)
                                        }
                                      >
                                        <Icon icon="tabler:plus" />
                                      </IconButton>
                                    </>
                                  </TableCell>
                                </TableRow>
                              ))}
                            </TableBody>
                          </Table>
                        </TableContainer>
                      
  
                    
                    </Card>
                  </Grid>
                ))}
              </Grid>
            </>
          </DialogContent>
          <DialogActions
            sx={{
              borderTop: (theme) => `1px solid ${theme.palette.divider}`,
              p: (theme) => `${theme.spacing(2.5)} !important`,
              justifyContent: "end",
              height: "50px",
              mr:4,
            }}
          >
            <Button variant="outlined" color="primary" onClick={handleClose}>
              Cancel
            </Button>
            <Button variant="contained" color="primary" onClick={handlePreview}>
              Preview
            </Button>
            {!data?.id ? (
              <Button
                size="medium"
                variant="contained"
                color="primary"
                onClick={handleSubmit(submit)}
              >
                Save
              </Button>
            ) : (
              <Button
                size="medium"
                variant="contained"
                color="primary"
                onClick={handleSubmit(update)}
              >
                Update
              </Button>
            )}
          </DialogActions>
        </Dialog>
  
        <Dialog
          open={openDialogContent}
          onClose={handleButtonClick}
          aria-labelledby="alert-dialog-title"
          aria-describedby="alert-dialog-description"
          PaperProps={{
            sx: {
              p: (theme) => `${theme.spacing(2.5)} !important`,
              backgroundColor: (theme) => theme.palette.primary.background,
            },
          }}
        >
          <Box
            sx={{
              width: "100%",
              borderRadius: 1,
              textAlign: "center",
              border: (theme) => `1px solid ${theme.palette.divider}`,
              borderColor: "primary.main",
            }}
          >
            <DialogContent>
              <DialogContentText
                id="alert-dialog-description"
                color="primary.main"
              >
                <div dangerouslySetInnerHTML={{ __html: dialogMessage }} />
              </DialogContentText>
            </DialogContent>
            <DialogActions>
              <Button
                variant="contained"
                onClick={handleButtonClick}
                sx={{ margin: "auto", width: 100 }}
              >
                Okay
              </Button>
            </DialogActions>
          </Box>
        </Dialog>
  
        <Snackbar
          open={showToast}
          autoHideDuration={2000} // Toast will be visible for 2 seconds
          onClose={handleToastClose}
          anchorOrigin={{ vertical: "bottom", horizontal: "center" }} // Position of the toast
        >
          <Alert
            onClose={handleToastClose}
            severity="error"
            sx={{
              color: "black",
              padding: "4px 8px", // Reduce padding to make it smaller
              fontSize: "0.875rem", // Adjust font size for a more compact look
              borderRadius: "2px", // Optional: you can adjust the border radius
              border: "0.5px solid #ccc", // Optional: set a border or remove it completely
            }}
          >
            {toastMessage}
          </Alert>
        </Snackbar>
  
        <PreviewDialog
          open={openPreview}
          onClose={handleClosePreview}
          data={sampleData}
        />
  
        <SectionsDialog
          open={sectionOpen}
          onClose={handleSectionCancel}
          sections={sections}
          setSections={setSections}
          setSaveSections={setSaveSections}
          allSectionsId={allSectionsId}
          setSectionsUpdate={setSectionsUpdate}
        />
      </>
    );
  };
  
  export default SpecificationCreate;
  