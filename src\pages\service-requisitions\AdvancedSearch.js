import { useContext, useEffect, useState, React } from "react";
import Drawer from "@mui/material/Drawer";
import Button from "@mui/material/Button";
import MenuItem from "@mui/material/MenuItem";
import { styled } from "@mui/material/styles";
import IconButton from "@mui/material/IconButton";
import Typography from "@mui/material/Typography";
import Box from "@mui/material/Box";
import Tooltip from "@mui/material/Tooltip";
import CustomAvatar from "src/@core/components/mui/avatar";
import SearchIcon from "@mui/icons-material/Search";
import SelectProject from "src/@core/components/custom-components/SelectProject";
import UpdatedSelectAutoComplete from "src/@core/components/custom-components/UpdatedSelectAutoComplete";
import {
  Grid,
  FormControl,
  InputLabel,
  Select,
  FormHelperText,
  FormControlLabel,
  Checkbox,
  TextField,
  InputAdornment,
  Slider,
} from "@mui/material";
import { Controller, useForm } from "react-hook-form";
import PerfectScrollbar from "react-perfect-scrollbar";
import authConfig from "src/configs/auth";
import axios from "axios";
import { getAuthorizationHeaders, getUrl } from "src/helpers/utils";
import Icon from "src/@core/components/icon";
import { AuthContext } from "src/context/AuthContext";
import MultiSelectAutoComplete from "src/@core/components/custom-components/MultiSelectAutoComplete";

const Header = styled(Box)(({ theme }) => ({
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
  position: "relative",
  padding: theme.spacing(3),
  borderBottom: `1px solid ${theme.palette.divider}`,
}));

const AdvancedSearch = (props) => {
  const {
    
    open,
    toggle,
    searchKeyword,
    searchData,
    setSearchKeyword,
    setSearchData,
    fetchRequisitions,
    page,
    pageSize,
    error,
    setError,
    budgetValue,
    setBudgetValue,
    budget,
    setBudget,
    searchingState,
    setSearchingState,
  } = props;
  const { user, getAllListValuesByListNameId } = useContext(AuthContext);
  
  const {
    control,
    setValue,
    reset,
    getValues,
    register,
    formState: { errors },
  } = useForm({
    defaultValues: {
      serviceTypeUUIDs: [],
      socitiesUUIDs : [],
      houzerSocietyTeamUUIDs: [],
      leadStatusUUIDs: [],
      leadPriorityUUIDs: [],
      assignedToUUIDs: [],
      requirementDeadLineFrom: "",
      requirementDeadLineTo: "",
      searchKeyword: "",// for search by requisition No.
      searchKeywordByReferenceType: "",
      searchKeywordByTeamReference: "",
      budget: "",
      searchingState: true,
    },
  });

  const rupees = [
    {
      value: "HUNDRED",
      key: "hundred",
    },
    {
      value: "THOUSAND",
      key: "thousand",
    },
    {
      value: "LAKHS",
      key: "lakhs",
    },
    {
      value: "CRORES",
      key: "crores",
    },
  ];

  function valuetext(value) {
    return `${value}`;
  }

  const marks = Array.from({ length: 20 }, (_, i) => ({
    value: (i + 1) * 5,
    label: `${(i + 1) * 5}`,
  }));

  
  

  const handleChange = (event, newValue) => {
    setBudgetValue(newValue);
  };
  useEffect(() => {
    reset(searchData);
  }, [searchData, reset]);

  const handleCancel = () => {
    setSearchingState(false);
    setSearchKeyword("");
    setBudget("");
    setBudgetValue([20, 30]);
    setError(false);
    setSearchingState(false);
    reset({
      serviceTypeUUIDs: [],
      socitiesUUIDs: [],
      houzerSocietyTeamUUIDs: [],
      leadStatusUUIDs: [],
      leadPriorityUUIDs: [],
      assignedToUUIDs: [],
      requirementDeadLineFrom: "",
      requirementDeadLineTo: "",
      searchKeyword: "",//search by requisition No.
      searchKeywordByReferenceType: "",
      searchKeywordByTeamReference: "",
    });
    setSearchData({});
    fetchRequisitions(page, pageSize, searchKeyword);
  };

  const handleClose = () => {
    toggle();
  };

  const handleApply =() => {
    setSearchingState(true);
    toggle();
  }
  const [listOfEmployees, setListOfEmployees] = useState([]);
  const [employeesOptions, setEmployeesOptions] = useState([]);
  

  useEffect(() => {
    axios({
      method: "get",
      url: getUrl(authConfig.selectDropdownNew) + "?selectionType=EMPLOYEES",
      headers: getAuthorizationHeaders(),
    })
      .then((res) => {
        setListOfEmployees(res.data.data);
      })
      .catch((err) => console.log("Employees error", err));
  }, []);

  

  useEffect(() => {
    if (listOfEmployees.length) {
      const data = listOfEmployees.map((entry) => ({
        value: entry.id,
        key: entry.name,
      }));
      setEmployeesOptions(data);
    }
  }, [listOfEmployees]);

  const [employeesData, setEmployeesData] = useState([]);
  const [listOfSocieties, setListOfSocieties] = useState([]);
  
  const societyOptions = listOfSocieties
  .filter((society) => society?.name)
  .map((society) => ({
    value: society,
    key: society?.name,
  }));

useEffect(() => {
  const fetchSocieties = async () => {
    axios({
      method: "get",
      url: getUrl(authConfig.selectDropdownNew) + "?selectionType=SOCIETY_NAME",
      headers: getAuthorizationHeaders(),
    })
      .then((res) => {
        const metadataArray = res.data?.data?.map((item) => item?.metaData);

        setListOfSocieties(metadataArray);
      })
      .catch((err) => console.log("error", err));
  };
  fetchSocieties();
}, []);

  useEffect(() => {
    // Fetch all employees
    axios({
      method: "get",
      url: getUrl(authConfig.selectDropdownNew) + "?selectionType=EMPLOYEES",
      headers: getAuthorizationHeaders(),
    })
      .then((res) => {
        setEmployeesData(res.data.data);
      })
      .catch((err) => console.log("Employees error", err));
  }, []);

  const updateFilters = (data) => {
    setSearchData(data);
    fetchRequisitions(page, pageSize, searchKeyword, data);
  };

  const [leadStatusData, setLeadStatusData] = useState(null);
  const [leadPriorityData, setLeadPriorityData] = useState(null);
  //const [locationsData, setLocationsData] = useState(null);
  const [services, setServices] = useState(null);
  const [assignedTo, setDesignation] = useState(null);
  //const [portalsRegistered, setPortalsRegisteredData] = useState(null);

  const validateDates = () => {
    const fromDate = getValues('requirementDeadLineFrom');
    const toDate = getValues('requirementDeadLineTo');
    console.log("request DAte", fromDate);
    console.log("request DAte", toDate);
    if ((fromDate && !toDate) || (!fromDate && toDate)) {
      setError(true);
    } else {
      setError(false);
    }
  };

  useEffect(() => {
    if (!!authConfig) {
      getAllListValuesByListNameId(
        authConfig.allServicesListNameId,
        (data) =>
          setServices(
            data?.listValues.map((item) => ({
              value: item.id,
              key: item.listValue,
            }))
          ),
        handleError
      );
    }   
    if (!!authConfig) {
      getAllListValuesByListNameId(
        authConfig.leadPriorityListNamesId,
        (data) =>
          setLeadPriorityData(
            data?.listValues.map((item) => ({
              value: item.id,
              key: item.listValue,
            }))
          ),
        handleError
      );
    }

    if (!!authConfig) {
      getAllListValuesByListNameId(
        authConfig.designationId,
        (data) =>
          setDesignation(
            data?.listValues.map((item) => ({
              value: item.id,
              key: item.listValue,
            }))
          ),
        handleError
      );
    }
    if (!!authConfig) {
      getAllListValuesByListNameId(
        authConfig.statusListNamesId,
        (data) =>
          setLeadStatusData(
            data?.listValues.map((item) => ({
              value: item.id,
              key: item.listValue,
            }))
          ),
        handleError
      );
    }
    
  }, [authConfig]);

  const handleLeadStatusSuccess = (data) => setLeadStatusData(data?.listValues);
  const handleLeadPrioritySuccess = (data) =>setLeadPriorityData(data?.listValues);
  const handleServices = (data) => setServices(data?.listValues);
  const handleError = (error) => console.error("Error:", error);
  const handleDesignations = (data) => setDesignation(data?.listValues)

  return (
    <>
      <Tooltip title="Advanced Search">
        <CustomAvatar
          variant="rounded"
          sx={{ width: 36, height: 36, cursor: "pointer" }}
          onClick={toggle}
        >
          <Icon icon="tabler:filter" fontSize={27} />
        </CustomAvatar>
      </Tooltip>

     

      <Drawer
        open={open}
        anchor="right"
        variant="temporary"
        onClose={handleClose}
        ModalProps={{ keepMounted: true }}
        sx={{ "& .MuiDrawer-paper": { width: { xs: "90%", sm: 500 } } }} 
      >
        <Header 
          sx={{position: "relative", 
            display: "flex",
            alignItems: "center", 
            justifyContent: "space-between"
           }}
           >
          <Typography variant="h5" sx={{
            ml:{
              xs :3,
              sm:3,

              xl :3
            }}}
            
            
            > Advanced Search&nbsp;   </Typography>
          <Box sx={{ position: "absolute", top: "8px", right: "14px", 
              mt:{
                xs:1,
              xl:1.5,
              lg:1.5,
              md:1.5,
              sm:1
            },
              mr:{
                xs:2.5,
                xl:2.5,
                lg:2.5,
                md:2.5,
                sm:2.5
              },
            

           }}>
            <IconButton
              size="small"
              onClick={handleClose}
              sx={{
                borderRadius: 1,
                color: "common.white",
                backgroundColor: "primary.main",
                "&:hover": {
                  backgroundColor: "#66BB6A",
                  transition: "background 0.5s ease, transform 0.5s ease",
                },
              }}
            >
              <Icon icon="tabler:x" fontSize="1rem" />
            </IconButton>
          </Box>
        </Header>
                <PerfectScrollbar options={{ wheelPropagation: false }}>
          <Box sx={{ p: (theme) => theme.spacing(4, 6) }}>
            <Grid container spacing={3} alignItems={"center"}>

              {/* Society Multi-Select */}
              <Grid item xs={12} md={12}>
                <FormControl fullWidth>
                  <Controller
                    name="socitiesUUIDs"
                    control={control}
                    rules={{ required: false }}
                    render={({ field }) => (
                      <MultiSelectAutoComplete
                        id="society-select"
                        size="small"
                        label="Societies"
                        nameArray={societyOptions || []}
                        value={field.value || []}
                        onChange={(e) => {
                          field.onChange(e);
                          updateFilters({
                            ...getValues(),
                            socitiesUUIDs: e.target.value,
                          });
                        }}
                        error={Boolean(errors.service)}
                      />
                    )}
                  />
                </FormControl>
                {errors.leadPriority && (
                  <FormHelperText
                    sx={{ color: "error.main" }}
                    id="validation-society"
                  >
                    {errors.socitiesUUIDs?.message}
                  </FormHelperText>
                )}
              </Grid>

              {/* Main Search TextField */}
              <Grid item xs={12} md={12}>
                <FormControl fullWidth>
                  <Controller
                    name="mainSearch"
                    control={control}
                    render={({ field: { onChange } }) => (
                      <TextField
                        id="mainSearch"
                        placeholder="Search by Requisition Number"
                        label="Requisition No."
                        variant="outlined"
                        InputLabelProps={{
                          shrink: true,
                        }}
                        value={searchKeyword}
                        onChange={(e) => {
                          onChange(e.target.value);
                          setSearchKeyword(e.target.value);
                        }}
                        onKeyDown={(e) => {
                          if (e.key === "Enter") {
                            setSearchKeyword(searchKeyword);
                            fetchRequisitions(
                              page,
                              pageSize,
                              searchKeyword,
                              searchData
                            );
                          }
                        }}
                        sx={{
                          "& .MuiInputBase-root": {
                            height: "40px",
                          },
                          "& .MuiInputBase-input::placeholder": {
                            fontSize: "0.9rem",
                          },
                        }}
                        InputProps={{
                          endAdornment: (
                            <InputAdornment position="start">
                              <SearchIcon
                                sx={{
                                  cursor: "pointer",
                                  marginRight: "-15px",
                                }}
                                onClick={() => {
                                  setSearchKeyword(searchKeyword);
                                  fetchRequisitions(
                                    page,
                                    pageSize,
                                    searchKeyword,
                                    searchData
                                  );
                                }}
                              />{" "}
                            </InputAdornment>
                          ),
                        }}
                      />
                    )}
                  />
                </FormControl>
              </Grid>

              {/* Reference Type Search */}
              {user.userCategory === "Super Admin" || user.userCategory === "Employee" ? (
                <Grid item xs={12} md={12}>
                  <FormControl fullWidth>
                    <Controller
                      name="searchKeywordByReferenceType"
                      control={control}
                      render={({ field }) => (
                        <TextField
                          {...field}
                          id="searchKeywordByReferenceType"
                          placeholder="Search by Reference Type"
                          label="Reference Type"
                          variant="outlined"
                          InputLabelProps={{
                            shrink: true,
                          }}
                          value={field.value || ""}
                          onChange={(event) => {
                            updateFilters({
                              ...getValues(),
                              searchKeywordByReferenceType: event.target.value,
                            });
                          }}
                          onKeyDown={(e) => {
                            if (e.key === "Enter") {
                              fetchRequisitions(
                                page,
                                pageSize,
                                searchKeyword,
                                searchData
                              );
                            }
                          }}
                          sx={{
                            "& .MuiInputBase-root": {
                              height: "40px",
                            },
                            "& .MuiInputBase-input::placeholder": {
                              fontSize: "0.9rem",
                            },
                          }}
                          InputProps={{
                            endAdornment: (
                              <InputAdornment position="start">
                                <SearchIcon
                                  sx={{
                                    cursor: "pointer",
                                    marginRight: "-15px",
                                  }}
                                  onClick={(event) => {
                                    updateFilters({
                                      ...getValues(),
                                      searchKeywordByReferenceType: event.target.value,
                                    });
                                  }}
                                />{" "}
                              </InputAdornment>
                            ),
                          }}
                        />
                      )}
                    />
                  </FormControl>
                </Grid>
              ) : null}

              {/* Team Reference Search */}
              {user.userCategory === "Super Admin" || user.userCategory === "Employee" ? (
                <Grid item xs={12} md={12}>
                  <FormControl fullWidth>
                    <Controller
                      name="searchKeywordByTeamReference"
                      control={control}
                      render={({ field }) => (
                        <TextField
                          {...field}
                          id="searchKeywordByTeamReference"
                          placeholder="Search by Team Reference"
                          label="Team Reference"
                          variant="outlined"
                          InputLabelProps={{
                            shrink: true,
                          }}
                          value={field.value || ""}
                          onChange={(event) => {
                            updateFilters({
                              ...getValues(),
                              searchKeywordByTeamReference: event.target.value,
                            });
                          }}
                          onKeyDown={(e) => {
                            if (e.key === "Enter") {
                              fetchRequisitions(
                                page,
                                pageSize,
                                searchKeyword,
                                searchData
                              );
                            }
                          }}
                          sx={{
                            "& .MuiInputBase-root": {
                              height: "40px",
                            },
                            "& .MuiInputBase-input::placeholder": {
                              fontSize: "0.9rem",
                            },
                          }}
                          InputProps={{
                            endAdornment: (
                              <InputAdornment position="start">
                                <SearchIcon
                                  sx={{
                                    cursor: "pointer",
                                    marginRight: "-15px",
                                  }}
                                  onClick={(event) => {
                                    updateFilters({
                                      ...getValues(),
                                      searchKeywordByTeamReference: event.target.value,
                                    });
                                  }}
                                />{" "}
                              </InputAdornment>
                            ),
                          }}
                        />
                      )}
                    />
                  </FormControl>
                </Grid>
              ) : null}

              {/* Service Type Multi-Select */}
              <Grid item xs={12} md={12}>
                <FormControl fullWidth>
                  <Controller
                    name="serviceTypeUUIDs"
                    control={control}
                    rules={{ required: false }}
                    render={({ field }) => (
                      <MultiSelectAutoComplete
                        id="serviceTypeUUIDs"
                        size="small"
                        label="Services"
                        nameArray={services || []}
                        value={field.value || []}
                        onChange={(event) => {
                          field.onChange(event.target.value);
                          updateFilters({
                            ...getValues(),
                            serviceTypeUUIDs: event.target.value,
                          });
                        }}
                        error={Boolean(errors.service)}
                      />
                    )}
                  />
                </FormControl>
                {errors.service && (
                  <FormHelperText sx={{ color: "error.main" }} id="validation-service">
                    {errors.service?.message}
                  </FormHelperText>
                )}
              </Grid>

              {/* Priority Multi-Select */}
              <Grid item xs={12} md={12}>
                <FormControl fullWidth>
                  <Controller
                    name="leadPriorityUUIDs"
                    control={control}
                    rules={{ required: false }}
                    render={({ field }) => (
                      <MultiSelectAutoComplete
                        id="lead-priority-select"
                        size="small"
                        label="Priority"
                        nameArray={leadPriorityData || []}
                        value={field.value || []}
                        onChange={(e) => {
                          field.onChange(e);
                          updateFilters({
                            ...getValues(),
                            leadPriorityUUIDs: e.target.value,
                          });
                        }}
                        error={Boolean(errors.service)}
                      />
                    )}
                  />
                </FormControl>
                {errors.leadPriority && (
                  <FormHelperText
                    sx={{ color: "error.main" }}
                    id="validation-leadPriority"
                  >
                    {errors.leadPriority?.message}
                  </FormHelperText>
                )}
              </Grid>

              {/* Status Multi-Select */}
              {user.userCategory === "Super Admin" || user.userCategory === "Employee" ? (
                <Grid item xs={12} md={12}>
                  <FormControl fullWidth>
                    <Controller
                      name="leadStatusUUIDs"
                      control={control}
                      rules={{ required: false }}
                      render={({ field }) => (
                        <MultiSelectAutoComplete
                          size="small"
                          id="leadStatus"
                          label="Status"
                          nameArray={leadStatusData || []}
                          value={field.value || []}
                          onChange={(e) => {
                            field.onChange(e);
                            updateFilters({
                              ...getValues(),
                              leadStatusUUIDs: e.target.value,
                            });
                          }}
                          error={Boolean(errors.serivcesProvided)}
                        />
                      )}
                    />
                  </FormControl>
                  {errors.leadStatus && (
                    <FormHelperText
                      sx={{ color: "error.main" }}
                      id="validation-leadStatus"
                    >
                      {errors.leadStatus?.message}
                    </FormHelperText>
                  )}
                </Grid>
              ) : null}

              {/* Houzer Society Team Member Multi-Select */}
              {user.userCategory === "Super Admin" || user.userCategory === "Employee" ? (
                <Grid item xs={12} md={12}>
                  <FormControl fullWidth>
                    <Controller
                      name="houzerSocietyTeamUUIDs"
                      control={control}
                      rules={{ required: false }}
                      render={({ field }) => (
                        <MultiSelectAutoComplete
                          id="Houzer-Society-Team-Member"
                          size="small"
                          label="Houzer Society Team Member"
                          nameArray={employeesOptions || []}
                          value={field.value || []}
                          onChange={(event) => {
                            field.onChange(event.target.value);
                            updateFilters({
                              ...getValues(),
                              houzerSocietyTeamUUIDs: event.target.value,
                            });
                          }}
                          error={Boolean(errors.societyTeamMember)}
                        />
                      )}
                    />
                  </FormControl>
                  {errors.location && (
                    <FormHelperText
                      sx={{ color: "error.main" }}
                      id="validation-societyTeamUUIDs"
                    >
                      {errors.societtyTeam?.message}
                    </FormHelperText>
                  )}
                </Grid>
              ) : null}

              {/* Assigned To Multi-Select */}
              <Grid item xs={12} md={12}>
                <FormControl fullWidth>
                  <Controller
                    name="assignedToUUIDs"
                    control={control}
                    rules={{ required: false }}
                    render={({ field }) => (
                      <MultiSelectAutoComplete
                        id="assignedTo-select"
                        size="small"
                        label="Assigned To"
                        nameArray={employeesOptions || []}
                        value={field.value || []}
                        onChange={(e) => {
                          field.onChange(e);
                          updateFilters({
                            ...getValues(),
                            assignedToUUIDs: e.target.value,
                          });
                        }}
                        error={Boolean(errors.service)}
                      />
                    )}
                  />
                </FormControl>
                {errors.leadPriority && (
                  <FormHelperText
                    sx={{ color: "error.main" }}
                    id="validation-leadPriority"
                  >
                    {errors.assignedTo?.message}
                  </FormHelperText>
                )}
              </Grid>

              {/* Date Range From */}
              <Grid item xs={12} md={12}>
                <FormControl fullWidth>
                  <Controller
                    name="requirementDeadLineFrom"
                    control={control}
                    defaultValue=""
                    render={({ field }) => (
                      <TextField
                        {...field}
                        size="small"
                        label="Requested Date From"
                        type="date"
                        InputLabelProps={{ shrink: true }}
                        aria-describedby="requirementDeadLineFrom"
                        value={field.value}
                        error={Boolean(errors.requirementDeadLineFrom) && !field.value}
                        helperText={
                          Boolean(errors.requirementDeadLineFrom) && !field.value
                            ? "This field is required"
                            : ""
                        }
                        onChange={(event) => {
                          field.onChange(event.target.value);
                          updateFilters({
                            ...getValues(),
                            requirementDeadLineFrom: event.target.value,
                          });
                          validateDates();
                        }}
                      />
                    )}
                  />
                </FormControl>
              </Grid>

              {/* Date Range To */}
              <Grid item xs={12} md={12}>
                <FormControl fullWidth>
                  <Controller
                    name="requirementDeadLineTo"
                    control={control}
                    defaultValue=""
                    render={({ field }) => (
                      <TextField
                        {...field}
                        size="small"
                        label="Requested Date To"
                        type="date"
                        InputLabelProps={{ shrink: true }}
                        aria-describedby="requirementDeadLineTo"
                        value={field.value}
                        inputProps={{
                          min: getValues("requirementDeadLineFrom"),
                        }}
                        error={Boolean(errors.requirementDeadLineTo) && !field.value}
                        helperText={
                          Boolean(errors.requirementDeadLineTo) && !field.value
                            ? "This field is required"
                            : ""
                        }
                        onChange={(event) => {
                          field.onChange(event.target.value);
                          updateFilters({
                            ...getValues(),
                            requirementDeadLineTo: event.target.value,
                          });
                          validateDates();
                        }}
                      />
                    )}
                  />
                </FormControl>
              </Grid>

              {/* Budget Slider */}
              <Grid item xs={12} md={12}>
                <Box display="flex" alignItems="center">
                  <Typography id="range-slider" gutterBottom>
                    Budget in
                  </Typography>
                  <Grid item xs={8} xl={4} sm={4} sx={{ marginLeft: "6px" }}>
                    <UpdatedSelectAutoComplete
                      register={register}
                      id={"budget"}
                      size={"small"}
                      label={"units"}
                      nameArray={rupees}
                      value={budget}
                      onChange={
                        
                        (e) => {
                          setBudget(e.target.value);
                        }
                      }
                      error={Boolean(errors.budget)}
                      aria-describedby="validation-budget"
                    />
                  </Grid>
                </Box>
                <Slider
                  value={budgetValue}
                  onChange={handleChange}
                  valueLabelDisplay="auto"
                  aria-labelledby="range-slider"
                  getAriaValueText={valuetext}
                  step={5}
                  marks={marks}
                  min={5}
                  max={100}
                />
              </Grid>
            </Grid>
          </Box>
        </PerfectScrollbar>

        <Box
          sx={{
            borderTop: (theme) => `1px solid ${theme.palette.divider}`,
            p: (theme) => theme.spacing(2),
            justifyContent: "flex-end",
            display: "flex",
            alignItems: "center",
      
          }}
        >
          <Button variant="tonal" sx={{ mr: 3 }} onClick={handleCancel}>
            Clear All
          </Button>
          <Button variant="contained" onClick={handleApply} sx={{ mr: {
                xs: 4, 
                sm: 4, 
                md: 4, 
                lg: 4, 
                xl: 4,
              },} }>
            Apply
          </Button>
        </Box>
      </Drawer>



    </>
  );
};

export default AdvancedSearch;
