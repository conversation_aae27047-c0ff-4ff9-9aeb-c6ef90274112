import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import SearchIcon from "@mui/icons-material/Search";
import {
  <PERSON>ton,
  CardContent,
  Chip,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  Divider,
  FormControl,
  FormControlLabel,
  Grid, 
  InputAdornment,
  Link,
  Menu,
  MenuItem,
  Radio,
  RadioGroup,
  TextField,
  Tooltip,
  Typography,
  useMediaQuery,
  useTheme
} from "@mui/material";
import IconButton from "@mui/material/IconButton";
import { DataGrid } from "@mui/x-data-grid";
import React, { useContext, useEffect, useState } from "react";
import Icon from "src/@core/components/icon";
import authConfig from "src/configs/auth";
import { getAuthorizationHeaders, getUrl } from "src/helpers/utils";
import { useAuth } from "src/hooks/useAuth";
import { useRBAC } from "src/pages/permission/RBACContext";
import axios from "axios";
import CustomAvatar from "src/@core/components/mui/avatar";
import { Controller, useForm } from "react-hook-form";
import { Box } from "@mui/system";
import CreateUser from "./CreateUser";
import { AuthContext } from "src/context/AuthContext";
import ExpandLessIcon from "@mui/icons-material/ExpandLess";
import ModeEditRoundedIcon from "@mui/icons-material/ModeEditRounded";
import QuestionAnswerOutlinedIcon from "@mui/icons-material/QuestionAnswerOutlined";
import SelectAutoComplete from "src/@core/components/custom-components/SelectAutoComplete";
import FallbackSpinner from "src/@core/components/spinner";
import CloseExpandIcons from "../all-profiles-old/CloseExpandIcons";
import IndexCHS from "../conversations/index-chs";
import { useRouter } from "next/router";
import AdvancedSearchNew from "./AdvancedSearchNew";
import SocietyEnrollmentDialog from "./profile/longform-v2/index";
import SocietyEnrollmentShortDialog from "./profile/shortform-v2/index";
import LongFormDialog from "./sections-longform/LongFormDialog";

const ExcelDownMenu = ({ children }) => {
  const [anchorEl, setAnchorEl] = useState(null);

  const handleExcelClick = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  return (
    <>
      <Tooltip title="Export to Excel">
        <IconButton onClick={handleExcelClick} size="medium">
          <Icon icon="vscode-icons:file-type-excel" fontSize="2.2rem" />
        </IconButton>
      </Tooltip>

      <Menu
        keepMounted
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleClose}
        PaperProps={{
          style: {
            marginTop: "4px",
          },
        }}
      >
        {children}
      </Menu>
    </>
  );
};

const UsersOverView = () => {
  const { can, rbacRoles } = useRBAC();
  const { entityData, user,getAllListValuesByListNameId } = useContext(AuthContext);
  const [menu, setMenu] = useState(null);

  const handleCloseMenuItems = () => {
    setMenu(null);
  };

  const router = useRouter();

  const { seriesName, duration } = router.query;
  const { frequency } = router.query;
  const { month } = router.query;
  const { assigned } = router.query;
  const { created } = router.query;
  // const [expanded, setExpanded] = useState(true);

  // const handleToggle = (value) => {
  //   setExpanded(value);
  // };
  const [dialogOpen, setDialogOpen] = useState(false);
  const [dialogMessage, setDialogMessage] = useState("");
  const [missingPhoneNumbers, setMissingPhoneNumbers] = useState([]);
  const [numberAvalabilty, setNumberAvalabilty] = useState(false);

  const [confirmationDialogOpen, setConfirmationDialogOpen] = useState(false);
  const [confirmationDialogMessage, setConfirmationDialogMessage] =
    useState("");
  const [isActivating, setIsActivating] = useState(false);
  const [showMissingNumbersDialog, setShowMissingNumbersDialog] =
    useState(false);
  const [radioValue, setRadioValue] = useState("");
  const [selectedTemplateName, setSelectedTemplateName] = useState("");
  const [templates, setTemplates] = useState([]);
  const [initialRowCount, setInitialRowCount] = useState(null);
  const [matchedLocations, setMatchedLocations] = useState([]);
  const [matchedServices, setMatchedServices] = useState([]);
  const [matchedLeadStatus, setMatchedLeadStatus] = useState([]);
  const [matchedBuildingAge, setMatchedBuildingAge] = useState([]);
  const [matchedLeadPriority, setMatchedLeadPriority] = useState([]);
  const [matchedAssignedTo, setMatchedAssignedTo] = useState([]);
  const [matchedPortalsRegistered, setPortalsRegistered] = useState([]);
  // const handleCloseDialog = () => {
  //   setDialogOpen(false);
  // };

  const [searchingState, setSearchingState] = useState(false);

  const handleCloseDialog = () => {
    setWhatsappOpenDialog(false);
    setSelectedTemplateName("");
    setSelectedTemplateParams([]);
    setShowSocietyName(false);
    setShowServiceRequirement(false);
    setShowMeasurements(false);
    setShowGoogleFormLink(false);
    setShowLocation(false);
    setMeasurements("");
    setIsButtonDisabled(true);
    setDialogOpen(false);
    setMessageStatus(null);
    setShowMissingNumbersDialog(false);
    setImageUrl("");
    setShowImageUrl(false);
    setMissingInfoDialogOpen(false);
  };
  const handleRadioChange = (event) => {
    setRadioValue(event.target.value);
  };

  const clearFilter = () => {
    router.replace(
      {
        query: {}, // Clear all query parameters
      },
      undefined,
      { shallow: false } // Allow full page reload
    );

    setSearchData(() => ({
      assignedTo: false,
      createdBy: false,
      searchByDurationDaily: false,
      searchByDurationThisWeek: false,
      searchByDurationMonthly: false,
      searchByDurationQuarterly: false,
      searchByDurationYearly: false,
    }));
  };

  const clearMonthFilter = () => {
    // Update the query to remove the month parameter
    router.replace(
      {
        query: {}, // Clear all query parameters
      },
      undefined,
      { shallow: false } // Allow full page reload
    );

    // Clear the month and update the search data
    setSearchData((prev) => ({ ...prev, month: null }));
  };

  useEffect(() => {
    if (assigned) {
      setSearchData((prev) => {
        const updatedData = { ...prev };

        updatedData.assignedTo = assigned;

        return updatedData;
      });
    }
  }, [assigned]);

  useEffect(() => {
    if (created) {
      setSearchData((prev) => {
        const updatedData = { ...prev };

        updatedData.createdBy = created;

        return updatedData;
      });
    }
  }, [created]);

  useEffect(() => {
    if (seriesName || duration) {
      setSearchData((prev) => {
        const updatedData = { ...prev };

        if (seriesName) {
          if (seriesName === "assignedTo") {
            updatedData.assignedTo = true;
          } else if (seriesName === "createdBy") {
            updatedData.createdBy = true;
          }
        }

        if (duration) {
          if (duration === "daily") {
            updatedData.searchByDurationDaily = true;
          } else if (duration === "weekly") {
            updatedData.searchByDurationThisWeek = true;
          } else if (duration === "monthly") {
            updatedData.searchByDurationMonthly = true;
          }
        }

        return updatedData;
      });
    }
  }, [seriesName, duration]);

  useEffect(() => {
    if (frequency) {
      setSearchData((prev) => {
        const updatedData = { ...prev };

        if (frequency) {
          if (frequency === "daily") {
            updatedData.searchByDurationDaily = true;
          } else if (frequency === "monthly") {
            updatedData.searchByDurationMonthly = true;
          } else if (frequency === "quarterly") {
            updatedData.searchByDurationQuarterly = true;
          } else if (frequency === "yearly") {
            updatedData.searchByDurationYearly = true;
          }
        }

        return updatedData;
      });
    }
  }, [frequency]);

  useEffect(() => {
    if (month) {
      setSearchData((prev) => ({ ...prev, month: parseInt(month, 10) }));
    }
  }, [month]);

  const handleOpenConfirmationDialog = (message, isActivating) => {
    setConfirmationDialogMessage(message);
    setIsActivating(isActivating);
    setConfirmationDialogOpen(true);
  };

  const handleCloseConfirmationDialog = () => {
    setConfirmationDialogOpen(false);
  };

  const handleConfirmStatusChange = async () => {
    setConfirmationDialogOpen(false);
    try {
      if (!currentRow || !currentRow.id) {
        throw new Error("Current row ID is not defined");
      }

      const url = isActivating
        ? getUrl(authConfig.individualEndpoint) + `/activate/${currentRow.id}`
        : getUrl(authConfig.individualEndpoint) +
          `/deactivate/${currentRow.id}`;

      const method = "patch";
      const response = await axios({
        method: method,
        url: url,
        headers: getAuthorizationHeaders(),
      });

      if (response.status !== 200 && response.status !== 204) {
        throw new Error(`Unexpected response status: ${response.status}`);
      }

      setDialogMessage(
        isActivating
          ? "Society activated successfully"
          : "Society de-activated successfully"
      );

      // Refresh data
      fetchUsers(page, pageSize, roleFilter, searchKeyword, selectedFilters);
    } catch (error) {
      console.error("Error changing status", error);
      setDialogMessage(
        isActivating ? "Error activating status" : "Failed to Deactivate Status"
      );

      if (error.response) {
        console.error("Error response data:", error.response.data);
      }
    } finally {
      setDialogOpen(true);
    }
  };

  const handleError = (error) => {
    console.error("society requisition: All Services:", error);
  };
  useEffect(() => {
    if (!!authConfig) {
      getAllListValuesByListNameId(
        authConfig.leadPriorityListNamesId,
        (data) =>
          setPriorityData(
            data?.listValues?.map((item) => ({
              value: item.id,
              key: item.listValue,
            }))
          ),
        handleError
      );
    }
  }, [authConfig]);

  // Constants
  const columns = [
    {
      field: "firstName",
      minWidth: 100,
      headerName: "Name",
      flex: 0.25,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <span>{params.value}</span>
        </Tooltip>
      ),
    },
    {
      field: "companyName",
      minWidth: 100,
      headerName: "Society Name",
      flex: 0.3,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <span>{params.value}</span>
        </Tooltip>
      ),
    },
    {
      field: "email",
      minWidth: 100,
      headerName: "Email",
      flex: 0.25,
      renderCell: (params) => {
        const email = params?.value;

        return email?.length > 21 ? (
          <Tooltip title={email}>
            <Link
              href={`mailto:${email}`}
              target="_blank"
              rel="noopener noreferrer"
              sx={{ color: "#6666ff" }}
            >
              <span>{email}</span>
            </Link>
          </Tooltip>
        ) : (
          <Link
            href={`mailto:${email}`}
            target="_blank"
            rel="noopener noreferrer"
            sx={{ color: "#6666ff" }}
          >
            {email}
          </Link>
        );
      },
    },
    {
      field: "mobileNumber",
      minWidth: 115,
      headerName: "Mobile No",
      flex: 0.1,
      renderCell: (params) => {
        const mobileNumber = params?.value;

        return mobileNumber?.length > 9 ? (
          <Tooltip title={mobileNumber}>
            <Link
              href={`tel:${mobileNumber}`}
              target="_blank"
              rel="noopener noreferrer"
              sx={{ color: "#6666ff" }}
            >
              <span>{mobileNumber}</span>
            </Link>
          </Tooltip>
        ) : (
          <Link
            href={`tel:${mobileNumber}`}
            target="_blank"
            rel="noopener noreferrer"
            sx={{ color: "#6666ff" }}
          >
            {mobileNumber}
          </Link>
        );
      },
    },

    {
      flex: 0.15,
      minWidth: 100,
      field: "assignedTo",
      headerName: "Assigned to",
      renderCell: (params) => {
        const assignedTo = employeesData?.find(
          (item) => item?.id === params?.row?.assignedTo
        );
        return (
          <Tooltip title={assignedTo?.name}>
            <span>{assignedTo?.name || ""}</span>
          </Tooltip>
        );
      },
    },

    {
      flex: 0.01,
      minWidth: 95,
      field: "createdBy",
      headerName: "Created By",
      renderCell: (params) => {
        const createdBy = params?.value;

        return (
          <Tooltip title={createdBy}>
            <span>{createdBy}</span>
          </Tooltip>
        );
      },
    },
    //Active or In-active status
    {
      field: "isActive",
      minWidth: 80,
      headerName: "Status",
      flex: 0.08,
      renderCell: (params) => {
        const status = params?.row.isActive;
        return (
          <Tooltip title={status ? "ACTIVE" : "INACTIVE"}>
            <CustomAvatar
              skin="none"
              sx={{
                width: status ? 13 : 13,
                height: status ? 13 : 13,
                m: "auto",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
              }}
              color={status ? "success" : "error"}
            >
              <Icon
                icon={
                  status
                    ? "fluent-emoji-flat:green-circle"
                    : "fluent-emoji-flat:red-circle"
                }
                style={{ width: 15, height: 15 }}
              />
            </CustomAvatar>
          </Tooltip>
        );
      },
    },
    {
      flex: 0.11,
      field: "actions",
      headerName: "Actions",
      sortable: false,
      minWidth: 80,
      disableClickEventBubbling: true,
      renderCell: (params) => {
        const handleClickMenu = (event) => {
          event.stopPropagation();
          setMenu(event.currentTarget);
          setCurrentRow(params.row);
        };

        const onEditClick = () => {
          // isLongFormDialogOpen(true);
          openLongFormDialog();
          handleCloseMenu();
          handleCloseMenuItems();
        };

        const onConversationsClick = () => {
          openConversationDialog();
          handleCloseMenu();
        };

        const handleActivateStatus = () => {
          handleOpenConfirmationDialog(
            "Are you sure you want to activate this user?",
            true
          );
          handleCloseMenuItems();
        };

        const handleDeactivateStatus = () => {
          handleOpenConfirmationDialog(
            "Are you sure you want to de-activate this user?",
            false
          );
          handleCloseMenuItems();
        };

        return (
          <div
            style={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
            }}
          >
            <CustomAvatar
              skin="light"
              variant="rounded"
              sx={{
                mr: { xs: 2, lg: 4 },
                width: 30,
                height: 30,
                cursor: "pointer",
              }}
              onClick={handleClickMenu}
            >
              <Icon icon="bi:three-dots-vertical" />
            </CustomAvatar>
            <Menu
              id="actions-menu"
              anchorEl={menu}
              keepMounted
              open={Boolean(menu)}
              onClose={handleCloseMenuItems}
            >
              {/* <MenuItem onClick={handleRowClick}>
                <ModeEditRoundedIcon style={{ marginRight: 8 }} />
                Edit V1
              </MenuItem> */}
              <MenuItem onClick={onEditClick}>
                <ModeEditRoundedIcon style={{ marginRight: 8 }} />
                Edit
              </MenuItem>
              <MenuItem onClick={onConversationsClick}>
                <QuestionAnswerOutlinedIcon style={{ marginRight: 8 }} />
                Add Conversation
              </MenuItem>
              {/* <MenuItem onClick={handleWhatsappOptionClick}>
                <WhatsAppIcon style={{ marginRight: 8 }} />
                Send WhatsApp message
              </MenuItem> */}

              {currentRow?.isActive ? (
                <MenuItem onClick={() => handleDeactivateStatus()}>
                  <Icon
                    icon="fluent:person-delete-20-filled"
                    style={{ marginRight: 8, fontSize: 25 }}
                  />{" "}
                  De-activate
                </MenuItem>
              ) : (
                <MenuItem onClick={() => handleActivateStatus()}>
                  {" "}
                  <Icon
                    icon="mdi:account-reactivate"
                    style={{ marginRight: 8, fontSize: 25 }}
                  />{" "}
                  Activate
                </MenuItem>
              )}
            </Menu>
          </div>
        );
      },
    },
  ];
  // Use States
  const {
    register,
    handleSubmit,
    control,
    reset,
    clearErrors,
    formState: { errors },
  } = useForm();
  const theme = useTheme();
  const isExtraSmallScreen = useMediaQuery(theme.breakpoints.down(365));
  const isSmallScreen = useMediaQuery(theme.breakpoints.down(395));
  const isMobile = useMediaQuery(theme.breakpoints.down(435));

  const auth = useAuth();
  const [currentRow, setCurrentRow] = useState(null);

  const [isLongFormDialogOpen, setLongFormDialogOpen] = useState(false);
  const [menuAnchorEl, setMenuAnchorEl] = useState(null);

  // State for holding fetched data
  // const [entityData, setEntityData] = useState({});
  // const [employeesData, setEmployeesData] = useState([]);
  const [userList, setUserList] = useState([]);
  const rowsPerPageOptions = [10, 15, 20, 25, 50, 100];

  const [pageSize, setPageSize] = useState(rowsPerPageOptions[0]);
  const [loading, setLoading] = useState(true);
  const [page, setPage] = useState(1);
  const totalGridHeight = pageSize * 52 + 80;
  const [searchKeyword, setSearchKeyword] = useState("");
  const [searchData, setSearchData] = useState({});
  const [rowCount, setRowCount] = useState(0);
  const [openDialog, setOpenDialog] = useState(false);
  const [selectedOption, setSelectedOption] = useState(null);
  const [selectedTab, setSelectedTab] = useState(0);
  const [selectedRows, setSelectedRows] = useState([]);
  const [isEditDialogOpens, setEditDialogOpens] = useState(false);
  const [openMenu, setOpenMenu] = useState(null);

  const [isConversationDialogOpen, setConversationDialogOpen] = useState(false);

  const [expanded, setExpanded] = useState(true);

  const [addUserOpen, setAddUserOpen] = useState(false);
  const toggleAddUserDrawer = () => setAddUserOpen(!addUserOpen);
  const [roleFilter, setRoleFilter] = useState(
    "ad60ce4e-f528-4722-82aa-8757baafce7a"
  );
  const [employeesData, setEmployeesData] = useState(null);
  const [keyword, setKeyword] = useState("");
  const [placeholderText, setPlaceholderText] = useState(
    "Search by Society name"
  );
  const { fromDate,toDate,employee} = router.query;
  useEffect(() => {
    // Fetch all employees
    axios({
      method: "get",
      url:
        getUrl(authConfig.selectDropdownNew) + "?selectionType=EMPLOYEES",
      headers: getAuthorizationHeaders(),
    })
      .then((res) => {
        setEmployeesData(res.data.data);
      })
      .catch((err) => console.log("Employees error", err));

    const phrases = ["Society name", "name", "email", "mobile number"];
    let phraseIndex = 0;
    const intervalId = setInterval(() => {
      phraseIndex = (phraseIndex + 1) % phrases?.length;
      setPlaceholderText(`Search by ${phrases[phraseIndex]}`);
    }, 1000);

    return () => clearInterval(intervalId);
  }, []);

  // const handleOptionSelect = (option) => {
  //   setSelectedOption(option);
  //   setOpenDialog(true);
  // };

  const openLongFormDialog = () => {
    setLongFormDialogOpen(true);
    // Fetch necessary data for the dialog
    // fetchEntityData(row.id);
    // fetchEmployeesData();
  };

  const [selectedFilters, setSelectedFilters] = useState([]);

  const [priorityData, setPriorityData] = useState([]);

  const handleApplyFilters = (filters) => {
    setSelectedFilters(filters); // Set filters for chips
    setSearchingState(true); // Trigger search
  };

  const handleRemoveFilterCHS = (key, id) => {
    if (
      (key === "priorityFilter" || key === "assignedToGivenEmployeeId") &&
      id
    ) {
      // Remove the specific ID from the servicesFilter value
      const updatedFilters = selectedFilters
        ?.map((filter) => {
          if (filter.key === key) {
            return {
              ...filter,
              value: filter.value?.filter((serviceId) => serviceId !== id),
            };
          }
          return filter;
        })
        ?.filter((filter) => filter.value?.length > 0); // Remove the filter if no values remain
      setSelectedFilters(updatedFilters);
    } else {
      // Remove the entire filter
      setSelectedFilters(
        selectedFilters?.filter((filter) => filter.key !== key)
      );
    }
  };

  // Clear all filters
  const clearAllFilters = () => {
    setSelectedFilters([]);
    setSearchingState(false); // Reset search state if needed
  };

  // Function to close the LongFormDialog
  const closeLongFormDialog = () => {
    setLongFormDialogOpen(false);
    setCurrentRow(null);
  };

  const handleDialogClose = () => {
    setOpenDialog(false);
  };

  const handleMenuOpen = (event) => {
    setMenuAnchorEl(event.currentTarget);
  };

  // Close the menu
  const handleMenuClose = () => {
    setMenuAnchorEl(null);
  };

  // Handle MenuItem click to open LongFormDialog
  const handleLongFormClick = () => {
    // setCurrentRow(null)
    // setLongFormDialogOpen(true)
    handleMenuClose(); // Close the menu
  };

  const [societyEnrollmentDialog, setSocietyEnrollmentDialog] = useState(false);
  const [societyEnrollmentShortDialog, setSocietyEnrollmentShortDialog] =
    useState(false);

  const openSocietyEnrollmentDialog = () => {
    setSocietyEnrollmentDialog(true); // Opens the dialog
    handleMenuCloses();
  };

  const handleShortFormClick = () => {
    setSocietyEnrollmentShortDialog(true);
    handleMenuCloses();
  };

  const closeSocietyEnrollmentDialog = () => {
    setSocietyEnrollmentDialog(false); // Closes the dialog
  };

  const closeSocietyEnrollmentShortDialog = () => {
    setSocietyEnrollmentShortDialog(false); // Closes the dialog
  };

  const handleSocietyEnrollmentFormClick = () => {};

  const handlePageChange = (direction) => {
    if (direction === page) {
      setPage(page + 1);
    } else {
      setPage(page - 1);
    }
  };

  const handlePageSizeChange = (params) => {
    if (params) {
      setPageSize(params);
      setPage(1);
    }
  };
  const [anchorEls, setAnchorEls] = React.useState(null);
  const opens = Boolean(anchorEls);

  const handleMenuClicks = (event) => {
    setAnchorEls(event.currentTarget);
  };

  const handleMenuCloses = () => {
    setAnchorEls(null);
  };

  // const handleMenuOpen = (event) => {
  //   setOpenMenu(event.currentTarget);
  // };

  // const handleMenuClose = () => {
  //   setOpenMenu(null);
  // };

  const handleOptionSelect = (option) => {
    setOpenMenu(null);
    // setOpenDialog(true);
  };

  useEffect(() => {
    fetchUsers(page, pageSize, roleFilter, searchKeyword, selectedFilters);
  }, [page, pageSize, roleFilter, searchKeyword, selectedFilters]);

  const openConversationDialog = () => {
    setConversationDialogOpen(true);
  };

  const closeConversationDialog = () => {
    setMenu(null);
    setConversationDialogOpen(false);
  };

  const closeEditDialogs = () => {
    setEditDialogOpens(false);
  };

  const openEditDialogs = () => {
    setEditDialogOpens(true);
  };

  const fetchUsers = async (
    currentPage,
    currentPageSize,
    roleFilter,
    searchKeyword,
    selectedFilters
  ) => {
    let url;
    if (user?.roleId === authConfig?.superAdminRoleId) {
      url = getUrl(authConfig.individualEndpoint) + "/all";
    } else {
      url = getUrl(authConfig.individualEndpoint) + "/employee-all";
    }

    let headers;

    if (user?.roleId === authConfig?.superAdminRoleId) {
      headers = getAuthorizationHeaders({
        contentType: authConfig.INDIVIDUAL_GET_ALL_REQ_V1,
        accept: authConfig.INDIVIDUAL_GET_ALL_RES_V1,
      });
    } else {
      headers = getAuthorizationHeaders({
        contentType: authConfig.INDIVIDUAL_GET_ALL_EMPLOYEE_REQ_V1,
        accept: authConfig.INDIVIDUAL_GET_ALL_EMPLOYEE_RES_V1,
      });
    }

   
    const data = {
      page: currentPage,
      pageSize: currentPageSize,
      roleFilter:roleFilter,
      searchKeyWord: searchKeyword,
    };

    selectedFilters?.forEach((filter) => {
      const key = filter.key;
      data[key] = filter.value;
    });

    try {
      const response = await axios({
        method: "post",
        url: url,
        headers: headers,
        data: data,
      });

      if (initialRowCount == null) {
        setInitialRowCount(response.data.rowCount || 0);
      }
      if (response.data) {
        setUserList(response.data?.individualResponseDTOS || []);
        setRowCount(response.data?.rowCount || 0);
      } else {
        console.error("Unexpected API response format:", response);
      }
    } catch (error) {
      console.error("Error fetching users:", error);
    } finally {
      setLoading(false);
    }
  };


  const [listValues, setListValues] = useState(null);

  useEffect(() => {
    const locationUUIDsKeys =
      searchData?.locationUUIDs?.map((item) => item.key) || [];
    setMatchedLocations(locationUUIDsKeys);
    const serviceTypeUUIDsKeys =
      searchData?.serviceTypeUUIDs?.map((item) => item.key) || [];
    setMatchedServices(serviceTypeUUIDsKeys);

    const leadStatusUUIDsKeys =
      searchData?.leadStatusUUIDs?.map((item) => item.key) || [];
    setMatchedLeadStatus(leadStatusUUIDsKeys);

    const leadPriorityUUIDsKeys =
      searchData?.leadPriorityUUIDs?.map((item) => item.key) || [];
    setMatchedLeadPriority(leadPriorityUUIDsKeys);

    const assignedToUUIDsKeys =
      searchData?.assignedToUUIDs?.map((item) => item.key) || [];
    setMatchedAssignedTo(assignedToUUIDsKeys);

    const portalsRegisteredUUIDsKeys =
      searchData?.portalsRegisteredUUIDs?.map((item) => item.key) || [];
    setPortalsRegistered(portalsRegisteredUUIDsKeys);

    let buildingAgeUUIDsKeys = [];

    if (Array.isArray(searchData?.buildingAge)) {
      // If buildingAge is already an array, map through it
      buildingAgeUUIDsKeys = searchData.buildingAge.map((item) => item.key);
    } else if (
      typeof searchData?.buildingAge === "object" &&
      searchData?.buildingAge !== null
    ) {
      // If it's an object, wrap it in an array
      buildingAgeUUIDsKeys = [searchData.buildingAge.key];
    } else if (typeof searchData?.buildingAge === "string") {
      // If it's a string, wrap it in an array as a single value
      buildingAgeUUIDsKeys = [searchData.buildingAge];
    }

    setMatchedBuildingAge(buildingAgeUUIDsKeys);
  }, [searchData, listValues]);

  const handleToggle = () => {
    setExpanded(!expanded);
  };

  useEffect(() => {
    axios({
      method: "post",
      url: getUrl(authConfig.allListNamesValues) + "?selectType=LIST_VALUES",
    })
      .then((res) => {
        setListValues(res.data.data);
        window.localStorage.setItem(
          authConfig.listValues,
          JSON.stringify(res.data.data)
        );
      })
      .catch((err) => console.log("List values error", err));
  }, []);

  const [userId, setUserId] = useState(null);

  useEffect(() => {
    if (!!currentRow && !!currentRow?.basicProfileData) {
      setUserId(currentRow?.id);
    }
  }, [currentRow]);

  const [pleaseVerifyEmailMessage, setPleaseVerifyEmailMessage] =
    useState(false);

  const [disableVerifyEmailButton, setDisableVerifyEmailButton] =
    useState(false);
  const [showForm, setShowForm] = useState(false);
  const [showOTPOptions, setShowOTPOptions] = useState(false);
  const [selectedColumns, setSelectedColumns] = useState(columns);
  const [empanelDialog, setEmpanelDialog] = useState(false);

  const [strategicDialog, setStrategicDialog] = useState(false);

  const [strategicDialogOpen, setStrategicDialogOpen] = useState(false);

  const [isListingEmpanelled, setIsListingEmpanelled] = useState(false);
  const [isMicrositeEmpanelled, setIsMicrositeEmpanelled] = useState(false);

  const [anchorEl, setAnchorEl] = React.useState(null);
  const [entityType, setEntityType] = useState("");

  const [selectedRoleDialog, setSelectedRoleDialog] = useState(false);
  const [submitSuccess, setSubmitSuccess] = useState(false);
  const [role, setRole] = useState(null);

  const open = Boolean(anchorEl);
  const [activity, setActivity] = useState(false);

  const [showImageUrl, setShowImageUrl] = useState(false);
  const [imageUrl, setImageUrl] = useState("");
  const [messageStatus, setMessageStatus] = useState(null); // 'success', 'error', or null
  const [selectedTemplateParams, setSelectedTemplateParams] = useState([]);
  const [viewModeOpen, setViewModeOpen] = useState(false);
  const [requirementName, setRequirementName] = useState("");

  const [showSocietyName, setShowSocietyName] = useState(false);
  const [showServiceRequirement, setShowServiceRequirement] = useState(false);
  const [isButtonDisabled, setIsButtonDisabled] = useState(true);
  const [showGoogleFormLink, setShowGoogleFormLink] = useState(false);
  const [showLocation, setShowLocation] = useState(false);

  const [missingInfoDialogOpen, setMissingInfoDialogOpen] = useState(false);
  const [missingInfoMessage, setMissingInfoMessage] = useState("");

  const [missingPhoneNumbersDialogOpen, setMissingPhoneNumbersDialogOpen] =
    useState(false);
  const [
    missingPhoneNumbersEmailsDialogOpen,
    setMissingPhoneNumbersEmailsDialogOpen,
  ] = useState(false);
  const [selectedSociety, setSelectedSociety] = useState("");
  const [listOfSocieties, setListOfSocieties] = useState([]);
  const [showMeasurements, setShowMeasurements] = useState(false);
  const [measurements, setMeasurements] = useState("");
  const handleCloseMenu = () => {
    setAnchorEl(null);
  };
  const handleActionsClick = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMessageClose = () => {
    setSubmitSuccess(false);
    setIsListingEmpanelled(false);
    setIsMicrositeEmpanelled(false);
    setPleaseVerifyEmailMessage(false);
  };

  const handleStrategicDialogClose = () => {
    setStrategicDialogOpen(false);
  };

  const handleYes = async () => {
    setSubmitSuccess(false);
    let successFlag = true;
    let allAlreadyEmpanelled = true;
    const successIds = [];
    const failedIds = [];
    const updatePromises = selectedRows.map((row) => {
      if (!row.isListingEmpanelled || !row.isMicrositeEmpanelled) {
        allAlreadyEmpanelled = false;
        const fields = {
          userId: row.id,
          isListingEmpanelled: row.isListingEmpanelled
            ? true
            : isListingEmpanelled,
          isMicrositeEmpanelled: row.isMicrositeEmpanelled
            ? true
            : isMicrositeEmpanelled,
        };

        return auth
          .updateIsEmpanelled(fields)
          .then((response) => {
            successIds.push(row.id); // Add to success list
            return response;
          })
          .catch((error) => {
            console.error(`API call failed for userId: ${row.id}`, error);
            failedIds.push(row.id); // Add to failed list
            successFlag = false;
          });
      }
    });

    setEmpanelDialog(false);
    await Promise.all(updatePromises);

    let message = `<div><h3>`;
    if (allAlreadyEmpanelled) {
      message += `All Selected Rows are Already Empanelled!`;
    } else if (successFlag) {
      message += `Empanelled success for ${successIds?.length} user(s).`;
      if (failedIds?.length > 0) {
        message += ` Failed for ${failedIds?.length} user(s).`;
      }
    } else {
      message += `Empanelment failed for ${failedIds?.length} user(s).`;
    }
    message += `</h3></div>`;

    setDialogMessage(message);
    setSubmitSuccess(true);
    setSelectedRows([]);

    fetchUsers(page, pageSize, roleFilter, searchKeyword, selectedFilters);
  };

  const handleNo = async () => {
    setSubmitSuccess(false);
    let successFlag = true;
    let allAlreadyUnEmpanelled = true;
    const successIds = [];
    const failedIds = [];

    const updatePromises = selectedRows.map((row) => {
      if (row.isListingEmpanelled || row.isMicrositeEmpanelled) {
        allAlreadyUnEmpanelled = false;
        const fields = {
          userId: row.id,
          isListingEmpanelled: isListingEmpanelled
            ? false
            : row.isListingEmpanelled,

          isMicrositeEmpanelled: isMicrositeEmpanelled
            ? false
            : row.isMicrositeEmpanelled,
        };

        return auth
          .updateIsEmpanelled(fields)
          .then((response) => {
            successIds.push(row.id); // Add to success list
            return response;
          })
          .catch((error) => {
            console.error(`API call failed for userId: ${row.id}`, error);
            failedIds.push(row.id); // Add to failed list
            successFlag = false;
          });
      }
    });

    setEmpanelDialog(false);
    await Promise.all(updatePromises);

    let message = `<div><h3>`;
    if (allAlreadyUnEmpanelled) {
      message += `All Selected Rows are Already UnEmpanelled!`;
    } else if (successFlag) {
      message += `Unempanelled success for ${successIds?.length} user(s).`;
      if (failedIds?.length > 0) {
        message += ` Failed for ${failedIds?.length} user(s).`;
      }
    } else {
      message += `Unempanelment failed for ${failedIds?.length} user(s).`;
    }
    message += `</h3></div>`;

    setDialogMessage(message);
    setSubmitSuccess(true);
    setSelectedRows([]);

    fetchUsers(page, pageSize, roleFilter, searchKeyword, selectedFilters);
  };

  const handleAssignStrategic = async () => {
    setStrategicDialogOpen(false);
    let successFlag = true;
    let allAlreadyStrategicPartnered = true;
    const updatePromises = selectedRows.map((row) => {
      if (!row.isStrategicPartner) {
        allAlreadyStrategicPartnered = false;
        const fields = {
          userId: row.id,
          isStrategicPartner: true,
        };

        // Return the update promise
        return auth
          .updateIsStrategicPartner(fields)
          .then((response) => {
            return response; // This will be used to check if at least one call was successful
          })
          .catch((error) => {
            successFlag = false;
            console.error("Employee Details failed", error);
          });
      }
    });

    setStrategicDialog(false);

    const responses = await Promise.all(updatePromises);

    if (allAlreadyStrategicPartnered) {
      const message = `<div> <h3>Action Not Required</h3><p>Selected entities are already strategic partners.</p></div>
        `;

      setDialogMessage(message);
      setStrategicDialogOpen(true);
      setSelectedRows([]);
    } else if (successFlag) {
      const message = `<div><h3>Update Successful</h3><p>Status updated to strategic partner.</p></div>

      `;
      setDialogMessage(message);
      setStrategicDialogOpen(true);
      setSelectedRows([]);
    } else {
      const message = `<div>
        <h3>Update Unsuccessful</h3>
        <p>The system encountered an issue while updating the status. Please try again.</p>
      </div>      
      `;
      setDialogMessage(message);
      setStrategicDialogOpen(true);
    }

    if (responses.some((response) => response !== null)) {
      fetchUsers(page, pageSize, roleFilter, searchKeyword, selectedFilters);
    }
  };

  const handleRemoveStrategic = async () => {
    setStrategicDialogOpen(false);
    let successFlag = true;
    let allAlreadyStrategicPartnered = true;
    const updatePromises = selectedRows.map((row) => {
      if (row.isStrategicPartner) {
        allAlreadyStrategicPartnered = false;
        const fields = {
          userId: row.id,
          isStrategicPartner: false,
        };

        // Return the update promise
        return auth
          .updateIsStrategicPartner(fields)
          .then((response) => {
            return response; // This will be used to check if at least one call was successful
          })
          .catch((error) => {
            successFlag = false;
            console.error("Employee Details failed", error);
          });
      }
    });

    setStrategicDialog(false);

    const responses = await Promise.all(updatePromises);

    if (allAlreadyStrategicPartnered) {
      const message = `<div>
        <h3>Action Not Required</h3>
        <p>No changes were made as the selected entity or entities are not designated as strategic partners.</p>
      </div>`;
      setDialogMessage(message);
      setStrategicDialogOpen(true);
      setSelectedRows([]);
    } else if (successFlag) {
      const message = `<div>
        <h3>Update Successful</h3>
        <p>The strategic partner status has been successfully removed.</p>
      </div>
      `;
      setDialogMessage(message);
      setStrategicDialogOpen(true);
      setSelectedRows([]);
    } else {
      const message = `<div>
        <h3>Update Unsuccessful</h3>
        <p>The system encountered an issue while updating the status. Please try again.</p>
      </div>
      `;
      setDialogMessage(message);
      setStrategicDialogOpen(true);
    }

    if (responses.some((response) => response !== null)) {
      fetchUsers(page, pageSize, roleFilter, searchKeyword, selectedFilters);
    }
  };

  const handleSelection = (selectionModel) => {
    // Assuming `userList` is an array of objects and each object has a unique `id` that corresponds to the `selectionModel`
    const selectedData = userList.filter((row) =>
      selectionModel.includes(row.id)
    );
    const selectedRoles = selectedData.map((data) => data.role);
    setRole(selectedRoles);
    setSelectedRows(selectedData);
  };

  const handleSociety = () => {
    if (role.includes("Society")) {
      setDialogMessage(
        "Please deselect Society Profile(s) to Empanel/UnEmpanel Service Provider(s)"
      );
      setSelectedRoleDialog(true);
    }
  };

  const [whatsappOpenDialog, setWhatsappOpenDialog] = useState(false);
  const [isBulkAction, setIsBulkAction] = useState(false);
  const [whatsappNumberUnavailableDialog, setWhatsappNumberUnavailableDialog] =
    useState(false);
  const handleWhatsAppMessages = () => {
    const missingMobileNumbers = selectedRows.filter(
      (row) => !row.mobileNumber
    );

    if (missingMobileNumbers?.length > 0) {
      const missingNames = missingMobileNumbers.map(
        (row) => row.firstName || row.companyName
      );
      setMissingPhoneNumbers(missingNames);
      setShowMissingNumbersDialog(true);
    } else {
      sendWhatsAppMessagesToUsersWithNumbers(selectedRows);
    }
  };

  const sendWhatsAppMessagesToUsersWithNumbers = (rows) => {
    // Implement your logic to send WhatsApp messages to users with numbers
    console.log("Sending WhatsApp messages to users with numbers:", rows);
  };

  // const handleCloseDialog = () => {
  //   setShowMissingNumbersDialog(false);
  // };

  const handleWhatsappMessagesOptionClick = () => {
    setIsBulkAction(true); // Bulk action
    setWhatsappOpenDialog(true);
    handleCloseMenu();
  };
  const [detailsExpanded, setDetailsExpanded] = useState(false);

  const toggleDetails = () => {
    setDetailsExpanded(!detailsExpanded);
  };

  const [error, setError] = useState(false);

  // Handle template change
  const handleTemplateChange = (event) => {
    const templateName = event.target.value;
    setSelectedTemplateName(templateName);
    setError(templateName === "");

    const selectedTemplate = templates.find(
      (template) => template.name === templateName
    );

    if (selectedTemplate) {
      setSelectedTemplateParams(selectedTemplate.parameters || []);
      setShowSocietyName(
        selectedTemplate.parameters.some(
          (param) => param.paramName === "society_name"
        )
      );
      setShowServiceRequirement(
        selectedTemplate.parameters.some(
          (param) => param.paramName === "service_requirement"
        )
      );
      setShowMeasurements(
        selectedTemplate.parameters.some(
          (param) => param.paramName === "measurements"
        )
      );
      setShowGoogleFormLink(
        selectedTemplate.parameters.some(
          (param) => param.paramName === "slot_url"
        )
      );
      setShowLocation(
        selectedTemplate.parameters.some(
          (param) => param.paramName === "location"
        )
      );
      setShowImageUrl(
        selectedTemplate.parameters.some(
          (param) => param.paramName === "image_url"
        )
      );
    } else {
      setSelectedTemplateParams([]);
      setShowSocietyName(false);
      setShowServiceRequirement(false);
      setShowMeasurements(false);
      setShowGoogleFormLink(false);
      setShowLocation(false);
      setShowImageUrl(false);
    }

    // Validate button state
    const isAllFilled =
      templateName &&
      (!showSocietyName || selectedSociety) &&
      (!showServiceRequirement || requirementName) &&
      (!showMeasurements || measurements) &&
      (!showGoogleFormLink || googleFormLink) &&
      (!showImageUrl || imageUrl) &&
      (!showLocation || location);
    setIsButtonDisabled(!isAllFilled);

    setViewModeOpen(true);
  };

  const renderViewModeDialog = () => (
    <Dialog
      open={viewModeOpen}
      onClose={() => setViewModeOpen(false)}
      maxWidth="sm"
      fullWidth
      sx={{
        "& .MuiDialog-paper": {
          width: "500px",
          maxWidth: "100%",
        },
      }}
    >
      <DialogTitle>Template Parameters</DialogTitle>
      <DialogContent>
        {selectedTemplateParams?.length > 0 ? (
          <Box>
            <Typography variant="h6">Parameters:</Typography>
            <ul>
              {selectedTemplateParams.map((param, index) => (
                <li key={index}>
                  <Typography variant="body1">
                    <strong>{param.name}:</strong> {param.value}
                  </Typography>
                </li>
              ))}
            </ul>
          </Box>
        ) : (
          <Typography variant="body1">
            No parameters available for this template.
          </Typography>
        )}
      </DialogContent>
      <DialogActions>
        <Button onClick={() => setViewModeOpen(false)}>Close</Button>
      </DialogActions>
    </Dialog>
  );

  const getServiceTypeValue = (id) => {
    const service = allServicesList.find((service) => service.id === id);
    return service ? service.listValue : "";
  };

  const sendMessage = async () => {
    setError(!selectedSociety);
    if (!selectedTemplateName || !currentRow.mobileNumber) {
      setDialogMessage("Template or WhatsApp number is missing");
      setMessageStatus("error");
      return;
    }

    const whatsappNumber = currentRow.mobileNumber;
    const templateName = selectedTemplateName;
    const broadcastName = selectedTemplateName;
    const parameters = [
      { name: "name", value: currentRow.firstName },
      {
        name: "company_name",
        value: currentRow.basicProfileData?.companyName || "N/A",
      },
      { name: "society_name", value: selectedSociety },
      {
        name: "service_requirement",
        value: getServiceTypeValue(requirementName),
      },
      { name: "measurements", value: measurements },
      { name: "location", value: location },
      { name: "slot_url", value: googleFormLink },
      { name: "image_url", value: imageUrl },
    ];

    sendWatiMessage(
      whatsappNumber,
      templateName,
      broadcastName,
      parameters,
      (error) => {
        setDialogMessage("Error sending message. Please try again.");
        setMessageStatus("error");
      },
      (data) => {
        if (data.validWhatsAppNumber) {
          setDialogMessage(
            `WhatsApp message successfully delivered to ${
              currentRow.basicProfileData?.companyName || "their company"
            }
            (${currentRow.firstName} ${
              currentRow.lastName
            } - ${whatsappNumber}).`
          );
          setMessageStatus("success");
        } else {
          setDialogMessage(
            `WhatsApp message not delivered to unregistered number ${currentRow.basicProfileData?.companyName} (${currentRow.firstName} ${currentRow.lastName}).`
          );
          setMessageStatus("error");
        }
      }
    );

    // Reset fields after sending the message
    setSelectedTemplateName("");
    setSelectedTemplateParams([]);
    setShowSocietyName(false);
    setShowServiceRequirement(false);
    setShowMeasurements(false);
    setShowGoogleFormLink(false);
    setShowLocation(false);
    setShowImageUrl(false);
    setSelectedSociety("");
    setRequirementName("");
    setIsButtonDisabled(true);
  };

  const sendMessages = async () => {
    const missingMobileNumbers = selectedRows.filter(
      (row) => !row.mobileNumber
    );

    if (missingMobileNumbers?.length > 0) {
      const missingNames = missingMobileNumbers.map(
        (row) => row.firstName || row.companyName
      );

      // Show dialog with missing phone number details
      setDialogMessage(
        `Mobile number is required for: ${missingNames.join(", ")}`
      );
      setMessageStatus("error");
      return; // Stop execution here if any mobile numbers are missing
    }

    // If all selected rows have mobile numbers, proceed to send messages
    sendMessagesToUsersWithNumbers(selectedRows);

    const usersWithoutPhoneNumbers = selectedRows.filter(
      (row) => !row.mobileNumber
    );

    const inActiveRows = selectedRows.filter(
      (row) => row.status === "INACTIVE"
    );

    if (usersWithoutPhoneNumbers?.length > 0 && inActiveRows?.length > 0) {
      const names = usersWithoutPhoneNumbers.map(
        (row) => row.basicProfileData?.companyName || row.firstName
      );
      setMissingPhoneNumbers(names);
      setRadioValue("");
      setShowMissingNumbersDialog(true);
      return;
    }

    if (usersWithoutPhoneNumbers > 0) {
      setActivity(true);
      const names = usersWithoutPhoneNumbers.map(
        (row) => row.basicProfileData?.companyName || row.firstName
      );
      setMissingPhoneNumbers(names);
      setRadioValue("");
      setShowMissingNumbersDialog(true);
      return;
    }

    if (inActiveRows > 0) {
      setNumberAvalabilty(true);
      setRadioValue("");
      setShowMissingNumbersDialog(true);
      return;
    }

    sendMessagesToUsersWithNumbers(selectedRows);
  };

  const sendMessagesToUsersWithNumbers = async (rows) => {
    const templateName = selectedTemplateName;
    const broadcastName = selectedTemplateName;
    let sentMessages = [];
    let notSentMessages = [];
    let invalidWhatsAppNumbers = [];

    for (const row of rows) {
      if (row.mobileNumber) {
        const params = [
          { name: "name", value: row.firstName },
          { name: "society_name", value: selectedSociety },
          {
            name: "service_requirement",
            value: getServiceTypeValue(requirementName),
          },
          { name: "measurements", value: measurements },
          { name: "location", value: location },
          { name: "slot_url", value: googleFormLink },
          { name: "image_url", value: imageUrl },
        ];

        await sendWatiMessage(
          row.mobileNumber,
          templateName,
          broadcastName,
          params,
          (error) => {
            notSentMessages.push({
              name: row.firstName + " " + row.lastName || "",
              mobileNumber: row.mobileNumber,
              companyName: row.basicProfileData?.companyName || "", // Ensure companyName is captured
              reason: "Failed to send message",
            });
          },
          (data) => {
            if (data.validWhatsAppNumber) {
              sentMessages.push({
                name: row.firstName + " " + row.lastName || "",
                mobileNumber: row.mobileNumber,
                companyName: row.basicProfileData?.companyName || "", // Ensure companyName is captured
              });
            } else {
              invalidWhatsAppNumbers.push({
                name: row.firstName + " " + row.lastName || "",
                mobileNumber: row.mobileNumber,
                companyName: row.basicProfileData?.companyName || "", // Ensure companyName is captured
              });
            }
          }
        );
      } else {
        notSentMessages.push({
          name: row.firstName + " " + row.lastName || "",
          mobileNumber: "N/A", // Handle the case where the number is missing
          companyName: row.basicProfileData?.companyName || "", // Ensure companyName is captured
          reason: "Missing mobile number",
        });
      }
    }

    const sentList = sentMessages
      .map(
        (entry, index) =>
          `${index + 1}. ${entry.companyName || "No Company Name"} (${
            entry.name
          } - ${entry.mobileNumber || "N/A"})`
      )
      .join("\n");

    const notSentList = notSentMessages
      .map(
        (entry, index) =>
          `${index + 1}. ${entry.companyName || "No Company Name"} (${
            entry.name
          } - ${entry.mobileNumber || "N/A"}) - ${entry.reason}`
      )
      .join("\n");

    const invalidList = invalidWhatsAppNumbers
      .map(
        (entry, index) =>
          `${index + 1}. ${entry.companyName || "No Company Name"} (${
            entry.name
          } - ${entry.mobileNumber || "N/A"})`
      )
      .join("\n");

    let message = "";

    if (sentList) {
      message += `WhatsApp messages sent successfully to:\n${sentList}\n`;
    }

    if (notSentList) {
      message += `\nMessages could not be sent to:\n${notSentList}\n`;
    }

    if (invalidList) {
      message += `\nThe below Mobile Numbers are Invalid or not registered with WhatsApp:\n${invalidList}`;
    }

    const formattedMessage = message.split("\n").map((line, index) => (
      <React.Fragment key={index}>
        {line}
        <br />
      </React.Fragment>
    ));

    setDialogMessage(formattedMessage);
    setMessageStatus("info");

    // Reset fields after sending the message
    setSelectedTemplateName("");
    setSelectedTemplateParams([]);
    setShowSocietyName(false);
    setShowServiceRequirement(false);
    setShowMeasurements(false);
    setShowGoogleFormLink(false);
    setShowLocation(false);
    setShowImageUrl(false);
    setSelectedSociety("");
    setRequirementName("");
    setIsButtonDisabled(true);
  };

  const token =
    "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.akjOAtxhzjTui78FKtgAWOgziULQsn0FoTnIAshlGTA";
  useEffect(() => {
    const fetchTemplates = async () => {
      try {
        const response = await fetch(
          "https://live-mt-server.wati.io/321777/api/v1/getMessageTemplates",
          {
            method: "GET",
            headers: {
              Authorization: `Bearer ${token}`,
              "Content-Type": "application/json",
            },
          }
        );
        const data = await response.json();

        const templateData = data.messageTemplates.map((template) => ({
          key: template?.elementName,
          value: template?.id,
          parameters: template.parameters || [], // Keep additional data if needed
          status: template.status,
        }));
        const filteredTemplates = templateData.filter(
          (template) => template.status !== "DELETED"
        );

        setTemplates(filteredTemplates);
      } catch (error) {}
    };

    fetchTemplates();
  }, [token]);

  const handleWhatsappOptionClick = () => {
    if (!currentRow?.mobileNumber) {
      setWhatsappNumberUnavailableDialog(true);
    } else {
      setIsBulkAction(false); // Individual action
      setWhatsappOpenDialog(true);
    }
    handleCloseMenu();
  };

  const handleClose = () => {
    setPleaseVerifyEmailMessage(false);
    setDisableVerifyEmailButton(false);
    setShowForm(false);
    setShowOTPOptions(false);

    reset({
      firstName: "",
      lastName: "",
      mobileNumber: "",
      email: "",
      entityType: "",
    });
    setEntityType("");
  };

  // const exportToCSV = (csvData, fileName) => {
  //   const message = `
  //     <div>
  //       <h3>The data have been exported successfully</h3>
  //     </div>
  //     `;
  //   setDialogMessage(message);
  //   setSubmitSuccess(true);

  //   const fileType =
  //     "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8";
  //   const fileExtension = ".xlsx";

  //   const filteredData = csvData.map((row) =>
  //     Object.keys(row)
  //       .filter((key) => selectedColumns.find((col) => col.field === key))
  //       .reduce((obj, key) => {
  //         obj[key] = row[key];
  //         return obj;
  //       }, {})
  //   );
  //   const ws = XLSX.utils.json_to_sheet(filteredData);

  //   const wb = { Sheets: { data: ws }, SheetNames: ["data"] };
  //   const excelBuffer = XLSX.write(wb, { bookType: "xlsx", type: "array" });
  //   const data = new Blob([excelBuffer], { type: fileType });
  //   const now = new Date();
  //   const year = now.getFullYear();
  //   const month = String(now.getMonth() + 1).padStart(2, "0");
  //   const day = String(now.getDate()).padStart(2, "0");
  //   const hours = String(now.getHours()).padStart(2, "0");
  //   const minutes = String(now.getMinutes()).padStart(2, "0");
  //   const seconds = String(now.getSeconds()).padStart(2, "0");

  //   const formattedDate = `${year}-${month}-${day}-${hours}-${minutes}-${seconds}`;

  //   const newFileName = `${fileName}_${formattedDate}${fileExtension}`;

  //   FileSaver.saveAs(data, newFileName);
  // };
  const handleColumnSelection = (columnField) => {
    setSelectedColumns((prevState) =>
      prevState.find((col) => col.field === columnField)
        ? prevState.filter((col) => col.field !== columnField)
        : [...prevState, columns.find((col) => col.field === columnField)]
    );
  };

  const handleSocietyDialogClose = () => {
    setSelectedRoleDialog(false);
  };

  const handleOpenDialog = () => {
    setOpenDialog(true);
    handleMenuClose();
  };

  useEffect(() => {
    reset(searchData);
  }, [searchData]);

  const handleRemoveFilter = (filterType, value = null) => {
    const updatedData = { ...searchData };

    if (typeof updatedData[filterType] === "boolean") {
      updatedData[filterType] = false;
    } else if (typeof updatedData[filterType] === "string") {
      updatedData[filterType] = "";
    } else {
      if (value !== null) {
        updatedData[filterType] = updatedData[filterType].filter(
          (item) => item.key !== value
        );
      }
    }

    setSearchData(updatedData);
    fetchUsers(page, pageSize, roleFilter, searchKeyword, selectedFilters);
  };
  const [showAllServices, setShowAllServices] = useState(false);
  const [showAllLocations, setShowAllLocations] = useState(false);
  const [showAllLeadStatus, setShowAllLeadStatus] = useState(false);
  const [showAllLeadPriority, setShowAllLeadPriority] = useState(false);
  const [showAllAssignedTo, setShowAllAssignedTo] = useState(false);
  const [showAllPortalsRegistered, setShowAllPortalsRegistered] =
    useState(false);
  const [showAllBuildingAge, setShowAllBuildingAge] = useState(false);

  const getVisibleChips = (dataArray, showAll) => {
    return showAll ? dataArray : dataArray.slice(0, 5);
  };

  const chipDisplayLimit = 5;

  const [showAllChips, setShowAllChips] = useState(false);

  // Number of chips to show initially
  const initialVisibleCount = 5;

  // Helper function to slice chips
  const visibleChips = (chips) =>
    showAllChips ? chips : chips.slice(0, initialVisibleCount);

  // Example UUID generation function
  function generateUUID() {
    return Math.random().toString(36).substr(2, 9);
  }

  const renderFilterSection = (
    label,
    data,
    matchedItems,
    showAll,
    setShowAll,
    handleRemoveFilter,
    UUIDs
  ) => {
    if (!data || data.length === 0) {
      return null;
    }

    const commonStyle = {
      fontSize: "12px",
      height: "24px",
      "& .MuiChip-label": {
        padding: "0 8px",
      },
      "& .MuiSvgIcon-root": {
        fontSize: "16px",
      },
    };

    return (
      <Box display="flex" flexDirection="column" gap={1}>
        <Box display="flex" flexWrap="wrap" gap={1} sx={{ marginTop: "6px" }}>
          {/* Label */}
          <Typography
            variant="subtitle1"
            sx={{
              fontSize: "12px",
              fontWeight: "bold",
              height: "24px",
              "& .MuiChip-label": {
                padding: "0 8px",
              },
              "& .MuiSvgIcon-root": {
                fontSize: "16px",
              },
            }}
          >
            {label}:
          </Typography>
          {/* Chips */}
          {getVisibleChips(matchedItems, showAll).map((item, index) => (
            <Chip
              key={index}
              label={item}
              onDelete={() => handleRemoveFilter(UUIDs, item)}
              variant="outlined"
              sx={commonStyle}
            />
          ))}
          {/* View All / View Less Button */}
          {matchedItems.length > 5 && (
            <Button
              size="small"
              onClick={() => setShowAll(!showAll)}
              sx={commonStyle}
            >
              {showAll ? "View Less ↑" : "View All ↓"}
            </Button>
          )}
        </Box>
      </Box>
    );
  };

  return (
    <>
      <Grid>
        <Dialog
          open={dialogOpen}
          aria-labelledby="alert-dialog-title"
          aria-describedby="alert-dialog-description"
          PaperProps={{
            sx: {
              p: (theme) => `${theme.spacing(2.5)} !important`,
              backgroundColor: (theme) => theme.palette.primary.background,
            },
          }}
        >
          <Box
            sx={{
              width: "100%",
              borderRadius: 1,
              textAlign: "center",
              border: (theme) => `1px solid ${theme.palette.divider}`,
              borderColor: "primary.main",
            }}
          >
            <DialogContent>
              <DialogContentText
                id="alert-dialog-description"
                color="primary.main"
              >
                <div dangerouslySetInnerHTML={{ __html: dialogMessage }} />
              </DialogContentText>
            </DialogContent>

            <DialogActions>
              <Button
                onClick={handleCloseDialog}
                style={{ margin: "10px auto", width: 100 }}
              >
                Okay
              </Button>
            </DialogActions>
          </Box>
        </Dialog>

        <Box
          sx={{
            width: "100%",
            borderRadius: 1,
            textAlign: "center",
            // border: (theme) => `2px solid ${theme.palette.divider}`,
            borderColor: "primary.dark",
          }}
        >
          <Dialog
            open={confirmationDialogOpen}
            aria-labelledby="alert-dialog-title"
            aria-describedby="alert-dialog-description"
            PaperProps={{
              sx: {
                p: (theme) => `${theme.spacing(2.5)} !important`,
                backgroundColor: (theme) => theme.palette.primary.background,
              },
            }}
          >
            <Box
              sx={{
                width: "100%",
                borderRadius: 1,
                textAlign: "center",
                border: (theme) => `1px solid ${theme.palette.divider}`,
                borderColor: "primary.main",
              }}
            >
              <DialogContent>
                <DialogContentText
                  id="alert-dialog-description"
                  color="primary.main"
                >
                  <div
                    dangerouslySetInnerHTML={{
                      __html: confirmationDialogMessage,
                    }}
                  />
                </DialogContentText>
              </DialogContent>
              <DialogActions
                sx={{
                  justifyContent: "center",
                }}
              >
                <Button
                  onClick={handleConfirmStatusChange}
                  style={{ margin: "0 10px auto", width: 100 }}
                  variant="contained"
                >
                  Yes
                </Button>

                <Button
                  onClick={handleCloseConfirmationDialog}
                  style={{ margin: "0 10px auto", width: 100 }}
                >
                  No
                </Button>
              </DialogActions>
            </Box>
          </Dialog>
        </Box>
        {/* <Box

            sx={{
              px: 6,
              rowGap: 2,
              columnGap: 4,
              display: "flex",
              flexWrap: "wrap",
              alignItems: "center",
              justifyContent: "space-between",
            }}
          > */}
        <Grid
          container
          sx={{
            py: 3,
            px: 6,
            rowGap: 2,
            columnGap: 4,
            display: "flex",
            flexWrap: "wrap",
            alignItems: "center",
            justifyContent: "space-between",
          }}
        >
          <Grid
            item
            xs={12}
            sm="auto"
            sx={{
              textAlign: "flex-start",
              ml: {
                xs: -7,
                sm: -1,
                lg: -1,
                xl: -1,
              },
            }}
          >
            <Typography variant="h6" fontWeight={"600"}>
              List of Societies
            </Typography>
          </Grid>

          <Grid
            item
            xs={13}
            sm={8}
            alignItems="center"
            justifyContent="flex-end"
            sx={{
              paddingTop: { xs: "16px", sm: "3px" },
              mr: "12px",
              ml: "2px",

              marginTop: { xs: "6px", sm: "0rem" },
              display: "flex",
              gap: 1,
            }}
          >
             <Grid item xs={12} sm="auto">
              <FormControl>
                <Controller
                  name="mainSearch"
                  control={control}
                  render={({ field: { onChange } }) => (
                    <TextField
                      id="mainSearch"
                      placeholder="Search by society name"
                      value={keyword}
                      onChange={(e) => {
                        onChange(e.target.value);
                        setKeyword(e.target.value);
                        setSearchKeyword(e.target.value);
                      }}
                      onKeyDown={(e) => {
                        if (e.key === "Enter") {
                          setSearchKeyword(keyword);
                          fetchUsers(
                            page,
                            pageSize,
                            roleFilter,
                            searchKeyword,
                            selectedFilters
                          );
                        }
                      }}
                      sx={{
                        "& .MuiInputBase-root": {
                          height: "40px",
                        },
                      }}
                      InputProps={{
                        endAdornment: (
                          <InputAdornment position="start">
                            <SearchIcon
                              sx={{
                                cursor: "pointer",
                                marginRight: "-15px",
                              }}
                              onClick={() => {
                                setSearchKeyword(keyword);
                                fetchUsers(
                                  page,
                                  pageSize,
                                  roleFilter,
                                  searchKeyword,
                                  selectedFilters
                                );
                              }}
                            />{" "}
                          </InputAdornment>
                        ),
                      }}
                    />
                  )}
                />
              </FormControl>
            </Grid>

            <AdvancedSearchNew
              open={addUserOpen}
              toggle={toggleAddUserDrawer}
              setSearchingState={setSearchingState}
              priorityData={priorityData}
              selectedFilters={selectedFilters}
              clearAllFilters={clearAllFilters}
              onApplyFilters={handleApplyFilters}
              employeesData={employeesData}
            />
            {/* <ExcelDownMenu>
                        {columns?.map((column) => (
                          <MenuItem key={column.field}>
                            {column.headerName === "Edit" ? null : (
                              <label>
                                <input
                                  type="checkbox"
                                  checked={
                                    !!selectedColumns.find(
                                      (col) => col.field === column.field
                                    )
                                  }
                                  onChange={() =>
                                    handleColumnSelection(column.field)
                                  }
                                />
                                {column.headerName}
                              </label>
                            )}
                          </MenuItem>
                        ))}
                        <Box sx={{ textAlign: "center", margin: 2 }}>
                          <Button
                            size="medium"
                            type="button"
                            variant="contained"
                            onClick={() => exportToCSV(userList, "users_data")}
                            disabled={selectedColumns?.length === 0}
                          >
                            Download
                          </Button>
                        </Box>
                      </ExcelDownMenu> */}

            {/* <Button
                      size="medium"
                      type="button"
                      variant="contained"
                      onClick={handleActionsClick}
                      sx={{
                        mr: {
                          xs: isExtraSmallScreen ? "0px" : "15px",
                          lg: "15px",
                        },
                        mt: {
                          xs: 0, 
                          sm: 0, 
                          lg: 0,
                        },

                        padding: {
                          xs: isSmallScreen
                            ? "5px 11px !important"
                            : "0.4375rem 1.1875rem",
                            sm: "0.4375rem 2.1875rem",

                        },
                      }}
                    >
                    
                      Actions
                    </Button>
                    <Menu
                      anchorEl={anchorEl}
                      open={open}
                      onClose={handleCloseMenu}
                    >
                      <Tooltip
                        title="Please select the profiles to enable WhatsApp messaging"
                        disableHoverListener={selectedRows?.length !== 0}
                      >
                        <span>
                          <MenuItem
                            onClick={handleWhatsappMessagesOptionClick}
                            disabled={selectedRows?.length === 0}
                          >
                            Send WhatsApp messages
                          </MenuItem>
                        </span>
                      </Tooltip>
                    </Menu> */}

            <Button
              aria-controls={opens ? "split-button-menu" : undefined}
              aria-haspopup="true"
              onClick={handleMenuClicks}
              variant="contained"
              sx={{
                whiteSpace: "nowrap",
                minWidth: { xs: "120px" },
                height: { xs: "33.5px", sm: "38px" },
                marginTop: {
                  xs: isExtraSmallScreen
                    ? "0.3rem !important"
                    : "1.5rem !important",
                  sm: "1rem",
                },
                mt: {
                  xs: 0,
                  sm: 0,
                  lg: 0,
                },
                padding: {
                  xs: isSmallScreen
                    ? "0px 10px !important"
                    : "0.4375rem 1.0000rem",
                  sm: "0.3375rem 1rem",
                },
              }}
            >
              Add New Society&nbsp;
              <Icon icon="tabler:chevron-down" />
            </Button>

            <Menu
              id="split-button-menu"
              anchorEl={anchorEls}
              open={opens}
              onClose={handleMenuCloses}
              PaperProps={{
                style: {
                  width: anchorEls ? anchorEls.clientWidth : undefined,
                },
              }}
            >
              <MenuItem
                onClick={() => {
                  handleOpenDialog(); // Call your dialog open function
                  handleMenuCloses(); // Close the menu
                }}
              >
                Short Form
              </MenuItem>
              {/* <MenuItem
                        onClick={() => {handleShortFormClick();
                          handleMenuCloses();
                        }}
                      >
                        Short Form V2
                      </MenuItem> */}

              {/* <MenuItem
                       onClick={()=> {handleLongFormClick();
                        handleMenuCloses();
                       }}
                      >
                        Long Form V1
                      </MenuItem>
                      <MenuItem
                      onClick={()=> {openSocietyEnrollmentDialog();
                        handleMenuCloses();
                      }}
                      >
                        Long Form V2
                      </MenuItem> */}
            </Menu>

            <LongFormDialog
              open={isLongFormDialogOpen}
              handleClose={closeLongFormDialog}
              fetchUsers={fetchUsers}
              page={page}
              pageSize={pageSize}
              roleFilter={roleFilter}
              currentRow={currentRow}
              employeesData={employeesData}
            />
            <SocietyEnrollmentDialog
              open={societyEnrollmentDialog}
              onClose={closeSocietyEnrollmentDialog}
            />
            <SocietyEnrollmentShortDialog
              open={societyEnrollmentShortDialog}
              onClose={closeSocietyEnrollmentShortDialog}
            />
          </Grid>
          {/* </Grid> */}
        </Grid>

        <Divider />
        <Menu
          anchorEl={openMenu}
          open={Boolean(openMenu)}
          onClose={handleMenuClose}
        >
          <MenuItem onClick={() => handleOptionSelect("SERVICE_PROVIDER")}>
            Service Provider
          </MenuItem>
          <MenuItem onClick={() => handleOptionSelect("SOCIETY")}>
            Society Member
          </MenuItem>
        </Menu>

        <CardContent>
          <Box sx={{ display: "flex", flexWrap: "wrap", mb: 2 }}>
            {selectedFilters?.map((filter) => {
              if (filter.label === "Priority" && Array.isArray(filter.value)) {
                // If the filter is for services and the value is an array of IDs
                return filter.value?.map((id) => {
                  // Find the corresponding object in servicesList for each ID
                  const matchedItem = priorityData.find(
                    (item) => item.value === id
                  );

                  // Use the key of matchedItem if found, otherwise display the ID itself
                  const displayValue = matchedItem ? matchedItem.key : id;

                  return (
                    <Chip
                      key={`${filter.key}-${id}`} // Ensure unique key for each chip
                      label={`${filter.label}: ${displayValue}`}
                      onDelete={() => handleRemoveFilterCHS(filter.key, id)} // Pass both filter key and ID
                      sx={{ mr: 1, mb: 1 }}
                    />
                  );
                });
              }

              if (
                filter.label === "Assigned To" &&
                Array.isArray(filter.value)
              ) {
                // If the filter is for services and the value is an array of IDs
                return filter.value?.map((id) => {
                  // Find the corresponding object in servicesList for each ID
                  const matchedItem = employeesData.find(
                    (item) => item.id === id
                  );

                  // Use the key of matchedItem if found, otherwise display the ID itself
                  const displayValue = matchedItem ? matchedItem.name : id;

                  return (
                    <Chip
                      key={`${filter.key}-${id}`} // Ensure unique key for each chip
                      label={`${filter.label}: ${displayValue}`}
                      onDelete={() => handleRemoveFilterCHS(filter.key, id)} // Pass both filter key and ID
                      sx={{ mr: 1, mb: 1 }}
                    />
                  );
                });
              }

              // For other filters, render a single chip
              return (
                filter.label && ( // Only render the Chip if label is not null or undefined
                  <Chip
                    key={filter.key}
                    label={`${filter.label}: ${filter.value}`}
                    onDelete={() => handleRemoveFilterCHS(filter.key)}
                    sx={{ mr: 1, mb: 1 }}
                  />
                )
              );
            })}
            {(fromDate || toDate) && (
              <Chip
                label={
                  <>
                    <Typography
                      component="span"
                      sx={{ fontWeight: "bold", color: "primary.main" }}
                    >
                      {(() => {
                        if (employee)
                          return `Societies onboarded  by ${
                            employeesData?.find((item) => item.id === employee)
                              ?.name || ""
                          }`;
                        else
                          return `Total Societies Onboarded from ${fromDate} to ${toDate}`;
                      })()}
                    </Typography>{" "}
                  </>
                }
                color="primary"
                variant="outlined"
                sx={{ mb: 3 }}
                // onDelete={clearFilterSR}
              />
            )}
          </Box>
          <div style={{ height: 380, width: "100%" }}>
            {loading ? (
              <Box
                display="flex"
                justifyContent="center"
                alignItems="center"
                height="60vh"
              >
                <FallbackSpinner />
              </Box>
            ) : (
              <DataGrid
                rows={userList || []}
                columns={columns}
                getRowId={(row) => row.id}
                checkboxSelection
                pagination
                pageSize={pageSize}
                page={page - 1}
                // onRowClick={handleRowClick}
                rowsPerPageOptions={rowsPerPageOptions}
                rowCount={rowCount}
                paginationMode="server"
                onPageChange={handlePageChange}
                onPageSizeChange={handlePageSizeChange}
                onSelectionModelChange={handleSelection}
                rowHeight={38}
                headerHeight={38}
                components={{
                  NoRowsOverlay: () => (
                    <Typography
                      variant="body1"
                      align="center"
                      sx={{ marginTop: "120px" }}
                    >
                      {userList?.length === 0 ? "No Data" : "No Rows"}
                    </Typography>
                  ),
                }}
              />
            )}
          </div>
        </CardContent>

        <CreateUser
          selectedOption={selectedOption}
          openDialog={openDialog}
          handleDialogClose={handleDialogClose}
          fetchUsers={fetchUsers}
          page={page}
          pageSize={pageSize}
          roleFilter={roleFilter}
          reset={reset}
        />

        <Dialog
          fullScreen
          open={isConversationDialogOpen}
          onClose={closeConversationDialog}
        >
          <DialogTitle
            sx={{
              position: "relative",
              borderBottom: (theme) => `1px solid ${theme.palette.divider}`,
              p: (theme) => `${theme.spacing(1.5)} !important`,
              display: "flex",
              flexDirection: "row",
              alignItems: "center",
              justifyContent: "space-between",
            }}
            textAlign="center"
            fontSize="20px !important"
            fontWeight="bold"
          >
            <div
              style={{ flex: 1, display: "flex", justifyContent: "flex-start" }}
            >
              <Typography
                variant="h6"
                fontWeight="bold"
                display="flex-start"
                sx={{ fontSize: "18px", textAlign: "left", marginLeft: 4 }}
              >
                Conversation Details
              </Typography>
            </div>
            <div
              style={{
                display: "flex",
                alignItems: "center",
                justifyContent: "flex-end",
                gap: 2,
              }}
            >
              {/* <CloseExpandIcons
                expanded={expanded}
                onToggle={handleToggle}
                sx={{ mt: 4 }}
              /> */}
              <IconButton
                size="small"
                onClick={closeConversationDialog}
                sx={{
                  // p: "0.438rem",
                  borderRadius: 1,
                  mt: 5,
                  mb: 3,
                  mr: { xl: 5, lg: 5.2, md: 5, sm: 4.2, xs: 4 },
                  color: "common.white",
                  backgroundColor: "primary.main",
                  "&:hover": {
                    backgroundColor: "#66BB6A",
                    transition: "background 0.5s ease, transform 0.5s ease",
                  },
                }}
              >
                <Icon icon="tabler:x" fontSize="1rem" />
              </IconButton>
            </div>
          </DialogTitle>

          <DialogContent>
            <IndexCHS
              currentRow={currentRow}
              setCurrentRow={setCurrentRow}
              expanded={expanded}
              closeConversationDialog={close}
              employeeData={employeesData}
            />
          </DialogContent>

          <DialogActions sx={{ justifyContent: "center" }}>
            <Grid item xs={12} sx={{ mt: 2 }}>
              {/* <Button
                size="medium"
                sx={{ mr: 3 }}
                onClick={() => handleClose()}
                variant="outlined"
                color="primary"
              >
                Close
              </Button> */}
            </Grid>
          </DialogActions>
        </Dialog>
        <Dialog
          open={showMissingNumbersDialog}
          onClose={() => setShowMissingNumbersDialog(false)}
          aria-labelledby="alert-dialog-title"
          aria-describedby="alert-dialog-description"
          maxWidth="sm"
          fullWidth
        >
          <DialogTitle id="alert-dialog-title" style={{ fontWeight: "bold" }}>
            Phone Numbers and Activity Status
          </DialogTitle>
          {!activity && (
            <DialogContent
              dividers
              style={{ overflowY: "auto", maxHeight: 300 }}
            >
              {" "}
              {/* Adjust maxHeight as needed */}
              <DialogContentText id="alert-dialog-description">
                <strong>
                  The following Service Providers do not have phone numbers:
                </strong>
                <ul>
                  {missingPhoneNumbers.map((name, index) => (
                    <li key={index}>{name}</li>
                  ))}
                </ul>
              </DialogContentText>
            </DialogContent>
          )}
          <hr />
          {!numberAvalabilty && (
            <DialogContent>
              {" "}
              {/* This part remains fixed and not scrollable */}
              <DialogContentText style={{ fontWeight: "bold" }}>
                Send message to inactive users as well?
              </DialogContentText>
              <RadioGroup
                aria-label="inactive-users"
                name="inactive-users"
                value={radioValue}
                onChange={handleRadioChange}
              >
                <FormControlLabel value="yes" control={<Radio />} label="Yes" />
                <FormControlLabel value="no" control={<Radio />} label="No" />
              </RadioGroup>
            </DialogContent>
          )}
          <DialogActions>
            <Button onClick={() => setShowMissingNumbersDialog(false)}>
              Cancel
            </Button>
            <Button
              onClick={() => {
                setShowMissingNumbersDialog(false);
                if (radioValue === "no") {
                  sendMessagesToUsersWithNumbers(
                    selectedRows.filter((row) => row.isActive)
                  );
                } else {
                  sendMessagesToUsersWithNumbers(selectedRows);
                }
                setActivity(false);
                setNumberAvalabilty(false);
              }}
              variant="contained"
              color="primary"
              disabled={!radioValue}
            >
              Send Messages
            </Button>
          </DialogActions>
        </Dialog>

        <Dialog
          open={whatsappNumberUnavailableDialog}
          onClose={() => setWhatsappNumberUnavailableDialog(false)}
          aria-labelledby="alert-dialog-title"
          aria-describedby="alert-dialog-description"
          PaperProps={{
            sx: {
              p: (theme) => `${theme.spacing(2.5)} !important`,
              backgroundColor: (theme) => theme.palette.text.primary,
            },
          }}
        >
          <Box
            sx={{
              width: "100%",
              borderRadius: 1,
              textAlign: "center",
              border: (theme) => `1px solid ${theme.palette.divider}`,
              borderColor: "primary.main",
            }}
          >
            <DialogContent>
              <DialogContentText
                id="alert-dialog-description"
                color="primary.main"
              >
                Mobile number is required for{" "}
                {currentRow?.firstName || currentRow?.companyName}.
              </DialogContentText>
            </DialogContent>
            <DialogActions
              sx={{
                display: "flex",
                justifyContent: "center",
              }}
            >
              <Button
                onClick={() => setWhatsappNumberUnavailableDialog(false)}
                sx={{ margin: "auto", width: 100 }}
              >
                Okay
              </Button>
            </DialogActions>
          </Box>
        </Dialog>

        {/* LongForm */}
        <Dialog fullScreen open={isEditDialogOpens} onClose={closeEditDialogs}>
          <DialogTitle
            sx={{
              position: "relative",
              borderBottom: (theme) => `1px solid ${theme.palette.divider}`,
              p: (theme) => `${theme.spacing(1.5)} !important`,
              display: "flex",

              flexDirection: "row", // Align items side by side
              alignItems: "center", // Center items vertically
              justifyContent: "space-between", // Space out the elements
              height: "50px", // height
            }}
            textAlign="center" // text align to left
            fontSize="20px !important"
            fontWeight="bold"
          >
            <Typography sx={{ fontWeight: "bold", mb: 4, fontSize: "20px" }}>
              {/* {currentRow?.userCategory} */}
              Society
            </Typography>

            <Box sx={{ position: "absolute", top: "9px", right: "14px" }}>
              <IconButton
                size="small"
                onClick={closeEditDialogs}
                sx={{
                  // p: "0.438rem",
                  borderRadius: 1,
                  color: "common.white",
                  backgroundColor: "primary.main",
                  "&:hover": {
                    backgroundColor: "#66BB6A",
                    transition: "background 0.5s ease, transform 0.5s ease",
                  },
                }}
              >
                <Icon icon="tabler:x" fontSize="1rem" />
              </IconButton>
            </Box>
          </DialogTitle>

          <DialogContent>
            <DialogContent>
              <Box>
                <Grid container spacing={2} className="match-height">
                  <Grid item xs={12}>
                    <Grid
                      container
                      spacing={2}
                      sx={{
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "space-between",
                      }}
                    >
                      <Grid item xs={12} sx={{ position: "relative" }}>
                        <Box sx={{ position: "absolute", top: 1, right: 0 }}>
                          <CloseExpandIcons
                            expanded={expanded}
                            onToggle={handleToggle}
                          />
                        </Box>
                      </Grid>
                    </Grid>
                  </Grid>
                </Grid>
              </Box>
            </DialogContent>
          </DialogContent>
          <DialogActions></DialogActions>
        </Dialog>

        <Dialog
          open={submitSuccess || pleaseVerifyEmailMessage}
          aria-labelledby="alert-dialog-title"
          aria-describedby="alert-dialog-description"
          PaperProps={{
            sx: {
              p: (theme) => `${theme.spacing(2.5)} !important`,
              backgroundColor: (theme) => theme.palette.primary.background,
            },
          }}
          onClose={(event, reason) => {
            if (reason == "backdropClick") {
              handleMessageClose();
            }
          }}
        >
          <Box
            sx={{
              width: "100%",
              borderRadius: 1,
              textAlign: "center",
              border: (theme) => `1px solid ${theme.palette.divider}`,
              borderColor: "primary.main",
            }}
          >
            <DialogContent>
              <DialogContentText
                id="alert-dialog-description"
                color="primary.main"
              >
                <div dangerouslySetInnerHTML={{ __html: dialogMessage }} />
              </DialogContentText>
            </DialogContent>
            <DialogActions
              style={{ display: "flex", justifyContent: "center" }}
            >
              <Button
                variant="contained"
                onClick={handleMessageClose}
                sx={{ margin: "auto", width: 100 }}
              >
                Okay
              </Button>
            </DialogActions>
          </Box>
        </Dialog>

        <Dialog
          open={selectedRoleDialog}
          aria-labelledby="alert-dialog-title"
          aria-describedby="alert-dialog-description"
          PaperProps={{
            sx: {
              p: (theme) => `${theme.spacing(2.5)} !important`,
              backgroundColor: (theme) => theme.palette.primary.background,
            },
          }}
          //disableEscapeKeyDown
          onClose={(event, reason) => {
            if (reason === "backdropClick") {
              handleSocietyDialogClose();
            }
          }}
        >
          <Box
            sx={{
              width: "100%",
              borderRadius: 1,
              textAlign: "center",
              border: (theme) => `1px solid ${theme.palette.divider} `,
              borderColor: "primary.main",
            }}
          >
            <DialogContent>
              <DialogContentText
                id="alert-dialog-description"
                color="primary.main"
              >
                <div dangerouslySetInnerHTML={{ __html: dialogMessage }} />
              </DialogContentText>
            </DialogContent>
            <DialogActions
              style={{ display: "flex", justifyContent: "center" }}
            >
              <Button
                variant="contained"
                onClick={handleSocietyDialogClose}
                sx={{
                  margin: "10px",
                  backgroundColor: "primary.main",
                  "&:disabled": {
                    backgroundColor: "white",
                    color: "grey",
                  },
                }}
              >
                Okay
              </Button>
            </DialogActions>
          </Box>
        </Dialog>
        <Dialog
          open={whatsappOpenDialog}
          onClose={handleCloseDialog}
          maxWidth="sm"
          fullWidth={!isBulkAction}
          PaperProps={{
            sx: {
              display: "flex",
              flexDirection: "column",
              height: isBulkAction ? "50vh" : "60vh",
              width: isBulkAction ? "60vw" : "60vw",
            },
          }}
        >
          <DialogTitle
            sx={{
              position: "relative",
              borderBottom: (theme) => `1px solid ${theme.palette.divider}`,
              p: (theme) => `${theme.spacing(1.75, 4)} !important`,
              display: "flex",
              alignItems: "center",
              justifyContent: { xs: "start" },
              fontSize: { xs: 19, md: 20 },
            }}
            textAlign={"center"}
          >
            <Box
              sx={{
                marginLeft: 3,
                fontSize: {
                  xs: 14, // Font size for extra-small screens
                  sm: 17, // Font size for small screens
                  md: 17, // Font size for medium screens
                  lg: 17, // Font size for large screens
                  xl: 17, // Font size for extra-large screens
                },
              }}
            >
              Send Message via WhatsApp
            </Box>
            <Box
              sx={{
                justifyContent: "flex-end",
                position: "absolute",
                top: {
                  xs: "3.5px",
                  sm: "5px",
                  md: "5px",
                  lg: "5px",
                  xl: "6px",
                },
                right: "10px",
                marginRight: 5.4,
              }}
            >
              <IconButton
                size="small"
                onClick={handleCloseDialog}
                sx={{
                  // p: "0.438rem",
                  borderRadius: 1,
                  color: "common.white",
                  backgroundColor: "primary.main",
                  "&:hover": {
                    backgroundColor: "#66BB6A",
                    transition: "background 0.5s ease, transform 0.5s ease",
                  },
                }}
              >
                <Icon icon="tabler:x" fontSize="1rem" />
              </IconButton>
            </Box>
          </DialogTitle>
          <DialogContent
            sx={{
              position: "relative",
              p: (theme) => `${theme.spacing(10, 8)} !important`,
            }}
          >
            <FormControl fullWidth sx={{ mt: 2 }} error={!templates?.length}>
              <Controller
                name="select-label"
                control={control}
                render={({ field }) => (
                  <SelectAutoComplete
                    id="select-label"
                    label="Select a Template"
                    value={selectedTemplateName}
                    nameArray={templates}
                    onChange={(e) => {
                      field.onChange(e);
                      handleTemplateChange(e);
                      setSelectedTemplateName(e.target.value); // Update local state
                    }}
                  />
                )}
              />
            </FormControl>

            {!isBulkAction && (
              <Box mt={2}>
                <Typography
                  variant="body1"
                  sx={{
                    display: "flex",
                    justifyContent: "space-between",
                    alignItems: "center",
                    cursor: "pointer",
                    fontWeight: "bold",
                  }}
                  onClick={toggleDetails}
                >
                  CHS details:
                  {detailsExpanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
                </Typography>
                {!detailsExpanded && (
                  <Box mt={2}>
                    {/* Align Name and Services directly under Service provider details */}
                    <Typography variant="body1" sx={{ mt: 1 }}>
                      <strong>Name:</strong> {currentRow?.firstName || ""}
                    </Typography>
                    <Typography variant="body1" sx={{ mt: 1 }}>
                      <strong>Services:</strong>{" "}
                      {currentRow?.basicProfileData?.servicesProvided?.length >
                      0 ? (
                        <ul
                          style={{
                            listStyle: "none",
                            padding: 0,
                            marginTop: "5px",
                          }}
                        >
                          {currentRow.basicProfileData.servicesProvided
                            .map((serviceId) => getServiceTypeValue(serviceId))
                            .filter(Boolean)
                            .map((serviceName, index) => (
                              <li key={index} style={{ marginBottom: "5px" }}>
                                {serviceName}
                              </li>
                            ))}
                        </ul>
                      ) : (
                        <span>No services provided</span>
                      )}
                    </Typography>
                  </Box>
                )}
              </Box>
            )}
            <Box mt={2}>
              {showSocietyName && (
                <FormControl
                  fullWidth
                  sx={{ mt: 2 }}
                  error={Boolean(errors.societyName)}
                >
                  <Autocomplete
                    id="society-name-autocomplete"
                    options={societyOptions}
                    getOptionLabel={(option) => option.key}
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        label="Select Society"
                        size="small"
                        error={Boolean(errors.societyName)}
                        helperText={errors.societyName?.message}
                      />
                    )}
                    value={
                      societyOptions.find(
                        (option) => option.value === selectedSociety
                      ) || null
                    }
                    onChange={(event, newValue) => {
                      setSelectedSociety(newValue?.value || "");
                      setError(newValue?.value.trim() === "");
                      const isAllFilled =
                        selectedTemplateName &&
                        (!showSocietyName || newValue?.value) &&
                        (!showServiceRequirement || requirementName) &&
                        (!showMeasurements || measurements) &&
                        (!showLocation || location) &&
                        (!showImageUrl || imageUrl) &&
                        (googleFormLink || !showGoogleFormLink);
                      setIsButtonDisabled(!isAllFilled);
                    }}
                  />
                  {error && (
                    <FormHelperText style={{ color: "red" }}>
                      This field is required
                    </FormHelperText>
                  )}
                </FormControl>
              )}

              {showServiceRequirement && (
                <FormControl
                  fullWidth
                  error={Boolean(errors.serviceType)}
                  sx={{ mt: 2 }}
                >
                  <Autocomplete
                    id="serviceType"
                    options={allServicesList}
                    getOptionLabel={(option) => option.listValue}
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        label="Service requirement"
                        size="small"
                        error={Boolean(errors.serviceType)}
                        helperText={errors.serviceType?.message}
                      />
                    )}
                    value={
                      allServicesList.find(
                        (service) => service.id === requirementName
                      ) || null
                    }
                    onChange={(event, newValue) => {
                      setRequirementName(newValue?.id || "");
                      const isAllFilled =
                        selectedTemplateName &&
                        (!showSocietyName || selectedSociety) &&
                        (!showServiceRequirement || newValue?.id) &&
                        (!showMeasurements || measurements) &&
                        (!showLocation || location) &&
                        (!showImageUrl || imageUrl) &&
                        (googleFormLink || !showGoogleFormLink);
                      setIsButtonDisabled(!isAllFilled);
                    }}
                  />
                  {error && (
                    <FormHelperText style={{ color: "red" }}>
                      This field is required
                    </FormHelperText>
                  )}
                </FormControl>
              )}

              {showMeasurements && (
                <FormControl
                  fullWidth
                  error={Boolean(errors.measurements)}
                  sx={{ mt: 2 }}
                >
                  <TextField
                    id="measurements"
                    label="Measurements"
                    size="small"
                    value={measurements}
                    onChange={(e) => {
                      setMeasurements(e.target.value);
                      const isAllFilled =
                        selectedTemplateName &&
                        (!showSocietyName || selectedSociety) &&
                        (!showServiceRequirement || requirementName) &&
                        (!showMeasurements || e.target.value) &&
                        (!showLocation || location) &&
                        (!showImageUrl || imageUrl) &&
                        (googleFormLink || !showGoogleFormLink);
                      setIsButtonDisabled(!isAllFilled);
                    }}
                    error={Boolean(errors.measurements)}
                    helperText={errors.measurements?.message}
                  />
                  {error && (
                    <FormHelperText style={{ color: "red" }}>
                      This field is required
                    </FormHelperText>
                  )}
                </FormControl>
              )}
              {showLocation && (
                <FormControl
                  fullWidth
                  sx={{ mt: 2 }}
                  error={Boolean(errors.location)}
                >
                  <Controller
                    name="location"
                    control={control}
                    rules={{ required: true }}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        label="Society Location"
                        InputLabelProps={{ shrink: true }}
                        size="small"
                        onChange={(e) => {
                          setLocation(e.target.value);
                          const isAllFilled =
                            selectedTemplateName &&
                            (!showSocietyName || selectedSociety) &&
                            (!showServiceRequirement || requirementName) &&
                            (!showMeasurements || measurements) &&
                            (!showLocation || e.target.value) &&
                            (!showImageUrl || imageUrl) &&
                            (googleFormLink || !showGoogleFormLink);
                          setIsButtonDisabled(!isAllFilled);
                        }}
                        placeholder="Click on the icon to navigate & paste the URL here"
                        error={Boolean(errors.location)}
                        helperText={errors.location?.message}
                        aria-describedby="validation-basic-location"
                        InputProps={{
                          endAdornment: (
                            <InputAdornment position="end">
                              <Tooltip title="Click to navigate to Google Maps">
                                <IconButton
                                  sx={{ cursor: "pointer" }}
                                  onClick={navigateToGoogleMaps}
                                  edge="end"
                                >
                                  <LocationOnTwoToneIcon />
                                </IconButton>
                              </Tooltip>
                            </InputAdornment>
                          ),
                        }}
                      />
                    )}
                  />
                </FormControl>
              )}
              {showImageUrl && (
                <FormControl
                  fullWidth
                  sx={{ mt: 2 }}
                  error={Boolean(errors.imageUrl)}
                >
                  <TextField
                    id="imageUrl"
                    label="Image URL"
                    size="small"
                    InputLabelProps={{ shrink: true }}
                    value={imageUrl}
                    onChange={(e) => {
                      setImageUrl(e.target.value);
                      const isAllFilled =
                        selectedTemplateName &&
                        (!showSocietyName || selectedSociety) &&
                        (!showServiceRequirement || requirementName) &&
                        (!showMeasurements || measurements) &&
                        (!showLocation || location) &&
                        (!showImageUrl || e.target.value) &&
                        (googleFormLink || !showGoogleFormLink);
                      setIsButtonDisabled(!isAllFilled);
                    }}
                    placeholder="Enter the URL for the image here"
                    error={Boolean(errors.imageUrl)}
                    helperText={errors.imageUrl?.message}
                  />
                </FormControl>
              )}
              {showGoogleFormLink && (
                <FormControl
                  fullWidth
                  sx={{ mt: 2 }}
                  error={Boolean(errors.googleFormLink)}
                >
                  <TextField
                    id="googleFormLink"
                    label="Google Form Link"
                    size="small"
                    InputLabelProps={{ shrink: true }}
                    value={googleFormLink}
                    onChange={(e) => {
                      setGoogleFormLink(e.target.value);
                      const isAllFilled =
                        selectedTemplateName &&
                        (!showSocietyName || selectedSociety) &&
                        (!showServiceRequirement || requirementName) &&
                        (!showMeasurements || measurements) &&
                        (!showLocation || location) &&
                        (!showImageUrl || imageUrl) &&
                        (e.target.value || !showGoogleFormLink);
                      setIsButtonDisabled(!isAllFilled);
                    }}
                    placeholder="Enter the Google Form URL here"
                    error={Boolean(errors.googleFormLink)}
                    helperText={errors.googleFormLink?.message}
                  />
                </FormControl>
              )}
            </Box>
          </DialogContent>

          <DialogActions
            sx={{
              justifyContent: "end",
              borderTop: (theme) => `1px solid ${theme.palette.divider}`,
              p: (theme) => `${theme.spacing(2.5)} !important`,
            }}
          >
            <Button onClick={handleCloseDialog}>Cancel</Button>
            {isBulkAction ? (
              <Button
                // onClick={() => {
                //   const missingMobileNumbers = selectedRows.filter(
                //     (row) => !row.mobileNumber
                //   );

                //   if (missingMobileNumbers?.length > 0) {
                //     const missingNames = missingMobileNumbers.map(
                //       (row) => row.firstName || row.companyName
                //     );
                //     setMissingPhoneNumbers(missingNames);
                //     setMissingPhoneNumbersDialogOpen(true);
                //   } else {
                //     sendMessagesToUsersWithNumbers(selectedRows);
                //   }
                // }}
                disabled={isButtonDisabled || selectedRows?.length === 0}
                variant="contained"
                color="primary"
                sx={{
                  marginRight: 5.2,
                }}
              >
                Send Messages
              </Button>
            ) : (
              <Button
                //onClick={sendMessage}
                disabled={isButtonDisabled || !currentRow?.mobileNumber}
                variant="contained"
                color="primary"
              >
                Send Message
              </Button>
            )}
          </DialogActions>
        </Dialog>

        <Dialog
          open={messageStatus !== null}
          onClose={() => setMessageStatus(null)}
          aria-labelledby="alert-dialog-title"
          aria-describedby="alert-dialog-description"
          PaperProps={{
            sx: {
              p: (theme) => `${theme.spacing(2.5)} !important`,
              backgroundColor: (theme) => theme.palette.text.primary.main,
              color: (theme) => theme.palette.text.secondary,
            },
          }}
        >
          <Box
            sx={{
              width: "100%",
              borderRadius: 1,
              textAlign: "center",
              border: (theme) => `1px solid ${theme.palette.divider}`,
              borderColor: "primary.main",
            }}
          >
            <DialogContent>
              <DialogContentText
                id="alert-dialog-description"
                color="primary.main"
                fontWeight={"bold"}
              >
                {dialogMessage}
              </DialogContentText>
            </DialogContent>
            <DialogActions
              sx={{
                display: "flex",
                justifyContent: "center",
              }}
            >
              <Button
                onClick={() => {
                  setMessageStatus(null);
                  handleCloseDialog();
                  handleCloseMenu(); // Close WhatsApp dialog as well
                }}
              >
                OK
              </Button>
            </DialogActions>
          </Box>
        </Dialog>

        <Dialog
          open={missingPhoneNumbersDialogOpen}
          onClose={() => setMissingPhoneNumbersDialogOpen(false)}
          aria-labelledby="alert-dialog-title"
          aria-describedby="alert-dialog-description"
          PaperProps={{
            sx: {
              p: (theme) => `${theme.spacing(2.5)} !important`,
              backgroundColor: (theme) => theme.palette.primary.background,
            },
          }}
        >
          <Box
            sx={{
              width: "100%",
              borderRadius: 1,
              textAlign: "center",
              border: (theme) => `1px solid ${theme.palette.divider}`,
              borderColor: "primary.main",
            }}
          >
            <DialogContent>
              <DialogContentText
                id="alert-dialog-description"
                color="primary.main"
              >
                The following Service Providers do not have phone numbers:
                <ul>
                  {missingPhoneNumbers.map((name, index) => {
                    const provider = selectedRows.find(
                      (row) =>
                        row.basicProfileData?.companyName || row.firstName
                    );
                    return (
                      <li key={index}>
                        <strong>
                          {provider?.basicProfileData?.companyName || "N/A"}
                        </strong>
                      </li>
                    );
                  })}
                </ul>
              </DialogContentText>
            </DialogContent>
            <DialogActions
              sx={{
                display: "flex",
                justifyContent: "center",
              }}
            >
              <Button
                onClick={() => {
                  setMissingPhoneNumbersDialogOpen(false);
                }}
                sx={{
                  margin: "10px auto",
                  width: 100,
                }}
              >
                Okay
              </Button>
            </DialogActions>
          </Box>
        </Dialog>

        <Dialog
          open={missingInfoDialogOpen}
          onClose={() => setMissingInfoDialogOpen(false)}
          aria-labelledby="alert-dialog-title"
          aria-describedby="alert-dialog-description"
          PaperProps={{
            sx: {
              p: (theme) => `${theme.spacing(2.5)} !important`,
              backgroundColor: (theme) => theme.palette.primary.background,
            },
          }}
        >
          <Box
            sx={{
              width: "100%",
              borderRadius: 1,
              textAlign: "center",
              border: (theme) => `1px solid ${theme.palette.divider}`,
              borderColor: "primary.main",
            }}
          >
            <DialogContent>
              <DialogContentText
                id="alert-dialog-description"
                color="primary.main"
              >
                <div dangerouslySetInnerHTML={{ __html: missingInfoMessage }} />
              </DialogContentText>
            </DialogContent>
            <DialogActions
              style={{ display: "flex", justifyContent: "center" }}
            >
              <Button
                variant="contained"
                onClick={() => setMissingInfoDialogOpen(false)}
                sx={{ margin: "auto", width: 100 }}
              >
                Okay
              </Button>
            </DialogActions>
          </Box>
        </Dialog>
      </Grid>
    </>
  );
};

export default UsersOverView;