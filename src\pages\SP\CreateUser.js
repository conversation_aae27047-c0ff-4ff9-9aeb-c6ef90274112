// ** React Imports
import { useContext, useEffect, useState } from "react";
import { useRef } from "react";
import { useWatch } from "react-hook-form";

// ** MUI Imports
import CheckCircleOutlineIcon from "@mui/icons-material/CheckCircleOutline";
import Button from "@mui/material/Button";
import FormControl from "@mui/material/FormControl";
import Grid from "@mui/material/Grid";
import TextField from "@mui/material/TextField";
import ViewDialogByLocation from "src/@core/components/custom-components/ViewDialogByLocation";
import MobileNumberValidation from "src/@core/components/custom-components/MobileNumberValidation";
import MenuItem from "@mui/material/MenuItem";
import FormHelperText from "@mui/material/FormHelperText";

// ** Third Party Imports

import { Controller, useForm } from "react-hook-form";

import axios from "axios";
import { getUrl } from "src/helpers/utils";

// ** Hooks
// ** Icon Imports
import {
  CircularProgress,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  Typography,
  InputLabel,
  Select,
  Divider,
  Autocomplete,
} from "@mui/material";
import { Box } from "@mui/system";
import EmailTextField from "src/@core/components/custom-components/EmailTextField";
import NameTextField from "src/@core/components/custom-components/NameTextField";
import authConfig from "src/configs/auth";
import { AuthContext } from "src/context/AuthContext";
import { useAuth } from "src/hooks/useAuth";
import IconButton from "@mui/material/IconButton";
import Icon from "src/@core/components/icon";
import SelectAutoComplete from "src/@core/components/custom-components/SelectAutoComplete";
import MultiSelectAutoComplete from "src/@core/components/custom-components/MultiSelectAutoComplete";
import SelectAutoCompleteLocation from "src/@core/components/custom-components/SelectAutoCompleteLocation";
import { fetchIpAddress } from "src/@core/components/custom-components/FetchIpAddress";

const CreateUser = ({
  openDialog,
  searchData,
  handleDialogClose,
  page,
  pageSize,
  roleFilter,
  searchKeyword,
  fetchServiceProviders,
  selectedFilters,
}) => {
  const { getAllListValuesByListNameId, userSaveSpPost } =
    useContext(AuthContext);

  const [dialogMessage, setDialogMessage] = useState("");

  const [submitSuccess, setSubmitSuccess] = useState(false);

  //Hooks
  const auth = useAuth();

  const {
    register,
    handleSubmit,
    setError,
    control,
    clearErrors,
    reset,
    formData,
    formState: { errors },
    setValue,
  } = useForm({
    defaultValues: {
      firstName: "",
      lastName: "",
      email: "",
    },
  });
  const [allServicesList, setAllServicesList] = useState([]);

  const [selectedLocationId, setSelectedLocationId] = useState(
    formData?.locationId
  );
  const [selectedDesignationId, setSelectedDesignationId] = useState(
    formData?.designation
  );
  const [designationsData, setDesignationsData] = useState(null);

  const [email, setEmail] = useState("");
  const [isEmailVerified, setIsEmailVerified] = useState(false);
  const [showOTPOptions, setShowOTPOptions] = useState(false);
  const [otp, setOTP] = useState("");
  const [countdown, setCountdown] = useState(0);

  const [loading, setLoading] = useState(false);
  const [resend, setResend] = useState(false);
  const [loadingEmail, setLoadingEmail] = useState(false);
  const [loadingSave, setLoadingSave] = useState(false);

  //const [isSubmitting, setIsSubmitting] = useState(false);

  const [disableVerifyEmailButton, setDisableVerifyEmailButton] =
    useState(false);

  const [okayButtonClose, setOkayButtonClose] = useState(false);

  const handleOTPDialogClose = () => {
    setSubmitSuccess(false);
  };

  const handleCancelClick = () => {
    setIsEmailVerified(false);
    setDisableVerifyEmailButton(false);
    reset({ firstName: "", lastName: "", email: "" });
    setSelectedLocationId(null);
    setSelectedDesignationId(null);
    handleDialogClose();
  };

  const handleSelectDesignationChange = (event) => {
    const selectedId = event.target.value;
    setSelectedDesignationId(selectedId);
  };

  // const handleDesignationSuccess = (data) => {
  //   setDesignationsData(data?.listValues);
  // };
  const handleError = (error) => {
    console.error("Basic profile: All Services:", error);
  };

  const [locationsData, setLocationsData] = useState(null);

  const [selectedLocationName, setSelectedLocationName] = useState(null);
  const watchServicesProvided = useWatch({ control, name: "servicesProvided" });

  const isApiCalling = useRef(false);

  const designationName = designationsData?.find(
    (designation) => designation.id === selectedDesignationId
  )?.listValue;
  async function submit(data) {
    if (isApiCalling.current) {
      // API call is already in progress, return early
      return;
    }

    isApiCalling.current = true;
    setLoadingSave(true);

    const ipAddress = await fetchIpAddress();

    const shortFormData = {
      firstName: data?.firstName,
      lastName: data?.lastName,
      orgName: data?.companyName,
      locationId: data?.selectedLocationId,
      servicesProvided: data?.servicesProvided.map((service) => service.value),
      email: data?.email,
      mobileNumber: data?.mobileNumber,
      designation: data?.selectedDesignationId,
      otherDesignation: data?.otherDesignation,
      ipAddress:ipAddress
    };
    try {
      const response = await auth.SPCreate(shortFormData);

      if (response) {
        const message = `
          <div>
            <h3>User Created Successfully!</h3>
          </div>
        `;
        setLoadingSave(false);
        setDialogMessage(message);
        setSubmitSuccess(true);
        fetchServiceProviders(page, pageSize, roleFilter, searchKeyword, selectedFilters);
        reset({ firstName: "", lastName: "", email: "" });
        setIsEmailVerified(false);
        handleCancelClick();
      }
    } catch (error) {
      console.error("Error creating user:", error);
      const message = `
        <div>
          <h3>Submit failed, try again later.</h3>
        </div>
      `;

      setDialogMessage(message);
      setSubmitSuccess(true);
    } finally {
      setLoadingSave(false);
      isApiCalling.current = false;
    }
    reset({ firstName: "", lastName: "", email: "" });
    setIsEmailVerified(false);
    handleDialogClose();
    isApiCalling.current = false;
  }
  const handleSelectChange = (event) => {
    const selectedId = event.target.value;
    setSelectedLocationId(selectedId);
    const selectedLocation = locationsData.find(
      (location) => location.id === selectedId
    );
    const locationName = selectedLocation ? selectedLocation.listValue : "";
    setSelectedLocationName(locationName);
  };

  const servicesProvidedValues = watchServicesProvided?.map(
    (id) => allServicesList.find((service) => service.id === id)?.listValue
  );

  async function sendOTP(data) {
    if (isApiCalling.current) {
      // API call is already in progress, return early
      return;
    }

    isApiCalling.current = true;

    const ipAddress = await fetchIpAddress();
    setLoadingEmail(true);
    const fields = {
      contactType: "EMAIL",
      contactValue: data?.email,
      ipAddress: ipAddress,
    };

    try {
      const response = await auth.postOTP(fields);
      if (response?.isVerified) {
        const message = `
        <div>
        <h3>
        Email already exists!!!
        </h3>
        </div>
        `;
        setLoadingEmail(false);
        setOkayButtonClose(true);
        setDisableVerifyEmailButton(false);
        setDialogMessage(message);
        setSubmitSuccess(true);
      } else {
        setEmail(data?.email);
        const message = `
        <div>
        <h3>
        OTP has been sent to <span style="color:primary.main;">${data?.email}</span>  for verification. Please check.
        </h3>
        </div>
      `;
        setLoadingEmail(false);
        setDialogMessage(message);
        setSubmitSuccess(true);
        setCountdown(30);
        setShowOTPOptions(true);
        setOkayButtonClose(false);
      }
    } catch (error) {
      const message = `
      <div>
      <h3>
      Error sending OTP to your Email. <br> Please Enter Valid Email (or) Please try again.
      </h3>
      </div>
    `;
      setLoadingEmail(false);
      setDialogMessage(message);
      setDisableVerifyEmailButton(false);
      setOkayButtonClose(true);
      setSubmitSuccess(true);
    }

    isApiCalling.current = false; // Reset the isSendingOTP ref
  }

  async function verifyOtp() {
    setLoading(true);

    const data = { contactType: "EMAIL", contactValue: email, otp: otp };
    try {
      const response = await auth.verifyOTP(data);
      if (response) {
        setIsEmailVerified(true);
        setOTP("");
        setShowOTPOptions(false);

        const message = `
        <div>
        <h3>
        Email has been verified successfully.
        </h3>
        </div>
      `;
        setLoading(false);
        setDialogMessage(message);
        setOkayButtonClose(true);
        setSubmitSuccess(true);
        window.localStorage.setItem("verifiedEmail", email);
      }
    } catch (error) {
      console.error("Employee Creation failed:", error);
      const message = `
      <div>
      <h3>
      OTP doesn't match. Please try again.
      </h3>
      </div>

    `;
      setLoading(false);
      setDialogMessage(message);
      setSubmitSuccess(true);
    }
  }

  async function resendOTP(data) {
    if (isApiCalling.current) {
      // API call is already in progress, return early
      return;
    }
    isApiCalling.current = true;
    setResend(true);
    const ipAddress = await fetchIpAddress();
    setLoadingEmail(true);

    const fields = {
      contactType: "EMAIL",
      contactValue: data?.email,
      ipAddress: ipAddress,
    };

    try {
      const response = await auth.resendOTP(fields);
      if (response) {
        if (response?.isVerified) {
          const message = `
        <div>
        <h3>
        Email already exists!!!
        </h3>
        </div>
        `;
          setLoadingEmail(false);
          setOkayButtonClose(true);
          setDisableVerifyEmailButton(false);
          setDialogMessage(message);
          setSubmitSuccess(true);
        } else {
          setEmail(data?.email);
          const message = `
            <div>
            <h3>
            OTP has been sent to your Email for verification. Please check.
            </h3>
            </div>
          `;
          setLoadingEmail(false);
          setDialogMessage(message);
          setSubmitSuccess(true);
          setCountdown(30);
          setShowOTPOptions(true);
          setOkayButtonClose(false);
        }
      }
    } catch (error) {
      const message = `
        <div>
        <h3>
        Error sending OTP to your Email. <br> Please Enter Valid Email (or) Please try again.
        </h3>
        </div>
      `;
      setLoadingEmail(false);
      setDialogMessage(message);
      setDisableVerifyEmailButton(false);
      setOkayButtonClose(true);
      setSubmitSuccess(true);
    }

    isApiCalling.current = false;
  }

  useEffect(() => {
    if (countdown > 0) {
      const timerId = setTimeout(() => setCountdown(countdown - 1), 1000);

      return () => clearTimeout(timerId);
    }
  }, [countdown]);

  useEffect(() => {
    if (!!authConfig) {
      getAllListValuesByListNameId(
        authConfig.designationSPId,
        (data) =>
          setDesignationsData(
            data?.listValues.map((item) => ({
              value: item.id,
              key: item.listValue,
            }))
          ),
        handleError
      );
      getAllListValuesByListNameId(
        authConfig.allServicesListNameId,
        (data) =>
          setAllServicesList(
            data?.listValues.map((item) => ({
              value: item.id,
              key: item.listValue,
            }))
          ),
        handleError
      );
      getAllListValuesByListNameId(
        authConfig.locationlistNameId,
        (data) =>
          setLocationsData(
            data?.listValues.map((item) => ({
              value: item.id,
              key: item.listValue,
            }))
          ),
        handleError
      );
    }
  }, [authConfig, setValue]);

  return (
    <Box sx={{ pt: 3 }}>
      <Dialog
        open={submitSuccess}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
        PaperProps={{
          sx: {
            p: (theme) => `${theme.spacing(2.5)} !important`,
            backgroundColor: (theme) => theme.palette.primary.background,
          },
        }}
      >
        {showOTPOptions ? (
          <>
            <DialogTitle
              sx={{
                position: "relative",
                borderBottom: (theme) => `1px solid ${theme.palette.divider}`,
                p: (theme) => `${theme.spacing(1.75, 4.5)} !important`,
                display: "flex",
                alignItems: "center",
                justifyContent: { xs: "start" },

                height: "40px",
              }}
              textAlign={"center"}
            >
              {"Verify OTP"}
              <Box sx={{ position: "absolute", top: "1px", right: "10px" }}>
                <IconButton
                  size="small"
                  onClick={handleOTPDialogClose}
                  sx={{
                    position: "absolute",
                    top: "5px",
                    right: "6.5px",
                    borderRadius: 1,
                    color: "common.white",
                    backgroundColor: "primary.main",
                    "&:hover": {
                      backgroundColor: "#66BB6A",
                      transition: "background 0.5s ease, transform 0.5s ease",
                    },
                  }}
                >
                  <Icon icon="tabler:x" fontSize="1rem" />
                </IconButton>
              </Box>
            </DialogTitle>
            <DialogContent>
              <DialogContentText
                id="alert-dialog-description"
                color="primary.main"
              >
                <div dangerouslySetInnerHTML={{ __html: dialogMessage }} />
              </DialogContentText>
            </DialogContent>
          </>
        ) : (
          <Box
            sx={{
              width: "100%",
              borderRadius: 1,
              textAlign: "center",
              border: (theme) => `1px solid ${theme.palette.divider}`,
              borderColor: "primary.main",
            }}
          >
            <DialogContent>
              <DialogContentText
                id="alert-dialog-description"
                color="primary.main"
              >
                <div dangerouslySetInnerHTML={{ __html: dialogMessage }} />
              </DialogContentText>
            </DialogContent>
            {okayButtonClose && (
              <DialogActions>
                <Button
                  onClick={handleOTPDialogClose}
                  style={{ margin: "10px auto", width: 100 }}
                >
                  Okay
                </Button>
              </DialogActions>
            )}
          </Box>
        )}

        {showOTPOptions && (
          <Grid container spacing={5}>
            <Grid container justifyContent="center">
              <TextField
                id="otp"
                type="text"
                inputProps={{
                  inputMode: "numeric",
                  pattern: "[0-9]*",
                }}
                placeholder="OTP"
                value={otp}
                onChange={(e) => {
                  const value = e.target.value;
                  if (/^\d*$/.test(value) && value.length <= 6) {
                    setOTP(value);
                  }
                }}
                InputLabelProps={{ shrink: true }}
                error={Boolean(errors?.otp)}
                helperText={errors?.otp?.message}
                sx={{
                  borderRadius: "5px",
                  background: "white",
                }}
              />
            </Grid>

            <Grid item xs={12} sm={12}>
              <Grid container justifyContent="center">
                <Button
                  id="validateOtp"
                  variant="contained"
                  disabled={!otp || Boolean(errors?.otp)}
                  onClick={verifyOtp}
                  sx={{
                    marginBottom: "16px",
                    "&:disabled": { color: "primary.main" },
                  }}
                >
                  {loading ? (
                    <CircularProgress color="inherit" size={22} />
                  ) : (
                    "VALIDATE OTP"
                  )}
                </Button>
                <Button
                  id="resendOtp"
                  variant={countdown > 0 ? "outlined" : "contained"}
                  disabled={countdown > 0}
                  onClick={handleSubmit(sendOTP)}
                  sx={{
                    marginLeft: "7px",
                    marginBottom: "16px",
                    "&:disabled": { color: "primary.main" },
                  }}
                >
                  {resend ? (
                    <CircularProgress color="inherit" size={24} />
                  ) : (
                    "RESEND OTP"
                  )}
                </Button>
              </Grid>
              <Grid container justifyContent="center">
                {countdown > 0 && (
                  <Typography
                    variant="body1"
                    sx={{
                      marginTop: "2px",
                      marginBottom: "10px",
                      color: "primary.main",
                    }}
                  >
                    Resend OTP in: {countdown}s
                  </Typography>
                )}
              </Grid>
            </Grid>
          </Grid>
        )}
      </Dialog>

      <Dialog
        maxWidth="xs"
        scroll="paper"
        open={openDialog}
        onClose={(event, reason) => {
          if (reason !== "backdropClick") {
            handleCancelClick();
          }
        }}
        //   onClose={handleDialogClose}
      >
        <DialogTitle
          sx={{
            position: "relative",
            borderBottom: (theme) => `1px solid ${theme.palette.divider}`,
            p: (theme) => `${theme.spacing(1.75, 4.5)} !important`,
            display: "flex",
            alignItems: "center",
            justifyContent: { xs: "start" },

            height: "50px",
          }}
          textAlign={"center"}
        >
          <Box
            sx={{
              fontSize: {
                xs: 14, // Smaller font size for mobile
                sm: 15, // Slightly larger font size
                md: 17, // Default font size for larger screens
                lg: 16,
              },
              fontWeight: 600, // Bold text if needed
            }}
          >
            Creating User as Service Provider
          </Box>

          {/* {"Creating User as Service Provider"} */}
          <Box sx={{ position: "absolute", top: "9px", right: "10px" }}>
            <IconButton
              id="closeDialog"
              size="small"
              onClick={handleCancelClick}
              sx={{
                position: "absolute",
                top: "5px", // Keep the vertical positioning as is
                right: "6.5px",
                // p: "0.438rem",
                borderRadius: 1,
                color: "common.white",
                backgroundColor: "primary.main",
                "&:hover": {
                  backgroundColor: "#66BB6A",
                  transition: "background 0.5s ease, transform 0.5s ease",
                },
              }}
            >
              <Icon icon="tabler:x" fontSize="1rem" />
            </IconButton>
          </Box>
        </DialogTitle>
        <DialogContent
          sx={{
            position: "relative",
            p: (theme) => `${theme.spacing(4, 4)} !important`,
          }}
        >
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <FormControl fullWidth>
                <Controller
                  name="companyName"
                  control={control}
                  rules={{ required: true }}
                  render={({ field }) => (
                    <TextField
                      id="companyName"
                      {...field}
                      label="Name of the company"
                      InputLabelProps={{ shrink: true }}
                      size="small"
                      placeholder="Enter your company name"
                      error={Boolean(errors.companyName)}
                      helperText={errors.companyName?.message}
                      aria-describedby="validation-basic-companyName"
                      inputProps={{ maxLength: 50 }}
                    />
                  )}
                />
              </FormControl>
            </Grid>
            <Grid item xs={12}>
              <FormControl fullWidth error={Boolean(errors.selectedLocationId)}>
                <Controller
                  name="selectedLocationId"
                  control={control}
                  rules={{ required: "Location is required" }}
                  render={({ field }) => (
                    <SelectAutoCompleteLocation
                      id="selectedLocationId"
                      label="Location"
                      nameArray={locationsData}
                      value={field.value}
                      onChange={(e) => field.onChange(e.target.value)}
                    />
                  )}
                />
                {errors.selectedLocationId && (
                  <FormHelperText sx={{ color: "error.main" }}>
                    {errors.selectedLocationId.message}
                  </FormHelperText>
                )}
              </FormControl>
            </Grid>
            <Grid item xs={12}>
              <FormControl fullWidth error={Boolean(errors.servicesProvided)}>
                <Controller
                  name="servicesProvided"
                  control={control}
                  defaultValue={formData?.servicesProvided || []}
                  rules={{ required: "Services are required" }}
                  render={({ field }) => (
                    <MultiSelectAutoComplete
                      id="servicesProvided"
                      label="Type of Vendor"
                      nameArray={allServicesList}
                      value={field.value}
                      onChange={(e) => field.onChange(e.target.value)}
                    />
                  )}
                />
                {errors.servicesProvided && (
                  <FormHelperText sx={{ color: "error.main" }}>
                    {errors.servicesProvided.message}
                  </FormHelperText>
                )}
              </FormControl>
            </Grid>
            {servicesProvidedValues &&
              servicesProvidedValues.includes("Any other") && (
                <Grid item xs={12}>
                  <FormControl fullWidth>
                    <Controller
                      name="anyOtherServiceProvided"
                      control={control}
                      defaultValue={formData?.anyOtherServiceProvided || ""}
                      render={({ field }) => (
                        <TextField
                          id="anyOtherServiceProvided"
                          {...field}
                          label="Specify Profession Type"
                          InputLabelProps={{ shrink: true }}
                          size="small"
                          placeholder="Enter your profession type"
                          error={Boolean(errors.anyOtherServiceProvided)}
                          helperText={errors.anyOtherServiceProvided?.message}
                          aria-describedby="validation-basic-other-service"
                          value={field.value}
                        />
                      )}
                    />
                    {/* {errors.anyOtherServiceProvided && (
        <FormHelperText sx={{ color: "error.main" }}>
          {errors.anyOtherServiceProvided.message}
        </FormHelperText>
      )} */}
                  </FormControl>
                </Grid>
              )}
            <Grid item xs={12}>
              <FormControl fullWidth>
                <Controller
                  name="mobileNumber"
                  control={control}
                  rules={{ required: true }}
                  defaultValue={formData?.mobileNumber}
                  render={({ field }) => (
                    <MobileNumberValidation
                      id="mobileNumber"
                      {...field}
                      type="tel"
                      label="Mobile Number"
                      size="small"
                      error={Boolean(errors.mobileNumber)}
                      helperText={errors.mobileNumber?.message}
                      InputLabelProps={{ shrink: true }}
                      placeholder="+91 1234567890"
                      inputProps={{
                        maxLength: field?.value?.startsWith("+91") ? 13 : 10,
                      }}
                    />
                  )}
                />
              </FormControl>
            </Grid>
              <Grid item xs={12} sm={6} mt={2}>
                <FormControl fullWidth>
                  <Controller
                    name="firstName"
                    control={control}
                    rules={{ required: true }}
                    render={({ field }) => (
                      <NameTextField
                        {...field}
                        label="First Name"
                        InputLabelProps={{ shrink: true }}
                        size="small"
                        placeholder="Enter your name"
                        error={Boolean(errors.firstName)}
                        helperText={errors.firstName?.message}
                        aria-describedby="validation-basic-firstName"
                      />
                    )}
                  />
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={6} mt={2}>
                <FormControl fullWidth>
                  <Controller
                    name="lastName"
                    control={control}
                    rules={{ required: true }}
                    render={({ field }) => (
                      <NameTextField
                        {...field}
                        label="Last Name"
                        InputLabelProps={{ shrink: true }}
                        size="small"
                        placeholder="Enter your last name"
                        error={Boolean(errors.lastName)}
                        helperText={errors.lastName?.message}
                        aria-describedby="validation-basic-lastName"
                      />
                    )}
                  />
                </FormControl>
              </Grid>
            <Grid item xs={12}>
              <FormControl
                fullWidth
                error={Boolean(errors.selectedDesignationId)}
              >
                <Controller
                  name="selectedDesignationId"
                  control={control}
                  rules={{ required: "Designation is required" }}
                  render={({ field }) => (
                    <SelectAutoComplete
                      id="designation"
                      label="Designation"
                      nameArray={designationsData}
                      value={field.value}
                      onChange={(e) => field.onChange(e.target.value)}
                    />
                  )}
                />
                {errors.selectedDesignationId && (
                  <FormHelperText sx={{ color: "error.main" }}>
                    {errors.selectedDesignationId.message}
                  </FormHelperText>
                )}
              </FormControl>
            </Grid>

            {designationName === "Any Other" && (
              <Grid item xs={12} sm={4}>
                <FormControl fullWidth>
                  <Controller
                    name="otherDesignation"
                    control={control}
                    defaultValue={formData?.otherDesignation || ""}
                    render={({ field }) => (
                      <TextField
                        id="otherDesignation"
                        {...field}
                        label="Specify Designation"
                        InputLabelProps={{ shrink: true }}
                        size="small"
                        placeholder="Enter your designation"
                        error={Boolean(errors.otherDesignation)}
                        helperText={errors.otherDesignation?.message}
                        aria-describedby="validation-basic-other-designation"
                      />
                    )}
                  />
                  {errors.otherDesignation && (
                    <FormHelperText sx={{ color: "error.main" }}>
                      {errors.otherDesignation.message}
                    </FormHelperText>
                  )}
                </FormControl>
              </Grid>
            )}

            <Grid item xs={12} lg={9.7} md={9.7} sm={9.7}>
              {!isEmailVerified && (
                <>
                  <FormControl fullWidth>
                    <Controller
                      name="email"
                      control={control}
                      rules={{ required: true }}
                      render={({ field }) => (
                        <EmailTextField
                          id="email"
                          {...field}
                          type="email"
                          label="Email address"
                          size="small"
                          error={Boolean(errors.email)}
                          inputProps={{ maxLength: 50 }}
                          InputLabelProps={{ shrink: true }}
                          placeholder="Enter email address"
                          helperText={errors.email?.message}
                        />
                      )}
                    />
                  </FormControl>
                </>
              )}

              {isEmailVerified && (
                <>
                  <FormControl fullWidth>
                    <div style={{ display: "flex", alignItems: "baseline" }}>
                      <Typography sx={{ mt: 4, fontWeight: "bold" }}>
                        Email:
                      </Typography>
                      <Typography
                        variant="body1"
                        sx={{
                          marginLeft: "12px",
                        }}
                      >
                        {email}
                      </Typography>
                    </div>
                  </FormControl>
                </>
              )}
            </Grid>
            <Grid item xs={4} sm={2}>
              {!isEmailVerified ? (
                <>
                  <Button
                    id="verifyButton"
                    color="primary"
                    variant="contained"
                    onClick={handleSubmit(sendOTP)}
                    disabled={disableVerifyEmailButton}
                    sx={{
                      fontSize: "0.76rem !important",
                      padding: "6px 16px !important",
                    }}
                    //disabled={disableVerifyEmailButton || isSubmitting}
                  >
                    {loadingEmail ? (
                      <CircularProgress color="inherit" size={22} />
                    ) : (
                      "Verify"
                    )}
                  </Button>
                </>
              ) : (
                <Box sx={{ display: "flex", alignItems: "center", mt: 4 }}>
                  <CheckCircleOutlineIcon
                    sx={{
                      color: "green",
                    }}
                  />
                  <Typography
                    sx={{
                      marginLeft: "6px",
                      marginBottom: "-10px",
                      paddingBottom: 2,
                    }}
                  >
                    Verified
                  </Typography>
                </Box>
              )}
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions
          sx={{
            justifyContent: "end",
            borderTop: (theme) => `1px solid ${theme.palette.divider}`,
            p: (theme) => `${theme.spacing(2.5)} !important`,
            height: "50px", // Set fixed height for footer
          }}
        >
          <Grid item xs={12}>
            <center>
              <Button
                id="cancelButton"
                size="medium"
                sx={{ mr: 3 }}
                variant="outlined"
                color="primary"
                onClick={() => handleCancelClick()}
              >
                Cancel
              </Button>
              <Button
                id="saveButton"
                size="medium"
                type="button"
                variant="contained"
                disabled={!isEmailVerified}
                //disabled={isSubmitting || !isEmailVerified}
                onClick={handleSubmit(submit)}
                sx={{
                  mr: {
                    xs: 2.2,
                    sm: 2.2,
                    md: 2.2,
                    lg: 2.2,
                    xl: 2.2,
                  },
                }}
              >
                {loadingSave ? (
                  <CircularProgress color="inherit" size={22} />
                ) : (
                  "Save"
                )}
              </Button>
            </center>
          </Grid>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default CreateUser;