import { useContext, useEffect, useState, React } from "react";
import Drawer from "@mui/material/Drawer";
import Button from "@mui/material/Button";
import MenuItem from "@mui/material/MenuItem";
import { styled } from "@mui/material/styles";
import IconButton from "@mui/material/IconButton";
import Typography from "@mui/material/Typography";
import Box from "@mui/material/Box";
import Tooltip from "@mui/material/Tooltip";
import CustomAvatar from "src/@core/components/mui/avatar";
import SearchIcon from "@mui/icons-material/Search";
import UpdatedSelectAutoComplete from "src/@core/components/custom-components/UpdatedSelectAutoComplete";

import {
  Grid,
  FormControl,
  InputLabel,
  Select,
  FormHelperText,
  FormControlLabel,
  Checkbox,
  TextField,
  InputAdornment,
  Slider,
} from "@mui/material";
import { Controller, useForm } from "react-hook-form";
import PerfectScrollbar from "react-perfect-scrollbar";
import authConfig from "src/configs/auth";
import axios from "axios";
import { getAuthorizationHeaders, getUrl } from "src/helpers/utils";
import Icon from "src/@core/components/icon";
import { AuthContext } from "src/context/AuthContext";
import MultiSelectAutoComplete from "src/@core/components/custom-components/MultiSelectAutoComplete";

const Header = styled(Box)(({ theme }) => ({
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
  position: "relative",
  padding: theme.spacing(3),
  borderBottom: `1px solid ${theme.palette.divider}`,
}));

const AdvancedSearch = (props) => {
  const {
    
    open,
    toggle,
    searchKeyword,
    searchData,
    setSearchKeyword,
    setSearchData,
    fetchUsers,
    page,
    pageSize,
    error,
    setError,
    budgetValue,
    setBudgetValue,
    budget,
    setBudget,
    balanceBudgetValue,
    setBalanceBudgetValue,
    balanceBudget,
    setBalanceBudget,
    searchingState,
    setSearchingState,
  } = props;
  const { user, getAllListValuesByListNameId } = useContext(AuthContext);
  
  const {
    control,
    setValue,
    reset,
    getValues,
    register,
    formState: { errors },
  } = useForm({
    defaultValues: {
      socitiesUUIDs: [],
      leadStatusUUIDs: [],
      issuedDateFrom: "",
      issuedDateTo: "",
      searchKeyword: "",
      budget: "",
      balanceBudget:"",
      searchingState: true,
    },
  });

  const rupees = [
    {
      value: "HUNDRED",
      key: "hundred",
    },
    {
      value: "THOUSAND",
      key: "thousand",
    },
    {
      value: "LAKHS",
      key: "lakhs",
    },
    {
      value: "CRORES",
      key: "crores",
    },
  ];

  function valuetext(value) {
    return `${value}`;
  }

  const marks = Array.from({ length: 20 }, (_, i) => ({
    value: (i + 1) * 5,
    label: `${(i + 1) * 5}`,
  }));

  
  

  const handleChange = (event, newValue) => {
    setBudgetValue(newValue);
  };
  const handleBalanceChange = (event, newValue) => {
    setBalanceBudgetValue(newValue);
  }
  useEffect(() => {
    reset(searchData);
  }, [searchData, reset]);

  const handleCancel = () => {
    setSearchingState(false);
    setSearchKeyword("");
    setBudget("");
    setBudgetValue([20, 30]);
    setBalanceBudget("");
    setBalanceBudgetValue([20, 30]);
    setError(false);
    reset({
      socitiesUUIDs: [],
      leadStatusUUIDs: [],
      issuedDateFrom: "",
      issuedDateTo: "",    
      searchKeyword: ""
    });
    setSearchData({});
    fetchUsers(page, pageSize, searchKeyword);
  };

  const handleClose = () => {
    toggle();
  };

  const handleApply = () => {
    toggle();
    setSearchingState(true);
  };

  const [listOfEmployees, setListOfEmployees] = useState([]);
  const [employeesOptions, setEmployeesOptions] = useState([]);
  

  useEffect(() => {
    axios({
      method: "get",
      url: getUrl(authConfig.selectDropdownNew) + "?selectionType=EMPLOYEES",
      headers: getAuthorizationHeaders(),
    })
      .then((res) => {
        setListOfEmployees(res.data.data);
      })
      .catch((err) => console.log("Employees error", err));
  }, []);

  

  useEffect(() => {
    if (listOfEmployees.length) {
      const data = listOfEmployees.map((entry) => ({
        value: entry.id,
        key: entry.name,
      }));
      setEmployeesOptions(data);
    }
  }, [listOfEmployees]);

  const [employeesData, setEmployeesData] = useState([]);
  const [listOfSocieties, setListOfSocieties] = useState([]);
  
  const societyOptions = listOfSocieties
  .filter((society) => society?.name)
  .map((society) => ({
    value: society,
    key: society?.name,
  }));

useEffect(() => {
  const fetchSocieties = async () => {
    axios({
      method: "get",
      url: getUrl(authConfig.selectDropdownNew) + "?selectionType=SOCIETY_NAME",
      headers: getAuthorizationHeaders(),
    })
      .then((res) => {
        const metadataArray = res.data?.data?.map((item) => item?.metaData);

        setListOfSocieties(metadataArray);
      })
      .catch((err) => console.log("error", err));
  };
  fetchSocieties();
}, []);

  useEffect(() => {
    // Fetch all employees
    axios({
      method: "get",
      url: getUrl(authConfig.selectDropdownNew) + "?selectionType=EMPLOYEES",
      headers: getAuthorizationHeaders(),
    })
      .then((res) => {
        setEmployeesData(res.data.data);
      })
      .catch((err) => console.log("Employees error", err));
  }, []);

  const updateFilters = (data) => {
    setSearchData(data);
    fetchUsers(page, pageSize, searchKeyword, data);
  };

  const [leadStatusData, setLeadStatusData] = useState(null);
  const [services, setServices] = useState(null);
  const [assignedTo, setDesignation] = useState(null);

  const validateDates = () => {
    const fromDate = getValues('issuedDateFrom');
    const toDate = getValues('issuedDateTo');
    if ((fromDate && !toDate) || (!fromDate && toDate)) {
      setError(true);
    } else {
      setError(false);
    }
  };

  useEffect(() => {
    if (!!authConfig) {
      getAllListValuesByListNameId(
        authConfig.allServicesListNameId,
        (data) =>
          setServices(
            data?.listValues.map((item) => ({
              value: item.id,
              key: item.listValue,
            }))
          ),
        handleError
      );
    }
   
    if (!!authConfig) {
      getAllListValuesByListNameId(
        authConfig.designationId,
        (data) =>
          setDesignation(
            data?.listValues.map((item) => ({
              value: item.id,
              key: item.listValue,
            }))
          ),
        handleError
      );
    }
    if (!!authConfig) {
      getAllListValuesByListNameId(
        authConfig.statusListNamesId,
        (data) =>
          setLeadStatusData(
            data?.listValues.map((item) => ({
              value: item.id,
              key: item.listValue,
            }))
          ),
        handleError
      );
    }
    
  }, [authConfig]);

  const handleLeadStatusSuccess = (data) => setLeadStatusData(data?.listValues);
  const handleServices = (data) => setServices(data?.listValues);
  const handleError = (error) => console.error("Error:", error);
  const handleDesignations = (data) => setDesignation(data?.listValues)


  return (
    <>
      <Tooltip title="Advanced Search">
        <CustomAvatar
          variant="rounded"
          sx={{ width: 36, height: 36, cursor: "pointer" }}
          onClick={toggle}
        >
          <Icon icon="tabler:filter" fontSize={27} />
        </CustomAvatar>
      </Tooltip>

     

      <Drawer
        open={open}
        anchor="right"
        variant="temporary"
        onClose={handleClose}
        ModalProps={{ keepMounted: true }}
        sx={{ "& .MuiDrawer-paper": { width: { xs: "90%", sm: 500 } } }} 
      >
        <Header>
          <Typography variant="h5">Advanced Search</Typography>
          <Box sx={{ position: "absolute", top: "8px", right: "14px" }}>
            <IconButton
              size="small"
              onClick={handleClose}
              sx={{
                borderRadius: 1,
                color: "common.white",
                backgroundColor: "primary.main",
                "&:hover": {
                  backgroundColor: "#66BB6A",
                  transition: "background 0.5s ease, transform 0.5s ease",
                },
              }}
            >
              <Icon icon="tabler:x" fontSize="1rem" />
            </IconButton>
          </Box>
        </Header>
                <PerfectScrollbar options={{ wheelPropagation: false }}>
          <Box sx={{ p: (theme) => theme.spacing(4, 6) }}>
            <Grid container spacing={3} alignItems={"center"}>


            <Grid item xs={12} md={12}>
                <FormControl fullWidth>
                  <Controller
                    name="socitiesUUIDs"
                    control={control}
                    rules={{ required: false }}
                    render={({ field }) => (
                      <MultiSelectAutoComplete
                        id="society-select"
                        size="small"
                        label="Societies"
                        nameArray={societyOptions || []}
                        value={field.value || []}
                        onChange={(e) => {
                          field.onChange(e);
                          updateFilters({
                            ...getValues(),
                            socitiesUUIDs: e.target.value,
                          });
                        }}
                        error={Boolean(errors.service)}
                      />
                    )}
                  />
                </FormControl>
                {errors.leadPriority && (
                  <FormHelperText
                    sx={{ color: "error.main" }}
                    id="validation-society"
                  >
                    {errors.socitiesUUIDs?.message}
                  </FormHelperText>
                )}
              </Grid>


              {/* Status Multi-Select */}
                <Grid item xs={12} md={12}>
                  <FormControl fullWidth>
                    <Controller
                      name="leadStatusUUIDs"
                      control={control}
                      rules={{ required: false }}
                      render={({ field }) => (
                        <MultiSelectAutoComplete
                          size="small"
                          id="leadStatus"
                          label="Work Order Status"
                          nameArray={leadStatusData || []}
                          value={field.value || []}
                          onChange={(e) => {
                            field.onChange(e);
                            updateFilters({
                              ...getValues(),
                              leadStatusUUIDs: e.target.value,
                            });
                          }}
                          error={Boolean(errors.leadStatusUUIDs)}
                        />
                      )}
                    />
                  </FormControl>
                  {errors.leadStatus && (
                    <FormHelperText
                      sx={{ color: "error.main" }}
                      id="validation-leadStatus"
                    >
                      {errors.leadStatus?.message}
                    </FormHelperText>
                  )}
                </Grid>
           

              {/* Date Range From */}
              <Grid item xs={12} md={12}>
                <FormControl fullWidth>
                  <Controller
                    name="issuedDateFrom"
                    control={control}
                    defaultValue=""
                    render={({ field }) => (
                      <TextField
                        {...field}
                        size="small"
                        label="Issued Date From"
                        type="date"
                        InputLabelProps={{ shrink: true }}
                        aria-describedby="issuedDateFrom"
                        value={field.value}
                        error={Boolean(errors.issuedDateFrom) && !field.value}
                        helperText={
                          Boolean(errors.issuedDateFrom) && !field.value
                            ? "This field is required"
                            : ""
                        }
                        onChange={(event) => {
                          field.onChange(event.target.value);
                          updateFilters({
                            ...getValues(),
                            issuedDateFrom: event.target.value,
                          });
                          validateDates();
                        }}
                      />
                    )}
                  />
                </FormControl>
              </Grid>

              {/* Date Range To */}
              <Grid item xs={12} md={12}>
                <FormControl fullWidth>
                  <Controller
                    name="issuedDateTo"
                    control={control}
                    defaultValue=""
                    render={({ field }) => (
                      <TextField
                        {...field}
                        size="small"
                        label="Issued Date To"
                        type="date"
                        InputLabelProps={{ shrink: true }}
                        aria-describedby="issuedDateTo"
                        value={field.value}
                        inputProps={{
                          min: getValues("issuedDateFrom"),
                        }}
                        error={Boolean(errors.issuedDateTo) && !field.value}
                        helperText={
                          Boolean(errors.issuedDateTo) && !field.value
                            ? "This field is required"
                            : ""
                        }
                        onChange={(event) => {
                          field.onChange(event.target.value);
                          updateFilters({
                            ...getValues(),
                            issuedDateTo: event.target.value,
                          });
                          validateDates();
                        }}
                      />
                    )}
                  />
                </FormControl>
              </Grid>

              <Grid item xs={12} md={12}>
                <Box display="flex" alignItems="center">
                  <Typography id="range-slider" gutterBottom>
                    Total amount in
                  </Typography>
                  <Grid item xs={4} sx={{ marginLeft: "6px" }}>
                    <UpdatedSelectAutoComplete
                      register={register}
                      id={"budget"}
                      label={"units"}
                      nameArray={rupees}
                      value={budget}
                      onChange={(e) => setBudget(e.target.value)}
                      error={Boolean(errors.budget)}
                      aria-describedby="validation-budget"
                    />
                  </Grid>
                </Box>
                <Slider
                  value={budgetValue}
                  onChange={handleChange}
                  valueLabelDisplay="auto"
                  aria-labelledby="range-slider"
                  getAriaValueText={valuetext}
                  step={5}
                  marks={marks}
                  min={5}
                  max={100}
                />
              </Grid>

              <Grid item xs={12} md={12}>
                <Box display="flex" alignItems="center">
                  <Typography id="range-slider" gutterBottom>
                    Balance Amount in
                  </Typography>
                  <Grid item xs={4} sx={{ marginLeft: "6px" }}>
                    <UpdatedSelectAutoComplete
                      register={register}
                      id={"balance"}
                      label={"units"}
                      nameArray={rupees}
                      value={balanceBudget}
                      onChange={(e) => setBalanceBudget(e.target.value)}
                      error={Boolean(errors.balanceBudget)}
                      aria-describedby="validation-balanceBudget"
                    />
                  </Grid>
                </Box>
                <Slider
                  value={balanceBudgetValue}
                  onChange={handleBalanceChange}
                  valueLabelDisplay="auto"
                  aria-labelledby="range-slider"
                  getAriaValueText={valuetext}
                  step={5}
                  marks={marks}
                  min={5}
                  max={100}
                />
              </Grid>
            </Grid>
          </Box>
        </PerfectScrollbar>

        <Box
          sx={{
            borderTop: (theme) => `1px solid ${theme.palette.divider}`,
            p: (theme) => theme.spacing(2),
            justifyContent: "center",
            display: "flex",
            alignItems: "center",
          }}
        >
          <Button variant="contained" sx={{ mr: 3 }} onClick={handleCancel}>
            Clear All
          </Button>
          <Button variant="tonal" onClick={handleApply}>
            Apply
          </Button>
        </Box>
      </Drawer>



    </>
  );
};

export default AdvancedSearch;
