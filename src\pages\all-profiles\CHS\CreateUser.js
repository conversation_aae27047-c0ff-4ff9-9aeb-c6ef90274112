// ** React Imports
import { useContext, useEffect, useState, useRef } from "react";

// ** MUI Imports
import CheckCircleOutlineIcon from "@mui/icons-material/CheckCircleOutline";
import Button from "@mui/material/Button";
import FormControl from "@mui/material/FormControl";
import Grid from "@mui/material/Grid";
import TextField from "@mui/material/TextField";
import MobileNumberValidation from "src/@core/components/custom-components/MobileNumberValidation";
import GoogleMapsIconButton from "src/@core/components/custom-components/toastDisplay.js"
import SelectAutoComplete from "src/@core/components/custom-components/SelectAutoComplete";
// ** Third Party Imports

import { Controller, useForm } from "react-hook-form";

import axios from "axios";
import { getAuthorizationHeaders, getUrl } from "src/helpers/utils";

// ** Hooks
// ** Icon Imports
import {
  Link,
  CircularProgress,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  InputLabel ,
  Typography,
  Divider,
  InputAdornment,
  FormHelperText,
} from "@mui/material";
import { Box } from "@mui/system";
import EmailTextField from "src/@core/components/custom-components/EmailTextField";
import NameTextField from "src/@core/components/custom-components/NameTextField";
import authConfig from "src/configs/auth";
import { AuthContext } from "src/context/AuthContext";
import { useAuth } from "src/hooks/useAuth";
import IconButton from "@mui/material/IconButton";
import Icon from "src/@core/components/icon";
import SelectCategory from "src/@core/components/custom-components/SelectCategory";

const CreateUser = ({
  selectedOption,
  openDialog,
  handleDialogClose,
  fetchUsers,
  formData,
}) => {
  const { CHSCreate,getAllListValuesByListNameId } = useContext(AuthContext);

  const [dialogMessage, setDialogMessage] = useState("");
  const [submitSuccess, setSubmitSuccess] = useState(false);

  //Hooks
  const auth = useAuth();

  const {
    register,
    handleSubmit,
    setError,
    control,
    clearErrors,
    reset,
    formState: { errors },
  } = useForm({
    defaultValues: {
      firstName: "",
      email: "",
    },
  });

  const [email, setEmail] = useState("");
  const [isEmailVerified, setIsEmailVerified] = useState(false);
  const [showOTPOptions, setShowOTPOptions] = useState(false);
  const [otp, setOTP] = useState("");
  const [countdown, setCountdown] = useState(0);

  const [designation,setDesignation] = useState(null)
  const [anyOther,setAnyOther] = useState(false)

  const [loading, setLoading] = useState(false);
  const [resend,setResend] = useState(false)
  const [loadingEmail, setLoadingEmail] = useState(false);
  const [loadingSave, setLoadingSave] = useState(false);

  const [disableVerifyEmailButton, setDisableVerifyEmailButton] =
    useState(false);

  const [pleaseVerifyEmailMessage, setPleaseVerifyEmailMessage] =
    useState(false);
  const [okayButtonClose, setOkayButtonClose] = useState(false);

  const handleOTPDialogClose = () => {
    setSubmitSuccess(false);
  };

  const [listOfSubCategories, setListOfSubCategories] = useState([])
 const handleError = (error) => console.error("Error:", error);
    
      useEffect(() => {
        if (!!authConfig) {
          getAllListValuesByListNameId(
            authConfig.ChsDesignation,
            (data) =>
              setListOfSubCategories(
                data?.listValues?.map((item) => ({
                  value: item.id,
                  key: item.listValue,
                }))
              ),
            handleError
          );
        }
      }, [authConfig]);

  const handleCancelClick = () => {
    setIsEmailVerified(false);
    setDesignation(null)
    setAnyOther(false)
    setDisableVerifyEmailButton(false);
    reset({ firstName: "", email: "" });
    handleDialogClose();
  };

  const apiCall = useRef(false);
  async function submit(data) {
  if (apiCall.current) {
      // API call is already in progress, return early
      return;
    }
    apiCall.current = true;
    setLoadingSave(true);
    try{const response = await auth.CHSCreate(data);

    
      if (response) {
        
        const message = `
        <div>
        <h3>
        User Created Successfully!
        </h3>
        </div>
        `;
        setLoadingSave(false);
        setDialogMessage(message);
        setOkayButtonClose(true)
        setSubmitSuccess(true); 
        fetchUsers();
      }
      reset({ firstName: "", email: "" });
      setIsEmailVerified(false);
      handleCancelClick();
    } catch (error) {
      console.error("Error creating user:", error);
      const message = `
        <div>
          <h3>Submit failed, try again later.</h3>
        </div>
      `;
      
      setDialogMessage(message);
      setSubmitSuccess(true);
    } finally {
      setLoadingSave(false);
      apiCall.current = false;
    }
    
    
  }

  const fetchIpAddress = async () => {
    try {
      const response = await fetch("https://api.ipify.org?format=json");
      const data = await response.json();

      return data.ip;
    } catch (error) {
      console.error("Error fetching IP address:", error);

      return null;
    }
  };

  async function sendOTP(data) {
    if (apiCall.current) {
      // API call is already in progress, return early
      return;
    }
    apiCall.current = true;
    setResend(true)
    const ipAddress = await fetchIpAddress();
    setPleaseVerifyEmailMessage(false);
    setLoadingEmail(true);
   

    await axios({
      method: "POST",
      url: getUrl(authConfig.registerEndpointNewV3),
      data: {
        firstName: data?.firstName,
        email: data?.email,
        ipAddress: ipAddress,
        role: selectedOption,
      },
    })
      .then((res) => {
        if (res.data.isVerified) {
          const message = `
      <div>
      <h3>
      Email already exists!!!
      </h3>
      </div>
      `;
          setLoadingEmail(false);
          setResend(false)
          setOkayButtonClose(true);
          setDisableVerifyEmailButton(false);
          setDialogMessage(message);
          setSubmitSuccess(true);
        } else {
          setEmail(data?.email);
          const message = `
          <div>
          <h3>
          OTP has been sent to your Email for verification. Please check.
          </h3>
          </div>
        `;
          setLoadingEmail(false);
          setDialogMessage(message);
          setResend(false)
          setSubmitSuccess(true);
          setCountdown(30);
          setShowOTPOptions(true);
          setOkayButtonClose(false);
        }
      })
      .catch((err) => {
        const message = `
        <div>
        <h3>
        Error sending OTP to your Email. <br> Please Enter Valid Email (or) Please try again.
        </h3>
        </div>
      `;
        setLoadingEmail(false);
        setResend(false);
        setDialogMessage(message);
        setDisableVerifyEmailButton(false);
        setOkayButtonClose(true);
        setSubmitSuccess(true);
      });
      apiCall.current = false;
  }

  
  async function verifyOtp(data) {
    
    
    setLoading(true);

    await axios({
      method: "POST",
      url: getUrl(authConfig.otpVerifyEndpoint3) + "?isMember=false",
      data: {
        otpCode: otp,
        email: email,
      },
    })
      .then((response) => {
        setIsEmailVerified(true);
        setOTP("");
        setShowOTPOptions(false);
        
        const message = `
        <div>
        <h3>
        Email has been verified successfully.
        </h3>
        </div>
      `;
        setLoading(false);
        setDialogMessage(message);
        setOkayButtonClose(true);
        setSubmitSuccess(true);
        window.localStorage.setItem("verifiedEmail", data?.email);
      })
      .catch((error) => {
        console.error("Error verifying OTP:", error);

        const message = `
        <div>
        <h3>
        OTP doesn't match. Please try again.
        </h3>
        </div>

      `;
        setLoading(false);
        setDialogMessage(message);
        setSubmitSuccess(true);
     
      });
  }

  const handleDesignationChange = (newValue) => {
    setDesignation(newValue)
    const matchedSubCategory = listOfSubCategories.find(subCategory => subCategory.value === newValue);
    if(matchedSubCategory.key == 'Any Other'){
      setAnyOther(true)
    }else{
      setAnyOther(false)
    }
  };

  useEffect(() => {
    if (countdown > 0) {
      const timerId = setTimeout(() => setCountdown(countdown - 1), 1000);

      return () => clearTimeout(timerId);
    }
  }, [countdown]);
  
  const validateLocationUrl = (value) => {
    const urlPattern = /^https:\/\/(www\.)?(maps\.app\.goo\.gl|google\.com\/maps)\/.+$/;
  if (!urlPattern.test(value)) {
    return 'Please enter a valid Google Maps URL';
  }
  try {
    const url = new URL(value);
    if (url.hostname !== 'maps.app.goo.gl' && url.hostname !== 'google.com') {
      return 'Please enter a valid Google Maps URL';
    }
  } catch (error) {
    return 'Invalid URL format';
  }
  return true;
};
  return (
    <Box sx={{ pt: 3 }}>
      <Dialog
        open={submitSuccess}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
        PaperProps={{
          sx: {
            p: (theme) => `${theme.spacing(2.5)} !important`,
            backgroundColor: (theme) => theme.palette.primary.background,
          },
        }}
      >
        <Box
          sx={{
            width: "100%",
            borderRadius: 1,
            textAlign: "center",
            border: (theme) => `1px solid ${theme.palette.divider}`,
            borderColor: "primary.main",
          }}
        >
          <DialogContent>
            <DialogContentText
              id="alert-dialog-description"
              color="primary.main"
            >
              <div dangerouslySetInnerHTML={{ __html: dialogMessage }} />
            </DialogContentText>
          </DialogContent>
          {showOTPOptions && (
            <Grid container spacing={5}>
              <Grid container justifyContent="center">
                <TextField
                  type="text"
                  inputProps={{
                    inputMode: "numeric",
                    pattern: "[0-9]*",
                  }}
                  placeholder="OTP"
                  value={otp}
                  onChange={(e) => {
                    const value = e.target.value;
                    if (/^\d*$/.test(value) && value.length <= 6) {
                      setOTP(value);
                    }
                  }}
                  InputLabelProps={{ shrink: true }}
                  error={Boolean(errors?.otp)}
                  helperText={errors?.otp?.message}
                  sx={{
                    borderRadius: "5px",
                    background: "white",
                  }}
                />
              </Grid>

              <Grid item xs={12} sm={12}>
                <Grid container justifyContent="center">
                  <Button
                    variant="contained"
                    disabled={!otp || Boolean(errors?.otp)}
                    onClick={verifyOtp}
                    sx={{
                      marginBottom: "16px",
                      "&:disabled": { color: "primary.main" },
                    }}
                  >
                    {loading ? (
                      <CircularProgress color="inherit" size={22} />
                    ) : (
                      "VALIDATE OTP"
                    )}
                  </Button>
                  <Button
                    variant={countdown > 0 ? "outlined" : "contained"}
                    disabled={countdown > 0}
                    onClick={handleSubmit(sendOTP)}
                    sx={{
                      marginLeft: "7px",
                      marginBottom: "16px",
                      "&:disabled": { color: "primary.main" },
                    }}
                  >
                    {resend ? (
                      <CircularProgress color="inherit" size={22} />
                    ) : (
                      "RESEND OTP"
                    )}
                  </Button>
                </Grid>
                {countdown > 0 && (
                  <Typography
                    variant="body1"
                    sx={{
                      marginTop: "2px",
                      marginBottom: "10px",
                      color: "primary.main",
                    }}
                  >
                    Resend OTP in: {countdown}s
                  </Typography>
                )}
              </Grid>
            </Grid>
          )}

          {okayButtonClose && (
            <DialogActions>
              <Button
                onClick={handleOTPDialogClose}
                style={{ margin: "10px auto", width: 100 }}
              >
                Okay
              </Button>
            </DialogActions>
          )}
        </Box>
      </Dialog>

      <Dialog
        fullWidth
        maxHeight='90%'
        width="600px !important"
        // maxWidth="md"
        scroll="paper"
        open={openDialog}
        onClose={(event, reason) => {
          if (reason !== "backdropClick") {
            handleCancelClick();
          }
        }}
      >
        <DialogTitle
         sx={{
          position: "relative",
          borderBottom: (theme) => `1px solid ${theme.palette.divider}`,
          p: (theme) => `${theme.spacing(1.75, 4)} !important`,
          display: "flex",
          alignItems: "center",
          justifyContent: { xs: "start"},
          fontSize: { xs: 19, md: 20  },
        }}
        textAlign={"center"}
        >
          Creating User as Society Member
          <Box sx={{ position: "absolute", top: "4px", right: "14px" }}>
            <IconButton
              size="small"
              onClick={handleCancelClick}
              sx={{
                // p: "0.438rem",
                borderRadius: 1,
                color:"common.white", 
                backgroundColor: "primary.main",
                "&:hover": {
                  backgroundColor:
                  '#66BB6A',
                  transition: 'background 0.5s ease, transform 0.5s ease',             
                },
              }}
            >
              <Icon icon="tabler:x" fontSize="1rem" />
            </IconButton>
          </Box>
        </DialogTitle>
        <DialogContent
  sx={{
    position: "relative",
    p: (theme) => `${theme.spacing(10, 8)} !important`,
  }}
>
    <Grid item xs={12} sm={6}  mt={2}>
      <FormControl fullWidth>
        <Controller
          name="societyName"
          control={control}
          rules={{ required: true }}
          render={({ field }) => (
            <TextField
              {...field}
              label="Society Name"
              InputLabelProps={{ shrink: true }}
              size="small"
              placeholder="Enter your Society name"
              error={Boolean(errors.societyName)}
              helperText={errors.societyName?.message}
              aria-describedby="validation-basic-societyName"
            />
          )}
        />
      </FormControl>
    </Grid>
    <Grid item xs={12} sm={6} mt={2}>
  <FormControl fullWidth>
    <Controller
      name="googleMapLocation"
      control={control}
      rules={{ required: true, validate: validateLocationUrl }}
      render={({ field }) => (
        <TextField
          {...field}
          label="Google location"
          InputLabelProps={{ shrink: true }}
          size="small"
          placeholder="Click on icon to navigate & paste URL here"
          error={Boolean(errors.googleMapLocation)}
          helperText={errors.googleMapLocation?.message}
          aria-describedby="validation-basic-googleMapLocation"
          InputProps={{
            endAdornment: (
              <InputAdornment position="end">
                <span style={{ position: 'absolute', right: 8, top: 0 }}>
                  <GoogleMapsIconButton />
                </span>
                {/* <Link
                  component="a"
                  href={field.value}
                  target="_blank"
                  rel="noopener noreferrer"
                  sx={{
                    textDecoration: 'none',
                    '&:hover': {
                      textDecoration: 'underline',
                    },
                  }}
                >
                  {field.value}
                </Link> */}
              </InputAdornment>
            ),
          }}
        />
      )}
    />
  </FormControl>
</Grid>
    <Grid item xs={12} sm={6}  mt={2}>
      <FormControl fullWidth>
        <Controller
          name="firstName"
          control={control}
          rules={{ required: true }}
          render={({ field }) => (
            <NameTextField
              {...field}
              label="Name of the person"
              InputLabelProps={{ shrink: true }}
              size="small"
              placeholder="Enter your name"
              error={Boolean(errors.firstName)}
              helperText={errors.firstName?.message}
              aria-describedby="validation-basic-firstName"
            />
          )}
        />
      </FormControl>
    </Grid>
    <Grid item xs={12} sm={6} mt={2}>
            <SelectCategory
              register={register}
              clearErrors={clearErrors}
              id={"designation"}
              label={" Designation "}
              name="designation"
              nameArray={listOfSubCategories}
              defaultValue={designation}
              value={designation}
              onChange={(event) => handleDesignationChange(event.target.value)}
              error={Boolean(errors.designation)}

              aria-describedby="designation"
            />
            {errors.designation && (
              <FormHelperText sx={{ color: 'error.main' }} id='validation-designation'>
              Please select designation
            </FormHelperText>
            )}
    </Grid>
          {anyOther && (
              <Grid container spacing={2}>

            <Grid item xs={12} sm={6}  mt={2}>
            <FormControl fullWidth>
              <Controller
                name="otherDesignation"
                control={control}
                rules={{ required: true }}
                render={({ field }) => (
                  <NameTextField
                    {...field}
                    label="Any Other Designation"
                    InputLabelProps={{ shrink: true }}
                    size="small"
                    placeholder="Enter your any other designation"
                    error={Boolean(errors.otherDesignation)}
                    helperText={errors.otherDesignation?.message}
                    aria-describedby="validation-basic-otherDesignation"
                  />
                )}
              />
            </FormControl>
          </Grid>
          </Grid>
          )}
          
      <Grid item xs={12} sm={6}  mt={2}>
        <FormControl fullWidth>
          <Controller
            name="mobileNumber"
            control={control}
            rules={{ required: true }}
            render={({ field }) => (
              <MobileNumberValidation
                {...field}
                type="tel"
                label="Contact Number"
                size="small"
                InputLabelProps={{ shrink: true }}
                error={Boolean(errors.mobileNumber)}
                helperText={errors.mobileNumber?.message}
                placeholder="+91 95 155 990 22"
                aria-describedby="validation-mobileNumber"
              />
            )}
          />
        </FormControl>
     </Grid>

  {anyOther && (
    <Grid item xs={12} ></Grid>
  )} 
  <Grid container spacing={2} >
  <Grid item xs={12} sm={10} mt={2}>
      {!isEmailVerified && (
        <>
          <FormControl fullWidth>
            <Controller
              name="email"
              control={control}
              rules={{ required: true }}
              render={({ field }) => (
                <EmailTextField
                  {...field}
                  type="email"
                  label="Email address"
                  size="small"
                  error={Boolean(errors.email)}
                  inputProps={{ maxLength: 50 }}
                  InputLabelProps={{ shrink: true }}
                  placeholder="Enter email address"
                  helperText={errors.email?.message}
                />
              )}
            />
          </FormControl>
        </>
      )}
      {isEmailVerified && (
        <>
          <FormControl fullWidth>
            <div style={{ display: "flex", alignItems: "baseline" }}>
              <Typography sx={{ mt: 4, fontWeight: "bold" }}>
                Email:
              </Typography>
              <Typography
                variant="body1"
                sx={{
                  marginLeft: "12px",
                }}
              >
                {email}
              </Typography>
            </div>
          </FormControl>
        </>
      )}
    </Grid>
    <Grid item xs={5} sm={2} sx={{
    mt: { sm: '0.5rem !important', xs: '0 !important' }, // mt=2 for small screens and up, mt=0 for extra small screens
  }}>
      {!isEmailVerified ? (
        <>
          <Button
            color="primary"
            variant="contained"
            onClick={handleSubmit(sendOTP)}
            disabled={disableVerifyEmailButton}
            sx={{fontSize:'0.76rem !important', padding:'6px 16px !important'}}
          >
            {loadingEmail ? (
              <CircularProgress color="inherit" size={22} />
            ) : (
              "Verify"
            )}
          </Button>
        </>
      ) : (
        <Box sx={{ display: "flex", alignItems: "center", mt: 4 }}>
          <CheckCircleOutlineIcon
            sx={{
              color: "green",
            }}
          />
          <Typography
            sx={{
              marginLeft: "6px",
              marginBottom: "-10px",
              paddingBottom: 2,
            }}
          >
            Verified
          </Typography>
        </Box>
      )}
    </Grid>
  </Grid>
</DialogContent>

        <DialogActions
          sx={{
            justifyContent: "end",
            borderTop: (theme) => `1px solid ${theme.palette.divider}`,
            p: (theme) => `${theme.spacing(2.5)} !important`,
          }}
        >
          <Grid item xs={12}>
            <center>
              <Button
                size="medium"
                sx={{ mr: 3 }}
                variant="outlined"
                color="primary"
                onClick={() => handleCancelClick()}
              >
                Cancel
              </Button>
              <Button
                size="medium"
                type="button"
                variant="contained"
                disabled={!isEmailVerified}
                onClick={handleSubmit(submit)}
              >
                {loadingSave ? (
                  <CircularProgress color="inherit" size={22} />
                ) : (
                  "Save"
                )}
              </Button>
            </center>
          </Grid>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default CreateUser;
