import { useTheme } from "@emotion/react";
import { Add as AddIcon, Delete as DeleteIcon } from "@mui/icons-material";
import {
  FormControl,
  Grid,
  IconButton,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TextField,
  Typography,
  useMediaQuery,
} from "@mui/material";
import { useContext, useEffect, useState } from "react";
import { Controller, useForm } from "react-hook-form";
import { AuthContext } from "src/context/AuthContext";
import authConfig from "src/configs/auth";
import SelectAutoComplete from "src/@core/components/custom-components/SelectAutoComplete";

const innerTableStylings = {
  fontWeight: "bold", // Make text bold
  padding: "6px 16px", // Reduce padding to decrease cell height
};

const PaymentSchedule = ({
  editablePaymentData,
  setEditablePaymentData,
  contractValue,
  setContractValue,
  gstPercentage,
  setGstPercentage,
  paymentData,
  setPaymentData,
}) => {
  const theme = useTheme();
  const { getAllListValuesByListNameId, user } = useContext(AuthContext);
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));

  const {
    control,
    setValue,
    formState: { errors },
  } = useForm();

  const [rowErrors, setRowErrors] = useState({}); // For storing per-row validation errors

  const [recurring, setRecurring] = useState("");
  const [frequency, setFrequency] = useState("");
  const [followUpFrequency, setFollowUpFrequency] = useState("");
  const handleError = (error) => {
    console.error("Payment schedule error:", error);
  };

  const [recurringList, setRecurringList] = useState([]);
  const [frequencyList, setFrequencyList] = useState([]);
  const [followUpFrequencyList, setFollowUpFrequencyList] = useState([]);
  const [paymentDescriptionList, setPaymentDescriptionList] = useState([]);
  useEffect(() => {
    if (!!authConfig) {
      getAllListValuesByListNameId(
        authConfig.recurringListNameId,
        (data) =>
          setRecurringList(
            data?.listValues.map((item) => ({
              value: item.id,
              key: item.listValue,
            }))
          ),
        handleError
      );

      getAllListValuesByListNameId(
        authConfig.frequencyListNameId,
        (data) =>
          setFrequencyList(
            data?.listValues.map((item) => ({
              value: item.id,
              key: item.listValue,
            }))
          ),
        handleError
      );

      getAllListValuesByListNameId(
        authConfig.followUpFrequencyListNameId,
        (data) =>
          setFollowUpFrequencyList(
            data?.listValues.map((item) => ({
              value: item.id,
              key: item.listValue,
            }))
          ),
        handleError
      );
      getAllListValuesByListNameId(
        authConfig.paymentDescriptionListNameId,
        (data) =>
          setPaymentDescriptionList(
            data?.listValues.map((item) => ({
              value: item.id,
              key: item.listValue,
            }))
          ),
        handleError
      );
    }
  }, [authConfig]);

  useEffect(() => {
    if (paymentData) {
      setRecurring(paymentData.recurring);
      setFrequency(paymentData.frequency);
      setFollowUpFrequency(paymentData.followUpFrequency);
      setValue("houzerPayment", paymentData?.houzerPayment);
      setValue("renewalDate", paymentData?.renewalDate);
      setValue("startDate", paymentData?.dateOfStart);
      setValue("endDate", paymentData?.dateOfFinish);
      setValue("paymentTerms", paymentData?.paymentTerms);
      setValue("remarks", paymentData?.remarks);
    }
  }, [paymentData]);

  // Handle input changes for the editable payment data
  const handleInputChange = (index, field, value) => {
    const updatedData = [...editablePaymentData];
    let totalPercentage = 0;

    if (field === "amountPercentage") {
      // Calculate total percentage excluding the current row
      totalPercentage = updatedData.reduce(
        (sum, row, i) =>
          i === index ? sum : sum + parseFloat(row.amountPercentage || 0),
        0
      );

      const newPercentage = parseFloat(value || 0);
      if (totalPercentage + newPercentage > 100) {
        // Show error if the total exceeds 100
        setRowErrors((prevErrors) => ({
          ...prevErrors,
          [index]: `Percentage should not exceed ${
            100 - totalPercentage
          }% (remaining)`,
        }));
        return;
      }

      // Clear the error if validation passes
      setRowErrors((prevErrors) => {
        const { [index]: removed, ...remainingErrors } = prevErrors;
        return remainingErrors;
      });

      // Calculate the amount based on contract value and amount percentage
      const amount = (newPercentage / 100) * contractValue;
      updatedData[index]["amount"] = amount.toFixed(2); // Update amount as a calculated field

      const gstAmount = (amount * gstPercentage) / 100;
      updatedData[index]["gstAmount"] = gstAmount.toFixed(2);
    }

    updatedData[index][field] = value;
    setEditablePaymentData(updatedData);
  };

  const handleAddRow = () => {
    setEditablePaymentData([
      ...editablePaymentData,
      {
        id: crypto.randomUUID(),
        date: "",
        paymentDescription: "",
        amountPercentage: "",
        amount: "",
        gstAmount: "",
      }, // Default values for new row
    ]);
  };

  const handleDeleteRow = (index) => {
    const updatedData = editablePaymentData.filter((_, i) => i !== index);
    setEditablePaymentData(updatedData);
  };

  return (
    <>
      <Grid container spacing={2} sx={{ marginTop: 2 }}>
        <Grid item xs={12} sm={12} sx={{ margin: 2 }}>
          {editablePaymentData?.length > 0 ? (
            ""
          ) : (
            <Typography>
              *Note : Please Fill total contract value (Excluding GST) to
              create a Payment Schedule
            </Typography>
          )}
        </Grid>
        <Grid item xs={10} sm={2}>
          <FormControl fullWidth>
            <Controller
              name="totalContractValue"
              control={control}
              render={({ field }) => (
                <TextField
                  {...field}
                  label="Total Contract Value(excluding GST)"
                  InputLabelProps={{ shrink: true }}
                  size="small"
                  type="number"
                  placeholder="Enter Contract Value"
                  value={contractValue}
                  onChange={(e) => {
                    setContractValue(e.target.value); // Update state on change
                    field.onChange(e); // Update react-hook-form field value
                  }}
                  error={Boolean(errors.contractValue)}
                  helperText={errors.contractValue?.message}
                  aria-describedby="validation-contractValue"
                  inputProps={{ maxLength: 50 }}
                />
              )}
            />
          </FormControl>
        </Grid>

        <Grid item xs={10} sm={2}>
          <FormControl fullWidth>
            <Controller
              name="gstPercentage"
              control={control}
              render={({ field }) => (
                <TextField
                  {...field}
                  label="GST Percentage"
                  InputLabelProps={{ shrink: true }}
                  size="small"
                  type="number"
                  placeholder="Enter GST Percentage"
                  value={gstPercentage}
                  onChange={(e) => {
                    setGstPercentage(e.target.value); // Update state on change
                    field.onChange(e); // Update react-hook-form field value
                  }}
                  error={Boolean(errors.gstPercentage)}
                  helperText={errors.gstPercentage?.message}
                  aria-describedby="validation-gstPercentage"
                  inputProps={{ maxLength: 50 }}
                />
              )}
            />
          </FormControl>
        </Grid>
        <Grid item xs={10} sm={2}>
          <FormControl fullWidth error={Boolean(errors.recurring)}>
            <Controller
              name="recurring"
              control={control}
              rules={{ required: false }}
              render={({ field }) => (
                <>
                  <SelectAutoComplete
                    id="recurring"
                    label="Recurring"
                    nameArray={recurringList}
                    value={recurring}
                    onChange={(e) => {
                      field.onChange(e.target.value);
                      setRecurring(e.target.value);
                      setPaymentData((prev) => ({
                        ...prev,
                        recurring: e.target.value,
                      }));
                    }}
                  />
                  {errors.recurring && (
                    <FormHelperText sx={{ color: "error.main" }}>
                      {errors.status.message}
                    </FormHelperText>
                  )}
                </>
              )}
            />
          </FormControl>
        </Grid>
        <Grid item xs={10} sm={2}>
          <FormControl fullWidth error={Boolean(errors.frequency)}>
            <Controller
              name="frequency"
              control={control}
              rules={{ required: false }}
              render={({ field }) => (
                <>
                  <SelectAutoComplete
                    id="frequency"
                    label="Frequency"
                    nameArray={frequencyList}
                    value={frequency}
                    onChange={(e) => {
                      field.onChange(e.target.value);
                      setFrequency(e.target.value);
                      setPaymentData((prev) => ({
                        ...prev,
                        frequency: e.target.value,
                      }));
                    }}
                  />
                  {errors.frequency && (
                    <FormHelperText sx={{ color: "error.main" }}>
                      {errors.status.message}
                    </FormHelperText>
                  )}
                </>
              )}
            />
          </FormControl>
        </Grid>
        <Grid item xs={10} sm={2}>
          <FormControl fullWidth error={Boolean(errors.followUpFrequency)}>
            <Controller
              name="followUpFrequency"
              control={control}
              rules={{ required: false }}
              render={({ field }) => (
                <>
                  <SelectAutoComplete
                    id="followUpFrequency"
                    label="Follow Up Frequency"
                    nameArray={followUpFrequencyList}
                    value={followUpFrequency}
                    onChange={(e) => {
                      field.onChange(e.target.value);
                      setFollowUpFrequency(e.target.value);
                      setPaymentData((prev) => ({
                        ...prev,
                        followUpFrequency: e.target.value,
                      }));
                    }}
                  />
                  {errors.followUpFrequency && (
                    <FormHelperText sx={{ color: "error.main" }}>
                      {errors.status.message}
                    </FormHelperText>
                  )}
                </>
              )}
            />
          </FormControl>
        </Grid>
        <Grid item xs={12} sm={2}>
          <FormControl fullWidth>
            <Controller
              name="houzerPayment"
              control={control}
              render={({ field }) => (
                <TextField
                  {...field}
                  type="number"
                  label="Houzer Payment"
                  InputLabelProps={{ shrink: true }}
                  size="small"
                  placeholder="Enter houzer payment"
                  value={field.value}
                  onChange={(e) => {
                    const value = e.target.value;
                    field.onChange(value);
                    setPaymentData((prev) => ({
                      ...prev,
                      houzerPayment: value,
                    }));
                  }}
                  error={Boolean(errors.houzerPayment)}
                  helperText={errors.houzerPayment?.message}
                  aria-describedby="validation-basic-houzerPayment"
                />
              )}
            />
          </FormControl>
        </Grid>
        <Grid item xs={12} sm={2}>
          <FormControl fullWidth>
            <Controller
              name="renewalDate"
              control={control}
              rules={{ required: false }}
              render={({ field }) => (
                <TextField
                  {...field}
                  size="small"
                  label="Renewal Date"
                  type="date"
                  InputLabelProps={{ shrink: true }}
                  aria-describedby="renewalDate"
                  value={field.value}
                  onChange={(e) => {
                    const value = e.target.value;
                    field.onChange(value);
                    setPaymentData((prev) => ({
                      ...prev,
                      renewalDate: value,
                    }));
                  }}
                />
              )}
            />
          </FormControl>
        </Grid>
        <Grid item xs={12} sm={2}>
          <FormControl fullWidth>
            <Controller
              name="startDate"
              control={control}
              rules={{ required: "Date of Start is required" }}
              render={({ field }) => (
                <TextField
                  {...field}
                  size="small"
                  label="Date of Start"
                  type="date"
                  InputLabelProps={{ shrink: true }}
                  aria-describedby="startDate"
                  value={field.value}
                  onChange={(e) => {
                    const value = e.target.value;
                    field.onChange(value);
                    setPaymentData((prev) => ({
                      ...prev,
                      dateOfStart: value,
                    }));
                  }}
                />
              )}
            />
          </FormControl>
        </Grid>
        <Grid item xs={12} sm={2}>
          <FormControl fullWidth>
            <Controller
              name="endDate"
              control={control}
              rules={{ required: "Finish Date is required" }}
              render={({ field }) => (
                <TextField
                  {...field}
                  size="small"
                  label="Date of Finish"
                  type="date"
                  InputLabelProps={{ shrink: true }}
                  aria-describedby="endDate"
                  value={field.value}
                  onChange={(e) => {
                    const value = e.target.value;
                    field.onChange(value);
                    setPaymentData((prev) => ({
                      ...prev,
                      dateOfFinish: value,
                    }));
                  }}
                />
              )}
            />
          </FormControl>
        </Grid>
        <Grid item xs={12} sm={12}>
          <FormControl fullWidth>
            <Controller
              name="paymentTerms"
              control={control}
              rules={{ required: false }}
              render={({ field }) => (
                <TextField
                  rows={2}
                  multiline
                  {...field}
                  label="Payment Terms"
                  value={field.value}
                  onChange={(e) => {
                    const value = e.target.value;
                    field.onChange(value);
                    setPaymentData((prev) => ({
                      ...prev,
                      paymentTerms: value,
                    }));
                  }}
                  error={Boolean(errors.paymentTerms)}
                  aria-describedby="Section1-paymentTerms"
                />
              )}
            />
          </FormControl>
        </Grid>
        <Grid item xs={12} sm={12}>
          <FormControl fullWidth>
            <Controller
              name="remarks"
              control={control}
              rules={{ required: false }}
              render={({ field }) => (
                <TextField
                  rows={2}
                  multiline
                  {...field}
                  label="Remarks"
                  value={field.value}
                  onChange={(e) => {
                    const value = e.target.value;
                    field.onChange(value);
                    setPaymentData((prev) => ({
                      ...prev,
                      remarks: value,
                    }));
                  }}
                  error={Boolean(errors.remarks)}
                  aria-describedby="Section1-remarks"
                />
              )}
            />
          </FormControl>
        </Grid>
        <Grid item xs={12} sm={12}>
          <Typography sx={{  fontWeight: "bold" ,mt:2, textDecoration: "underline"}}>
            Payment Breakdown
          </Typography>
        </Grid>
      </Grid>
      {contractValue && (
        <TableContainer component={Paper} sx={{ marginTop: 2 }}>
          <Table aria-label="payment schedule" size="small">
            <TableHead>
              <TableRow>
                <TableCell style={innerTableStylings}>Date</TableCell>
                <TableCell style={innerTableStylings}>
                  Payment Description
                </TableCell>
                <TableCell style={innerTableStylings}>
                  Amount Percentage (%)
                </TableCell>
                <TableCell style={innerTableStylings}>Amount (₹)</TableCell>
                <TableCell style={innerTableStylings}>GST Amount</TableCell>
                {user.organisationCategory !== "SOCIETY" &&
                  user.organisationCategory !== "SERVICE_PROVIDER" && (
                    <TableCell style={innerTableStylings}>Actions</TableCell>
                  )}
              </TableRow>
            </TableHead>
            <TableBody>
              {editablePaymentData?.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={5} style={innerTableStylings}>
                    Click the Plus icon to add a row.
                  </TableCell>
                  {user.organisationCategory !== "SOCIETY" &&
                    user.organisationCategory !== "SERVICE_PROVIDER" && (
                      <TableCell style={innerTableStylings}>
                        <IconButton color="secondary" onClick={handleAddRow}>
                          <AddIcon />
                        </IconButton>
                      </TableCell>
                    )}
                </TableRow>
              ) : (
                editablePaymentData?.map((row, index) => (
                  <TableRow key={index}>
                    <TableCell style={innerTableStylings}>
                      <TextField
                        fullWidth
                        size="small"
                        type="date"
                        value={row.date}
                        onChange={(e) =>
                          handleInputChange(index, "date", e.target.value)
                        }
                      />
                    </TableCell>
                    <TableCell style={innerTableStylings}>
                      <FormControl fullWidth>
                        <Controller
                          name="paymentDescription"
                          control={control}
                          rules={{ required: false }}
                          render={({ field }) => (
                            <>
                              <SelectAutoComplete
                                id="paymentDescription"
                                label="Payment Description"
                                nameArray={paymentDescriptionList}
                                value={row.paymentDescription}
                                onChange={(e) =>
                                  handleInputChange(
                                    index,
                                    "paymentDescription",
                                    e.target.value
                                  )
                                }
                              />
                            </>
                          )}
                        />
                      </FormControl>
                    </TableCell>
                    <TableCell style={innerTableStylings}>
                      <TextField
                        fullWidth
                        size="small"
                        type="number"
                        value={row.amountPercentage}
                        onChange={(e) =>
                          handleInputChange(
                            index,
                            "amountPercentage",
                            e.target.value
                          )
                        }
                        error={Boolean(rowErrors[index])}
                        helperText={rowErrors[index] || ""}
                      />
                    </TableCell>
                    <TableCell style={innerTableStylings}>
                      <Typography>{row.amount || 0}</Typography>
                    </TableCell>
                    <TableCell style={innerTableStylings}>
                      <Typography>{row.gstAmount || 0}</Typography>
                    </TableCell>
                    {user.organisationCategory !== "SOCIETY" &&
                      user.organisationCategory !== "SERVICE_PROVIDER" && (
                        <TableCell style={innerTableStylings}>
                          <IconButton
                            color="secondary"
                            onClick={() => handleDeleteRow(index)}
                          >
                            <DeleteIcon />
                          </IconButton>
                          <IconButton color="secondary" onClick={handleAddRow}>
                            <AddIcon />
                          </IconButton>
                        </TableCell>
                      )}
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </TableContainer>
      )}
    </>
  );
};
export default PaymentSchedule;
