import { useEffect, useState } from "react";

// ** MUI Imports
import Box from "@mui/material/Box";
import Button from "@mui/material/Button";
import Grid from "@mui/material/Grid";
import Menu from "@mui/material/Menu";
import MenuItem from "@mui/material/MenuItem";
import Tooltip from "@mui/material/Tooltip";
import { DataGrid } from "@mui/x-data-grid";
import Icon from "src/@core/components/icon";
import CustomAvatar from "src/@core/components/mui/avatar";
// ** Third Party Imports
import { useForm } from "react-hook-form";
import CompanyAddresses from "./CompanyAddresses";

const CompanyAddressInformation = ({ formData, locationsData,addressTypeList,addresses,setAddresses }) => {
  const {
    formState: { errors },
  } = useForm({
    defaultValues: formData?.spAddresses || {}, // Initialize form fields with existing data
  });
 

  const rowsPerPageOptions = [10, 15, 20, 25, 50, 100];
  const [pageSize, setPageSize] = useState(rowsPerPageOptions[0]);
  const [page, setPage] = useState(1);
  const [rowCount, setRowCount] = useState(0);
 

  useEffect(()=>{
    setAddresses(formData?.spAddresses || [])
  },[formData])

  const [currentRow, setCurrentRow] = useState(null);
  const [societyMemberOpen, setSocietyMemberOpen] = useState(false);


  const handleSocietyMemberDialogOpen = () => {
    setCurrentRow(null);
    setSocietyMemberOpen(true);
  };
  const handleSocietyMemberDialogClose = () => {
    setSocietyMemberOpen(false);
    setCurrentRow(null);
  };
 
  const [menu, setMenu] = useState(null);

  const handleCloseMenuItems = () => {
    setMenu(null);
  };

  const cols = [
    {
      field: "address",
      headerName: "Address",
      minWidth: 125,
      flex: 1.5,
      renderCell: (params) => (
        <Tooltip title={params.row.address}>
          <span>{params.row.address}</span>
        </Tooltip>
      ),
    },
    {
      field: "addressType",
      headerName: "Address Type",
      minWidth: 95,
      flex: 1,
      renderCell: (params) => {
        const designation = addressTypeList?.find(
          (item) => item?.value === params?.row?.addressType
        );
        return (
          <Tooltip title={designation?.key || ""}>
            <span>{designation ? designation?.key : ""}</span>
          </Tooltip>
        );
      },
    },
    {
        field: "location",
        headerName: "Location",
        minWidth: 95,
        flex: 1,
        renderCell: (params) => {
          const designation = locationsData?.find(
            (item) => item?.value === params?.row?.locationId
          );
          return (
            <Tooltip title={designation?.key || ""}>
              <span>{designation ? designation?.key : ""}</span>
            </Tooltip>
          );
        },
      },
    {
      field: "actions",
      headerName: "Actions",
      sortable: false,
      minWidth: 50,
      flex: 0.3,
      disableClickEventBubbling: true,
      renderCell: (params) => {
        const onClick = (event) => {
          event.stopPropagation();
          setMenu(event.currentTarget);
          const row = params.row;
          setCurrentRow(row);
        };

        const onClickViewProfile = () => {
          setSocietyMemberOpen(true);
          handleCloseMenuItems();
        };
        const onClickDeleteProfile = () => {
          const sm = addresses?.filter(
            (member) => member?.id !== currentRow?.id
          );
          setAddresses(sm);
          handleCloseMenuItems();
        };

        return (
          <div
            style={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
            }}
          >
            <CustomAvatar
              skin="light"
              variant="rounded"
              sx={{
                mr: { xs: 2, lg: 4 },
                width: 34,
                height: 34,
                cursor: "pointer",
              }}
              onClick={onClick}
            >
              <Icon icon="bi:three-dots-vertical" />
            </CustomAvatar>
            <Menu
              id="actions-menu"
              anchorEl={menu}
              keepMounted
              open={Boolean(menu)}
              onClose={handleCloseMenuItems}
            >
              <MenuItem onClick={onClickViewProfile}>Edit</MenuItem>
              <MenuItem onClick={onClickDeleteProfile}>Delete</MenuItem>
            </Menu>
          </div>
        );
      },
    },
  ];

  const handlePageChange = (direction) => {
    if (direction === page) {
      setPage(page + 1);
    } else {
      setPage(page - 1);
    }
  };

  const handlePageSizeChange = (params) => {
    if (params) {
      setPageSize(params);
      setPage(1);
    }
  };

  return (
    <>
      <Box
        sx={{
          display: "flex",
          justifyContent: "left",
        }}
      >
        <div>
          <Button
          id="addCompanyAddress"
            sx={{ marginBottom: "15px" }}
            variant="contained"
            onClick={handleSocietyMemberDialogOpen}
          >
            Add&nbsp;Company&nbsp;Address
          </Button>
        </div>
      </Box>
      <Grid item xs={12} sm={4}>
        <>
          {currentRow ? (
            <CompanyAddresses
              open={societyMemberOpen}
              onClose={handleSocietyMemberDialogClose}
              addresses={addresses}
              setAddresses={setAddresses}
              locationsData={locationsData}
              addressTypeList={addressTypeList}
              rowData={currentRow}
            />
          ) : (
            <CompanyAddresses
              open={societyMemberOpen}
              onClose={handleSocietyMemberDialogClose}
              addresses={addresses}
              setAddresses={setAddresses}
              locationsData={locationsData}
              addressTypeList={addressTypeList}
            />
          )}
        </>
      </Grid>
      {addresses?.length > 0 && (
        <div style={{ height: "50%", width: "100%" }}>
          <DataGrid
            rows={addresses || []}
            columns={cols}
            autoHeight
            checkboxSelection
            rowHeight={38}
            headerHeight={38}
          />
        </div>
      )}
    </>
  );
};

export default CompanyAddressInformation;

