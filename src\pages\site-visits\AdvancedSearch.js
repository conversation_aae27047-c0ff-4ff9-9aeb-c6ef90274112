import { useContext, useEffect, useState, React } from "react";
import Drawer from "@mui/material/Drawer";
import Button from "@mui/material/Button";
import MenuItem from "@mui/material/MenuItem";
import { styled } from "@mui/material/styles";
import IconButton from "@mui/material/IconButton";
import Typography from "@mui/material/Typography";
import Box from "@mui/material/Box";
import Tooltip from "@mui/material/Tooltip";
import CustomAvatar from "src/@core/components/mui/avatar";
import SearchIcon from "@mui/icons-material/Search";
import {
  Grid,
  FormControl,
  InputLabel,
  Select,
  FormHelperText,
  FormControlLabel,
  Checkbox,
  TextField,
  InputAdornment,
} from "@mui/material";
import { Controller, useForm } from "react-hook-form";
import PerfectScrollbar from "react-perfect-scrollbar";
import authConfig from "src/configs/auth";
import axios from "axios";
import { getAuthorizationHeaders, getUrl } from "src/helpers/utils";
import Icon from "src/@core/components/icon";
import { AuthContext } from "src/context/AuthContext";
import MultiSelectAutoComplete from "src/@core/components/custom-components/MultiSelectAutoComplete";

const Header = styled(Box)(({ theme }) => ({
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
  position: "relative",
  padding: theme.spacing(3),
  borderBottom: `1px solid ${theme.palette.divider}`,
}));

const AdvancedSearch = (props) => {
  const {
    open,
    toggle,
    searchKeyword,
    searchData,
    setSearchKeyword,
    setSearchData,
    fetchUsers,
    page,
    pageSize,
    error,
    setError,
    searchingState,
    setSearchingState,
  } = props;
  const { getAllListValuesByListNameId } = useContext(AuthContext);

  const {
    control,
    setValue,
    reset,
    getValues,
    formState: { errors },
  } = useForm({
    defaultValues: {
      socitiesUUIDs : [],
      leadStatusUUIDs: [],
      assignedToUUIDs: [],
      requirementDeadLineFrom: "",
      requirementDeadLineTo: "",
      searchKeyword: "",// for search by requisition No.
      searchKeywordByMobile: "",
      searchKeywordByCompanyName: "",
      searchingState: true, 
    },
  });

  


  useEffect(() => {
    reset(searchData);
  }, [searchData, reset]);

  const handleCancel = () => {
    setSearchingState(false);
    setSearchKeyword("");
    setError(false);
    reset({
      socitiesUUIDs: [],  
      leadStatusUUIDs: [],
      assignedToUUIDs: [],
      requirementDeadLineFrom: "",
      requirementDeadLineTo: "",
      searchByRequisitionDateTo: "",
      searchKeyword: "",//search by requisition No.
      searchKeywordByCompanyName:"",
      searchKeywordByMobile : ""
    });
    setSearchData({});
    fetchUsers(page, pageSize, searchKeyword);
  };

  const handleClose = () => {
    toggle();
  };

  const handleApply = () => {
    setSearchingState(true);
    toggle();
  }

  const [listOfEmployees, setListOfEmployees] = useState([]);
  const [employeesOptions, setEmployeesOptions] = useState([]);
  

  useEffect(() => {
    axios({
      method: "get",
      url: getUrl(authConfig.selectDropdownNew) + "?selectionType=EMPLOYEES",
      headers: getAuthorizationHeaders(),
    })
      .then((res) => {
        setListOfEmployees(res.data.data);
      })
      .catch((err) => console.log("Employees error", err));
  }, []);

  

  useEffect(() => {
    if (listOfEmployees.length) {
      const data = listOfEmployees.map((entry) => ({
        value: entry.id,
        key: entry.name,
      }));
      setEmployeesOptions(data);
    }
  }, [listOfEmployees]);

  const [employeesData, setEmployeesData] = useState([]);
  const [listOfSocieties, setListOfSocieties] = useState([]);
  
  const societyOptions = listOfSocieties
  .filter((society) => society?.name)
  .map((society) => ({
    value: society,
    key: society?.name,
  }));

useEffect(() => {
  const fetchSocieties = async () => {
    axios({
      method: "get",
      url: getUrl(authConfig.selectDropdownNew) + "?selectionType=SOCIETY_NAME",
      headers: getAuthorizationHeaders(),
    })
      .then((res) => {
        const metadataArray = res.data?.data?.map((item) => item?.metaData);

        setListOfSocieties(metadataArray);
      })
      .catch((err) => console.log("error", err));
  };
  fetchSocieties();
}, []);

  useEffect(() => {
    // Fetch all employees
    axios({
      method: "get",
      url: getUrl(authConfig.selectDropdownNew) + "?selectionType=EMPLOYEES",
      headers: getAuthorizationHeaders(),
    })
      .then((res) => {
        setEmployeesData(res.data.data);
      })
      .catch((err) => console.log("Employees error", err));
  }, []);

  const updateFilters = (data) => {
    setSearchData(data);
    fetchUsers(page, pageSize, searchKeyword, data);
  };

  const [leadStatusData, setLeadStatusData] = useState(null);
  const [leadPriorityData, setLeadPriorityData] = useState(null);
  //const [locationsData, setLocationsData] = useState(null);
  const [services, setServices] = useState(null);
  const [assignedTo, setDesignation] = useState(null);
  //const [portalsRegistered, setPortalsRegisteredData] = useState(null);

  const validateDates = () => {
    const fromDate = getValues('requirementDeadLineFrom');
    const toDate = getValues('requirementDeadLineTo');
    console.log("request DAte", fromDate);
    console.log("request DAte", toDate);
    if ((fromDate && !toDate) || (!fromDate && toDate)) {
      setError(true);
    } else {
      setError(false);
    }
  };

  useEffect(() => {
    if (!!authConfig) {
      getAllListValuesByListNameId(
        authConfig.allServicesListNameId,
        (data) =>
          setServices(
            data?.listValues.map((item) => ({
              value: item.id,
              key: item.listValue,
            }))
          ),
        handleError
      );
    }
    // if (!!authConfig) {
    //   getAllListValuesByListNameId(
    //     authConfig.locationlistNameId,
    //     (data) =>
    //       setLocationsData(
    //         data?.listValues.map((item) => ({
    //           value: item.id,
    //           key: item.listValue,
    //         }))
    //       ),
    //     handleError
    //   );
    // }
   
    if (!!authConfig) {
      getAllListValuesByListNameId(
        authConfig.leadPriorityListNamesId,
        (data) =>
          setLeadPriorityData(
            data?.listValues.map((item) => ({
              value: item.id,
              key: item.listValue,
            }))
          ),
        handleError
      );
    }

    if (!!authConfig) {
      getAllListValuesByListNameId(
        authConfig.designationId,
        (data) =>
          setDesignation(
            data?.listValues.map((item) => ({
              value: item.id,
              key: item.listValue,
            }))
          ),
        handleError
      );
    }
    // if(!!authConfig){
    //     getAllListValuesByListNameId(
    //       authConfig.portalsRegisteredListNameId,
    //       (data) =>
    //         setPortalsRegisteredData(
    //           data?.listValues.map((item) => ({
    //             value: item.id,
    //             key: item.listValue,
    //           }))
    //         ),
    //       handleError
    //     );
    //   }
    if (!!authConfig) {
      getAllListValuesByListNameId(
        authConfig.statusListNamesId,
        (data) =>
          setLeadStatusData(
            data?.listValues.map((item) => ({
              value: item.id,
              key: item.listValue,
            }))
          ),
        handleError
      );
    }
    
  }, [authConfig]);

  const handleLeadStatusSuccess = (data) => setLeadStatusData(data?.listValues);
  const handleLeadPrioritySuccess = (data) =>setLeadPriorityData(data?.listValues);
  const handleServices = (data) => setServices(data?.listValues);
  const handleError = (error) => console.error("Error:", error);
  const handleDesignations = (data) => setDesignation(data?.listValues)

  return (
    <>
      <Tooltip title="Advanced Search">
        <CustomAvatar
          variant="rounded"
          sx={{ width: 36, height: 36, cursor: "pointer" }}
          onClick={toggle}
        >
          <Icon icon="tabler:filter" fontSize={27} />
        </CustomAvatar>
      </Tooltip>

     

      <Drawer
        open={open}
        anchor="right"
        variant="temporary"
        onClose={handleClose}
        ModalProps={{ keepMounted: true }}
        sx={{ "& .MuiDrawer-paper": { width: { xs: "90%", sm: 500 } } }} 
      >
        <Header sx={{
          position: "relative", 
          display: "flex",
          alignItems: "center", 
          justifyContent: "space-between", 
        }}>
          <Typography variant="h5"  sx={{
            marginLeft: "12px"}}>Advanced Search</Typography>
          <Box sx={{ position: "absolute", top: "16px", right: "26px"  }}>
            <IconButton
              size="small"
              onClick={handleClose}
              sx={{
                borderRadius: 1,
                color: "common.white",
                backgroundColor: "primary.main",
                "&:hover": {
                  backgroundColor: "#66BB6A",
                  transition: "background 0.5s ease, transform 0.5s ease",
                },
              }}
            >
              <Icon icon="tabler:x" fontSize="1rem" />
            </IconButton>
          </Box>
        </Header>
        <PerfectScrollbar options={{ wheelPropagation: false }}>
  <Box sx={{ p: (theme) => theme.spacing(4, 6) }}>
    <Grid container spacing={3} alignItems={"center"}>

      {/* Society Multi-Select */}
      <Grid item xs={12} md={12}>
        <FormControl fullWidth>
          <Controller
            name="socitiesUUIDs"
            control={control}
            rules={{ required: false }}
            render={({ field }) => (
              <MultiSelectAutoComplete
                id="socitiesUUIDs"
                size="small"
                label="Societies"
                nameArray={societyOptions || []}
                value={field.value || []}
                onChange={(e) => {
                  field.onChange(e);
                  updateFilters({
                    ...getValues(),
                    socitiesUUIDs: e.target.value,
                  });
                }}
                error={Boolean(errors.service)}
              />
            )}
          />
        </FormControl>
        {errors.leadPriority && (
          <FormHelperText sx={{ color: "error.main" }} id="validation-society">
            {errors.socitiesUUIDs?.message}
          </FormHelperText>
        )}
      </Grid>

      {/* Main Search TextField */}
      <Grid item xs={12} md={12}>
        <FormControl fullWidth>
          <Controller
            name="mainSearch"
            control={control}
            render={({ field: { onChange } }) => (
              <TextField
                id="mainSearch"
                placeholder="Search by Requisition Number"
                label="Requisition No."
                variant="outlined"
                InputLabelProps={{
                  shrink: true,
                }}
                value={searchKeyword}
                onChange={(e) => {
                  onChange(e.target.value);
                  setSearchKeyword(e.target.value);
                }}
                onKeyDown={(e) => {
                  if (e.key === "Enter") {
                    setSearchKeyword(searchKeyword);
                    fetchUsers(page, pageSize, searchKeyword, searchData);
                  }
                }}
                sx={{
                  "& .MuiInputBase-root": {
                    height: "40px",
                  },
                  "& .MuiInputBase-input::placeholder": {
                    fontSize: "0.9rem",
                  },
                }}
                InputProps={{
                  endAdornment: (
                    <InputAdornment position="start">
                      <SearchIcon
                        sx={{
                          cursor: "pointer",
                          marginRight: "-15px",
                        }}
                        onClick={() => {
                          setSearchKeyword(searchKeyword);
                          fetchUsers(page, pageSize, searchKeyword, searchData);
                        }}
                      />{" "}
                    </InputAdornment>
                  ),
                }}
              />
            )}
          />
        </FormControl>
      </Grid>

      {/* Search by Company Name */}
      <Grid item xs={12} md={12}>
        <FormControl fullWidth>
          <Controller
            name="searchKeywordByCompanyName"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                id="searchKeywordByCompanyName"
                placeholder="Search by contact name"
                label="Contact Name"
                variant="outlined"
                InputLabelProps={{
                  shrink: true,
                }}
                value={field.value || ""}
                onChange={(event) => {
                  updateFilters({
                    ...getValues(),
                    searchKeywordByCompanyName: event.target.value,
                  });
                }}
                onKeyDown={(e) => {
                  if (e.key === "Enter") {
                    fetchUsers(page, pageSize, searchKeyword, searchData);
                  }
                }}
                sx={{
                  "& .MuiInputBase-root": {
                    height: "40px",
                  },
                  "& .MuiInputBase-input::placeholder": {
                    fontSize: "0.9rem",
                  },
                }}
                InputProps={{
                  endAdornment: (
                    <InputAdornment position="start">
                      <SearchIcon
                        sx={{
                          cursor: "pointer",
                          marginRight: "-15px",
                        }}
                        onClick={(event) => {
                          updateFilters({
                            ...getValues(),
                            searchKeywordByCompanyName: event.target.value,
                          });
                        }}
                      />{" "}
                    </InputAdornment>
                  ),
                }}
              />
            )}
          />
        </FormControl>
      </Grid>

      {/* Search by Mobile Number */}
      <Grid item xs={12} md={12}>
        <FormControl fullWidth>
          <Controller
            name="searchKeywordByMobile"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                id="searchKeywordByMobile"
                placeholder="Search by Contact No."
                label="Contact No."
                variant="outlined"
                InputLabelProps={{
                  shrink: true,
                }}
                value={field.value || ""}
                onChange={(event) => {
                  updateFilters({
                    ...getValues(),
                    searchKeywordByMobile: event.target.value,
                  });
                }}
                onKeyDown={(e) => {
                  if (e.key === "Enter") {
                    fetchUsers(page, pageSize, searchKeyword, searchData);
                  }
                }}
                sx={{
                  "& .MuiInputBase-root": {
                    height: "40px",
                  },
                  "& .MuiInputBase-input::placeholder": {
                    fontSize: "0.9rem",
                  },
                }}
                InputProps={{
                  endAdornment: (
                    <InputAdornment position="start">
                      <SearchIcon
                        sx={{
                          cursor: "pointer",
                          marginRight: "-15px",
                        }}
                        onClick={(event) => {
                          updateFilters({
                            ...getValues(),
                            searchKeywordByMobile: event.target.value,
                          });
                        }}
                      />{" "}
                    </InputAdornment>
                  ),
                }}
              />
            )}
          />
        </FormControl>
      </Grid>

      {/* Lead Status Multi-Select */}
      <Grid item xs={12} md={12}>
        <FormControl fullWidth>
          <Controller
            name="leadStatusUUIDs"
            control={control}
            rules={{ required: false }}
            render={({ field }) => (
              <MultiSelectAutoComplete
                size="small"
                id="leadStatus"
                label="Status"
                nameArray={leadStatusData || []}
                value={field.value || []}
                onChange={(e) => {
                  field.onChange(e);
                  updateFilters({
                    ...getValues(),
                    leadStatusUUIDs: e.target.value,
                  });
                }}
                error={Boolean(errors.serivcesProvided)}
              />
            )}
          />
        </FormControl>
        {errors.leadStatus && (
          <FormHelperText sx={{ color: "error.main" }} id="validation-leadStatus">
            {errors.leadStatus?.message}
          </FormHelperText>
        )}
      </Grid>
      <Grid item xs={12} md={12}>
        <FormControl fullWidth>
          <Controller
            name="requirementDeadLineFrom"
            control={control}
            defaultValue=""
            render={({ field }) => (
              <TextField
                {...field}
                size="small"
                label="Site Visit From"
                type="date"
                InputLabelProps={{ shrink: true }}
                aria-describedby="requirementDeadLineFrom"
                value={field.value}
                error={Boolean(errors.requirementDeadLineFrom) && !field.value}
                helperText={
                  Boolean(errors.requirementDeadLineFrom) && !field.value
                    ? "This field is required"
                    : ""
                }
                onChange={(event) => {
                  updateFilters({
                    ...getValues(),
                    requirementDeadLineFrom: event.target.value,
                  });
                  validateDates();
                }}
              />
            )}
          />
        </FormControl>
      </Grid>

      {/* Site Visit Date To */}
      <Grid item xs={12} md={12}>
        <FormControl fullWidth>
          <Controller
            name="requirementDeadLineTo"
            control={control}
            defaultValue=""
            render={({ field }) => (
              <TextField
                {...field}
                size="small"
                label="Site Visit To"
                type="date"
                InputLabelProps={{ shrink: true }}
                aria-describedby="requirementDeadLineTo"
                value={field.value}
                inputProps={{
                  min: getValues("requirementDeadLineFrom"),
                }}
                error={Boolean(errors.requirementDeadLineTo) && !field.value}
                helperText={
                  Boolean(errors.requirementDeadLineTo) && !field.value
                    ? "This field is required"
                    : ""
                }
                onChange={(event) => {
                  updateFilters({
                    ...getValues(),
                    requirementDeadLineTo: event.target.value,
                  });
                  validateDates();
                }}
              />
            )}
          />
        </FormControl>
      </Grid>
      
    </Grid>
  </Box>
</PerfectScrollbar>

        <Box
          sx={{
            borderTop: (theme) => `1px solid ${theme.palette.divider}`,
            p: (theme) => theme.spacing(2),
            justifyContent: "flex-end",
            display: "flex",
            alignItems: "center",
          }}
        >
          <Button variant="tonal" sx={{ mr: 2 }} onClick={handleCancel}>
            Clear All
          </Button>
          <Button variant="contained" sx={{ mr: {
                xs: 4, 
                sm: 4, 
                md: 4, 
                lg: 4, 
                xl: 4,
              },} } onClick={handleApply}>
            Apply
          </Button>
        </Box>
      </Drawer>



    </>
  );
};

export default AdvancedSearch;
