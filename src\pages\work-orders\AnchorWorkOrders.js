import React, { useContext, useEffect } from "react";
import {
  Dialog,
  DialogTitle,
  DialogActions,
  Button,
  Table,
  TableBody,
  TableRow,
  Typography,
  TableContainer,
  Box,
  IconButton,
} from "@mui/material";
import { useTheme } from "@emotion/react";
import MUITableCell from "src/pages/SP/MUITableCell";
import { Icon } from "@iconify/react";
import { format } from "date-fns";

const fieldLabelStyle = { fontSize: "12.75px" };
const fieldValueStyle = {
  fontWeight: "bold",
  wordWrap: "break-word",
  whiteSpace: "pre-wrap",
  color: "#108A00",
};
const MUITableCellLabelStyle = {
  width: { xs: "50%", sm: "50%", md: "40%" },
  textAlign: "right",
  verticalAlign: "middle",
  height: "2px !important",
};
const MUITableCellValueStyle = {
  justifyContent: "flex-start",
  alignItems: "center",
  width: { xs: "50%", sm: "50%", md: "60%" },
  height: "2px !important",
};

const AnchorWorkOrders = ({ open, onClose, currentRow }) => {
  const theme = useTheme();
  return (
    <Dialog open={open} onClose={onClose} fullWidth>
      <DialogTitle
        sx={{
          borderBottom: `1px solid ${theme.palette.divider}`,
          p: theme.spacing(1.75, 4),
          display: "flex",
          alignItems: "center",
          justifyContent: "start",
          fontSize: { xs: 19, md: 20 },
          height: "auto",
        }}
      >
        <Box
          sx={{
            ml: {
              xs: 4,
              xl: 3,
            },
          }}
        >
          Work Orders Details
        </Box>
        <Box
          sx={{
            position: "absolute",
            top: "6px",
            right: "3px",
            mr: {
              xs: 4,
              xl: 6,
            },
          }}
        >
          <IconButton
            size="small"
            onClick={onClose}
            sx={{
              borderRadius: 1,
              color: "common.white",
              backgroundColor: "primary.main",
              "&:hover": {
                backgroundColor: "#66BB6A",
                transition: "background 0.5s ease, transform 0.5s ease",
              },
            }}
          >
            <Icon icon="tabler:x" fontSize="1rem" />
          </IconButton>
        </Box>
      </DialogTitle>
      <TableContainer className="tableBody">
        <Table>
          <TableBody
            sx={{
              "& .MuiTableCell-root": {
                p: `${theme.spacing(1.35, 1.125)} !important`,
              },
            }}
          >
            {/* Invoice Date */}
            <TableRow>
              <MUITableCell sx={MUITableCellLabelStyle}>
                <Typography className="data-field" style={fieldLabelStyle}>
                  Work Order Number:
                </Typography>
              </MUITableCell>
              <MUITableCell sx={MUITableCellValueStyle}>
                <Typography style={fieldValueStyle}>
                  {currentRow?.workOrdersSystemCode || "-"}
                </Typography>
              </MUITableCell>
            </TableRow>

            {/* Service Requisition Number */}
            <TableRow>
              <MUITableCell sx={MUITableCellLabelStyle}>
                <Typography className="data-field" style={fieldLabelStyle}>
                  Service Requisition Number:
                </Typography>
              </MUITableCell>
              <MUITableCell sx={MUITableCellValueStyle}>
                <Typography style={fieldValueStyle}>
                  {currentRow?.systemCode || "-"}
                </Typography>
              </MUITableCell>
            </TableRow>

            {/* society Name */}
            <TableRow>
              <MUITableCell sx={MUITableCellLabelStyle}>
                <Typography className="data-field" style={fieldLabelStyle}>
                  Society Name:
                </Typography>
              </MUITableCell>
              <MUITableCell sx={MUITableCellValueStyle}>
                <Typography style={fieldValueStyle}>
                  {currentRow?.societyName || "-"}
                </Typography>
              </MUITableCell>
            </TableRow>

            {/* Status */}
            <TableRow>
              <MUITableCell sx={MUITableCellLabelStyle}>
                <Typography className="data-field" style={fieldLabelStyle}>
                  Status:
                </Typography>
              </MUITableCell>
              <MUITableCell sx={MUITableCellValueStyle}>
                <Typography style={fieldValueStyle}>
                  {currentRow?.isActive ? "Active" : "Inactive" || "-"}
                </Typography>
              </MUITableCell>
            </TableRow>

            {/* Scope of Work */}
            <TableRow>
              <MUITableCell sx={MUITableCellLabelStyle}>
                <Typography className="data-field" style={fieldLabelStyle}>
                  Scope of Work:
                </Typography>
              </MUITableCell>
              <MUITableCell sx={MUITableCellValueStyle}>
                {currentRow?.scopeOfWork?.length > 0 ? (
                  currentRow.scopeOfWork.map((item, index) => (
                    <div key={index}>
                      <Typography style={fieldValueStyle}>
                        {item?.sequence || "-"}. {item?.name || "-"}
                      </Typography>
                    </div>
                  ))
                ) : (
                  <Typography style={fieldValueStyle}>-</Typography>
                )}
              </MUITableCell>
            </TableRow>

            {/* Terms and Condition */}
            <TableRow>
              <MUITableCell sx={MUITableCellLabelStyle}>
                <Typography className="data-field" style={fieldLabelStyle}>
                  Terms and Conditions:
                </Typography>
              </MUITableCell>
              <MUITableCell sx={MUITableCellValueStyle}>
                {currentRow?.termsAndConditions?.descriptions?.length > 0 ? (
                  currentRow.termsAndConditions.descriptions.map(
                    (item, index) => (
                      <Typography key={index} style={fieldValueStyle}>
                        {item?.description || "-"}
                      </Typography>
                    )
                  )
                ) : (
                  <Typography style={fieldValueStyle}>-</Typography>
                )}
              </MUITableCell>
            </TableRow>

            {/* Created On */}
            <TableRow>
              <MUITableCell sx={MUITableCellLabelStyle}>
                <Typography className="data-field" style={fieldLabelStyle}>
                  Created On:
                </Typography>
              </MUITableCell>
              <MUITableCell sx={MUITableCellValueStyle}>
                <Typography style={fieldValueStyle}>
                  {currentRow?.createdOn
                    ? format(
                        new Date(currentRow?.createdOn),
                        "dd-MM-yyyy"
                      )
                    : "-"}
                </Typography>
              </MUITableCell>
            </TableRow>

            {/* updated on */}
            <TableRow>
              <MUITableCell sx={MUITableCellLabelStyle}>
                <Typography className="data-field" style={fieldLabelStyle}>
                  Updated On:
                </Typography>
              </MUITableCell>
              <MUITableCell sx={MUITableCellValueStyle}>
                <Typography style={fieldValueStyle}>
                  {currentRow?.createdOn
                    ? format(
                        new Date(currentRow?.updatedOn),
                        "dd-MM-yyyy"
                      )
                    : "-"}
                </Typography>
              </MUITableCell>
            </TableRow>
          </TableBody>
        </Table>
      </TableContainer>

      <DialogActions
        sx={{
          justifyContent: "end",
          borderTop: (theme) => `1px solid ${theme.palette.divider}`,
          p: (theme) => `${theme.spacing(2.5)} !important`,
          height: "46px", // height
        }}
      >
        <Box
          sx={{
            mr: {
              xs: 3,
              xl: 5,
            },
          }}
        >
          <Button onClick={onClose} color="primary">
            Close
          </Button>
        </Box>
      </DialogActions>
    </Dialog>
  );
};

export default AnchorWorkOrders;
