import { DataGrid } from "@mui/x-data-grid";
import { useContext, useEffect, useState } from "react";
import authConfig from "src/configs/auth";
import { getAuthorizationHeaders, getUrl } from "src/helpers/utils";
import { useAuth } from "src/hooks/useAuth";
import { useRBAC } from "src/pages/permission/RBACContext";
import { MENUS, PAGES, PERMISSIONS } from "src/constants";
import { useRouter } from "next/router";

import CustomChip from "src/@core/components/mui/chip";
import {
  Button,
  Card,
  CardContent,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Divider,
  FormControl,
  Grid,
  IconButton,
  InputAdornment,
  TextField,
  Tooltip,
  Typography,
  Menu,
  MenuItem,
  Link,
} from "@mui/material";
import { Tabs, Tab } from "@mui/material";
import Icon from "src/@core/components/icon";
import SearchIcon from "@mui/icons-material/Search";

import CustomAvatar from "src/@core/components/mui/avatar";
import axios from "axios";

import { Controller, useForm } from "react-hook-form";

import { Box } from "@mui/system";

import { AuthContext } from "src/context/AuthContext";

const userStatusObj = {
  true: "Active",
  false: "InActive",
};

const WhatsAppMessages = () => {
  const { user, listValues } = useContext(AuthContext);

  const [expanded, setExpanded] = useState(true);

  const [openViewDialog, setOpenViewDialog] = useState(false);
  // Constants
  const rowsPerPageOptions = [5, 10, 15, 20, 25, 50, 100];
  const [anchorEl, setAnchorEl] = useState(null);

  const [selectedTab, setSelectedTab] = useState(0);

  const rows = [
    {
      id: 1,
      category: "SP",
      name: "Thogiti Shashank",
      cName: "Dial4clean",
      contactNumber: "+91 9515599023",
      siteVisitDate: "08-08-2023",
      startTime: "10:00 AM",
      status: true,
      endTime: "12:00 PM",
    },
    {
      id: 2,
      category: "CHS",
      name: "Siva Nasina",
      cName: "Aditya heights",
      contactNumber: "+91 6300573655",
      siteVisitDate: "10-08-2023",
      startTime: "2:00 PM",
      status: false,
      endTime: "4:00 PM",
    },
  ];

  const mapIsActiveToLabel = (isActive) => {
    return userStatusObj[isActive] || "Unknown";
  };

  // Define columns
  const columns = [
    {
      field: "category",
      minWidth: 75,
      headerName: "Category",
      flex: 0.05,
      //   valueGetter: (params) => params.row.additionalDetails.title,
    },
    {
      field: "name",
      minWidth: 135,
      headerName: "Name",
      flex: 0.1,
      //   valueGetter: (params) => params.row.additionalDetails.title,
    },
    {
      field: "contactNumber",
      minWidth: 150,
      headerName: "Contact Number",
      flex: 0.1,

      renderCell: (params) => {
        const contactNumber = params?.value;
        return contactNumber?.length > 9 ? (
          <Tooltip title={contactNumber}>
            <Link
              href={`tel:${contactNumber}`}
              target="_blank"
              rel="noopener noreferrer"
              sx={{ color: "#6666ff" }}
            >
              <span>{contactNumber}</span>
            </Link>
          </Tooltip>
        ) : (
          <Link
            href={`tel:${contactNumber}`}
            target="_blank"
            rel="noopener noreferrer"
            sx={{ color: "#6666ff" }}
          >
            {contactNumber}
          </Link>
        )
      },
    },
    {
      field: "cName",
      minWidth: 135,
      headerName: "Company Name / Society Name",
      flex: 0.13,
      //   valueGetter: (params) => params.row.additionalDetails.title,
    },
    {
      field: "siteVisitDate",
      minWidth: 95,
      headerName: "Message Sent Date",
      flex: 0.1,
    },
    {
      field: "status",
      minWidth: 135,
      headerName: "Status",
      flex: 0.1,
      renderCell: ({ row }) => {
        return (
          <CustomChip
            rounded={true}
            skin="light"
            size="small"
            label={mapIsActiveToLabel(row.status)}
            color={row.status === true ? "success" : "error"}
            sx={{ textTransform: "capitalize" }}
          />
        );
      },
    },
    {
      flex: 0.07,
      field: "edit",
      headerName: "Actions",
      sortable: false,
      minWidth: 90,
      disableClickEventBubbling: true,
      renderCell: (params) => {
        const row = params.row;

        const handleClick = (event) => {
          setAnchorEl(event.currentTarget);
        };

        const handleClose = () => {
          setAnchorEl(null);
        };

        const handleEdit = () => {
          setCurrentRow(row);
          //   setOpenViewDialog(true);
          handleClose();
        };

        const handleDelete = () => {
          // Add delete logic here
          handleClose();
        };

        return (
          <div
            style={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
            }}
          >
              <CustomAvatar
                skin="light"
                variant="rounded"
                sx={{
                  mr: { xs: 2, lg: 4 },
                  width: 34,
                  height: 34,
                  cursor: "pointer",
                }}
                onClick={handleClick}
              >
                <Icon icon="bi:three-dots-vertical" />
              </CustomAvatar>
            <Menu
              anchorEl={anchorEl}
              open={Boolean(anchorEl)}
              onClose={handleClose}
            >
              <MenuItem onClick={handleEdit}>Edit</MenuItem>
              <MenuItem onClick={handleEdit}>Remind</MenuItem>
              <MenuItem onClick={handleDelete}>Delete</MenuItem>
            </Menu>
          </div>
        );
      },
    },
  ].filter(Boolean); // Filter out null values if the condition is false

  // Use States
  const {
    register,
    handleSubmit,
    setError,
    control,
    reset,
    clearErrors,
    formState: { errors },
  } = useForm();

  const auth = useAuth();
  const [userList, setUserList] = useState([]);
  const [pageSize, setPageSize] = useState(rowsPerPageOptions[0]);
  const [loading, setLoading] = useState(false);
  const [page, setPage] = useState(1);
  const [rowCount, setRowCount] = useState(0);
  const [keyword, setKeyword] = useState("");
  const [searchKeyword, setSearchKeyword] = useState("");
  const [currentRow, setCurrentRow] = useState("");

  const [openDialog, setOpenDialog] = useState(false);

  const submit = (data) => {
    handleCloseDialog();
  };

  const close = (data) => {
    handleCloseEditDialog();
  };

  useEffect(() => {
    fetchUsers(page, pageSize, searchKeyword);
  }, [page, pageSize, searchKeyword]);

  const fetchUsers = async (currentPage, currentPageSize, searchKeyword) => {
    setLoading(true);

    // const url = getUrl(authConfig.getAllTasks)
    const headers = getAuthorizationHeaders();

    const data = {
      page: currentPage,
      pageSize: currentPageSize,
      searchKeyword: searchKeyword,
    };

    try {
      const response = await axios({
        method: "post",
        url: url,
        headers: headers,
        data: data,
      });

      if (response.data) {
        setUserList(response.data?.tasks || []);
        setRowCount(response.data?.rowCount || 0);
      } else {
        console.error("Unexpected API response format:", response);
      }
    } catch (error) {
      console.error("Error fetching users:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleOpenDialog = () => {
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    reset();
    setOpenDialog(false);
  };

  const handleCloseEditDialog = () => {
    reset();
    setOpenViewDialog(false);
  };

  const handlePageChange = (direction) => {
    if (direction === page) {
      setPage(page + 1);
    } else {
      setPage(page - 1);
    }
  };

  const handlePageSizeChange = (params) => {
    if (params) {
      setPageSize(params);
      setPage(1);
    }
  };

  const { canMenuPage, rbacRoles } = useRBAC();

  const router = useRouter();

  const canAccessWhatsApp = (requiredPermission) =>
    canMenuPage(MENUS.LEFT, PAGES.WHATS_APP, requiredPermission);

  useEffect(() => {
    if (rbacRoles != null && rbacRoles.length > 0) {
      if (!canAccessWhatsApp(PERMISSIONS.READ)) {
        router.push("/401");
      }
    }
  }, [rbacRoles]);

  if(canAccessWhatsApp(PERMISSIONS.READ)) {
  return (
    <>
      <div>
        <Box
          sx={{
            py: 3,
            px: 6,
            rowGap: 2,
            columnGap: 4,
            display: "flex",
            flexWrap: "wrap",
            alignItems: "center",
            justifyContent: "space-between",
          }}
        >
          <Grid container spacing={3} alignItems="center">
            <Grid item xs={12} sm={4} sx={{ textAlign: "flex-start" }}>
              <Typography variant="h6">Broadcast WhatsApp Messages</Typography>
            </Grid>

            <Grid item xs={12} sm={8}>
              <Grid
                container
                spacing={2}
                alignItems="center"
                justifyContent="flex-end"
              >
                <Grid item xs={12} sm="auto">
                  <FormControl>
                    <Controller
                      name="mainSearch"
                      control={control}
                      // defaultValue={name}
                      render={({ field: { onChange } }) => (
                        <TextField
                          id="mainSearch"
                          placeholder="Search by Name"
                          value={keyword}
                          onChange={(e) => {
                            onChange(e.target.value);
                            setKeyword(e.target.value);
                            setSearchKeyword(e.target.value);
                          }}
                          onKeyDown={(e) => {
                            if (e.key === "Enter") {
                              setSearchKeyword(keyword);
                              fetchValues(page, pageSize, searchKeyword);
                            }
                          }}
                          sx={{
                            "& .MuiInputBase-root": {
                              height: "40px",
                            },
                          }}
                          InputProps={{
                            endAdornment: (
                              <InputAdornment position="start">
                                <SearchIcon
                                  sx={{
                                    cursor: "pointer",
                                    marginRight: "-15px",
                                  }}
                                  onClick={() => {
                                    setSearchKeyword(keyword);
                                    fetchValues(page, pageSize, searchKeyword);
                                  }}
                                />{" "}
                              </InputAdornment>
                            ),
                          }}
                        />
                      )}
                    />
                  </FormControl>
                </Grid>
                <Grid item xs="auto" sm="auto">
                  <Tooltip title="Advanced Search">
                    <CustomAvatar
                      variant="rounded"
                      sx={{ width: 36, height: 36, cursor: "pointer" }}
                      // onClick={toggle}
                    >
                      <Icon icon="tabler:filter" fontSize={27} />
                    </CustomAvatar>
                  </Tooltip>
                </Grid>
              </Grid>
            </Grid>
          </Grid>
        </Box>
        <Divider />
        <CardContent>
          <div style={{ height: 380, width: "100%" }}>
            <DataGrid
              // rows={userList || []}
              rows={rows}
              columns={columns}
              pagination
              pageSize={pageSize}
              page={page - 1}
              rowsPerPageOptions={userList.length > 0 ? rowsPerPageOptions : []}
              rowCount={rowCount}
              paginationMode="server"
              checkboxSelection
              onPageChange={handlePageChange}
              onPageSizeChange={handlePageSizeChange}
              rowHeight={38}
              headerHeight={38}
            />
          </div>
        </CardContent>
      </div>

      <Dialog
        open={openDialog}
        onClose={handleCloseDialog}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle
          sx={{
            position: "relative",
            borderBottom: (theme) => `1px solid ${theme.palette.divider}`,
            p: (theme) => `${theme.spacing(1.75, 4)} !important`,
            display: "flex",
            alignItems: "center",
            justifyContent: { xs: "start" },
            fontSize: { xs: 19, md: 20 },
            margin: "2px 2px 2px 2px",
          }}
          textAlign={"center"}
        >
          Site Visit Timings
          <Box
            sx={{
              position: "absolute",
              top: "4px",
              right: "14px",
            }}
          >
            <IconButton
              size="small"
              onClick={handleCloseDialog}
              sx={{
                p: "0.2rem",
                borderRadius: 1,
                color: "common.white",
                backgroundColor: "primary.main",
                "&:hover": {
                  backgroundColor: "#66BB6A",
                  transition: "background 0.5s ease, transform 0.5s ease",
                },
              }}
            >
              <Icon icon="tabler:x" fontSize="1rem" />
            </IconButton>
          </Box>
        </DialogTitle>
        <DialogContent
          sx={{
            position: "relative",
            pt: (theme) => `${theme.spacing(8)} !important`,
            pb: (theme) => `${theme.spacing(5)} !important`,
            px: (theme) => [`${theme.spacing(8)} !important`],
          }}
        >
          <Grid
            container
            alignItems="center"
            spacing={3}
            sx={{ marginBottom: 2 }}
          ></Grid>
        </DialogContent>
        <DialogActions
          sx={{
            justifyContent: "end",
            borderTop: (theme) => `1px solid ${theme.palette.divider}`,
            p: (theme) => `${theme.spacing(2.5)} !important`,
          }}
        >
          <Button onClick={handleCloseDialog}>Cancel</Button>

          <Button onClick={submit} variant="contained">
            Add
          </Button>
        </DialogActions>
      </Dialog>

      {/* Edit Dialog Box */}
      <Dialog
        open={openViewDialog}
        onClose={handleCloseEditDialog}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle
          sx={{
            position: "relative",
            borderBottom: (theme) => `1px solid ${theme.palette.divider}`,
            p: (theme) => `${theme.spacing(1.75, 4)} !important`,
            display: "flex",
            alignItems: "center",
            justifyContent: { xs: "start" },
            fontSize: { xs: 19, md: 20 },
            margin: "2px 2px 2px 2px",
          }}
          textAlign={"center"}
        >
          Edit Site Visit Timings
          <Box
            sx={{
              position: "absolute",
              top: "4px",
              right: "14px",
            }}
          >
            <IconButton
              size="small"
              onClick={handleCloseEditDialog}
              sx={{
                p: "0.2rem",
                borderRadius: 1,
                color: "common.white",
                backgroundColor: "primary.main",
                "&:hover": {
                  backgroundColor: "#66BB6A",
                  transition: "background 0.5s ease, transform 0.5s ease",
                },
              }}
            >
              <Icon icon="tabler:x" fontSize="1rem" />
            </IconButton>
          </Box>
        </DialogTitle>
        <DialogContent
          sx={{
            position: "relative",
            pt: (theme) => `${theme.spacing(8)} !important`,
            pb: (theme) => `${theme.spacing(5)} !important`,
            px: (theme) => [`${theme.spacing(8)} !important`],
          }}
        >
          <Grid
            container
            alignItems="center"
            spacing={3}
            sx={{ marginBottom: 2 }}
          ></Grid>
        </DialogContent>
        <DialogActions
          sx={{
            justifyContent: "end",
            borderTop: (theme) => `1px solid ${theme.palette.divider}`,
            p: (theme) => `${theme.spacing(2.5)} !important`,
          }}
        >
          <Button onClick={handleCloseEditDialog}>Cancel</Button>

          <Button onClick={close} variant="contained">
            Update
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
} else {
  return null;
}
};

export default WhatsAppMessages;
