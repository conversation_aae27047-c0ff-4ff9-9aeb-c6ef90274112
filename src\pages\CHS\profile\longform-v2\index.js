import React from "react";
import {
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  Divider,
  FormHelperText,
  IconButton,
  InputLabel,
  MenuItem,
  Select,
  Table,
  TableBody,
  TableCell,
  Grid,
  Button,
  TableHead,
  TableRow,
  Typography,
  DialogTitle,
  Box,
  InputAdornment
} from "@mui/material";
import Icon from "src/@core/components/icon";
import FormControl from "@mui/material/FormControl";
import { Controller, useForm, useWatch } from "react-hook-form";
import { useAuth } from "src/hooks/useAuth";
import EmailTextField from "src/@core/components/custom-components/EmailTextField";
import NameTextField from "src/@core/components/custom-components/NameTextField";
import TextField from "@mui/material/TextField";
import MobileNumberValidation from "src/@core/components/custom-components/MobileNumberValidation";
import GoogleMapsIconButton from "src/@core/components/custom-components/toastDisplay.js"
import CheckCircleOutlineIcon from "@mui/icons-material/CheckCircleOutline";
import SelectAutoComplete from "src/@core/components/custom-components/SelectAutoComplete";
import { useContext, useEffect, useState, useRef } from "react";
import axios from "axios";
import { getAuthorizationHeaders, getUrl } from "src/helpers/utils";
import authConfig from "src/configs/auth";
import { AuthContext } from "src/context/AuthContext";
import UploadFile from "./upload-team-member-signature";
import MultiSelectAutoComplete from "src/@core/components/custom-components/MultiSelectAutoComplete";
import UploadFileCommittee from "./upload-committe-signature";

const SocietyEnrollmentDialog = ({ open, onClose }) => {
  const { getAllListValuesByListNameId } = useContext(AuthContext);
  const [listOfSubCategories, setListOfSubCategories] = useState([])
  const [designation,setDesignation] = useState(null)
  const [anyOther,setAnyOther] = useState(false)
  const [locationsData,setLocationsData] = useState([])
  const [services,setServices] = useState([])
  const [leadStatusData, setLeadStatusData] = useState(null);
  const [leadPriorityData, setLeadPriorityData] = useState(null);
  const [referenceOption, setReferenceOption] = useState("");
  const [showUpload, setShowUpload] = useState(false); // State to toggle UploadFile visibility
  const [selectedFiles, setSelectedFiles] = useState([]);
  const [selectedFilesCommitte, setSelectedFilesCommitte] = useState([]);
  const [document, setDocument] = useState(null);
  const [documentcommitte, setDocumentcommitte] = useState(null);
  const handleError = (error) => console.error("Error:", error);
  const [leadStatus, setLeadStatus] = useState();
  const [leadPriority, setLeadPriority] = useState();
  const [reference, setReference] = useState();


  const referenceOptions = [
    { value: "Broker", key: "Broker" },
    { value: "Friend", key: "Friend" },
    { value: "Professional", key: "Professional" },
    { value: "Social Media", key: "Social Media" },
    { value: "Advertisement", key: "Advertisement" },
    { value: "Website", key: "Website" },
    { value: "Houzer Team Member", key: "Houzer Team Member" },
    { value: "Any Other", key: "Any Other" },
  ];

  const handleReferenceTypeChange = (e) => {
    const selectedValue = e.target.value;
    setReference(selectedValue);
  };


  const handleReferenceChange = (event) => {
    setReferenceOption(event.target.value);
  };

  const handleLeadPriorityChange = (event) => {
    const selectedId = event.target.value;
    setLeadPriority(selectedId);
  };


  const handleUploadClick = () => {
    setShowUpload(true); 
  };

   



  const auth = useAuth();
  const { entityData } = useContext(AuthContext);

  const {
    register,
    handleSubmit,
    setError,
    control,
    clearErrors,
    reset,
    formState: { errors },
  } = useForm({
    defaultValues: {
      firstName: "",
      email: "",
    },
    });



    useEffect(() => {
      if (!!authConfig) {
        getAllListValuesByListNameId(
          authConfig.allServicesListNameId,
          (data) =>
            setServices(
              data?.listValues.map((item) => ({
                value: item.id,
                key: item.listValue,
              }))
            ),
          handleError
        );
      }
      if (!!authConfig) {
        getAllListValuesByListNameId(
          authConfig.locationlistNameId,
          (data) =>
            setLocationsData(
              data?.listValues.map((item) => ({
                value: item.id,
                key: item.listValue,
              }))
            ),
          handleError
        );
      }
      if (!!authConfig) {
        getAllListValuesByListNameId(
          authConfig.leadStatusListNamesId,
          (data) =>
            setLeadStatusData(
              data?.listValues.map((item) => ({
                value: item.id,
                key: item.listValue,
              }))
            ),
          handleError
        );
      }
      if (!!authConfig) {
        getAllListValuesByListNameId(
          authConfig.leadPriorityListNamesId,
          (data) =>
            setLeadPriorityData(
              data?.listValues.map((item) => ({
                value: item.id,
                key: item.listValue,
              }))
            ),
          handleError
        );
      }
      if(!!authConfig){
        getAllListValuesByListNameId(
          authConfig.ChsDesignation,
          (data) =>
            setListOfSubCategories(
              data?.listValues?.map((item) => ({
                value: item.id,
                key: item.listValue,
              }))
            ),
          handleError
        );
      }
      
    }, [authConfig]);


    useEffect(() => {
      axios({
        method: "get",
        url: getUrl(authConfig.selectDropdownNew) + "?selectionType=EMPLOYEES",
        headers: getAuthorizationHeaders(),
      })
        .then((res) => {
          setListOfEmployees(res.data.data);
        })
        .catch((err) => console.log("Employees error", err));
    }, []);

    
    const validateLocationUrl = (value) => {
      const urlPattern = /^https:\/\/(www\.)?(maps\.app\.goo\.gl|google\.com\/maps)\/.+$/;
    if (!urlPattern.test(value)) {
      return 'Please enter a valid Google Maps URL';
    }
    try {
      const url = new URL(value);
      if (url.hostname !== 'maps.app.goo.gl' && url.hostname !== 'google.com') {
        return 'Please enter a valid Google Maps URL';
      }
    } catch (error) {
      return 'Invalid URL format';
    }
    return true;
  };

  const handleLeadStatusChange = (event) => {
    const value = event.target.value;
    setLeadStatus(value);
  };
  const handleDesignationChange = (newValue) => {
    setDesignation(newValue)
    const matchedSubCategory = listOfSubCategories.find(subCategory => subCategory.value === newValue);
    if(matchedSubCategory?.key == 'Any Other'){
      setAnyOther(true)
    }else{
      setAnyOther(false)
    }
  };
 
  
  const [listOfEmployees, setListOfEmployees] = useState([]);
  const [employeesOptions, setEmployeesOptions] = useState([]);
  useEffect(() => {
    if (listOfEmployees.length) {
      const data = listOfEmployees.map((entry) => ({
        value: entry.id,
        key: entry.name,
      }));
      setEmployeesOptions(data);
    }
  }, [listOfEmployees]);

  const gstOption = useWatch({ control, name: "gstOption" });
  return (
    <Dialog open={open} onClose={onClose} fullScreen maxWidth="sm" scroll="body">
      <DialogTitle
          sx={{
          position: "sticky",
          top: 0, // Sticks it to the top of the dialog
          zIndex: 10, // Ensures it stays above the content
          backgroundColor: "background.paper",
            borderBottom: (theme) => `1px solid ${theme.palette.divider}`,
            p: (theme) => `${theme.spacing(1.75, 7)} !important`,
            display: "flex",
            alignItems: "center",
            justifyContent:"space-between",
            fontSize: { xs: 19, md: 20 },
            height: "50px", // height
          }}
          textAlign={"center"}
          
        >
            Add New CHS(Long Form)&nbsp;

            <Box sx={{ top: "9px", right: "10px" }}>

            <IconButton
              size="small"
              onClick={onClose}
              sx={{
                // p: "0.438rem",
                borderRadius: 1,
                color: "common.white",
                backgroundColor: "primary.main",
                "&:hover": {
                  backgroundColor: "#66BB6A",
                  transition: "background 0.5s ease, transform 0.5s ease",
                },
              }}
            >
              <Icon icon="tabler:x" fontSize="1rem" />
            </IconButton>
          </Box>
     
         




        </DialogTitle>
        <DialogContent
            sx={{ p: (theme) => `${theme.spacing(10, 7)} !important` }}
          >
            <Grid container spacing={5}>
              {/* Section Header */}
              <Grid
                item
                xs={12}
                sx={{
                  backgroundColor: "#f2f7f2",
                  mt: 4,
                  ml:5,
                  paddingTop: 0,  
                  height: "36px",
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "space-between"
                }}
              >
                <Typography
                  variant="body1"
                  fontWeight={"bold"}
                  sx={{ mt: 0, ml: -3, mb: 5 ,
                    textAlign: "center", // Aligns text in the center
                    
                  }}
                  
                >
                  Society Information
                </Typography>
                <Divider />
              </Grid>

              {/* Input Field for Society Name */}
              <Grid item xl={4} lg={4} xs={12} sm={4}>
                <FormControl fullWidth>
                  <Controller
                    name="societyName"
                    control={control}
                    rules={{ required: true }}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        label="Society Name"
                        InputLabelProps={{ shrink: true }}
                        size="small"
                        placeholder="Enter your Society name"
                        error={Boolean(errors.societyName)}
                        helperText={errors.societyName?.message || ""}
                        aria-describedby="validation-basic-societyName"
                      />
                    )}
                  />
                </FormControl>
              </Grid>



              <Grid item xl={4} lg={4}xs={12} sm={4}>
                <FormControl fullWidth>
                  <Controller
                    name="googleMapLocation"
                    control={control}
                    rules={{ required: true, validate: validateLocationUrl }}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        label="Google location"
                        InputLabelProps={{ shrink: true }}
                        size="small"
                        placeholder="Click on icon to navigate & paste URL here"
                        error={Boolean(errors.googleMapLocation)}
                        helperText={errors.googleMapLocation?.message}
                        aria-describedby="validation-basic-googleMapLocation"
                        InputProps={{
                          endAdornment: (
                            <InputAdornment position="end">
                              <span style={{ position: 'absolute', right: 8, top: 0 }}>
                                <GoogleMapsIconButton />
                              </span>
                            </InputAdornment>
                          ),
                        }}
                      />
                    )}
                  />
                </FormControl>
              </Grid>


              
              <Grid item lg={4} xl={4} xs={12} sm={4}>
                <FormControl fullWidth>
                  <InputLabel id="registered-label">Registered For</InputLabel>
                  <Controller
                    name="registeredOption"
                    control={control}
                    render={({ field }) => (
                      <Select
                        {...field}
                        size="small"
                        label="Registered For"
                        InputLabelProps={{ shrink: true }}
                        placeholder="Select"
                      >
                        <MenuItem value="RED">RED</MenuItem>
                        <MenuItem value="PRO">PRO</MenuItem>
                      </Select>
                    )}
                  />
                </FormControl>
              </Grid>


              <Grid item xs={12} sm={4}>
            <FormControl fullWidth>
              <InputLabel id="requisition-label">Requisition</InputLabel>
              <Controller
                name="requisitionOption"
                control={control}
                // defaultValue={formData?.requisitionOption || ""}
                render={({ field }) => (
                  <Select
                    {...field}
                    size="small"
                    labelId="requisition-label"
                    label="Requisition"
                    InputLabelProps={{ shrink: true }}
                    placeholder="Select"
                  >
                    <MenuItem value="yes">Yes</MenuItem>
                    <MenuItem value="no">No</MenuItem>
                  </Select>
                )}
              />
            </FormControl>
          </Grid>


          <Grid item xs={12} sm={4} >
                <FormControl fullWidth>
                  <InputLabel id="readiness-label">Readiness</InputLabel>
                  <Controller
                    name="readinessOption"
                    control={control}
                    // defaultValue={formData?.readinessOption || ""}
                    render={({ field }) => (
                      <Select
                        {...field}
                        size="small"
                        labelId="readiness-label"
                        label="Readiness"
                      >
                        <MenuItem value="yes">Yes</MenuItem>
                        <MenuItem value="no">No</MenuItem>
                      </Select>
                    )}
                  />
                </FormControl>
              </Grid>







              {/* address */}
              <Grid item xs={12} lg={30} >
          <FormControl fullWidth>
            <Controller
              name="address"
              control={control}
              rules={{ required: false }}
              
              render={({ field }) => (
                <TextField
                  rows={2}
                  multiline
                  {...field}
                  label="Address"
                  error={Boolean(errors.address)}
                  aria-describedby="Section1-address"
                />
              )}
            />
            {errors.address && (
              <FormHelperText
                sx={{ color: "error.main" }}
                id="Section1-address"
              >
                This field is required
              </FormHelperText>
            )}
          </FormControl>
        </Grid>

       



              <Grid
                item
                xs={12}
                sx={{
                  backgroundColor: "#f2f7f2",
                  mt: 4,
                  ml:5,
                  paddingTop: 0,  
                  height: "36px",
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "space-between"
                }}
              >
                <Typography
                  variant="body1"
                  fontWeight={"bold"}
                  sx={{ mt: 0, ml: -3, mb: 5 ,
                    textAlign: "center", // Aligns text in the center
                    
                  }}
                  
                >
                  Society Member Information
                </Typography>
                <Divider />
              </Grid>


              {/* name and designation */}

              <Grid item xl={4} lg={4} xs={12} sm={4}>
                    <FormControl fullWidth>
                      <Controller
                        name="firstName"
                        control={control}
                        rules={{ required: true }}
                        render={({ field }) => (
                          <NameTextField
                            {...field}
                            label="Name of the Society Member"
                            InputLabelProps={{ shrink: true }}
                            size="small"
                            placeholder="Enter your name"
                            error={Boolean(errors.firstName)}
                            helperText={errors.firstName?.message}
                            aria-describedby="validation-basic-firstName"
                          />
                        )}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item xl={4} lg={4} xs={12} sm={4}>
                  <SelectAutoComplete
                      id={"designation"}
                      label={"Designation"}
                      nameArray={listOfSubCategories}
                      value={designation}
                      onChange={(event) => handleDesignationChange(event.target.value)}
                    />
                    {errors.designation && (
                      <FormHelperText sx={{ color: 'error.main' }} id='validation-designation'>
                        Please select designation
                      </FormHelperText>
                        )}
                  </Grid>
                        {anyOther && (
                            <Grid container spacing={2}>

                          <Grid item xs={12} sm={6}  mt={2}>
                          <FormControl fullWidth>
                            <Controller
                              name="otherDesignation"
                              control={control}
                              rules={{ required: true }}
                              render={({ field }) => (
                                <NameTextField
                                  {...field}
                                  label="Any Other Designation"
                                  InputLabelProps={{ shrink: true }}
                                  size="small"
                                  placeholder="Enter your any other designation"
                                  error={Boolean(errors.otherDesignation)}
                                  helperText={errors.otherDesignation?.message}
                                  aria-describedby="validation-basic-otherDesignation"
                                />
                              )}
                            />
                          </FormControl>
                        </Grid>
                        </Grid>
                        )}


              <Grid item xl={4} lg={4} xs={12} sm={4}>
                  <FormControl fullWidth>
                    <Controller
                      name="mobileNumber"
                      control={control}
                      rules={{ required: true }}
                      render={({ field }) => (
                        <MobileNumberValidation
                          {...field}
                          type="tel"
                          label="Contact Number"
                          size="small"
                          InputLabelProps={{ shrink: true }}
                          error={Boolean(errors.mobileNumber)}
                          helperText={errors.mobileNumber?.message}
                          placeholder="+91 95 155 990 22"
                          aria-describedby="validation-mobileNumber"
                        />
                      )}
                    />
                  </FormControl>
              </Grid>

              {/* alternateNumber */}

              <Grid item xl={4} lg={4} xs={12} sm={4}>
          <FormControl fullWidth>
            <Controller
              name="alternateMobileNumber"
              control={control}
             
              render={({ field }) => (
                <TextField
                  {...field}
                  type="tel"
                  label="Alternate Number"
                  size="small"
                  error={Boolean(errors.alternateMobileNumber)}
                  helperText={errors.alternateNumber?.message}
                  InputLabelProps={{ shrink: true }}
                  placeholder="+91 1234567890"
                  inputProps={{
                    maxLength: field?.value?.startsWith("+91") ? 13 : 10,
                  }}
                />
              )}
            />
          </FormControl>
        </Grid>


        {/* Email Verify */}


        <Grid item xl={4} lg={4} xs={12} sm={4}
         
    >
          <FormControl fullWidth>
            <Controller
              name="individualName"
              control={control}
              rules={{ required: false }}
              // defaultValue={formData?.individualName}
              render={({ field }) => (
                <NameTextField
                  {...field}
                  label="Email address"
                  InputLabelProps={{ shrink: true }}
                  size="small"
                  placeholder="Enter your Email address"
                  // error={Boolean(errors.name)}
                  // helperText={errors.name?.message}
                  aria-describedby="validation-basic-name"
                />
              )}
            />
          </FormControl>
          
        </Grid>


        <Grid item xl={1}>
          <Button sx = {{ padding : "6px" , ml :{
            sm : -3,
            
          }}}
                    variant="contained"
                  >Verify</Button>
          </Grid>


          
          

  

            

          
              <Grid
          item
          xs={12}
          sx={{
            backgroundColor: "#f2f7f2",
            mt: 4,
            paddingTop: 0,
            height: "36px",
            display: "flex",
            alignItems: "center",
            ml:5,
          }}
        >
          <Typography
            variant="body1"
            fontWeight={"bold"}
            sx={{ mt: 0,  ml: -3, mb: 5 }}
          >
            Business Information
          </Typography>
          <Divider />
        </Grid>

        {/* Bank Name */}
        <Grid item xs={12} sm={4}>
          <FormControl fullWidth>
            <Controller
              name="bankName"
              control={control}
              rules={{ required: false }}
              // defaultValue={formData?.bankName}
              render={({ field }) => (
                <TextField
                  {...field}
                  label="Bank Name"
                  size="small"
                  InputLabelProps={{ shrink: true }}
                  placeholder="Enter Bank Name"
                  error={Boolean(errors.bankName)}
                  helperText={errors.bankName?.message}
                  aria-describedby="validation-basic-bankName"
                />
              )}
            />
            {errors.bankName?.type === "required" && (
              <FormHelperText sx={{ color: "error.main" }}>
                This field is required
              </FormHelperText>
            )}
          </FormControl>
        </Grid>
        {/* branch */}
        <Grid item xs={12} sm={4}>
          <FormControl fullWidth>
            <Controller
              name="branch"
              control={control}
              rules={{ required: false }}
              // defaultValue={formData?.branch}
              render={({ field }) => (
                <TextField
                  {...field}
                  label="Branch"
                  size="small"
                  InputLabelProps={{ shrink: true }}
                  placeholder="Enter branch name"
                  error={Boolean(errors.branch)}
                  helperText={errors.branch?.message}
                  aria-describedby="validation-basic-branch"
                />
              )}
            />
            {/* {errors.branch?.type === "required" && (
              <FormHelperText sx={{ color: "error.main" }}>
                This field is required
              </FormHelperText>
            )} */}
          </FormControl>
        </Grid>

        {/* Account nUmber */}
        <Grid item xs={12} sm={4}>
          <FormControl fullWidth>
            <Controller
              name="accountNumber"
              control={control}
              rules={{
                required: false,
                maxLength: 18,
                validate: {
                  isAlphanumeric: (value) =>
                    /^[a-zA-Z0-9]*$/.test(value) ||
                    "Please enter a valid alphanumeric account number",
                },
              }}
              // defaultValue={formData?.accountNumber}
              render={({ field: { value, onChange } }) => (
                <TextField
                  type="text"
                  value={value}
                  onChange={onChange}
                  size="small"
                  label="Account Number"
                  InputLabelProps={{ shrink: true }}
                  inputProps={{
                    maxLength: 18,
                  }}
                  error={Boolean(errors.accountNumber)}
                  placeholder="Enter account number"
                />
              )}
            />
            {/* {errors.accountNumber?.type === "required" && (
              <FormHelperText sx={{ color: "error.main" }}>
                This field is required
              </FormHelperText>
            )} */}
            {errors.accountNumber?.type === "maxLength" && (
              <FormHelperText sx={{ color: "error.main" }}>
                Account number cannot exceed 18 characters
              </FormHelperText>
            )}
            {errors.accountNumber?.type === "pattern" && (
              <FormHelperText sx={{ color: "error.main" }}>
                Please enter a valid alphanumeric account number
              </FormHelperText>
            )}
          </FormControl>
        </Grid>

        {/* ifsc code*/}
        <Grid item xs={12} sm={4}>
          <FormControl fullWidth>
            <Controller
              name="ifscCode"
              control={control}
              rules={{ required: false }}
              // defaultValue={formData?.ifscCode}
              render={({ field }) => (
                <TextField
                  {...field}
                  onChange={(e) => field.onChange(e.target.value.toUpperCase())}
                  label="IFSC Code"
                  size="small"
                  InputLabelProps={{ shrink: true }}
                  placeholder="Enter IFSC Code"
                  error={Boolean(errors.ifscCode)}
                  helperText={errors.ifscCode?.message}
                  aria-describedby="validation-basic-ifscCode"
                  inputProps={{
                    maxLength: 11,
                  }}
                />
              )}
            />
            {/* {errors.ifscCode?.type === "required" && (
              <FormHelperText sx={{ color: "error.main" }}>
                This field is required
              </FormHelperText>
            )} */}
          </FormControl>
        </Grid>


        <Grid item xs={12} sm={4}>
          <FormControl fullWidth>
            <InputLabel id="gst-label">Do you have GST?</InputLabel>
            <Controller
              name="gstOption"
              control={control}
           
              render={({ field }) => (
                <Select
                  {...field}
                  size="small"
                  labelId="gst-label"
                  label="Do you have GST?"
                >
                  <MenuItem value="yes">Yes</MenuItem>
                  <MenuItem value="no">No</MenuItem>
                </Select>
              )}
            />
          </FormControl>
        </Grid>

        {gstOption === "yes" && (
          <Grid item xs={12} sm={4}>
            <FormControl fullWidth>
              <Controller
                name="gstNo"
                control={control}
                rules={{ required: false }}
                render={({ field }) => (
                  <TextField
                    {...field}
                    label="GST No."
                    size="small"
                    InputLabelProps={{ shrink: true }}
                    placeholder="Enter your GST No."
                    error={Boolean(errors.gstNo)}
                    helperText={errors.gstNo ? "GST No. is required" : ""}
                  />
                )}
              />
            </FormControl>
          </Grid>
        )}

        {/* PAN NO. */}
        <Grid item xs={12} sm={4}>
          <FormControl fullWidth>
            <Controller
              name="panNo"
              control={control}
           
              rules={{ required: false }}
              render={({ field }) => (
                <TextField
                  {...field}
                  label="PAN No."
                  size="small"
                  InputLabelProps={{ shrink: true }}
                  placeholder="Enter your PAN No."
                  error={Boolean(errors.panNo)}
                  helperText={errors.panNo?.message}
                  aria-describedby="validation-basic-panNo"
                  inputProps={{
                    maxLength: 10, // Limits the input to 10 characters
                  }}
                />
              )}
            />
            {/* {errors.panNo?.type === "required" && (
              <FormHelperText sx={{ color: "error.main" }}>
                Pan No is required
              </FormHelperText>
            )} */}
          </FormControl>
        </Grid>


        <Grid
                item
                xs={12}
                sx={{
                  backgroundColor: "#f2f7f2",
                  mt: 4,
                  ml:5,
                  paddingTop: 0,  
                  height: "36px",
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "space-between"
                }}
              >
                <Typography
                  variant="body1"
                  fontWeight={"bold"}
                  sx={{ mt: 0, ml: -3, mb: 5 ,
                    textAlign: "center", // Aligns text in the center
                    
                  }}
                  
                >
                  Assignment And Status
                </Typography>
                <Divider />
              </Grid>
                  
              <Grid item xs={12} sm={4} lg={4}>
        <FormControl fullWidth>
          <Controller
            name="assignedToUUIDs"
            control={control}
            rules={{ required: false }}
            render={({ field }) => (
              <SelectAutoComplete
                id="assignedTo-select"
                size="small"
                label="Assigned To"
                nameArray={employeesOptions || []}
                value={field.value || []}
                onChange={(e) => {
                  field.onChange(e);
                }}
                error={Boolean(errors.service)}
              />
            )}
          />
        </FormControl>
        {errors.leadPriority && (
          <FormHelperText
            sx={{ color: "error.main" }}
            id="validation-leadPriority"
          >
            {errors.leadPriority?.message}
          </FormHelperText>
        )}
      </Grid>
              


              <Grid item xs={12} sm={4}>
                <FormControl fullWidth>
                <Controller
                  name="status"
                  control={control}
                  size="small"
                 
                  InputLabelProps={{ shrink: true }}
                  render={({ field }) => (
                    <SelectAutoComplete
                      id="status"
                      label="Lead Status"
                      nameArray={leadStatusData}
                      value={leadStatus}
                      onChange={(e) => {
                        field.onChange(e);
                        handleLeadStatusChange(e);
                      }}
                    />
                  )}
                />
              </FormControl>
              </Grid>


              <Grid item xs={12} sm={4}>
              <FormControl fullWidth>
                <Controller
                  name="lead-priority"
                  control={control}
                  size="small"
                
                  render={({ field }) => (
                    <SelectAutoComplete
                      id="lead-priority"
                      label="Lead Priority"
                      nameArray={leadPriorityData}
                      value={leadPriority}
                      onChange={(e) => {
                        field.onChange(e);
                        handleLeadPriorityChange(e);
                      }}
                    />
                  )}
                />
              </FormControl>
            </Grid>



            
           {/* Reference Section */}
           <Grid
                item
                xs={12}
                sx={{
                  backgroundColor: "#f2f7f2",
                  mt: 4,
                  ml:5,
                  paddingTop: 0,  
                  height: "36px",
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "space-between"
                }}
              >
                <Typography
                  variant="body1"
                  fontWeight={"bold"}
                  sx={{ mt: 0, ml: -3, mb: 5 ,
                    textAlign: "center", // Aligns text in the center
                    
                  }}
                  
                >
                  Reference 
                </Typography>
                <Divider />
              </Grid>

            {/* reference */}


            <Grid item xs={12} sm={4} lg ={4}>
  <FormControl fullWidth>
    <Controller
      name="reference"
      control={control}
      rules={{ required: "Reference type is required" }}
      render={({ field }) => (
        <SelectAutoComplete
          {...field}
          id="reference"
          label="Reference Type"
          name="reference"
          nameArray={referenceOptions}
          value={reference}
          onChange={(e) => {
            field.onChange(e);
            handleReferenceTypeChange(e); // Ensure this function is properly defined and updates `reference` state
          }}
          error={Boolean(errors.reference)}
          aria-describedby="validation-reference"
        />
      )}
    />
    {errors.reference && (
      <FormHelperText sx={{ color: "error.main" }} id="validation-reference">
        {errors.reference?.message}
      </FormHelperText>
    )}
  </FormControl>
</Grid>

{/* Render conditional fields based on the `reference` */}
{reference && (
  <>
    {/* Referral Name */}
    <Grid item xl={4} lg ={4} xs={12} sm={4}>
      <FormControl fullWidth>
        <Controller
          name="referenceName"
          control={control}
          rules={{ required: "Referral name is required" }}
          render={({ field }) => (
            <TextField
              {...field}
              size="small"
              label="Referral Name"
              InputLabelProps={{ shrink: true }}
              placeholder="Enter Name"
              error={Boolean(errors.referenceName)}
              helperText={errors.referenceName?.message}
            />
          )}
        />
      </FormControl>
    </Grid>

    {/* Referral Phone Number */}
    <Grid item xl={4} lg ={4} xs={12} sm={4}>
      <FormControl fullWidth>
        <Controller
          name="referencePhone"
          control={control}
          rules={{ required: "Referral phone number is required" }}
          render={({ field }) => (
            <TextField
              {...field}
              size="small"
              label="Referral Phone Number"
              InputLabelProps={{ shrink: true }}
              placeholder="Enter Phone Number"
              type="tel"
              error={Boolean(errors.referencePhone)}
              helperText={errors.referencePhone?.message}
            />
          )}
        />
      </FormControl>
    </Grid>

    {/* Referral Company Name */}
    <Grid item xl={4} lg ={4} xs={12} sm={4}>
      <FormControl fullWidth>
        <Controller
          name="referenceCompany"
          control={control}
          rules={{ required: "Referral company is required" }}
          render={({ field }) => (
            <TextField
              {...field}
              size="small"
              label="Referral Company Name"
              InputLabelProps={{ shrink: true }}
              placeholder="Enter Company Name"
              error={Boolean(errors.referenceCompany)}
              helperText={errors.referenceCompany?.message}
            />
          )}
        />
      </FormControl>
    </Grid>

    {/* Referral Email */}
    <Grid item xl={4} lg ={4} xs={12} sm={4}>
      <FormControl fullWidth>
        <Controller
          name="referenceEmail"
          control={control}
          rules={{
            required: "Referral email is required",
            pattern: {
              value: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
              message: "Enter a valid email address",
            },
          }}
          render={({ field }) => (
            <TextField
              {...field}
              size="small"
              label="Referral Email"
              InputLabelProps={{ shrink: true }}
              placeholder="Enter Email ID"
              type="email"
              error={Boolean(errors.referenceEmail)}
              helperText={errors.referenceEmail?.message}
            />
          )}
        />
      </FormControl>
    </Grid>
  </>
)}



            <Grid
                item
                xs={12}
                sx={{
                  backgroundColor: "#f2f7f2",
                  mt: 4,
                  ml:5,
                  paddingTop: 0,  
                  height: "36px",
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "space-between"
                }}
              >
                <Typography
                  variant="body1"
                  fontWeight={"bold"}
                  sx={{ mt: 0, ml: -3, mb: 5 ,
                    textAlign: "center", // Aligns text in the center
                    
                  }}
                  
                >
                  Upload Signatures
                </Typography>
                <Divider />
              </Grid>


      

            <Grid container spacing={2} margin = {10} gap ={20}>
  {/* Team Member Name and Signature */}
  <Grid item xl={4} lg={4} xs={12} sm={6}>
    <FormControl fullWidth>
      <Controller
        name="teamMemberNameSignature"
        control={control}
        rules={{ required: true }}
        render={({ field }) => (
          <TextField
            {...field}
            label="Name and Signature of Team Member"
            InputLabelProps={{ shrink: true }}
            size="small"
            placeholder="Enter the name and signature of the team member"
            error={Boolean(errors.teamMemberNameSignature)}
            helperText={errors.teamMemberNameSignature?.message || ""}
            aria-describedby="validation-team-member-name-signature"
          />
        )}
      />
      {/* Upload functionality placed here */}
      <UploadFile
        selectedDocument={document}
        setSelectedDocument={setDocument}
        selectedFiles={selectedFiles}
        setSelectedFiles={setSelectedFiles}
      />
    </FormControl>
  </Grid>

  {/* Committee Member Signature */}
  <Grid item xl={4} lg={4} xs={12} sm={6}>
    <FormControl fullWidth>
      <Controller
        name="committeeMemberSignature"
        control={control}
        rules={{ required: true }}
        render={({ field }) => (
          <TextField
            {...field}
            label="Signature of the Committee Member"
            InputLabelProps={{ shrink: true }}
            size="small"
            placeholder="Enter the signature of the committee member"
            error={Boolean(errors.committeeMemberSignature)}
            helperText={errors.committeeMemberSignature?.message || ""}
            aria-describedby="validation-committee-member-signature"
          />
        )}
      />
      {/* Upload functionality placed here */}
      <UploadFileCommittee
        selectedDocument={documentcommitte}
        setSelectedDocument={setDocument}
        selectedFiles={selectedFilesCommitte}
        setSelectedFiles={setSelectedFilesCommitte}
      />
    </FormControl>
  </Grid>
</Grid>



            


          
              

          </Grid>
          </DialogContent>


        <DialogActions
          sx={{
            position: "sticky",
            bottom: 0, // Sticks it to the bottom of the dialog
            zIndex: 10, // Ensures it stays above the content
            backgroundColor: "background.paper",
            borderTop: (theme) => `1px solid ${theme.palette.divider}`,
            p: (theme) => `${theme.spacing(1.75, 7)} !important`,
            justifyContent: "flex-end",
          
            justifyContent: "flex-end", // Align all content to the right
     
        
          }}
        >
          <Button
            display="flex"
            justifyContent="center"
            variant="outlined"
            color="primary"
            onClick={onclose}
          >
            Cancel
          </Button>
          <Button
            display="flex"
            justifyContent="Flex-end"
            variant="contained"
            color="primary"
            // onClick={handleSubmit(submit)}
            >
            Save
          </Button>
          </DialogActions>
         
    </Dialog>
  );
};

export default SocietyEnrollmentDialog;
