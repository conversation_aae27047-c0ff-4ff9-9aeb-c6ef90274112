// ** MUI Imports
import Typography from "@mui/material/Typography";
import { Fragment, useContext, useEffect, useState } from "react";

// ** Custom Components Imports
import axios from "axios";
import authConfig from "src/configs/auth";
import { getAuthorizationHeaders, getUrl } from "src/helpers/utils";
// ** Demo Components Imports
import { useTheme } from "@emotion/react";

import Icon from "src/@core/components/icon";
import CustomAvatar from "src/@core/components/mui/avatar";

// ** Styled Component
import {
  Box,
  Button,
  Card,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Divider,
  Grid,
  IconButton,
  Table,
  TableBody,
  TableContainer,
  TableHead,
  TableRow,
  Tooltip,
} from "@mui/material";

import { DataGrid } from "@mui/x-data-grid";
import { AuthContext } from "src/context/AuthContext";
import MUITableCell from "src/pages/SP/MUITableCell";
import EditRequisition from "./EditRequisition";

const field = {
  fontSize: "12.75px",
  //fontWeight: 500,
};
const fieldLabelStyle = {
  fontSize: "12.75px",
};

const fieldValueStyle = {
  fontWeight: "bold",
  wordWrap: "break-word",
  whiteSpace: "pre-wrap",
  // color: "#66BB6A", // Sets color for the TableCell
  color: "#108A00",
  fontSize: "14px",
};

const tablecellLabelStyle = {
  width: { xs: "50%", sm: "50%", md: "40%" }, // Responsive width adjustment
  padding: "4px", // Ensure padding for better alignment
  textAlign: "right", // Align the text to the left
  verticalAlign: "middle", // Vertically align the text to the middle
  textIndent: {
    lg: "80px",
    md: "80px",
    sm: "100px",
    xs: "0",
  },
};

const tablecellValueStyle = {
  justifyContent: "flex-start",
  alignItems: "center",
  width: { xs: "50%", sm: "50%", md: "60%" }, // Responsive width for small and medium screens
  height: "10px",
};

const ViewRequisition = ({
  open,
  onClose,
  data,
  fetchRequisitions,
  employeesData,
  referenceData,
  referralNameData,
}) => {
  // ** Hook
  const theme = useTheme();

  const [subCategories, setSubCategories] = useState([]);
  const [listOfSubCategories, setListOfSubCategories] = useState([]);
  const rowsPerPageOptions = [10, 15, 20, 25, 50, 100];
  const [pageSize, setPageSize] = useState(rowsPerPageOptions[0]);
  const [page, setPage] = useState(1);
  const [rowCount, setRowCount] = useState(0);
  const [currentRow, setCurrentRow] = useState("");

  const [siteVisitOpen, setSiteVisitOpen] = useState(false);
  const handleSiteVisitDialogOpen = () => setSiteVisitOpen(true);
  const handleSiteVisitDialogClose = () => setSiteVisitOpen(false);

  useEffect(() => {
    axios({
      method: "get",
      url:
        getUrl(authConfig.selectDropdown) +
        "?selectionType=SOCIETY_SUB_CATEGORIES",
      headers: getAuthorizationHeaders(),
    })
      .then((res) => {
        if (
          res.data.data.length > 0 &&
          res.data.data[0].metaData &&
          res.data.data[0].metaData.subRoleTypes
        ) {
          setSubCategories(res.data.data[0].metaData.subRoleTypes);
        }
      })
      .catch((err) => console.log("Employees error", err));
  }, []);

  useEffect(() => {
    if (!!subCategories) {
      let data = [];
      subCategories.map((entry) => {
        data.push({ value: entry.id, key: entry.name });
      });
      setListOfSubCategories(data);
    }
  }, [subCategories]);

  const { user, listValues, listNames } = useContext(AuthContext);

  const [openEditDialog, setOpenEditDialog] = useState(false);
  const [conversation, setConversation] = useState({});

  const handleCloseEditDialog = () => {
    setSpecifications([]);
    setOpenEditDialog(false);
    onClose();
  };

  const [openMoreInfoDialog, setOpenMoreInfoDialog] = useState(false);

  const handleDialogClose = () => {
    setOpenMoreInfoDialog(false);
  };

  const handleOpenEditDialog = () => {
    setOpenEditDialog(true);
  };

  const serviceType = data?.serviceTypeId
    ? listValues?.find((item) => item.id === data?.serviceTypeId)?.name
    : null;

  const priorityName = data?.priority
    ? listValues?.find((item) => item.id === data?.priority)?.name
    : null;

  const getNamesFromIds = (ids, listValues) => {
    return ids?.map((id) => {
      const foundItem = listValues?.find((item) => item?.id === id);
      return foundItem ? foundItem?.name : null;
    });
  };

  const [specifications, setSpecifications] = useState([]);

  const [userList, setUserList] = useState([]);

  useEffect(() => {
    if (data?.serviceTypeId) {
      const fetchAll = async (serviceId, data) => {
        const url = `${getUrl(
          authConfig.getAllServiceProfiles
        )}/${serviceId}/requisitionFields`;
        const headers = getAuthorizationHeaders();

        try {
          const response = await axios({
            method: "get",
            url: url,
            headers: headers,
            data: data,
          });

          if (response.data) {
            setUserList(response.data);
          } else {
            console.error("Unexpected API response format:", response);
          }
        } catch (error) {
          console.error("Error fetching users:", error);
        }
      };

      const serviceId = data?.serviceTypeId;
      fetchAll(serviceId, data);
    }
  }, [data]);

  useEffect(() => {
    if (data?.specifications?.serviceSpecifications?.listNames.length > 0) {
      // Create a mapping of listValueId to listValue
      const listValueMap = userList?.reduce((map, item) => {
        item?.values?.forEach((value) => {
          map[value.id] = { id: value.id, name: value.name };
        });
        return map;
      }, {});

      // Get all list names from specifications
      const listNames = userList?.map((item) => {
        const metadataItem =
          data?.specifications?.serviceSpecifications?.listNames?.find(
            (list) => list.listNameId === item.id
          );
        const otherValue = metadataItem ? metadataItem.otherValue : null;

        return {
          id: item.id,
          name: item.name,
          otherValue: otherValue,
          values:
            metadataItem && metadataItem.listValues.length
              ? metadataItem.listValues.map(
                  (value) => listValueMap[value.listValueId]
                )
              : [],
        };
      });
      setSpecifications(listNames);
    }
  }, [data, userList]);

  // Using the function to get names
  // const names = getNamesFromIds(data?.requisitionData?.subServices, listValues);

  // const subServices = names?.filter((name) => name !== null).join(", ");

  const status = data?.status
    ? listValues?.find((item) => item.id === data?.status)?.name
    : null;

  const referenceType = data?.referral?.referenceType
    ? referenceData?.find(
        (item) => item.value === data?.referral?.referenceType
      )?.key
    : null;

  const teamMember = data?.referral?.houzerSocietyTeamMember
    ? employeesData?.find(
        (item) => item.id === data?.referral?.houzerSocietyTeamMember
      )?.name
    : null;

  const curatedBy = data?.referral?.curatedBy
    ? employeesData?.find((item) => item.id === data?.referral?.curatedBy)?.name
    : null;

  const teamReference = data?.referral?.teamReference
    ? referralNameData?.find(
        (item) => item.value === data?.referral?.teamReference
      )?.key
    : null;

  const [anchorEl, setAnchorEl] = useState(null);

  const [sampleData, setSampleData] = useState([]);

  useEffect(() => {
    setSampleData(data?.siteVisitTimings);
  }, [data]);

  const [menu, setMenu] = useState(null);

  const handleCloseMenuItems = () => {
    setMenu(null);
  };

  const cols = [
    { field: "contactPersonName", headerName: "Name", minWidth: 145, flex: 2 },
    {
      field: "contactNumber",
      headerName: "Contact Number",
      minWidth: 145,
      flex: 2,
    },
    {
      field: "siteVisitDate",
      headerName: "Site Visit Date",
      minWidth: 145,
      flex: 2,
    },
    { field: "startTime", headerName: "Start Time", minWidth: 140, flex: 2 },
    { field: "endTime", headerName: "End Time", minWidth: 140, flex: 2 },
  ];

  const handlePageChange = (direction) => {
    if (direction === page) {
      setPage(page + 1);
    } else {
      setPage(page - 1);
    }
  };

  const handlePageSizeChange = (params) => {
    if (params) {
      setPageSize(params);
      setPage(1);
    }
  };
  const handleClose = () => {
    onClose();
    setUserList([]);
    setSpecifications([]);
  };

  function getEmployeeNamesByWorkFor(workForValue) {
    // Step 1: Filter `workAssignments` by `workFor`
    const assignments = data?.workAssignments?.filter(
      (assignment) => assignment.workFor === workForValue
    );

    // Step 2: Map each `assignedTo` to the corresponding employee name
    return assignments
      ?.map((assignment) => {
        const employee = employeesData?.find(
          (emp) => emp.id === assignment.assignedTo
        );
        return employee ? employee.name : null; // Return name if found, otherwise null
      })
      .filter((name) => name !== null); // Filter out any null values
  }

  return (
    <>
      <Dialog open={open} onClose={handleClose} fullScreen>
        <DialogTitle
          sx={{
            position: "relative",
            borderBottom: (theme) => `1px solid ${theme.palette.divider}`,
            p: (theme) => `${theme.spacing(1.75, 4)} !important`,
            display: "flex",
            alignItems: "center",
            justifyContent: "start",
            fontSize: { xs: 19, md: 20 },
            height: "50px", // height
          }}
          textAlign={"center"}
        >
          <Box
            sx={{
              ml: {
                xs: 4,
                xl: 3,
              },
            }}
          >
            Requisition Details&nbsp;
          </Box>
          <Box
            sx={{
              position: "absolute",
              top: "9px",
              right: "30px",
              mr: {
                xs: 4,
                xl: 8,
              },
            }}
          >
            {user.organisationCategory !== "SERVICE_PROVIDER" && (
              <Tooltip title="Edit">
                <CustomAvatar
                  skin="light"
                  variant="rounded"
                  sx={{ mr: 5, width: 34, height: 30 }}
                  onClick={handleOpenEditDialog}
                >
                  <Icon icon="iconamoon:edit" />
                </CustomAvatar>
              </Tooltip>
            )}
          </Box>
          <Box
            sx={{
              position: "absolute",
              top: "9px",
              right: "12px",
              mr: {
                xs: 5,
                sm: 5,
                sm: 5,
                xl: 9,
              },
            }}
          >
            <IconButton
              size="small"
              onClick={handleClose}
              sx={{
                // p: "0.438rem",
                borderRadius: 1,
                color: "common.white",
                backgroundColor: "primary.main",
                "&:hover": {
                  backgroundColor: "#66BB6A",
                  transition: "background 0.5s ease, transform 0.5s ease",
                },
              }}
            >
              <Icon icon="tabler:x" fontSize="1rem" />
            </IconButton>
          </Box>
        </DialogTitle>
        <DialogContent
          sx={{
            position: "relative",
            pt: (theme) => `${theme.spacing(8)} !important`,
            pb: (theme) => `${theme.spacing(5)} !important`,
            px: (theme) => [`${theme.spacing(8)} !important`],
          }}
        >
          <>
            {user.organisationCategory !== "SOCIETY" && (
              <Card>
                <Grid
                  sx={{
                    backgroundColor: "#f2f7f2",
                    mt: 4,
                    paddingTop: 0,
                    height: "36px",
                    display: "flex",
                    alignItems: "center",
                  }}
                >
                  <Typography
                    variant="body1"
                    fontWeight={"bold"}
                    sx={{ mt: 0, ml: 2 }}
                  >
                    Society Details
                  </Typography>
                  <Divider />
                </Grid>
                <Divider />
                <TableContainer
                  sx={{ padding: "4px 6px" }}
                  className="tableBody"
                >
                  <Table>
                    <TableBody
                      sx={{
                        "& .MuiTableCell-root": {
                          p: `${theme.spacing(1.35, 1.125)} !important`,
                        },
                      }}
                    >
                      <TableRow>
                        <MUITableCell sx={tablecellLabelStyle}>
                          <Typography sx={fieldLabelStyle}>
                            Society name:
                          </Typography>
                        </MUITableCell>
                        <MUITableCell sx={tablecellValueStyle}>
                          <Typography
                            className="data-field"
                            style={fieldValueStyle}
                          >
                            {data?.initiatingEntity?.orgName}
                          </Typography>
                        </MUITableCell>
                      </TableRow>

                      <TableRow>
                        <MUITableCell sx={tablecellLabelStyle}>
                          <Typography style={fieldLabelStyle}>
                            Society member name:
                          </Typography>
                        </MUITableCell>
                        <MUITableCell sx={tablecellValueStyle}>
                          <Typography
                            className="data-field"
                            style={fieldValueStyle}
                          >
                            {data?.initiator?.firstName}{" "}
                            {data?.initiator?.lastName}
                          </Typography>
                        </MUITableCell>
                      </TableRow>

                      <TableRow>
                        <MUITableCell sx={tablecellLabelStyle}>
                          <Typography style={fieldLabelStyle}>
                            Email:
                          </Typography>
                        </MUITableCell>
                        <MUITableCell sx={tablecellValueStyle}>
                          <Typography
                            className="data-field"
                            style={fieldValueStyle}
                          >
                            {data?.initiator?.email}
                          </Typography>
                        </MUITableCell>
                      </TableRow>

                      <TableRow>
                        <MUITableCell sx={tablecellLabelStyle}>
                          <Typography sx={fieldLabelStyle}>
                            Location:
                          </Typography>
                        </MUITableCell>
                        <MUITableCell sx={tablecellValueStyle}>
                          <Typography
                            className="data-field"
                            style={fieldValueStyle}
                          >
                            {listValues?.find(
                              (item) =>
                                item.id === data?.initiatingEntity?.location
                            )?.name || ""}
                          </Typography>
                        </MUITableCell>
                      </TableRow>

                      <TableRow>
                        <MUITableCell sx={tablecellLabelStyle}>
                          <Typography sx={fieldLabelStyle}>Zone:</Typography>
                        </MUITableCell>
                        <MUITableCell sx={tablecellValueStyle}>
                          <Typography
                            className="data-field"
                            style={fieldValueStyle}
                          >
                            {data?.initiatingEntity?.zone}
                          </Typography>
                        </MUITableCell>
                      </TableRow>

                      <TableRow>
                        <MUITableCell sx={tablecellLabelStyle}>
                          <Typography sx={fieldLabelStyle}>Address:</Typography>
                        </MUITableCell>
                        <MUITableCell sx={tablecellValueStyle}>
                          <Typography
                            className="data-field"
                            style={fieldValueStyle}
                          >
                            {data?.initiatingEntity?.address}
                          </Typography>
                        </MUITableCell>
                      </TableRow>
                    </TableBody>
                  </Table>
                </TableContainer>
              </Card>
            )}

            <Card>
              <Grid
                sx={{
                  backgroundColor: "#f2f7f2",
                  mt: 4,
                  paddingTop: 0,
                  height: "36px",
                  display: "flex",
                  alignItems: "center",
                }}
              >
                <Typography
                  variant="body1"
                  fontWeight={"bold"}
                  sx={{ mt: 0, ml: 2 }}
                >
                  Requisition Details
                </Typography>
                <Divider />
              </Grid>
              <Divider />
              <TableContainer sx={{ padding: "4px 6px" }} className="tableBody">
                <Table>
                  <TableBody
                    sx={{
                      "& .MuiTableCell-root": {
                        p: `${theme.spacing(1.35, 1.125)} !important`,
                      },
                    }}
                  >
                    <TableRow>
                      <MUITableCell sx={tablecellLabelStyle}>
                        <Typography sx={fieldLabelStyle}>
                          Service Type:
                        </Typography>
                      </MUITableCell>
                      <MUITableCell sx={tablecellValueStyle}>
                        <Typography
                          className="data-field"
                          style={fieldValueStyle}
                        >
                          {serviceType}
                        </Typography>
                      </MUITableCell>
                    </TableRow>

                    {data?.specifications?.anyOtherServiceProvided && (
                      <TableRow>
                        <MUITableCell sx={tablecellLabelStyle}>
                          <Typography sx={fieldLabelStyle}>
                            Other Services:
                          </Typography>
                        </MUITableCell>
                        <MUITableCell sx={tablecellValueStyle}>
                          <Typography
                            className="data-field"
                            sx={fieldValueStyle}
                          >
                            {data?.specifications?.anyOtherServiceProvided}
                          </Typography>
                        </MUITableCell>
                      </TableRow>
                    )}

                    <TableRow>
                      <MUITableCell sx={tablecellLabelStyle}>
                        <Typography sx={fieldLabelStyle}>Priority:</Typography>
                      </MUITableCell>
                      <MUITableCell sx={tablecellValueStyle}>
                        <Typography
                          className="data-field"
                          style={fieldValueStyle}
                        >
                          {priorityName}
                        </Typography>
                      </MUITableCell>
                    </TableRow>
                    <TableRow>
                      <MUITableCell sx={tablecellLabelStyle}>
                        <Typography sx={fieldLabelStyle}>Budget:</Typography>
                      </MUITableCell>
                      <MUITableCell sx={tablecellValueStyle}>
                        <Typography
                          className="data-field"
                          style={fieldValueStyle}
                        >
                          {data?.budget?.rangeFrom} - {data?.budget?.rangeTo}{" "}
                          {
                            listValues?.find(
                              (item) => item.id === data?.budget?.units
                            )?.name
                          }{" "}
                          {
                            listValues?.find(
                              (item) => item.id === data?.budget?.condition
                            )?.name
                          }
                        </Typography>
                      </MUITableCell>
                    </TableRow>

                    <TableRow>
                      <MUITableCell sx={tablecellLabelStyle}>
                        <Typography sx={fieldLabelStyle}>
                          Requirement Dead Line:
                        </Typography>
                      </MUITableCell>
                      <MUITableCell sx={tablecellValueStyle}>
                        <Typography
                          className="data-field"
                          style={fieldValueStyle}
                        >
                          {data?.requirementDeadLine?.substring(0, 10)}
                        </Typography>
                      </MUITableCell>
                    </TableRow>
                    <TableRow>
                      <MUITableCell sx={tablecellLabelStyle}>
                        <Typography sx={fieldLabelStyle}>
                          Society Remarks:
                        </Typography>
                      </MUITableCell>
                      <MUITableCell sx={tablecellValueStyle}>
                        <Typography
                          className="data-field"
                          style={fieldValueStyle}
                        >
                          {data?.remarks}
                        </Typography>
                      </MUITableCell>
                    </TableRow>
                  </TableBody>
                </Table>
              </TableContainer>
            </Card>

            {data?.specifications?.sections.length > 0 && (
              <>
                {" "}
                {/* specifications */}
                <TableContainer>
                  <Table>
                    <TableRow>
                      <MUITableCell sx={tablecellValueStyle} colSpan={2}>
                        <Typography
                          className="data-field"
                          style={fieldValueStyle}
                          sx={{ backgroundColor: "#f2f7f2", mt: 2 }}
                        >
                          Specifications
                        </Typography>
                      </MUITableCell>
                    </TableRow>

                    {data?.specifications?.sections
                      ?.filter((section) => section.isChecked)
                      ?.map((section) => (
                        <Fragment key={section.id}>
                          {/* Display section ID */}
                          <TableRow>
                            <MUITableCell colSpan={2}>
                              <Typography
                                variant="body2"
                                fontWeight={"bold"}
                                sx={{ mt: 0, ml: 2 }}
                              >
                                {
                                  listValues?.find(
                                    (item) => item.id === section.id
                                  )?.name
                                }
                              </Typography>
                            </MUITableCell>
                          </TableRow>

                          {section.fields?.map((field) => (
                            <TableRow key={field.id}>
                              <MUITableCell sx={tablecellLabelStyle}>
                                <Typography style={field}>
                                  {
                                    listNames?.find(
                                      (item) => item.id === field.labelId
                                    )?.name
                                  }
                                  {":"}
                                </Typography>
                              </MUITableCell>
                              <MUITableCell sx={tablecellValueStyle}>
                                <Typography
                                  className="data-field"
                                  style={fieldValueStyle}
                                >
                                  {
                                    field.componentId ===
                                      authConfig.textFieldComponentId ||
                                    field.componentId ===
                                      authConfig.textAreaComponentId ||
                                    field.componentId ===
                                      authConfig.numberTextFieldComponentId
                                      ? field.providedTextValue // Show providedTextValue if the componentId matches
                                      : field.selectedDropdownValues
                                          ?.map((item) => item.key)
                                          .join(", ") // Show the keys as a comma-separated string
                                  }
                                </Typography>
                              </MUITableCell>
                            </TableRow>
                          ))}
                        </Fragment>
                      ))}
                  </Table>
                </TableContainer>
              </>
            )}

            {user.organisationCategory === "EMPLOYEE" && (
              <Card>
                <Grid
                  sx={{
                    backgroundColor: "#f2f7f2",
                    mt: 4,
                    paddingTop: 0,
                    height: "36px",
                    display: "flex",
                    alignItems: "center",
                  }}
                >
                  <Typography
                    variant="body1"
                    fontWeight={"bold"}
                    sx={{ mt: 0, ml: 2 }}
                  >
                    Status and Assignment Details
                  </Typography>
                  <Divider />
                </Grid>
                <Divider />
                <TableContainer
                  sx={{ padding: "4px 6px" }}
                  className="tableBody"
                >
                  <Table>
                    <TableBody
                      sx={{
                        "& .MuiTableCell-root": {
                          p: `${theme.spacing(1.35, 1.125)} !important`,
                        },
                      }}
                    >
                      <TableRow>
                        <MUITableCell sx={tablecellLabelStyle}>
                          <Typography sx={fieldLabelStyle}>
                            Assigned To:
                          </Typography>
                        </MUITableCell>
                        <MUITableCell sx={tablecellValueStyle}>
                          <Typography
                            className="data-field"
                            style={fieldValueStyle}
                          >
                            {getEmployeeNamesByWorkFor("DRI")}
                          </Typography>
                        </MUITableCell>
                      </TableRow>

                      <TableRow>
                        <MUITableCell sx={tablecellLabelStyle}>
                          <Typography style={fieldLabelStyle}>
                            Work With SP:
                          </Typography>
                        </MUITableCell>
                        <MUITableCell sx={tablecellValueStyle}>
                          <Typography
                            className="data-field"
                            style={fieldValueStyle}
                          >
                            {getEmployeeNamesByWorkFor("SP")}
                          </Typography>
                        </MUITableCell>
                      </TableRow>

                      <TableRow>
                        <MUITableCell sx={tablecellLabelStyle}>
                          <Typography style={fieldLabelStyle}>
                            Work With CHS:
                          </Typography>
                        </MUITableCell>
                        <MUITableCell sx={tablecellValueStyle}>
                          <Typography
                            className="data-field"
                            style={fieldValueStyle}
                          >
                            {getEmployeeNamesByWorkFor("CHS")}
                          </Typography>
                        </MUITableCell>
                      </TableRow>

                      <TableRow>
                        <MUITableCell sx={tablecellLabelStyle}>
                          <Typography style={fieldLabelStyle}>
                            Work For Work Order:
                          </Typography>
                        </MUITableCell>
                        <MUITableCell sx={tablecellValueStyle}>
                          <Typography
                            className="data-field"
                            style={fieldValueStyle}
                          >
                            {getEmployeeNamesByWorkFor("WORK_ORDERS")}
                          </Typography>
                        </MUITableCell>
                      </TableRow>
                      <TableRow>
                        <MUITableCell sx={tablecellLabelStyle}>
                          <Typography style={fieldLabelStyle}>
                            Status:
                          </Typography>
                        </MUITableCell>
                        <MUITableCell sx={tablecellValueStyle}>
                          <Typography
                            className="data-field"
                            style={fieldValueStyle}
                          >
                            {status}
                          </Typography>
                        </MUITableCell>
                      </TableRow>
                      <TableRow>
                        <MUITableCell sx={tablecellLabelStyle}>
                          <Typography sx={fieldLabelStyle}>
                            Curated By:
                          </Typography>
                        </MUITableCell>
                        <MUITableCell sx={tablecellValueStyle}>
                          <Typography
                            className="data-field"
                            style={fieldValueStyle}
                          >
                            {curatedBy}
                          </Typography>
                        </MUITableCell>
                      </TableRow>
                      <TableRow>
                        <MUITableCell sx={tablecellLabelStyle}>
                          <Typography sx={fieldLabelStyle}>
                            Curated On:
                          </Typography>
                        </MUITableCell>
                        <MUITableCell sx={tablecellValueStyle}>
                          <Typography
                            className="data-field"
                            style={fieldValueStyle}
                          >
                            {data?.referral?.curatedOn}
                          </Typography>
                        </MUITableCell>
                      </TableRow>
                      <TableRow>
                        <MUITableCell sx={tablecellLabelStyle}>
                          <Typography sx={fieldLabelStyle}>
                            Data Sent Date:
                          </Typography>
                        </MUITableCell>
                        <MUITableCell sx={tablecellValueStyle}>
                          <Typography
                            className="data-field"
                            style={fieldValueStyle}
                          >
                            {data?.referral?.dataSentDate}
                          </Typography>
                        </MUITableCell>
                      </TableRow>
                      <TableRow>
                        <MUITableCell sx={tablecellLabelStyle}>
                          <Typography sx={fieldLabelStyle}>
                            Deci-Matrix Presentation Date:
                          </Typography>
                        </MUITableCell>
                        <MUITableCell sx={tablecellValueStyle}>
                          <Typography
                            className="data-field"
                            style={fieldValueStyle}
                          >
                            {data?.referral?.deciMatrixPresentationDate}
                          </Typography>
                        </MUITableCell>
                      </TableRow>
                      <TableRow>
                        <MUITableCell sx={tablecellLabelStyle}>
                          <Typography sx={fieldLabelStyle}>
                            Lead Time Difference:
                          </Typography>
                        </MUITableCell>
                        <MUITableCell sx={tablecellValueStyle}>
                          <Typography
                            className="data-field"
                            style={fieldValueStyle}
                          >
                            {data?.leadTimeDifference}
                          </Typography>
                        </MUITableCell>
                      </TableRow>
                      <TableRow>
                        <MUITableCell sx={tablecellLabelStyle}>
                          <Typography sx={fieldLabelStyle}>
                            Requisition Lead Time:
                          </Typography>
                        </MUITableCell>
                        <MUITableCell sx={tablecellValueStyle}>
                          <Typography
                            className="data-field"
                            style={fieldValueStyle}
                          >
                            {data?.requisitionLeadTime}
                          </Typography>
                        </MUITableCell>
                      </TableRow>
                      <TableRow>
                        <MUITableCell sx={tablecellLabelStyle}>
                          <Typography sx={fieldLabelStyle}>
                            Reference Type:
                          </Typography>
                        </MUITableCell>
                        <MUITableCell sx={tablecellValueStyle}>
                          <Typography
                            className="data-field"
                            style={fieldValueStyle}
                          >
                            {referenceType}
                          </Typography>
                        </MUITableCell>
                      </TableRow>

                      <TableRow>
                        <MUITableCell sx={tablecellLabelStyle}>
                          <Typography sx={fieldLabelStyle}>
                            Referral Name:
                          </Typography>
                        </MUITableCell>
                        <MUITableCell sx={tablecellValueStyle}>
                          <Typography
                            className="data-field"
                            style={fieldValueStyle}
                          >
                            {teamReference}
                          </Typography>
                        </MUITableCell>
                      </TableRow>
                    </TableBody>
                  </Table>
                </TableContainer>
              </Card>
            )}

            {user.organisationCategory !== "SERVICE_PROVIDER" && (
              <>
                <Card>
                  <Grid
                    sx={{
                      backgroundColor: "#f2f7f2",
                      mt: 4,
                      paddingTop: 0,
                      height: "36px",
                      display: "flex",
                      alignItems: "center",
                    }}
                  >
                    <Typography
                      variant="body1"
                      fontWeight={"bold"}
                      sx={{ mt: 0, ml: 2 }}
                    >
                      Site Visit Timings
                    </Typography>
                    <Divider />
                  </Grid>
                  <Divider />
                  <Box style={{ height: "100%", width: "100%" }}>
                    <DataGrid
                      rows={sampleData || []}
                      columns={cols || []}
                      autoHeight
                      checkboxSelection
                      pagination
                      pageSize={pageSize}
                      page={page - 1}
                      rowsPerPageOptions={rowsPerPageOptions}
                      rowCount={rowCount}
                      paginationMode="server"
                      onPageChange={handlePageChange}
                      onPageSizeChange={handlePageSizeChange}
                      rowHeight={38}
                      headerHeight={38}
                    />
                  </Box>
                </Card>
                <Card>
                  <Grid
                    sx={{
                      backgroundColor: "#f2f7f2",
                      mt: 4,
                      paddingTop: 0,
                      height: "36px",
                      display: "flex",
                      alignItems: "center",
                    }}
                  >
                    <Typography
                      variant="body1"
                      fontWeight={"bold"}
                      sx={{ mt: 0, ml: 2 }}
                    >
                      Conversations
                    </Typography>
                    <Divider />
                  </Grid>
                  <Divider />
                  {data?.conversations?.length > 0 ? (
                    <TableContainer>
                      <Table>
                        <TableHead>
                          <TableRow>
                            {user?.organisationCategory === "EMPLOYEE" && (
                              <>
                                <MUITableCell>Last Contacted Date</MUITableCell>
                                <MUITableCell>Follow Up Date</MUITableCell>
                                <MUITableCell>Follow Up Action</MUITableCell>
                              </>
                            )}
                            <MUITableCell>Conversation</MUITableCell>
                            <MUITableCell>More Info</MUITableCell>
                          </TableRow>
                        </TableHead>
                        <TableBody>
                          {data?.conversations?.map((row, index) => (
                            <TableRow key={index}>
                              {user?.organisationCategory === "EMPLOYEE" && (
                                <>
                                  <MUITableCell sx={{ width: "15%" }}>
                                    {row.lastContactedDate?.split("T")[0]}
                                  </MUITableCell>
                                  <MUITableCell sx={{ width: "10%" }}>
                                    {row.followUpDate}
                                  </MUITableCell>
                                  <MUITableCell sx={{ width: "30%" }}>
                                    {row.followUpActions}
                                  </MUITableCell>
                                </>
                              )}
                              <MUITableCell sx={{ width: "30%" }}>
                                {row.message}
                              </MUITableCell>
                              <MUITableCell sx={{ width: "15%" }}>
                                <Tooltip title="More Info">
                                  <CustomAvatar
                                    skin="light"
                                    variant="rounded"
                                    sx={{
                                      width: 28,
                                      height: 28,
                                      cursor: "pointer",
                                    }}
                                    onClick={() => {
                                      setConversation(row);
                                      setOpenMoreInfoDialog(true);
                                    }}
                                  >
                                    <Icon
                                      icon="tabler:info-circle"
                                      fontSize="2.2rem"
                                    />
                                  </CustomAvatar>
                                </Tooltip>
                              </MUITableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </TableContainer>
                  ) : (
                    <>
                      <Box
                        sx={{
                          display: "flex",
                          justifyContent: "center",
                          alignItems: "center",
                          height: "50px", // Adjust height as needed
                        }}
                      >
                        <Typography variant="body1">
                          No comments/conversation happened yet
                        </Typography>
                      </Box>
                    </>
                  )}
                </Card>
              </>
            )}
          </>
        </DialogContent>
        <DialogActions
          sx={{
            justifyContent: "end",
            borderTop: (theme) => `1px solid ${theme.palette.divider}`,
            p: (theme) => `${theme.spacing(2.5)} !important`,
            height: "50px", // height
            mr: {
              xs: 6,
              xl: 10,
            },
          }}
        >
          <Button
            display="flex"
            justifyContent="flex-end"
            variant="outlined"
            color="primary"
            onClick={handleClose}
          >
            Close
          </Button>
        </DialogActions>
      </Dialog>
      <Dialog
        open={openMoreInfoDialog}
        onClose={handleDialogClose}
        fullWidth
        scroll="paper"
      >
        <DialogTitle
          sx={{
            position: "relative",
            borderBottom: (theme) => `1px solid ${theme.palette.divider}`,
            p: (theme) => `${theme.spacing(1.5)} !important`,
            display: "flex",
            flexDirection: "column",
            alignItems: "start",
            margin: 3,
            fontSize: {
              xs: "16px",
              md: "20px",
            },
            fontWeight: "bold",
          }}
        >
          Conversation Details
          <Box sx={{ position: "absolute", top: "4px", right: "12px" }}>
            <IconButton
              size="small"
              onClick={handleDialogClose}
              sx={{
                p: "0.438rem",
                borderRadius: 1,
                color: "common.white",
                backgroundColor: "primary.main",
                "&:hover": {
                  backgroundColor: "#66BB6A",
                  transition: "background 0.5s ease, transform 0.5s ease",
                },
              }}
            >
              <Icon icon="tabler:x" fontSize="1rem" />
            </IconButton>
          </Box>
        </DialogTitle>
        <DialogContent maxWidth="lg">
          <TableContainer sx={{ padding: "2px 3px" }} className="tableBody">
            <Table>
              <TableBody
                sx={{
                  "& .MuiTableCell-root": {
                    p: `${theme.spacing(1.35, 1.125)} !important`,
                  },
                }}
              >
                {user?.organisationCategory === "EMPLOYEE" && (
                  <>
                    <TableRow>
                      <MUITableCell>
                        <Typography sx={{ fontWeight: 600 }}>
                          Last Contacted Date
                        </Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography>
                          {conversation?.lastContactedDate?.split("T")[0]}
                        </Typography>
                      </MUITableCell>
                    </TableRow>
                    <TableRow>
                      <MUITableCell>
                        <Typography sx={{ fontWeight: 600 }}>
                          Follow Up Date
                        </Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography>{conversation?.followUpDate}</Typography>
                      </MUITableCell>
                    </TableRow>
                    <TableRow>
                      <MUITableCell>
                        <Typography sx={{ fontWeight: 600 }}>
                          Follow Up Action
                        </Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography>{conversation?.followUpActions}</Typography>
                      </MUITableCell>
                    </TableRow>
                  </>
                )}

                <TableRow>
                  <MUITableCell>
                    <Typography sx={{ fontWeight: 600 }}>
                      Conversation
                    </Typography>
                  </MUITableCell>
                  <MUITableCell>
                    <Typography>{conversation?.message}</Typography>
                  </MUITableCell>
                </TableRow>
              </TableBody>
            </Table>
          </TableContainer>
        </DialogContent>
        <DialogActions
          sx={{
            justifyContent: "end",
            borderTop: (theme) => `1px solid ${theme.palette.divider}`,
            p: (theme) => `${theme.spacing(2.5)} !important`,
          }}
        >
          <Button
            size="medium"
            sx={{ mr: 3 }}
            onClick={() => handleDialogClose()}
            variant="outlined"
            color="primary"
          >
            Close
          </Button>
        </DialogActions>
      </Dialog>

      <EditRequisition
        open={openEditDialog}
        formData={data}
        listOfSubCategories={listOfSubCategories}
        formattedData={specifications}
        setSpecifications={setSpecifications}
        employeesData={employeesData}
        onClose={handleCloseEditDialog}
        fetchRequisitions={fetchRequisitions}
        referenceData={referenceData}
        referralNameData={referralNameData}
      />
    </>
  );
};
export default ViewRequisition;