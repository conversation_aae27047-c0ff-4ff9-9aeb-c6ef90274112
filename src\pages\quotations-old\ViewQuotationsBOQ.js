import React, { useContext,useState,useEffect } from "react";
import Dialog from "@mui/material/Dialog";
import DialogActions from "@mui/material/DialogActions";
import MUITableCell from "src/pages/SP/MUITableCell";
import { useTheme } from "@emotion/react";
import CloseIcon from "@mui/icons-material/Close";

import CloseExpandIcons from "../../@core/components/custom-components/CloseExpandIcons";
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableRow,
  Card,
  useMediaQuery,
  TableHead,
  Select,
  MenuItem,
  TextField,
} from "@mui/material";
import DialogContent from "@mui/material/DialogContent";
import {
  Button,
  DialogTitle,
  IconButton,
  Typography,
} from "@mui/material";
import axios from "axios";
import authConfig from "src/configs/auth";
import { getAuthorizationHeaders, getUrl } from "src/helpers/utils";
import { AuthContext } from "src/context/AuthContext";
import Box from "@mui/material/Box";
import Icon from "src/@core/components/icon";
import Paper from "@mui/material/Paper";

import AccordionBasic from "src/@core/components/custom-components/AccordionBasic";
import { useForm } from "react-hook-form";
import UploadFile from "./UploadFile";
import DecilogicReportDialog from "./DecilogicReport";

const field = {
  fontWeight: 500,
  fontSize: "12.75px",
};
const ViewQuotationsBOQ = ({ open,onClose, data,disableDelete = false  }) => {
  const theme = useTheme();

  const [dataView, setDataView] = useState({});

  const [designation, setDesignation] = useState("");

  const [subCategories, setSubCategories] = useState([]);
  const [listOfSubCategories, setListOfSubCategories] = useState([]);
  // const [selectedFiles, setSelectedFiles] = useState([]);


  const [selectedProvider1, setSelectedProvider1] = useState("Service Provider A");
  const [selectedProvider2, setSelectedProvider2] = useState("");
  const [selectedProvider3, setSelectedProvider3] = useState("");

 

  const serviceProviderData = {
    "Service Provider A": {
      "Wifi Camera": {
        Brand: "Brand A",
        Model: "Model X",
        Quantity: "10",
        Rate: "100",
        Amount: "1000",
      },
      "Memory Card": {
        Brand: "Brand B",
        Model: "Model Y",
        Memory: "32GB",
        Quantity: "5",
        Rate: "20",
        Amount: "100",
      },
      "HD TV Monitor": {
        Brand: "Brand C",
        Model: "Model Z",
        Quantity: "3",
        Rate: "400",
        Amount: "1200",
      },
      "4K HDMI Extender": {
        Brand: "Brand D",
        Model: "Extender Model X",
        Quantity: "2",
        Rate: "50",
        Amount: "100",
      },
    },
    
  }
  const fixedCategories = {
    "Wifi Camera": ["Brand", "Model", "Quantity", "Rate", "Amount"],
    "Memory Card": ["Brand", "Model", "Memory", "Quantity", "Rate", "Amount"],
    "HD TV Monitor": ["Brand", "Model", "Quantity", "Rate", "Amount"],
    "4K HDMI Extender": ["Brand", "Model", "Quantity", "Rate", "Amount"],
  };

  const sampleData = {
    serviceType: "Plumbing",
    priorityName: "High",
    budget: "300000",
    requirementDeadLine: "28-09-2024",
    societyRemarks: "No Remarks",
  };

  const innerTableStylings = {
    fontWeight: "bold", // Make text bold
    padding: "6px 16px", // Reduce padding to decrease cell height
  };

  const paymentData = [
    {
      date: "1-Oct-2024",
      description: "Monthly Payment",
      amount: 40000,
      gst: "0.00",
    },
    {
      date: "1-Nov-2024",
      description: "Monthly Payment",
      amount: 40000,
      gst: "0.00",
    },
    {
      date: "1-Dec-2024",
      description: "Monthly Payment",
      amount: 40000,
      gst: "0.00",
    },
    // Continue adding all other rows similarly for each month
    {
      date: "1-Jan-2025",
      description: "Monthly Payment",
      amount: 40000,
      gst: "0.00",
    },
  ];
  
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));

  const handleProviderChange1 = (event) => setSelectedProvider1(event.target.value);
  const handleProviderChange2 = (event) => setSelectedProvider2(event.target.value);
  const handleProviderChange3 = (event) => setSelectedProvider3(event.target.value);
  const [selectedFiles, setSelectedFiles] = useState([
    {
      name: "SampleDocument.pdf",
      type: "application/pdf",
      // size: 1024 * 1024,
      url: "/pdf/SampleDocument.pdf"
      , 
    },
   
  ]);

  const [rows, setRows] = useState([
    { date: "", description: "", amount: "" },
  ]);

  const handleAddRow = () => {
    setRows([...rows, { date: "", description: "", amount: "" }]);
  };

  const handleDeleteRow = (index) => {
    setRows(rows.filter((_, rowIndex) => rowIndex !== index));
  };

  const handleInputChange = (index, field, value) => {
    const newRows = [...rows];
    newRows[index][field] = value;
    setRows(newRows);
  };

  const handleClosePayment = () => {
    onClose();
    setRows([{ date: "", description: "", amount: "" }]); // Reset rows
  };

  const [pdfUrl, setPdfUrl] = useState(null);

  // Custom function to handle static files
  const handleOpenDialog = (index) => {
    const file = selectedFiles[index];
    if (file.url) {
      // If the file has a static URL, set it directly
      setPdfUrl(file.url);
    } else {
      // Otherwise, handle dynamic files using default logic
      openDialog(index);
    }
  };

  const handleCloseDialog = () => {
    setPdfUrl(null);
  };

  // Dummy function to demonstrate the default openDialog logic
  const [selectedFileIndex, setSelectedFileIndex] = useState(null);
  const openDialog = async (index) => {
    setSelectedFileIndex(index);
    const file = selectedFiles[index];

    if (
      file.type ===
      "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
    ) {
      try {
        const arrayBuffer = await file.arrayBuffer();
        const result = await mammoth.convertToHtml({ arrayBuffer });
        setDocxContent(result.value);
      } catch (error) {
        console.error("Error converting DOCX to HTML:", error);
      }
    }

    if (file.type === "application/pdf") {
      try {
        const url = URL.createObjectURL(file);
        setPdfUrl(url);
      } catch (error) {
        console.error("Error creating object URL for PDF:", error);
      }
    }
  };
  useEffect(() => {
    const fetchSocieties = async () => {
      axios({
        method: "get",
        url: getUrl(authConfig.selectDropdownNew) + "?selectionType=SOCIETY_NAME",
        headers: getAuthorizationHeaders(),
      })
        .then((res) => {
          const metadataArray = res.data?.data?.map((item) => item?.metaData);
          const matchingSociety = metadataArray.find(
            (society) => society?.userId === data?.userId
          );

          // Set the matching location as the defaultValue for the SelectCategory
          if (matchingSociety) {
            setDataView({
              ...dataView, // This keeps the existing properties
              ...matchingSociety, // This adds/updates properties from newValue
            });

            const loc = matchingSociety?.designation
              ? listOfSubCategories.find(
                  (item) => item.value === matchingSociety?.designation
                )?.key
              : null;

            setDesignation(loc);
          }
        })
        .catch((err) => console.log("error", err));
    };
    fetchSocieties();
  }, [data]);


  const handleError = (error) => console.error("Error:", error);

  useEffect(() => {
    if (!!authConfig) {
      getAllListValuesByListNameId(
        authConfig.ChsDesignation,
        (data) =>
          setListOfSubCategories(
            data?.listValues?.map((item) => ({
              value: item.id,
              key: item.listValue,
            }))
          ),
        handleError
      );
    }
  }, [authConfig]);

  const { user, listValues,getAllListValuesByListNameId } = useContext(AuthContext);

  const [openEditDialog, setOpenEditDialog] = useState(false);
  const [conversation, setConversation] = useState({});

  const handleCloseEditDialog = () => {
    setOpenEditDialog(false);
  };

  const [openMoreInfoDialog, setOpenMoreInfoDialog] = useState(false);

  const handleDialogClose = () => {
    setOpenMoreInfoDialog(false);
  };

  const handleOpenEditDialog = () => {
    setOpenEditDialog(true);
  };

  const serviceType = data?.requisitionData?.serviceType
    ? listValues?.find((item) => item.id === data?.requisitionData?.serviceType)
        ?.name
    : null;

  const priorityName = data?.requisitionData?.priority
    ? listValues?.find((item) => item.id === data?.requisitionData?.priority)
        ?.name
    : null;

  const getNamesFromIds = (ids, listValues) => {
    return ids?.map((id) => {
      const foundItem = listValues?.find((item) => item?.id === id);
      return foundItem ? foundItem?.name : null;
    });
  };

  const [specifications, setSpecifications] = useState([]);

  const [userList, setUserList] = useState([]);

  useEffect(() => {
    if (data?.requisitionData?.serviceType) {
      const fetchAll = async (serviceId, data) => {
        // const url = `${getUrl(
        //   authConfig.getAllServiceProfiles
        // )}/${serviceId}/requisitionFields`;
        const headers = getAuthorizationHeaders();

        try {
          const response = await axios({
            method: "get",
            url: url,
            headers: headers,
            data: data,
          });

          if (response.data) {
            setUserList(response.data);
          } else {
            console.error("Unexpected API response format:", response);
          }
        } catch (error) {
          console.error("Error fetching users:", error);
        }
      };

      const serviceId = data?.requisitionData?.serviceType;
      fetchAll(serviceId);
    }
  }, [data]);

  useEffect(() => {
    if (data?.requisitionData?.specifications?.listNames.length > 0) {
      // Create a mapping of listValueId to listValue
      const listValueMap = userList?.reduce((map, item) => {
        item?.values?.forEach((value) => {
          map[value.id] = { id: value.id, name: value.name };
        });
        return map;
      }, {});

      // Get all list names from specifications
      const listNames = userList?.map((item) => {
        const metadataItem =
          data?.requisitionData?.specifications?.listNames?.find(
            (list) => list.listNameId === item.id
          );
        const otherValue = metadataItem ? metadataItem.otherValue : null;

        return {
          id: item.id,
          name: item.name,
          otherValue: otherValue,
          values:
            metadataItem && metadataItem.listValues.length
              ? metadataItem.listValues.map(
                  (value) => listValueMap[value.listValueId]
                )
              : [],
        };
      });
      setSpecifications(listNames);
    }
  }, [data, userList]);

  // Using the function to get names
  const names = getNamesFromIds(data?.requisitionData?.subServices, listValues);

  const subServices = names?.filter((name) => name !== null).join(", ");

  const assignedName = data?.assignedTo
    ? employeesData?.find((item) => item.id === data?.assignedTo)?.name
    : null;

  const status = data?.status
    ? listValues?.find((item) => item.id === data?.status)?.name
    : null;

  const teamMember = data?.teamMember
    ? employeesData?.find((item) => item.id === data?.teamMember)?.name
    : null;

  const handleClose = () => {
    onClose();
    setUserList([]);
    setSpecifications([]);
  };
  const renderTableRows = () => {
    let lastCategory = null; // Initialize variable to track last category processed
    return Object.entries(fixedCategories).flatMap(
      ([category, subcategories], categoryIndex) => {
        const rows = []; // Array to hold all rows for the current category
  
        // Check if current category is different from the last one and add a divider
        if (lastCategory !== null && lastCategory !== category) {
          rows.push(
            <TableRow key={`divider-${categoryIndex}`} style={{ backgroundColor: "#ccc" }}>
              <TableCell colSpan={5} 
                 style={{ 
                  padding: 0, // Remove padding
                  lineHeight: '0px', // Ensures no extra space is added for line height
                  minHeight: '1px', // Ensures that the cell does not enforce a larger height
                  backgroundColor: "rgb(142, 142, 142)"
                }} 
              />
            </TableRow>
          );
        }
  
        // Add the subcategory rows
        const subcategoryRows = subcategories.map((subcategory, index) => {
          const provider1Value = selectedProvider1
            ? serviceProviderData[selectedProvider1]?.[category]?.[subcategory] || "-"
            : "-";
         
          return (
            <TableRow key={`${category}-${subcategory}`}>
              {index === 0 && (
                <TableCell
                  rowSpan={subcategories.length}
                  sx={{
                    fontWeight: "bold !important",
                    minWidth: "150px",
                    verticalAlign: 'top',
                    textAlign: "left",
                    borderRight: "1px solid #ddd",
                    wordWrap: "break-word",
                    whiteSpace: "normal",
                    color: "#108A00 !important",
                    fontSize: '15px'
                  }}
                >
                  {category.toUpperCase()}
                </TableCell>
              )}
              <TableCell
                  sx={{
                    color: "black !important",
                    fontWeight: "bold !important",
                    minWidth: "150px",
                    textAlign: "left",
                    borderRight: "1px solid #ddd",
                    wordWrap: "break-word",
                    whiteSpace: "normal",
                    fontSize: '14px',
                        paddingLeft: '10px !important',
                        paddingRight:'0px !important'
                  }}
              >
                {subcategory}
              </TableCell>
              <TableCell
                  sx={{
                    textAlign: "left",
                    minWidth: "200px",
                    borderRight: "1px solid #ddd",
                    wordWrap: "break-word",
                    whiteSpace: "normal",
                    overflow: "auto",
                    maxWidth: 200,
                    color: "black !important",
                    fontSize: '13px'
                  }}
              >
                {provider1Value}
              </TableCell>
            </TableRow>
          );
        });
  
        rows.push(...subcategoryRows); // Add all subcategory rows to the main row array
        lastCategory = category; // Update lastCategory to current category
        return rows; // Return all rows for the current category
      }
    );
  };

  return (
    <>
      <Dialog open={open} onClose={handleClose} fullScreen>
        <DialogTitle
          sx={{
            position: "relative",
            borderBottom: (theme) => `1px solid ${theme.palette.divider}`,
            p: (theme) => `${theme.spacing(1.75, 4)} !important`,
            display: "flex",
            alignItems: "center",
            justifyContent: "start",
            fontSize: { xs: 19, md: 20 },
          }}
          textAlign={"center"}
        >
          View Quotation
          <Box sx={{ position: "absolute", top: "4px", right: "12px" }}>
            <IconButton
              size="small"
              onClick={handleClose}
              sx={{
                // p: "0.438rem",
                borderRadius: 1,
                color: "common.white",
                backgroundColor: "primary.main",
                "&:hover": {
                  backgroundColor: "#66BB6A",
                  transition: "background 0.5s ease, transform 0.5s ease",
                },
              }}
            >
              <Icon icon="tabler:x" fontSize="1rem" />
            </IconButton>
          </Box>
        </DialogTitle>
        <DialogContent
          sx={{
            position: "relative",
            pb: (theme) => `${theme.spacing(5)} !important`,
            px: (theme) => [`${theme.spacing(8)} !important`],
          }}
        >
          <>
            <Card>
              <AccordionBasic
                id={"panel-header-2"}
                ariaControls={"panel-content-2"}
                heading={"Requisition Details"}
                body={
                  <>
                    <TableContainer
                      sx={{ padding: "4px 6px", paddingTop: "0px !important" }}
                      className="tableBody"
                    >
                      <Table>
                        <TableBody
                          sx={{
                            "& .MuiTableCell-root": {
                              p: `${theme.spacing(1.35, 1.125)} !important`,
                            },
                          }}
                        >
                          <TableRow>
                            <MUITableCell sx={{ padding: "4px 8px" ,
                               width: { xs: "50%", sm: "50%",md:"40%" },  // Responsive width adjustment
                               textAlign: "right", // Align the text to the left
                               verticalAlign: "middle", // Vertically align the text to the middle
                               textIndent: { lg: "80px",md: "80px", sm:"100px",xs: "0" },
                            }}>
                              {/* Adjust padding here */}
                              <Typography
                                style={{
                                  ...field,
                                  lineHeight: "1.2",
                                }}
                              >
                                Service Type:
                              </Typography>
                            </MUITableCell>
                            <MUITableCell sx={{ padding: "4px 8px",
                              justifyContent: "flex-start",
                              alignItems: "center",
                              paddingRight: theme.spacing(2),
                              width: { xs: "50%",sm: "50%", md: "60%" }, // Responsive width for small and medium screens
                              height: '10px', 
                             }}>
                              {/* Adjust padding here */}
                              <Typography
                                className="data-field"
                                style={{
                                  lineHeight: "1.2",color:"#388E3C",
                                }}
                              >
                                {sampleData.serviceType}
                              </Typography>
                            </MUITableCell>
                          </TableRow>

                          {data?.requisitionData?.subServices?.length > 0 && (
                            <TableRow>
                              <MUITableCell sx={{ padding: "4px 8px",
                                 width: { xs: "50%", sm: "50%",md:"40%" },  // Responsive width adjustment
                                 textAlign: "right", // Align the text to the left
                                 verticalAlign: "middle", // Vertically align the text to the middle
                                 textIndent: { lg: "80px",md: "80px", sm:"100px",xs: "0" },
                               }}>
                                {" "}
                                {/* Adjust padding here */}
                                <Typography
                                  style={{
                                    ...field,
                                    lineHeight: "1.2", // Adjust line height
                                  }}
                                >
                                  Sub Services:
                                </Typography>
                              </MUITableCell>
                              <MUITableCell sx={{ padding: "4px 8px",
                                justifyContent: "flex-start",
                                alignItems: "center",
                                paddingRight: theme.spacing(2),
                                width: { xs: "50%",sm: "50%", md: "60%" }, // Responsive width for small and medium screens
                                height: '10px', 
                               }}>
                                {" "}
                                {/* Adjust padding here */}
                                <Typography
                                  className="data-field"
                                  style={{ lineHeight: "1.2",color:"#388E3C", }}
                                >
                                  {subServices}
                                </Typography>
                              </MUITableCell>
                            </TableRow>
                          )}
                          {data?.requisitionData?.anyOtherServices && (
                            <TableRow>
                              <MUITableCell sx={{ padding: "4px 8px" }}>
                                {" "}
                                {/* Adjust padding here */}
                                <Typography
                                  style={{
                                    ...field,
                                    lineHeight: "1.2", // Adjust line height
                                  }}
                                >
                                  Other Services:
                                </Typography>
                              </MUITableCell>
                              <MUITableCell sx={{ padding: "4px 8px" }}>
                                {" "}
                                {/* Adjust padding here */}
                                <Typography
                                  className="data-field"
                                  style={{ lineHeight: "1.2",color:"#388E3C", }}
                                >
                                  {data?.requisitionData?.anyOtherServices}
                                </Typography>
                              </MUITableCell>
                            </TableRow>
                          )}

                          <TableRow>
                            <MUITableCell sx={{ padding: "4px 8px",
                               width: { xs: "50%", sm: "50%",md:"40%" },  // Responsive width adjustment
                               textAlign: "right", // Align the text to the left
                               verticalAlign: "middle", // Vertically align the text to the middle
                               textIndent: { lg: "80px",md: "80px", sm:"100px",xs: "0" },
                             }}>
                              {" "}
                              {/* Adjust padding here */}
                              <Typography
                                style={{ ...field, lineHeight: "1.2" }}
                              >
                                Priority:
                              </Typography>
                            </MUITableCell>
                            <MUITableCell sx={{ padding: "4px 8px",
                              justifyContent: "flex-start",
                              alignItems: "center",
                              paddingRight: theme.spacing(2),
                              width: { xs: "50%",sm: "50%", md: "60%" }, // Responsive width for small and medium screens
                              height: '10px', 
                             }}>
                              {" "}
                              {/* Adjust padding here */}
                              <Typography
                                className="data-field"
                                style={{ lineHeight: "1.2" ,color:"#388E3C",}}
                              >
                                {sampleData.priorityName}
                              </Typography>
                            </MUITableCell>
                          </TableRow>
                          <TableRow>
                            <MUITableCell sx={{ padding: "4px 8px",
                               width: { xs: "50%", sm: "50%",md:"40%" },  // Responsive width adjustment
                               textAlign: "right", // Align the text to the left
                               verticalAlign: "middle", // Vertically align the text to the middle
                               textIndent: { lg: "80px",md: "80px", sm:"100px",xs: "0" },
                             }}>
                              {" "}
                              {/* Adjust padding here */}
                              <Typography
                                style={{ ...field, lineHeight: "1.2" }}
                              >
                                Budget:
                              </Typography>
                            </MUITableCell>
                            <MUITableCell sx={{ padding: "4px 8px",
                              justifyContent: "flex-start",
                              alignItems: "center",
                              paddingRight: theme.spacing(2),
                              width: { xs: "50%",sm: "50%", md: "60%" }, // Responsive width for small and medium screens
                              height: '10px', 
                            }}>
                              {" "}
                              {/* Adjust padding here */}
                              <Typography
                                className="data-field"
                                style={{ lineHeight: "1.2",color:"#388E3C", }}
                              >
                               {sampleData?.budget}
                              </Typography>
                            </MUITableCell>
                          </TableRow>

                          <TableRow>
                            <MUITableCell sx={{ padding: "4px 8px",
                               width: { xs: "50%", sm: "50%",md:"40%" },  // Responsive width adjustment
                               textAlign: "right", // Align the text to the left
                               verticalAlign: "middle", // Vertically align the text to the middle
                               textIndent: { lg: "80px",md: "80px", sm:"100px",xs: "0" },
                             }}>
                              {" "}
                              {/* Adjust padding here */}
                              <Typography
                                style={{ ...field, lineHeight: "1.2" }}
                              >
                                Requirement Dead Line
                              </Typography>
                            </MUITableCell>
                            <MUITableCell sx={{ padding: "4px 8px",
                              justifyContent: "flex-start",
                              alignItems: "center",
                              paddingRight: theme.spacing(2),
                              width: { xs: "50%",sm: "50%", md: "60%" }, // Responsive width for small and medium screens
                              height: '10px', 
                             }}>
                              {" "}
                              {/* Adjust padding here */}
                              <Typography
                                className="data-field"
                                style={{ lineHeight: "1.2",color:"#388E3C", }}
                              >
                                 {sampleData?.requirementDeadLine}
                              </Typography>
                            </MUITableCell>
                          </TableRow>
                          <TableRow>
                            <MUITableCell sx={{ padding: "4px 8px",
                               width: { xs: "50%", sm: "50%",md:"40%" },  // Responsive width adjustment
                               textAlign: "right", // Align the text to the left
                               verticalAlign: "middle", // Vertically align the text to the middle
                               textIndent: { lg: "80px",md: "80px", sm:"100px",xs: "0" },
                             }}>
                              {" "}
                              {/* Adjust padding here */}
                              <Typography
                                style={{ ...field, lineHeight: "1.2" }}
                              >
                                Society Remarks:
                              </Typography>
                            </MUITableCell>
                            <MUITableCell sx={{ padding: "4px 8px",
                              justifyContent: "flex-start",
                              alignItems: "center",
                              paddingRight: theme.spacing(2),
                              width: { xs: "50%",sm: "50%", md: "60%" }, // Responsive width for small and medium screens
                              height: '10px', 
                            }}>
                              {" "}
                              {/* Adjust padding here */}
                              <Typography
                                className="data-field"
                                style={{ lineHeight: "1.2",color:"#388E3C", }}
                              >
                                 {sampleData?.societyRemarks}
                              </Typography>
                            </MUITableCell>
                          </TableRow>
                          {specifications
                            ?.slice()
                            ?.sort((a, b) => a.listSequence - b.listSequence)
                            ?.map((category) =>
                              category.values.length > 0 ||
                              category.otherValue ? (
                                <TableRow key={category.name}>
                                  <MUITableCell sx={{ padding: "4px 8px" }}>
                                    {" "}
                                    {/* Adjust padding here */}
                                    <Typography
                                      style={{ ...field, lineHeight: "1.2" }}
                                    >
                                      {category.name}
                                    </Typography>
                                  </MUITableCell>
                                  <MUITableCell sx={{ padding: "4px 8px" }}>
                                    {" "}
                                    {/* Adjust padding here */}
                                    {category?.values?.length > 0
                                      ? category?.values?.map(
                                          (value, index) => (
                                            <Typography
                                              key={index}
                                              className="data-field"
                                              style={{ lineHeight: "1.2" }}
                                            >
                                              {value.name}
                                            </Typography>
                                          )
                                        )
                                      : category?.otherValue && (
                                          <Typography
                                            className="data-field"
                                            style={{ lineHeight: "1.2" }}
                                          >
                                            {category.otherValue}
                                          </Typography>
                                        )}
                                  </MUITableCell>
                                </TableRow>
                              ) : null
                            )}
                        </TableBody>
                      </Table>
                    </TableContainer>
                  </>
                }
              
              />
            </Card>
           
            {/* <AccordionBasic
              id={"panel-header-2"}
              ariaControls={"panel-content-2"}
              heading={"Payment schedule"}
             
              body={
        <TableContainer component={Paper}>
                <Table aria-label="payment schedule" size="small">
                  <TableHead>
                    <TableRow>
                      <TableCell style={innerTableStylings}>Date</TableCell>
                      <TableCell style={innerTableStylings}>
                        Payment Description
                      </TableCell>
                      <TableCell style={innerTableStylings}>
                        Amount(₹)
                      </TableCell>
                      <TableCell style={innerTableStylings}>GST</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {paymentData.map((row, index) => (
                      <TableRow key={index}>
                        <TableCell style={innerTableStylings}>
                          {row.date}
                        </TableCell>
                        <TableCell style={innerTableStylings}>
                          {row.description}
                        </TableCell>
                        <TableCell style={innerTableStylings}>{`${Number(
                          row.amount
                        ).toFixed(2)}`}</TableCell>
                        <TableCell style={innerTableStylings}>
                          {row.gst}
                        </TableCell>
                      </TableRow>
                    ))}

                    
                    <TableRow>
                      <TableCell style={innerTableStylings}></TableCell>
                      <TableCell style={innerTableStylings}>
                        <strong>Advance Payment (10%):</strong>
                      </TableCell>
                      <TableCell style={innerTableStylings}>
                        <strong>{40000.0}</strong>
                      </TableCell>
                      <TableCell style={innerTableStylings}></TableCell>
                    </TableRow>


                    <TableRow>
                      <TableCell style={innerTableStylings}></TableCell>
                      <TableCell style={innerTableStylings}>
                        <strong>Total Amount (₹):</strong>
                      </TableCell>
                      <TableCell style={innerTableStylings}>
                        <strong>{200000.0}</strong>
                      </TableCell>
                      <TableCell style={innerTableStylings}></TableCell>
                    </TableRow>
                  </TableBody>
                </Table>
              </TableContainer>
              }
              /> */}
              
            <Card sx={{ p: 2 }}>
  <Typography variant="h6" sx={{ mb: 2, ml: 3 }}>
    Final/Revised BOQ
  </Typography>

  {/* Service Provider Selection Section */}
  <TableContainer
          component={Paper}
          sx={{ overflowX: "auto", maxHeight: "81vh" }}
        >
          <Table aria-label="comparison table" stickyHeader>
            <TableHead>
              <TableRow>
                <TableCell
                  sx={{
                  backgroundColor: (theme) => theme.palette.primary.background,
                  fontWeight: "bold !important",
                  minWidth: isMobile ? 120 : 150,
                  zIndex: 2,
                  textAlign: "left",
                  borderRight: "1px solid #ddd", // Vertical line
                  wordWrap: "break-word", // Wrap text if too long
                  whiteSpace: "normal",
                  width:2,
                }}
                >
                  Section
                </TableCell>
                <TableCell
                  sx={{
                    zIndex: 2,
                    fontWeight: "bold !important",
                    minWidth: isMobile ? 120 : 150,
                    borderRight: "1px solid #ddd", // Vertical line
                    paddingLeft: 0,
                    textAlign: "center",
                    position: "sticky",
                    top: 0, // Fixed header
                    backgroundColor: (theme) => theme.palette.primary.background,
                    width:2,
                  }}
                >
                  Subsection
                </TableCell>
                <TableCell
                  sx={{
                    textAlign: "left",
                    fontWeight: "bold",
                    minWidth: isMobile ? 150 : 200,
                    borderRight: "0.5px solid #ddd", // Vertical line
                    position: "sticky",
                    top: 0, // Fixed header
                    backgroundColor: (theme) => theme.palette.primary.background,
                  }}
                >
                <Typography
            sx={{
              textAlign: "left",
              fontWeight: "bold",
              minWidth: isMobile ? 150 : 200,
              padding: "4px", // Adjust the padding for a more compact appearance
              fontSize: "14px", // Optional: Adjust font size if necessary
            }}
          >
            Service Provider A
          </Typography>
                </TableCell>
               
              </TableRow>
            </TableHead>
            <TableBody>{renderTableRows()}</TableBody>
          </Table>
        </TableContainer>
</Card>



          </>
        </DialogContent>
    
      </Dialog>
    </>
  );
};

export default ViewQuotationsBOQ;
