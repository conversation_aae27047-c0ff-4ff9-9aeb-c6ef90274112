import React, { useState, useEffect, useContext } from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Typography,
  Box,
  IconButton,
  Button,
  Grid,
  FormControl,
  FormHelperText,
  TextField,
  Table,
  TableRow,
  TableContainer,
  TableCell as MUITableCell,
  TextareaAutosize,
  TableHead,
  TableBody,
  MenuItem,
  Select,
  DialogContentText,
  Snackbar,
  Alert,
} from "@mui/material";
import { useForm, Controller } from "react-hook-form";
import Icon from "src/@core/components/icon";
import { AuthContext } from "src/context/AuthContext";
import SelectAutoComplete from "src/@core/components/custom-components/SelectAutoComplete";
import axios from "axios";
import {
  getUrl,
  getAuthorizationHeaders,
  getFileUploadPDFHeaders,
} from "src/helpers/utils";
import authConfig from "src/configs/auth";
import { saveAs } from "file-saver";

const InvoiceDialog = ({
  open,
  onClose,
  data,
  statusList,
  fetchInvoices,
  page,
  pageSize,
  searchFilters,
  status,
  setStatus,
  senderType,
  setSenderType,
  recipientType,
  setRecipientType,
  orgName,
  setOrgName,
  recipientOrgName,
  setRecipientOrgName,
  invoiceCode,
}) => {
  const {
    handleSubmit,
    reset,
    setValue,
    control,
    clearErrors,
    formState: { errors },
  } = useForm();

  const { postInvoice, putInvoice } = useContext(AuthContext);

  const [invoiceNumber, setInvoiceNumber] = useState(""); // State for invoice number
  const [invoiceDate, setInvoiceDate] = useState(""); // State for invoice date
  const [dueDate, setDueDate] = useState(""); // State for due date

  const [showToast, setShowToast] = useState(false); // State for toast visibility
  const [toastMessage, setToastMessage] = useState("");

  const [senderRecipientTypes, setSenderRecipientTypes] = useState([]);

  const [dialogMessage, setDialogMessage] = useState("");
  const [dialogSuccess, setDialogSuccess] = useState(false);
  const [generatingPDF, setGeneratingPDF] = useState(false);
  const [senderGstin, setSenderGstin] = useState("");
  const [recipientGstin, setRecipientGstin] = useState("");
  const [senderOrganisationDetails, setSenderOrganisationDetails] = useState(
    {}
  );
  const [recipientOrgDetails, setRecipientOrgDetails] = useState({});

  const [senderOrganizationName, setSenderOrganizationName] = useState([]);

  const [recipientOrganizationName, setRecipientOrganizationName] = useState(
    []
  );
  const [showSenderDetails, setShowSenderDetails] = useState(false);
  const [showReceiverDetails, setShowReceiverDetails] = useState(false);
  // Initial lineItems state
  const [lineItems, setLineItems] = useState([
    {
      description: "",
      quantity: 1,
      rate: 0.0,
      gstPercentage: 0, // The total GST percentage input by user
      cgst: 0,
      sgst: 0,
      igst: 0,
      gstAmount: 0.0,
      finalAmount: 0.0,
    },
  ]);

  const [senderChange, setSenderChange] = useState(false);
  const [recipientChange, setRecipientChange] = useState(false);

  useEffect(() => {
    if (data) {
      setSenderType(data?.senderDetails?.type);
      setOrgName(data?.senderDetails?.orgId);
      setShowSenderDetails(true);
      setValue("senderPanNumber", data?.senderDetails?.panNo);
      setValue("senderState", data?.senderDetails?.stateName);
      setSenderGstin(data?.senderDetails?.gstIn);
      setValue("senderAddress", data?.senderDetails?.address);
      setRecipientType(data?.receiverDetails?.type);
      setRecipientOrgName(data?.receiverDetails?.orgId);
      setShowReceiverDetails(true);
      setValue("receiverPanNumber", data?.receiverDetails?.panNo);
      setValue("receiverState", data?.receiverDetails?.stateName);
      setRecipientGstin(data?.receiverDetails?.gstIn);
      setValue("receiverAddress", data?.receiverDetails?.address);
      setStatus(data?.statusId);
      setValue("subject", data?.metaDataInvoice?.subject);
      setValue("invoiceDate", data?.metaDataInvoice?.invoiceDate);
      setValue("dueDate", data?.metaDataInvoice?.dueDate);
      setValue("summary", data?.mediatorDetails?.summary);
      const items = reverseTransform(data?.billingBreakdown?.items);
      setLineItems(items);
    } else {
      setShowSenderDetails(false);
      setOrgName("");
      setSenderType("");
      setShowReceiverDetails(false);
      setRecipientOrgName("");
      setRecipientType("");
      setValue("subject", "");
      setValue("invoiceDate", "");
      setValue("dueDate", "");
      setValue("summary", "");
      setSenderGstin("");
      setRecipientGstin("");
      setLineItems([
        {
          description: "",
          quantity: 1,
          rate: 0.0,
          gstPercentage: 0, // The total GST percentage input by user
          cgst: 0,
          sgst: 0,
          igst: 0,
          gstAmount: 0.0,
          finalAmount: 0.0,
        },
      ]);
    }
  }, [data]);

  const handleToastClose = () => {
    setShowToast(false);
    setToastMessage("");
  };

  const handleSenderOrgChange = (event) => {
    const selectedOrgName = event.target.value;
    setOrgName(selectedOrgName); // Update the organization name
    setShowSenderDetails(!!selectedOrgName); // Show details if a value is selected
  };

  // Handler for Receiver Organization Name
  const handleReceiverOrgChange = (event) => {
    const selectedReceiverOrgName = event.target.value;
    setRecipientOrgName(selectedReceiverOrgName); // Update the organization name
    setShowReceiverDetails(!!selectedReceiverOrgName); // Show details if a value is selected
  };

  useEffect(() => {
    setValue("senderPanNumber", senderOrganisationDetails?.panNo || "");
    setValue("senderAddress", senderOrganisationDetails?.address || "");
    setValue("senderState", senderOrganisationDetails?.stateName || "");
    setSenderGstin(senderOrganisationDetails?.gstNo);
  }, [senderOrganisationDetails]);

  useEffect(() => {
    setValue("receiverPanNumber", recipientOrgDetails?.panNo);
    setValue("receiverAddress", recipientOrgDetails?.address);
    setValue("receiverState", recipientOrgDetails?.stateName);
    setRecipientGstin(recipientOrgDetails?.gstNo);
  }, [recipientOrgDetails]);

  useEffect(() => {
    if (open) {
      const generatedInvoiceNumber = `INV-${Math.floor(
        1000 + Math.random() * 9000
      )}`;
      setInvoiceNumber(generatedInvoiceNumber);

      const today = new Date().toISOString().split("T")[0];
      const defaultDueDate = new Date(
        new Date().setDate(new Date().getDate() + 30)
      )
        .toISOString()
        .split("T")[0];
      setInvoiceDate(today);
      setDueDate(defaultDueDate);
    }
  }, [open]);

  useEffect(() => {
    axios({
      method: "get",
      url:
        getUrl(authConfig.organisationsEndpoint) + "/organisation-categories",
      headers: getAuthorizationHeaders(),
    })
      .then((res) => {
        setSenderRecipientTypes(res.data);
      })
      .catch((err) => console.log("Categories error", err));
  }, []);

  useEffect(() => {
    if (senderType) {
      axios({
        method: "get",
        url: getUrl(authConfig.organisationsEndpoint) + "/" + senderType,
        headers: getAuthorizationHeaders(
          authConfig.ORGANISATION_GET_NAME_BY_CATEGORY_V1
        ),
      })
        .then((res) => {
          setSenderOrganizationName(res.data);
          setSenderChange(true);
        })
        .catch((err) => console.log("Categories error", err));
    }
  }, [senderType]);

  useEffect(() => {
    if (recipientType) {
      axios({
        method: "get",
        url: getUrl(authConfig.organisationsEndpoint) + "/" + recipientType,
        headers: getAuthorizationHeaders(
          authConfig.ORGANISATION_GET_NAME_BY_CATEGORY_V1
        ),
      })
        .then((res) => {
          setRecipientOrganizationName(res.data);
          setRecipientChange(true);
        })
        .catch((err) => console.log("Categories error", err));
    }
  }, [recipientType]);

  useEffect(() => {
    if (orgName && senderChange) {
      axios({
        method: "get",
        url:
          getUrl(authConfig.organisationsEndpoint) +
          "/organisation-meta-data/" +
          orgName,
        headers: getAuthorizationHeaders({
          accept: authConfig.ORGANISATION_GET_ORGANISATION_META_DATA_RES_V1,
        }),
      })
        .then((res) => {
          setSenderOrganisationDetails(res.data);
        })
        .catch((err) => console.log("Categories error", err));
    }
  }, [orgName]);

  useEffect(() => {
    if (recipientOrgName && recipientChange) {
      axios({
        method: "get",
        url:
          getUrl(authConfig.organisationsEndpoint) +
          "/organisation-meta-data/" +
          recipientOrgName,
        headers: getAuthorizationHeaders({
          accept: authConfig.ORGANISATION_GET_ORGANISATION_META_DATA_RES_V1,
        }),
      })
        .then((res) => {
          setRecipientOrgDetails(res.data);
        })
        .catch((err) => console.log("Categories error", err));
    }
  }, [recipientOrgName]);

  const reverseTransform = (billingArray) => {
    const lineItems = billingArray?.map((item) => {
      const gst = item.gst || {}; // Ensure gst exists

      // Calculate GST-related values
      const gstPercentage = gst.gstPercentage || 0;
      const cgst = gst.cgst || 0;
      const sgst = gst.sgst || 0;
      const igst = gst.igst || 0;

      return {
        description: item.description || "",
        quantity: parseFloat(item.quantity) || 0,
        rate: parseFloat(item.rate) || 0,
        gstPercentage: gstPercentage,
        cgst: cgst,
        sgst: sgst,
        igst: igst,
        gstAmount:
          parseFloat(gst.cgstAmount || 0) +
          parseFloat(gst.sgstAmount || 0) +
          parseFloat(gst.igstAmount || 0),
        finalAmount:
          parseFloat(item.amount) +
          parseFloat(gst.cgstAmount || 0) +
          parseFloat(gst.sgstAmount || 0) +
          parseFloat(gst.igstAmount || 0),
      };
    });

    return lineItems;
  };

  const handleDialogClose = () => {
    reset();
    setSenderType("");
    setOrgName("");
    setShowSenderDetails(false);
    setRecipientType("");
    setRecipientOrgName("");
    setShowReceiverDetails(false);
    setStatus("");
    setValue("subject", "");
    setValue("invoiceDate", "");
    setValue("dueDate", "");
    setValue("summary", "");
    setSenderGstin("");
    setRecipientGstin("");
    setSenderChange(false);
    setRecipientChange(false);
    setLineItems([
      {
        description: "",
        quantity: 1,
        rate: 0.0,
        gstPercentage: 0, // The total GST percentage input by user
        cgst: 0,
        sgst: 0,
        igst: 0,
        gstAmount: 0.0,
        finalAmount: 0.0,
      },
    ]);
    onClose();
  };

  const handleClose = () => {
    setDialogSuccess(false);
  };

  const [totalBeforeGST, setTotalBeforeGST] = useState(0.0);
  const [totalGST, setTotalGST] = useState(0.0);
  const [totalAfterGST, setTotalAfterGST] = useState(0.0);

  // Utility function to extract state code (first two digits) from GSTIN
  const getGstStateCode = (gstin) => {
    // Basic validation: GSTIN typically 15 chars, but we only need first two digits if available
    if (gstin && gstin?.length >= 2) {
      return gstin.substring(0, 2);
    }
    return null;
  };

  // Determine GST type (CGST+SGST or IGST) based on sender and recipient GSTIN state codes
  const determineGstType = (sender, recipient) => {
    const senderStateCode = getGstStateCode(sender);
    const recipientStateCode = getGstStateCode(recipient);

    if (!senderStateCode || !recipientStateCode) {
      // GST is not available or not provided correctly -> treat as CGST+SGST scenario
      return "CGST_SGST";
    }

    if (senderStateCode === recipientStateCode) {
      // Same state -> CGST+SGST
      return "CGST_SGST";
    } else {
      // Different state -> IGST
      return "IGST";
    }
  };

  const gstType = determineGstType(senderGstin, recipientGstin);

  // Handle changes in line item fields
  const handleLineItemChange = (index, key, value) => {
    const updatedLineItems = [...lineItems];
    updatedLineItems[index][key] = value;

    // Convert values to float to avoid NaN issues
    const quantity = parseFloat(updatedLineItems[index].quantity) || 0;
    const rate = parseFloat(updatedLineItems[index].rate) || 0;
    const gstPerc = parseFloat(updatedLineItems[index].gstPercentage) || 0;

    const baseAmount = quantity * rate;

    let cgst = 0,
      sgst = 0,
      igst = 0;
    if (gstType === "CGST_SGST") {
      // Split equally
      cgst = gstPerc / 2;
      sgst = gstPerc / 2;
    } else {
      // IGST
      igst = gstPerc;
    }

    // Calculate GST amounts
    const cgstAmount = (baseAmount * cgst) / 100;
    const sgstAmount = (baseAmount * sgst) / 100;
    const igstAmount = (baseAmount * igst) / 100;
    const totalGstAmount = cgstAmount + sgstAmount + igstAmount;

    const finalAmount = baseAmount + totalGstAmount;

    updatedLineItems[index].cgst = cgst;
    updatedLineItems[index].sgst = sgst;
    updatedLineItems[index].igst = igst;
    updatedLineItems[index].gstAmount = totalGstAmount.toFixed(2);
    updatedLineItems[index].finalAmount = finalAmount.toFixed(2);

    setLineItems(updatedLineItems);
  };

  // Handle GST percentage selection change
  const handleGstPercentageChange = (index, selectedPercentage) => {
    handleLineItemChange(index, "gstPercentage", selectedPercentage);
  };

  // Add line item
  const addLineItem = () => {
    setLineItems((prevItems) => [
      ...prevItems,
      {
        description: "",
        quantity: 1,
        rate: 0.0,
        gstPercentage: 0,
        cgst: 0,
        sgst: 0,
        igst: 0,
        gstAmount: 0.0,
        finalAmount: 0.0,
      },
    ]);
  };

  // Remove line item
  const removeLineItem = (index) => {
    if (lineItems?.length > 1) {
      const updatedLineItems = [...lineItems];
      updatedLineItems.splice(index, 1);
      setLineItems(updatedLineItems);
    }
  };

  // Calculate totals whenever lineItems change
  const calculateTotals = (items) => {
    const totalBase = items?.reduce((sum, item) => {
      const qty = parseFloat(item.quantity) || 0;
      const rt = parseFloat(item.rate) || 0;
      return sum + qty * rt;
    }, 0);

    const totalGstCalc = items?.reduce((sum, item) => {
      const itemGst = parseFloat(item.gstAmount) || 0;
      return sum + itemGst;
    }, 0);

    const totalWithGst = totalBase + totalGstCalc;

    setTotalBeforeGST(totalBase?.toFixed(2));
    setTotalGST(totalGstCalc?.toFixed(2));
    setTotalAfterGST(totalWithGst?.toFixed(2));
  };

  useEffect(() => {
    calculateTotals(lineItems);
  }, [lineItems]);

  const validateGSTLength = (value) => {
    if (value && value.length > 15) return "GST number cannot exceed 15 characters";
    return true;
};
  const validateGST = (value) => {
    if (!value) return true; // Skip validation if GST number is not provided
    const gstRegex =
      /^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/;
    return gstRegex.test(value) || "Invalid GST number format";
  };

  const cellSx = { p: "2px", verticalAlign: "middle" };
  const MAX_RATE_DIGITS = 8;
  const getTableHeaders = () => {
    if (gstType === "IGST") {
      // Adjust IGST (%) width from 5% to 3% to reduce its width
      return (
        <>
          <MUITableCell sx={{ ...cellSx, width: "50%" }}>
            Particulars
          </MUITableCell>
          <MUITableCell sx={{ ...cellSx, width: "5%" }} align="center">
            Quantity
          </MUITableCell>
          <MUITableCell sx={{ ...cellSx, width: "12%" }} align="center">
            Rate
          </MUITableCell>
          <MUITableCell sx={{ ...cellSx, width: "3%" }} align="center">
            IGST (%)
          </MUITableCell>
          <MUITableCell sx={{ ...cellSx, width: "10%" }} align="center">
            Amount
          </MUITableCell>
          <MUITableCell sx={{ ...cellSx, width: "10%" }} align="center">
            GST Amount
          </MUITableCell>
          <MUITableCell sx={{ ...cellSx, width: "7%" }} align="center">
            Actions
          </MUITableCell>
        </>
      );
    } else {
      // Adjust GST (%) width from 5% to 3%
      // Adjust CGST and SGST similarly if needed
      return (
        <>
          <MUITableCell sx={{ ...cellSx, width: "50%" }}>
            Particulars
          </MUITableCell>
          <MUITableCell sx={{ ...cellSx, width: "5%" }} align="center">
            Quantity
          </MUITableCell>
          <MUITableCell sx={{ ...cellSx, width: "10%" }} align="center">
            Rate
          </MUITableCell>
          <MUITableCell sx={{ ...cellSx, width: "3%" }} align="center">
            GST (%)
          </MUITableCell>
          <MUITableCell sx={{ ...cellSx, width: "5%" }} align="center">
            CGST (%)
          </MUITableCell>
          <MUITableCell sx={{ ...cellSx, width: "5%" }} align="center">
            SGST (%)
          </MUITableCell>
          <MUITableCell sx={{ ...cellSx, width: "10%" }} align="center">
            Amount
          </MUITableCell>
          <MUITableCell sx={{ ...cellSx, width: "10%" }} align="center">
            GST Amount
          </MUITableCell>
          <MUITableCell sx={{ ...cellSx, width: "2%" }} align="center">
            Actions
          </MUITableCell>
        </>
      );
    }
  };

  const getTableRows = () => {
    return lineItems?.map((item, index) => {
      const quantity = parseFloat(item.quantity) || 0;
      const rate = parseFloat(item.rate) || 0;
      const amount = (quantity * rate).toFixed(2);

      const handleQuantityChange = (val) => {
        let num = val.replace(/\D/g, "");
        if (num !== "") {
          let parsedNum = parseInt(num, 10);
          if (parsedNum < 1) parsedNum = 1;
          if (parsedNum > 999) parsedNum = 999;
          num = parsedNum.toString();
        }
        handleLineItemChange(index, "quantity", num);
      };

      const handleRateChange = (val) => {
        let cleaned = val.replace(/[^\d.]/g, "");
        const parts = cleaned.split(".");
        parts[0] = parts[0].slice(0, MAX_RATE_DIGITS);
        cleaned = parts.join(".");
        handleLineItemChange(index, "rate", cleaned);
      };

      if (gstType === "IGST") {
        return (
          <TableRow key={index}>
            <MUITableCell sx={cellSx}>
              <TextField
                variant="outlined"
                size="small"
                value={item.description}
                onChange={(e) =>
                  handleLineItemChange(index, "description", e.target.value)
                }
                placeholder="Enter Particulars"
                sx={{ width: "100%" }}
              />
            </MUITableCell>

            <MUITableCell align="center" sx={cellSx}>
              <TextField
                variant="outlined"
                size="small"
                type="text"
                value={item.quantity}
                onChange={(e) => handleQuantityChange(e.target.value)}
                inputProps={{ maxLength: 3 }}
                sx={{ width: "100%" }}
              />
            </MUITableCell>

            <MUITableCell align="center" sx={cellSx}>
              <TextField
                variant="outlined"
                size="small"
                type="text"
                value={item.rate}
                onChange={(e) => handleRateChange(e.target.value)}
                inputProps={{ maxLength: MAX_RATE_DIGITS + 3 }}
                sx={{ width: "100%" }}
              />
            </MUITableCell>

            <MUITableCell align="center" sx={cellSx}>
              <Select
                variant="outlined"
                size="small"
                value={item.gstPercentage}
                onChange={(e) =>
                  handleGstPercentageChange(index, e.target.value)
                }
                sx={{ width: "80%", p: 0 }}
                MenuProps={{
                  PaperProps: { style: { maxHeight: 200 } },
                }}
              >
                {[...Array(100)]?.map((_, i) => (
                  <MenuItem key={i + 1} value={i + 1}>
                    {i + 1}
                  </MenuItem>
                ))}
              </Select>
            </MUITableCell>

            <MUITableCell align="center" sx={cellSx}>
              {amount}
            </MUITableCell>

            <MUITableCell align="center" sx={cellSx}>
              {item.gstAmount}
            </MUITableCell>

            <MUITableCell align="center" sx={cellSx}>
              {lineItems?.length > 1 && (
                <IconButton
                  size="small"
                  color="error"
                  onClick={() => removeLineItem(index)}
                >
                  <Icon icon="tabler:trash" />
                </IconButton>
              )}
              <IconButton size="small" onClick={addLineItem}>
                <Icon icon="tabler:plus" />
              </IconButton>
            </MUITableCell>
          </TableRow>
        );
      } else {
        // CGST+SGST scenario
        return (
          <TableRow key={index}>
            <MUITableCell sx={cellSx}>
              <TextField
                variant="outlined"
                size="small"
                value={item.description}
                onChange={(e) =>
                  handleLineItemChange(index, "description", e.target.value)
                }
                placeholder="Enter Particulars"
                sx={{ width: "100%" }}
              />
            </MUITableCell>

            <MUITableCell align="center" sx={cellSx}>
              <TextField
                variant="outlined"
                size="small"
                type="text"
                value={item.quantity}
                onChange={(e) => handleQuantityChange(e.target.value)}
                inputProps={{ maxLength: 3 }}
                sx={{ width: "100%" }}
              />
            </MUITableCell>

            <MUITableCell align="center" sx={cellSx}>
              <TextField
                variant="outlined"
                size="small"
                type="text"
                value={item.rate}
                onChange={(e) => handleRateChange(e.target.value)}
                inputProps={{ maxLength: MAX_RATE_DIGITS + 3 }}
                sx={{ width: "100%" }}
              />
            </MUITableCell>

            {/* GST (%) reduced width inside the select */}
            <MUITableCell align="center" sx={cellSx}>
              <Select
                variant="outlined"
                size="small"
                value={item.gstPercentage}
                onChange={(e) =>
                  handleGstPercentageChange(index, e.target.value)
                }
                sx={{ width: "80%", p: 0 }}
                MenuProps={{
                  PaperProps: { style: { maxHeight: 200 } },
                }}
              >
                {[...Array(100)]?.map((_, i) => (
                  <MenuItem key={i + 1} value={i + 1}>
                    {i + 1}
                  </MenuItem>
                ))}
              </Select>
            </MUITableCell>

            <MUITableCell align="center" sx={cellSx}>
              {item.cgst}%
            </MUITableCell>

            <MUITableCell align="center" sx={cellSx}>
              {item.sgst}%
            </MUITableCell>

            <MUITableCell align="center" sx={cellSx}>
              {amount}
            </MUITableCell>

            <MUITableCell align="center" sx={cellSx}>
              {item.gstAmount}
            </MUITableCell>

            <MUITableCell align="center" sx={cellSx}>
              {lineItems?.length > 1 && (
                <IconButton
                  size="small"
                  color="error"
                  onClick={() => removeLineItem(index)}
                >
                  <Icon icon="tabler:trash" />
                </IconButton>
              )}
              <IconButton size="small" onClick={addLineItem}>
                <Icon icon="tabler:plus" />
              </IconButton>
            </MUITableCell>
          </TableRow>
        );
      }
    });
  };

  <Table sx={{ borderCollapse: "collapse", width: "100%" }} size="small">
    <TableHead>
      <TableRow>{getTableHeaders()}</TableRow>
    </TableHead>
    <TableBody>{getTableRows()}</TableBody>
  </Table>;

  async function onSubmit(data) {
    // Prepare the billingBreakdown payload
    const billingBreakdown = {
      totalAmountInclusiveOfGST: parseFloat(totalAfterGST),
      items: lineItems?.map((item) => {
        const quantity = parseFloat(item.quantity) || 0;
        const rate = parseFloat(item.rate) || 0;
        const baseAmount = quantity * rate;
        const gstPercentage = parseFloat(item.gstPercentage) || 0;

        let gstObj = {};

        if (gstType === "CGST_SGST") {
          // For CGST_SGST scenario, cgst and sgst should be half of gstPercentage each
          const cgstVal = parseFloat(item.cgst) || gstPercentage / 2;
          const sgstVal = parseFloat(item.sgst) || gstPercentage / 2;

          gstObj = {
            gstType: "CGST_SGST",
            gstPercentage: gstPercentage,
            cgst: cgstVal,
            sgst: sgstVal,
            igst: null,
            cgstAmount: (baseAmount * (cgstVal / 100)).toFixed(2),
            sgstAmount: (baseAmount * (sgstVal / 100)).toFixed(2),
            igstAmount: null,
          };
        } else {
          // IGST scenario
          const igstVal = parseFloat(item.igst) || gstPercentage;
          gstObj = {
            gstType: "IGST",
            gstPercentage: gstPercentage,
            cgst: null,
            sgst: null,
            igst: igstVal,
            cgstAmount: null,
            sgstAmount: null,
            igstAmount: (baseAmount * (igstVal / 100)).toFixed(2),
          };
        }

        return {
          id: crypto.randomUUID(), // Use a unique ID or item.id if available
          description: item.description,
          quantity: quantity,
          rate: rate,
          amount: baseAmount.toFixed(2),
          gst: gstObj,
        };
      }),
    };

    if (billingBreakdown.totalAmountInclusiveOfGST === 0) {
      setToastMessage("Atleast One Billing Breakdown is mandatory!");
      setShowToast(true);
      return;
    }

    // This is the final payload you can send to your backend
    const payload = {
      senderDetails: {
        type: senderType,
        orgId: orgName,
        address: data?.senderAddress,
        gstIn: senderGstin,
        panNo: data?.senderPanNumber,
        stateName: data?.senderState,
      },
      receiverDetails: {
        type: recipientType,
        orgId: recipientOrgName,
        address: data?.receiverAddress,
        gstIn: recipientGstin,
        panNo: data?.receiverPanNumber,
        stateName: data?.receiverState,
      },
      mediatorDetails: {
        summary: data?.summary,
      },
      billingBreakdown: billingBreakdown,
      metaDataInvoice: {
        subject: data?.subject,
        invoiceDate: data?.invoiceDate,
        dueDate: data?.dueDate,
      },
      status: status,
      // ...Include any other invoice fields or data from `data` as needed
    };

    await postInvoice(
      payload,
      () => {
        const message = `Invoice Created Successfully`;
        setDialogMessage(message);
        setDialogSuccess(true);
      },
      () => {
        const message = `Failed to create Invoice`;
        setDialogMessage(message);
        setDialogSuccess(true);
      }
    );

    fetchInvoices(page, pageSize, searchFilters,status);
    handleDialogClose();
  }

  const handleOpenDialog = () => {
    const message = `Are you sure you want to download a PDF?`;
    setDialogMessage(message);
    setGeneratingPDF(true);
  };

  const handleClosePDFDialog = () => {
    setGeneratingPDF(false); // Close the dialog
  };

  const handlePDFGeneration = async () => {
    const url = getUrl(authConfig.invoicesEndpoint) + "/generate/" + data?.id;

    const headers = getFileUploadPDFHeaders();

    try {
      const response = await axios({
        method: "post",
        url: url,
        headers: headers,
        responseType: "arraybuffer",
      });

      if (response.status === 200) {
        const pdfBlob = new Blob([response.data], { type: "application/pdf" });
        saveAs(pdfBlob, "Invoice.pdf");
      } else {
        console.error("Document object is not available.");
      }
    } catch (error) {
      console.error("Error fetching users:", error);
    }

    handleClosePDFDialog();
    handleDialogClose();
  };

  async function onUpdate(formData) {
    // Prepare the billingBreakdown payload
    const billingBreakdown = {
      totalAmountInclusiveOfGST: parseFloat(totalAfterGST),
      items: lineItems?.map((item) => {
        const quantity = parseFloat(item.quantity) || 0;
        const rate = parseFloat(item.rate) || 0;
        const baseAmount = quantity * rate;
        const gstPercentage = parseFloat(item.gstPercentage) || 0;

        let gstObj = {};

        if (gstType === "CGST_SGST") {
          // For CGST_SGST scenario, cgst and sgst should be half of gstPercentage each
          const cgstVal = parseFloat(item.cgst) || gstPercentage / 2;
          const sgstVal = parseFloat(item.sgst) || gstPercentage / 2;

          gstObj = {
            gstType: "CGST_SGST",
            gstPercentage: gstPercentage,
            cgst: cgstVal,
            sgst: sgstVal,
            igst: null,
            cgstAmount: (baseAmount * (cgstVal / 100)).toFixed(2),
            sgstAmount: (baseAmount * (sgstVal / 100)).toFixed(2),
            igstAmount: null,
          };
        } else {
          // IGST scenario
          const igstVal = parseFloat(item.igst) || gstPercentage;
          gstObj = {
            gstType: "IGST",
            gstPercentage: gstPercentage,
            cgst: null,
            sgst: null,
            igst: igstVal,
            cgstAmount: null,
            sgstAmount: null,
            igstAmount: (baseAmount * (igstVal / 100)).toFixed(2),
          };
        }

        return {
          id: crypto.randomUUID(), // Use a unique ID or item.id if available
          description: item.description,
          quantity: quantity,
          rate: rate,
          amount: baseAmount.toFixed(2),
          gst: gstObj,
        };
      }),
    };

    // This is the final payload you can send to your backend
    const payload = {
      senderDetails: {
        type: senderType,
        orgId: orgName,
        address: formData?.senderAddress,
        gstIn: senderGstin,
        panNo: formData?.senderPanNumber,
        stateName: formData?.senderState,
      },
      receiverDetails: {
        type: recipientType,
        orgId: recipientOrgName,
        address: formData?.receiverAddress,
        gstIn: recipientGstin,
        panNo: formData?.receiverPanNumber,
        stateName: formData?.receiverState,
      },
      mediatorDetails: {
        summary: formData?.summary,
      },
      billingBreakdown: billingBreakdown,
      metaDataInvoice: {
        subject: formData?.subject,
        invoiceDate: formData?.invoiceDate,
        dueDate: formData?.dueDate,
      },
      status: status,
    };

    await putInvoice(
      data?.id,
      payload,
      () => {
        const message = `Invoice Updated Successfully`;
        setDialogMessage(message);
        setDialogSuccess(true);
      },
      () => {
        const message = `Failed to update Invoice`;
        setDialogMessage(message);
        setDialogSuccess(true);
      }
    );

    fetchInvoices(page, pageSize, searchFilters,status);
    handleDialogClose();
  }
  const handleValidationErrors = (errors) => {
    const firstErrorKey = Object.keys(errors)[0];
    if (firstErrorKey) {
      const errorMessage =
        errors[firstErrorKey]?.message || "Validation error occurred!";
      setToastMessage(errorMessage);
      setShowToast(true);
    }
  };
  return (
    <>
      <Dialog open={open} onClose={handleDialogClose} fullScreen maxWidth="sm">
        <DialogTitle
          sx={{
            position: "relative",
            borderBottom: (theme) => `1px solid ${theme.palette.divider}`,
            p: (theme) => `${theme.spacing(1.75, 4)} !important`,
            display: "flex",
            alignItems: "center",
            justifyContent: { xs: "start" },
            fontSize: { xs: 19, md: 20 },
            height: "50px",
            marginLeft: { xl: 4, lg: 4, md: 4, sm: 4, xs: 3.8 },
          }}
          textAlign={"center"}
        >
          {!data || Object.keys(data)?.length === 0
            ? "Create Invoice"
            : "Edit Invoice"}
          <Box
            sx={{
              position: "absolute",
              top: "9px",
              right: "10px",
              marginRight: { xl: 8, lg: 4, md: 4, sm: 4, xs: 4 },
            }}
          >
            <IconButton
              size="small"
              onClick={handleDialogClose}
              sx={{
                borderRadius: 1,
                color: "common.white",
                backgroundColor: "primary.main",
                "&:hover": {
                  backgroundColor: "#66BB6A",
                  transition: "background 0.5s ease, transform 0.5s ease",
                },
              }}
            >
              <Icon icon="tabler:x" fontSize="1rem" />
            </IconButton>
          </Box>
        </DialogTitle>
        <DialogContent sx={{ overflowX: "hidden", ml: 2 }}>
          <>
            <Grid
              sx={{
                backgroundColor: "#f2f7f2",
                paddingTop: 0,
                height: "36px",
                display: "flex",
                alignItems: "center",
              }}
            >
              <Typography
                variant="body1"
                fontWeight={"bold"}
                sx={{ mt: 0, ml: 2 }}
              >
                Sender Details:
              </Typography>
            </Grid>

            <Grid container spacing={3} sx={{ mt: 0, mb: 2 }}>
              {/* Sender Type */}
              <Grid item xs={12} sm={3}>
                <FormControl fullWidth error={Boolean(errors.senderType)}>
                  <Controller
                    name="senderType"
                    control={control}
                    rules={{
                      required:
                        !data || Object.keys(data).length === 0
                          ? "Sender Type is required"
                          : false,
                    }}
                    render={({ field }) => (
                      <>
                        <SelectAutoComplete
                          id="senderType"
                          label="Sender Type"
                          nameArray={senderRecipientTypes
                            .filter((type) => type.category !== "SOCIETY")
                            .map((type) => ({
                              key: type.category === "EMPLOYEE"
                                ? "Houzer"
                                : type.category
                                    .replace(/_/g, " ") // Replace underscores with spaces
                                    .toLowerCase() // Convert to lowercase
                                    .replace(/\b\w/g, (char) => char.toUpperCase()), // Capitalize first letter
                              value: type.category,
                            }))}
                          
                          value={senderType}
                          onChange={(e) => {
                            field.onChange(e.target.value);
                            setSenderType(e.target.value); // Update dependent state
                          }}
                        />
                        {errors.senderType && (
                          <FormHelperText sx={{ color: "error.main" }}>
                            {errors.senderType.message}
                          </FormHelperText>
                        )}
                      </>
                    )}
                  />
                </FormControl>
              </Grid>

              {/* sender Orginization Name */}

              <Grid item xs={12} sm={3}>
                <FormControl
                  fullWidth
                  error={Boolean(errors.senderOrganizationName)}
                >
                  <Controller
                    name="senderOrganizationName"
                    control={control}
                    defaultValue=""
                    rules={{
                      required:
                        !data || Object.keys(data).length === 0
                          ? "Sender Organisation Name is required"
                          : false,
                    }}
                    render={({ field }) => (
                      <>
                        <SelectAutoComplete
                          id="senderOrganizationName"
                          label="Sender Organization Name"
                          nameArray={senderOrganizationName?.map((type) => ({
                            key: type.name,
                            value: type.id,
                          }))}
                          value={orgName}
                          onChange={(event) => {
                            field.onChange(event.target.value);
                            handleSenderOrgChange(event);
                          }}
                        />
                        {errors.senderOrganizationName && (
                          <FormHelperText sx={{ color: "error.main" }}>
                            {errors.senderOrganizationName.message}
                          </FormHelperText>
                        )}
                      </>
                    )}
                  />
                </FormControl>
              </Grid>
            </Grid>
            <Grid container spacing={3} sx={{ mt: 0, mb: 2 }}>
              {/* Conditionally render the sender fields */}
              {showSenderDetails && (
                <>
                  <Grid item xs={12} sm={3}>
                    <FormControl fullWidth>
                      <Controller
                        name="senderPanNumber"
                        control={control}
                        rules={{
                          required: "PAN Number is required",
                          pattern: {
                            value: /^[A-Z]{5}[0-9]{4}[A-Z]{1}$/,
                            message: "Invalid PAN Number format",
                          },
                          maxLength: {
                            value: 10,
                            message: "PAN Number cannot exceed 10 characters",
                          },
                        }}
                        render={({ field }) => (
                          <TextField
                            {...field}
                            size="small"
                            label="PAN Number"
                            placeholder="Enter PAN Number"
                            error={Boolean(errors.senderPanNumber)}
                            InputLabelProps={{ shrink: true }}
                            helperText={errors.senderPanNumber?.message}
                            inputProps={{
                              maxLength: 10,
                              style: { textTransform: "uppercase" },
                              onInput: (e) => {
                                e.target.value = e.target.value.toUpperCase();
                              },
                            }}
                            onChange={(e) => {
                              field.onChange(e);
                              if (e.target.value.length > 10) {
                                setError("panNumber", {
                                  message:
                                    "PAN Number cannot exceed 10 characters",
                                });
                              } else {
                                clearErrors("panNumber");
                              }
                            }}
                          />
                        )}
                      />
                    </FormControl>
                  </Grid>

                  <Grid item xs={12} sm={3}>
                    <FormControl fullWidth>
                      <Controller
                        name="senderState"
                        control={control}
                        rules={{ required: "State is required" }}
                        render={({ field }) => (
                          <TextField
                            {...field}
                            size="small"
                            label="State"
                            placeholder="Enter State"
                            error={Boolean(errors.senderState)}
                            InputLabelProps={{ shrink: true }}
                            helperText={errors.senderState?.message}
                          />
                        )}
                      />
                    </FormControl>
                  </Grid>

                  <Grid item xs={12} sm={3}>
                    <FormControl fullWidth error={Boolean(errors.senderGstin)}>
                      <Controller
                        name="senderGstin"
                        control={control}
                        rules={{
                          validate: {
                            length: validateGSTLength, // Check length
                            format: validateGST, // Check GST format
                        }
                        }}
                        render={({ field }) => (
                          <TextField
                            {...field}
                            size="small"
                            label="GSTIN/UIN"
                            placeholder="Enter GSTIN/UIN"
                            value={senderGstin}
                            error={Boolean(errors.senderGstin)}
                            helperText={errors.senderGstin?.message}
                            InputLabelProps={{ shrink: true }}
                            inputProps={{
                              style: { textTransform: "uppercase" },
                              onInput: (e) => {
                                e.target.value = e.target.value.toUpperCase();
                              },
                            }}
                            onChange={(e) => {
                              field.onChange(e.target.value); // Update form state
                              setSenderGstin(e.target.value); // Update local state
                            }}
                          />
                        )}
                      />
                    </FormControl>
                  </Grid>
                  {/* 
                  <Grid item xs={12} sm={3}>
                    <FormControl fullWidth>
                      <Grid container alignItems="center" wrap="nowrap">
                        <Grid item>
                          <Typography
                            fontSize="body1"
                            sx={{
                              fontWeight: "bold",
                              mr: 1,
                              ml: 1,
                              alignContent: "center",
                            }}
                          >
                            Code(GST):
                          </Typography>
                        </Grid>
                        <Grid
                          item
                          xs
                          container
                          alignItems="center"
                          justifyContent="flex-start"
                          sx={{ overflow: "hidden" }}
                        >
                          <Typography
                            variant="body1"
                            sx={{
                              textOverflow: "ellipsis",
                              overflow: "hidden",
                              whiteSpace: "nowrap",
                            }}
                          ></Typography>
                        </Grid>
                      </Grid>
                    </FormControl>
                  </Grid> */}

                  <Grid item xs={12} sx={{ mt: 2 }}>
                    <FormControl fullWidth>
                      <Controller
                        name="senderAddress"
                        control={control}
                        rules={{ required: "Sender Address is required" }}
                        render={({ field }) => (
                          <TextField
                            rows={2}
                            multiline
                            {...field}
                            label="Address"
                            placeholder="Enter Address"
                            InputLabelProps={{ shrink: true }}
                            error={Boolean(errors.senderAddress)}
                            aria-describedby="senderAddress"
                          />
                        )}
                      />
                      {errors.senderAddress && (
                        <FormHelperText
                          sx={{ color: "error.main" }}
                          id="senderAddress"
                        >
                          Sender Address is required
                        </FormHelperText>
                      )}
                    </FormControl>
                  </Grid>
                </>
              )}
            </Grid>
          </>

          <>
            <Grid
              sx={{
                backgroundColor: "#f2f7f2",
                paddingTop: 0,
                height: "36px",
                display: "flex",
                alignItems: "center",
              }}
            >
              <Typography
                variant="body1"
                fontWeight={"bold"}
                sx={{ mt: 0, ml: 2 }}
              >
                Receiver Details:
              </Typography>
            </Grid>
            <Grid container spacing={3} sx={{ mt: 0, mb: 2 }}>
              {/* Recipient Type */}
              <Grid item xs={12} sm={3}>
                <FormControl fullWidth error={Boolean(errors.recipientType)}>
                  <Controller
                    name="recipientType"
                    control={control}
                    defaultValue=""
                    rules={{
                      required:
                        !data || Object.keys(data).length === 0
                          ? "Receiver Type is required"
                          : false,
                    }}
                    render={({ field }) => (
                      <>
                        <SelectAutoComplete
                          id="recipientType"
                          label="Receiver Type"
                          nameArray={senderRecipientTypes?.map((type) => ({
                            key: type.category === "EMPLOYEE"
                            ? "Houzer"
                            : type.category
                              .replace(/_/g, " ") // Replace underscores with spaces
                              .toLowerCase() // Convert to lowercase to standardize
                              .replace(/\b\w/g, (char) => char.toUpperCase()),
                            value: type.category,
                          }))}
                          value={recipientType}
                          onChange={(e) => {
                            field.onChange(e.target.value);
                            setRecipientType(e.target.value); // Update dependent state
                          }}
                        />
                        {errors.recipientType && (
                          <FormHelperText sx={{ color: "error.main" }}>
                            {errors.recipientType.message}
                          </FormHelperText>
                        )}
                      </>
                    )}
                  />
                </FormControl>
              </Grid>

              {/* receiver organization name */}
              <Grid item xs={12} sm={3}>
                <FormControl fullWidth error={Boolean(errors.senderType)}>
                  <Controller
                    name="recipientOrganizationName"
                    control={control}
                    defaultValue=""
                    rules={{
                      required:
                        !data || Object.keys(data).length === 0
                          ? "Receiver organisation Name is required"
                          : false,
                    }}
                    render={({ field }) => (
                      <>
                        <SelectAutoComplete
                          id="recipientOrganizationName"
                          label="Receiver Organization Name"
                          nameArray={recipientOrganizationName?.map((type) => ({
                            key: type.name,
                            value: type.id,
                          }))}
                          value={recipientOrgName}
                          onChange={(event) => {
                            field.onChange(event.target.value);
                            handleReceiverOrgChange(event);
                          }}
                        />
                        {errors.recipientOrganizationName && (
                          <FormHelperText sx={{ color: "error.main" }}>
                            {errors.recipientOrganizationName.message}
                          </FormHelperText>
                        )}
                      </>
                    )}
                  />
                </FormControl>
              </Grid>
            </Grid>

            <Grid container spacing={3} sx={{ mt: 0, mb: 2 }}>
              {/* Conditionally render the sender fields */}
              {showReceiverDetails && (
                <>
                  <Grid item xs={12} sm={3}>
                    <FormControl fullWidth>
                      <Controller
                        name="receiverPanNumber"
                        control={control}
                        rules={{
                          required: "PAN Number is required",
                          pattern: {
                            value: /^[A-Z]{5}[0-9]{4}[A-Z]{1}$/,
                            message: "Invalid PAN Number format",
                          },
                          maxLength: {
                            value: 10,
                            message: "PAN Number cannot exceed 10 characters",
                          },
                        }}
                        render={({ field }) => (
                          <TextField
                            {...field}
                            size="small"
                            label="PAN Number"
                            placeholder="Enter PAN Number"
                            error={Boolean(errors.receiverPanNumber)}
                            InputLabelProps={{ shrink: true }}
                            helperText={errors.receiverPanNumber?.message}
                            inputProps={{
                              maxLength: 10,
                              style: { textTransform: "uppercase" },
                              onInput: (e) => {
                                e.target.value = e.target.value.toUpperCase();
                              },
                            }}
                            onChange={(e) => {
                              field.onChange(e);
                              if (e.target.value.length > 10) {
                                setError("panNumber", {
                                  message:
                                    "PAN Number cannot exceed 10 characters",
                                });
                              } else {
                                clearErrors("panNumber");
                              }
                            }}
                          />
                        )}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item xs={12} sm={3}>
                    <FormControl fullWidth>
                      <Controller
                        name="receiverState"
                        control={control}
                        rules={{ required: "State is required" }}
                        render={({ field }) => (
                          <TextField
                            {...field}
                            size="small"
                            label="State"
                            placeholder="Enter State"
                            error={Boolean(errors.receiverState)}
                            InputLabelProps={{ shrink: true }}
                            helperText={errors.receiverState?.message}
                          />
                        )}
                      />
                    </FormControl>
                  </Grid>

                  <Grid item xs={12} sm={3}>
                    <FormControl
                      fullWidth
                      error={Boolean(errors.recipientGSTIN)}
                    >
                      <Controller
                        name="recipientGSTIN"
                        control={control}
                        render={({ field }) => (
                          <TextField
                            {...field}
                            size="small"
                            label="GSTIN/UIN"
                            value={recipientGstin}
                            placeholder="Enter GSTIN/UIN"
                            error={Boolean(errors.recipientGSTIN)}
                            InputLabelProps={{ shrink: true }}
                            helperText={errors.recipientGSTIN?.message}
                            onChange={(e) => {
                              field.onChange(e.target.value);
                              setRecipientGstin(e.target.value);
                            }}
                          />
                        )}
                      />
                    </FormControl>
                  </Grid>

                  {/* <Grid item xs={12} sm={3}>
                    <FormControl fullWidth>
                      <Grid container alignItems="center" wrap="nowrap">
                        <Grid item>
                          <Typography
                            fontSize="body1"
                            sx={{
                              fontWeight: "bold",
                              mr: 1,
                              ml: 1,
                              alignContent: "center",
                            }}
                          >
                            Code(GST):
                          </Typography>
                        </Grid>
                        <Grid
                          item
                          xs
                          container
                          alignItems="center"
                          justifyContent="flex-start"
                          sx={{ overflow: "hidden" }}
                        >
                          <Typography
                            variant="body1"
                            sx={{
                              textOverflow: "ellipsis",
                              overflow: "hidden",
                              whiteSpace: "nowrap",
                            }}
                          ></Typography>
                        </Grid>
                      </Grid>
                    </FormControl>
                  </Grid> */}

                  <Grid item xs={12} sx={{ mt: 2 }}>
                    <FormControl fullWidth>
                      <Controller
                        name="receiverAddress"
                        control={control}
                        rules={{ required: "receiver Address is Required" }}
                        render={({ field }) => (
                          <TextField
                            rows={2}
                            multiline
                            {...field}
                            label="Address"
                            placeholder="Enter Address"
                            InputLabelProps={{ shrink: true }}
                            error={Boolean(errors.receiverAddress)}
                            aria-describedby="receiverAddress"
                          />
                        )}
                      />
                      {errors.receiverAddress && (
                        <FormHelperText
                          sx={{ color: "error.main" }}
                          id="receiverAddress"
                        >
                          Receiver Address is required
                        </FormHelperText>
                      )}
                    </FormControl>
                  </Grid>
                </>
              )}
            </Grid>
          </>

          <Grid
            sx={{
              backgroundColor: "#f2f7f2",
              paddingTop: 0,
              height: "36px",
              display: "flex",
              alignItems: "center",
              mt: 3,
            }}
          >
            <Typography
              variant="body1"
              fontWeight={"bold"}
              sx={{ mt: 0, ml: 2 }}
            >
              Invoice Details:
            </Typography>
          </Grid>

          <Grid container spacing={2}>
            <Grid item xs={12} sx={{ mt: 3, mb: 3 }}>
              <FormControl fullWidth>
                <Controller
                  name="subject"
                  control={control}
                  rules={{ required: "Subject is required" }}
                  render={({ field }) => (
                    <TextField
                      rows={2}
                      multiline
                      {...field}
                      label="Subject"
                      placeholder="Enter Subject"
                      error={Boolean(errors.subject)}
                      aria-describedby="subject"
                    />
                  )}
                />
                {errors.subject && (
                  <FormHelperText sx={{ color: "error.main" }} id="subject">
                    This field is required
                  </FormHelperText>
                )}
              </FormControl>
            </Grid>

            {data && Object.keys(data)?.length > 0 && (
              <Grid item xs={12} sm={3}>
                <Typography>
                  <>
                    <span variant="body1" color="text.secondary">
                      Invoice No:
                    </span>
                    <span variant="body2">
                      <b> {invoiceCode}</b>
                    </span>
                  </>
                </Typography>
              </Grid>
            )}

            {/* Invoice Date */}
            <Grid item xs={12} sm={3}>
              <FormControl fullWidth>
                <Controller
                  name="invoiceDate"
                  control={control}
                  rules={{ required: "Invoice Date is required" }}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      label="Invoice Date"
                      type="date"
                      InputLabelProps={{ shrink: true }}
                      error={Boolean(errors.invoiceDate)}
                      aria-describedby="invoiceDate"
                      size="small"
                    />
                  )}
                />
                {errors.invoiceDate && (
                  <FormHelperText sx={{ color: "error.main" }}>
                    {errors.invoiceDate.message}
                  </FormHelperText>
                )}
              </FormControl>
            </Grid>

            {/* Due Date */}
            <Grid item xs={12} sm={3}>
              <FormControl fullWidth>
                <Controller
                  name="dueDate"
                  control={control}
                  rules={{ required: "Due Date is required" }}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      label="Due Date"
                      type="date"
                      InputLabelProps={{ shrink: true }}
                      error={Boolean(errors.dueDate)}
                      aria-describedby="dueDate"
                      size="small"
                    />
                  )}
                />
                {errors.dueDate && (
                  <FormHelperText sx={{ color: "error.main" }}>
                    {errors.dueDate.message}
                  </FormHelperText>
                )}
              </FormControl>
            </Grid>

            {/* status */}
            <Grid item xs={12} sm={3}>
              <FormControl fullWidth error={Boolean(errors.status)}>
                <Controller
                  name="status"
                  control={control}
                  rules={{
                    required:
                      !data || Object.keys(data).length === 0
                        ? "Status is required"
                        : false,
                  }}
                  render={({ field }) => (
                    <>
                      <SelectAutoComplete
                        id="status"
                        label="Status"
                        nameArray={statusList}
                        value={status}
                        onChange={(e) => {
                          field.onChange(e.target.value);
                          setStatus(e.target.value);
                        }}
                      />
                      {errors.status && (
                        <FormHelperText sx={{ color: "error.main" }}>
                          {errors.status.message}
                        </FormHelperText>
                      )}
                    </>
                  )}
                />
              </FormControl>
            </Grid>
          </Grid>

          <Box>
            <Grid
              sx={{
                backgroundColor: "#f2f7f2",
                paddingTop: 0,
                height: "36px",
                display: "flex",
                alignItems: "center",
                mt: 2,
                mb: 2,
              }}
            >
              <Typography
                variant="body1"
                fontWeight={"bold"}
                sx={{ mt: 0, ml: 2 }}
              >
                Billing Breakdown:
              </Typography>
            </Grid>

            <TableContainer sx={{ padding: "4px 6px" }}>
              <Table>
                <TableRow>{getTableHeaders()}</TableRow>
                {getTableRows()}
              </Table>
            </TableContainer>

            <Grid container sx={{ mt: 2, mb: 2, justifyContent: "right" }}>
              {/* Display totals */}
              <Grid item xs="auto" sx={{ textAlign: "right", mr: 2 }}>
                <Typography variant="body2">Amount:</Typography>
                <Typography variant="body1" fontWeight="bold">
                  ₹ {totalBeforeGST || "0.00"}
                </Typography>
              </Grid>
              <Grid item xs="auto" sx={{ textAlign: "right", mr: 2 }}>
                <Typography variant="body2">GST Amount:</Typography>
                <Typography variant="body1" fontWeight="bold">
                  ₹ {totalGST || "0.00"}
                </Typography>
              </Grid>
              <Grid item xs="auto" sx={{ textAlign: "right" }}>
                <Typography variant="body2">Total Amount:</Typography>
                <Typography variant="body1" fontWeight="bold">
                  ₹ {totalAfterGST || "0.00"}
                </Typography>
              </Grid>
            </Grid>
          </Box>

          <Grid
            sx={{
              backgroundColor: "#f2f7f2",
              paddingTop: 0,
              height: "36px",
              display: "flex",
              alignItems: "center",
              mt: 2,
              mb: 2,
            }}
          >
            <Typography
              variant="body1"
              fontWeight={"bold"}
              sx={{ mt: 0, ml: 2 }}
            >
              Summary:
            </Typography>
          </Grid>
          <Grid container spacing={2}>
            <Grid item xs={12} sx={{ mt: 2 }}>
              <FormControl fullWidth>
                <Controller
                  name="summary"
                  control={control}
                  rules={{ required: "Summary is required" }}
                  render={({ field }) => (
                    <TextField
                      rows={3}
                      multiline
                      {...field}
                      label="Summary (Please Enter Bank Details)"
                      placeholder="Enter Summary (Bank Details are Required)"
                      error={Boolean(errors.summary)}
                      aria-describedby="summary"
                    />
                  )}
                />
                {errors.summary && (
                  <FormHelperText sx={{ color: "error.main" }} id="summary">
                    Summary is required
                  </FormHelperText>
                )}
              </FormControl>
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions
          sx={{
            justifyContent: "end",
            borderTop: (theme) => `1px solid ${theme.palette.divider}`,
            p: (theme) => `${theme.spacing(2.5)} !important`,
            marginRight: { xl: 8, lg: 4, md: 4, sm: 4, xs: 4 },
          }}
        >
          <Button onClick={handleDialogClose} variant="outlined">
            Cancel
          </Button>
          {!data || Object.keys(data)?.length === 0 ? (
            <Button
              variant="contained"
              color="primary"
              onClick={handleSubmit(onSubmit, handleValidationErrors)}
            >
              Save
            </Button>
          ) : (
            <Button
              variant="contained"
              color="primary"
              onClick={handleSubmit(onUpdate)}
            >
              Update
            </Button>
          )}

          {!data || Object.keys(data)?.length === 0 ? (
            ""
          ) : (
            <Button
              variant="contained"
              color="primary"
              onClick={handleOpenDialog}
            >
              Generate PDF
            </Button>
          )}
        </DialogActions>
      </Dialog>
      <Snackbar
        open={showToast}
        autoHideDuration={2000} // Toast will be visible for 2 seconds
        onClose={handleToastClose}
        anchorOrigin={{ vertical: "top", horizontal: "right" }} // Position of the toast
      >
        <Alert
          onClose={handleToastClose}
          severity="error"
          sx={{
            color: "black",
            padding: "4px 8px", // Reduce padding to make it smaller
            fontSize: "0.875rem", // Adjust font size for a more compact look
            borderRadius: "2px", // Optional: you can adjust the border radius
            border: "0.5px solid #ccc", // Optional: set a border or remove it completely
          }}
        >
          {toastMessage}
        </Alert>
      </Snackbar>
      <Dialog
        open={dialogSuccess}
        onClose={handleClose}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
        PaperProps={{
          sx: {
            p: (theme) => `${theme.spacing(2.5)} !important`,
            backgroundColor: (theme) => theme.palette.primary.background,
          },
        }}
      >
        <Box
          sx={{
            width: "100%",
            borderRadius: 1,
            textAlign: "center",
            border: (theme) => `1px solid ${theme.palette.divider}`,
            borderColor: "primary.main",
          }}
        >
          <DialogContent>
            <DialogContentText
              id="alert-dialog-description"
              color="primary.main"
            >
              <div dangerouslySetInnerHTML={{ __html: dialogMessage }} />
            </DialogContentText>
          </DialogContent>
          <DialogActions>
            <Button
              variant="contained"
              onClick={handleClose}
              sx={{ margin: "auto", width: 100 }}
            >
              Ok
            </Button>
          </DialogActions>
        </Box>
      </Dialog>
      <Dialog
        open={generatingPDF}
        onClose={handleClosePDFDialog}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
        PaperProps={{
          sx: {
            p: (theme) => `${theme.spacing(2.5)} !important`,
            backgroundColor: (theme) => theme.palette.primary.background,
          },
        }}
      >
        <Box
          sx={{
            width: "100%",
            borderRadius: 1,
            textAlign: "center",
            border: (theme) => `1px solid ${theme.palette.divider}`,
            borderColor: "primary.main",
          }}
        >
          <DialogContent>
            <DialogContentText
              id="alert-dialog-description"
              color="primary.main"
            >
              <div dangerouslySetInnerHTML={{ __html: dialogMessage }} />
            </DialogContentText>
          </DialogContent>
          <DialogActions>
            <Button
              variant="contained"
              onClick={handlePDFGeneration}
              sx={{ margin: "auto", width: 100 }}
            >
              Yes
            </Button>
            <Button
              variant="outlined"
              onClick={handleClosePDFDialog}
              sx={{ margin: "auto", width: 100 }}
            >
              No
            </Button>
          </DialogActions>
        </Box>
      </Dialog>
    </>
  );
};

export default InvoiceDialog;
