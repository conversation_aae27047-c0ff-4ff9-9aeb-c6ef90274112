import { useTheme } from "@emotion/react";
import {
  FormControl,
  Grid,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableRow,
  Typography,
} from "@mui/material";
import { useContext, useEffect } from "react";
import { Controller, useForm } from "react-hook-form";
import SelectAutoComplete from "src/@core/components/custom-components/SelectAutoComplete";
import SelectClearAutoComplete from "src/@core/components/custom-components/SelectClearAutoComplete";
import { AuthContext } from "src/context/AuthContext";
import MUITableCell from "src/pages/SP/MUITableCell";


const tablecellLabelStyle = {
  width: { xs: "50%", sm: "50%", md: "40%" }, // Responsive width adjustment
  padding: "4px", // Ensure padding for better alignment
  textAlign: "right", // Align the text to the left
  verticalAlign: "middle", // Vertically align the text to the middle
  textIndent: {
    lg: "80px",
    md: "80px",
    sm: "100px",
    xs: "0",
  },
};
const tablecellValueStyle = {
  justifyContent: "flex-start",
  alignItems: "center",
  width: { xs: "50%", sm: "50%", md: "60%" }, // Responsive width for small and medium screens
  height: "10px",
};

const field = {
  fontSize: "12.75px",
};

const fieldValueStyle = {
  fontWeight: "bold",
  wordWrap: "break-word",
  whiteSpace: "pre-wrap",
  // color: "#66BB6A", // Sets color for the TableCell
  color: "#108A00",
  fontSize: "14px",
  lineHeight: "1.2",
};

const NewSectionView = ({
  data,
}) => {
  const theme = useTheme();

  const { listValues } = useContext(AuthContext);

  const {
    control,
    formState: { errors },
  } = useForm();



  return (
    <>
      <TableContainer sx={{ padding: "4px 6px" }}>
        <Table>
          <TableBody
            sx={{
              "& .MuiTableCell-root": {
                p: `${theme.spacing(1.35, 1.125)} !important`,
              },
            }}
          >
            <>
              <TableRow>
                <MUITableCell sx={tablecellLabelStyle}>
                  <Typography style={field}>Last Contacted Date:</Typography>
                </MUITableCell>
                <MUITableCell sx={tablecellValueStyle}>
                  <Typography className="data-field" style={fieldValueStyle}>
                    {data?.lastContactDate}
                  </Typography>
                </MUITableCell>
              </TableRow>
              <TableRow>
                <MUITableCell sx={tablecellLabelStyle}>
                  <Typography style={field}>Next FollowUp Date:</Typography>
                </MUITableCell>
                <MUITableCell sx={tablecellValueStyle}>
                  <Typography className="data-field" style={fieldValueStyle}>
                    {data?.nextFollowUpDate}
                  </Typography>
                </MUITableCell>
              </TableRow>
              <TableRow>
                <MUITableCell sx={tablecellLabelStyle}>
                  <Typography style={field}>Work Status:</Typography>
                </MUITableCell>
                <MUITableCell sx={tablecellValueStyle}>
                  <Typography className="data-field" style={fieldValueStyle}>
                    {listValues?.find(
                      (item) => item.id === data?.workStatus
                    )?.name || "-"}
                  </Typography>
                </MUITableCell>
              </TableRow>
              <TableRow>
                <MUITableCell sx={tablecellLabelStyle}>
                  <Typography style={field}>Work Order Status:</Typography>
                </MUITableCell>
                <MUITableCell sx={tablecellValueStyle}>
                  <Typography className="data-field" style={fieldValueStyle}>
                    {listValues?.find(
                      (item) => item.id === data?.workOrderStatus
                    )?.name || "-"}
                  </Typography>
                </MUITableCell>
              </TableRow>
            </>
          </TableBody>
        </Table>
      </TableContainer>
    </>
  );
};

export default NewSectionView;
