// ** MUI Imports
import Typography from "@mui/material/Typography";
import { Fragment, useContext, useEffect, useState } from "react";

// ** Custom Components Imports
import axios from "axios";
import AccordionBasic from "src/@core//components/custom-components/AccordionBasic";
import authConfig from "src/configs/auth";
import { getAuthorizationHeaders, getUrl } from "src/helpers/utils";
// ** Demo Components Imports
import { useTheme } from "@emotion/react";

import Icon from "src/@core/components/icon";

// ** Styled Component
import {
  Box,
  Button,
  Card,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  Divider,
  FormControl,
  FormHelperText,
  Grid,
  IconButton,
  Table,
  TableBody,
  TableContainer,
  TableRow,
} from "@mui/material";

import { Controller, useForm } from "react-hook-form";
import { AuthContext } from "src/context/AuthContext";
import MUITableCell from "src/pages/SP/MUITableCell";

import BOQWizard from "./BOQWizard";
import { useAuth } from "src/hooks/useAuth";
import SelectAutoComplete from "src/@core/components/custom-components/SelectAutoComplete";
import { fetchIpAddress } from "src/@core/components/custom-components/FetchIpAddress";

const field = {
  fontSize: "12.75px",
};

const fieldValueStyle = {
  fontWeight: "bold",
  wordWrap: "break-word",
  whiteSpace: "pre-wrap",
  // color: "#66BB6A", // Sets color for the TableCell
  color: "#108A00",
  fontSize: "14px",
};

const tablecellLabelStyle = {
  width: { xs: "50%", sm: "50%", md: "40%" }, // Responsive width adjustment
  padding: "4px", // Ensure padding for better alignment
  textAlign: "right", // Align the text to the left
  verticalAlign: "middle", // Vertically align the text to the middle
  textIndent: {
    lg: "80px",
    md: "80px",
    sm: "100px",
    xs: "0",
  },
};

const tablecellValueStyle = {
  justifyContent: "flex-start",
  alignItems: "center",
  width: { xs: "50%", sm: "50%", md: "60%" }, // Responsive width for small and medium screens
  height: "10px",
};

const SubmitBoqDialog = ({
  open,
  onClose,
  role,
  formData,
  fetchRequisitions,
  employeesData,
}) => {
  // ** Hook
  const theme = useTheme();

  const {
    control,
    formState: { errors },
  } = useForm();

  const [updatedData, setUpdatedData] = useState([]);

  const { user, listValues, listNames } = useContext(AuthContext);

  const serviceType = formData?.serviceTypeId
    ? listValues?.find((item) => item.id === formData?.serviceTypeId)?.name
    : null;

  const priorityName = formData?.priority
    ? listValues?.find((item) => item.id === formData?.priority)?.name
    : null;

  const [specifications, setSpecifications] = useState([]);

  const [userList, setUserList] = useState([]);

  const [templateData, setTemplateData] = useState([]);

  useEffect(() => {
    if (formData?.serviceTypeId) {
      const fetchAll = async (serviceId, data) => {
        const url = `${getUrl(
          authConfig.getAllServiceProfiles
        )}/${serviceId}/requisitionFields`;
        const headers = getAuthorizationHeaders();

        try {
          const response = await axios({
            method: "get",
            url: url,
            headers: headers,
            data: data,
          });

          if (response.data) {
            setUserList(response.data);
          } else {
            console.error("Unexpected API response format:", response);
          }
        } catch (error) {
          console.error("Error fetching users:", error);
        }
      };

      const serviceId = formData?.serviceTypeId;
      fetchAll(serviceId);
    }

    if (formData?.id && user.organisationCategory === "SERVICE_PROVIDER") {
      const fetchQuotation = async (id) => {
        const url = `${getUrl(
          authConfig.quotationsEndpoint
        )}/get-boq-by-orgId-srId/${user.orgId}/${id}`;
        const headers = getAuthorizationHeaders({
          accept: authConfig.SR_QUOTATIONS_GET_BOQ_SERVICE_PROVIDER_ORG_RES,
        });

        try {
          const response = await axios({
            method: "get",
            url: url,
            headers: headers,
          });

          if (response.data) {
            setTemplateData(response?.data?.metaData);
            setDateOfSubmission(response?.data?.dateOfSubmission);
          } else {
            console.error("Unexpected API response format:", response);
          }
        } catch (error) {
          console.error("Error fetching users:", error);
        }
      };

      fetchQuotation(formData?.id);
    }
    if (formData?.id && user.organisationCategory === "EMPLOYEE") {
      axios({
        method: "get",
        url:
          getUrl(authConfig.quotationsEndpoint) +
          "/" +
          formData?.id +
          "/quotation-type?srQuotationEnum=FINAL",
        headers: getAuthorizationHeaders(
          authConfig.SR_QUOTATIONS_GET_BROAD_CASTED_SP_RES_V1
        ),
      })
        .then((res) => {
          setSPOrganizationName(res.data);
        })
        .catch((err) => console.log("Categories error", err));
    }
  }, [formData]);

  useEffect(() => {
    if (formData?.specifications?.listNames?.length > 0) {
      // Create a mapping of listValueId to listValue
      const listValueMap = userList?.reduce((map, item) => {
        item?.values?.forEach((value) => {
          map[value.id] = { id: value.id, name: value.name };
        });
        return map;
      }, {});

      // Get all list names from specifications
      const listNames = userList?.map((item) => {
        const metadataItem =
          data?.requisitionData?.specifications?.listNames?.find(
            (list) => list.listNameId === item.id
          );
        const otherValue = metadataItem ? metadataItem.otherValue : null;

        return {
          id: item.id,
          name: item.name,
          otherValue: otherValue,
          values:
            metadataItem && metadataItem.listValues.length
              ? metadataItem.listValues.map(
                  (value) => listValueMap[value.listValueId]
                )
              : [],
        };
      });
      setSpecifications(listNames);
    }
  }, [formData, userList]);

  const [dialogMessage, setDialogMessage] = useState("");
  const [openDialogContent, setOpenDialogContent] = useState(false);

  const handleSuccess = () => {
    const message = `
    <div> 
      <h3>BOQ Submitted Successfully.</h3>
    </div>
  `;
    setDialogMessage(message);
    setOpenDialogContent(true);
  };

  const handleFailure = () => {
    const message = `
    <div> 
      <h3> Failed to Submit BOQ. Please try again later.</h3>
    </div>
  `;
    setDialogMessage(message);
    setOpenDialogContent(true);
  };

  const handleClose = () => {
    onClose();
    setUserList([]);
    setSpecifications([]);
    setOrgName("");
    setTemplateData([]);
  };

  const auth = useAuth();

  const [spOrganizationName, setSPOrganizationName] = useState([]);
  const [orgName, setOrgName] = useState("");

  const [dateOfSubmission, setDateOfSubmission] = useState();

  useEffect(() => {
    if (formData?.id && orgName && user.organisationCategory === "EMPLOYEE") {
      const fetchQuotation = async (id) => {
        const url = `${getUrl(
          authConfig.quotationsEndpoint
        )}/get-boq-by-orgId-srId/${orgName}/${id}`;
        const headers = getAuthorizationHeaders({
          accept: authConfig.SR_QUOTATIONS_GET_BOQ_SERVICE_PROVIDER_ORG_RES,
        });

        try {
          const response = await axios({
            method: "get",
            url: url,
            headers: headers,
          });

          if (response.data) {
            setTemplateData(response?.data?.metaData);
            setDateOfSubmission(response?.data?.dateOfSubmission);

          } else {
            console.error("Unexpected API response format:", response);
          }
        } catch (error) {
          console.error("Error fetching users:", error);
        }
      };

      fetchQuotation(formData?.id);
    }
  }, [orgName]);

  async function update() {
    let organisationId;

    if (user.organisationCategory === "SERVICE_PROVIDER") {
      organisationId = user?.orgId;
    } else {
      organisationId = orgName;
    }
    const ipAddress = await fetchIpAddress();

    const fields = {
      templateId: templateData?.templateId,
      serviceTypeId: templateData?.serviceTypeId,
      templateName: templateData?.templateName,
      description: templateData?.description,
      featureName: "BOQ",
      sections: updatedData,
      ipAddress: ipAddress,
    };

    try {
      const response = await auth.submitFinalBOQ(
        formData?.id,
        organisationId,
        fields,
        handleSuccess,
        handleFailure
      );
    } catch (error) {
      console.error("submission failed:", error);
      handleFailure();
    }

    handleClose();
  }

  const handleDialogClose = () => setOpenDialogContent(false);

  return (
    <>
      <Dialog open={open} onClose={handleClose} fullScreen>
        <DialogTitle
          sx={{
            position: "relative",
            borderBottom: (theme) => `1px solid ${theme.palette.divider}`,
            p: (theme) => `${theme.spacing(1.75, 4)} !important`,
            display: "flex",
            alignItems: "center",
            justifyContent: "start",
            fontSize: { xs: 19, md: 20 },
            marginLeft: { xl: 4, lg: 4, md: 4, sm: 4, xs: 1 },
          }}
          textAlign={"center"}
        >
          Submit Revised / Final (BOQ) quote
          <Box
            sx={{
              position: "absolute",
              top: "4px",
              right: "12px",
              marginRight: { xl: 5.5, lg: 5.5, md: 5.5, sm: 5.5, xs: 0 },
            }}
          >
            <IconButton
              size="small"
              onClick={handleClose}
              sx={{
                borderRadius: 1,
                color: "common.white",
                backgroundColor: "primary.main",
                "&:hover": {
                  backgroundColor: "#66BB6A",
                  transition: "background 0.5s ease, transform 0.5s ease",
                },
              }}
            >
              <Icon icon="tabler:x" fontSize="1rem" />
            </IconButton>
          </Box>
        </DialogTitle>
        <DialogContent
          sx={{
            position: "relative",
            pb: (theme) => `${theme.spacing(5)} !important`,
            px: (theme) => [`${theme.spacing(8)} !important`],
          }}
        >
          <>
            <Card>
              <AccordionBasic
                id={"panel-header-2"}
                ariaControls={"panel-content-2"}
                heading={"Requisition Details"}
                body={
                  <>
                    <TableContainer
                      sx={{ padding: "4px 6px", paddingTop: "0px !important" }}
                      className="tableBody"
                    >
                      <Table>
                        <TableBody
                          sx={{
                            "& .MuiTableCell-root": {
                              p: `${theme.spacing(1.35, 1.125)} !important`,
                            },
                          }}
                        >
                          <TableRow>
                            <MUITableCell sx={tablecellLabelStyle}>
                              <Typography style={field}>
                                Service Requisition No:
                              </Typography>
                            </MUITableCell>
                            <MUITableCell sx={tablecellValueStyle}>
                              <Typography
                                className="data-field"
                                style={fieldValueStyle}
                              >
                                {formData?.systemCode}
                              </Typography>
                            </MUITableCell>
                          </TableRow>
                          <TableRow>
                            <MUITableCell sx={tablecellLabelStyle}>
                              <Typography style={field}>
                                Society Name:
                              </Typography>
                            </MUITableCell>
                            <MUITableCell sx={tablecellValueStyle}>
                              <Typography
                                className="data-field"
                                style={fieldValueStyle}
                              >
                                {formData?.initiatingEntity?.orgName}
                              </Typography>
                            </MUITableCell>
                          </TableRow>
                          <TableRow>
                            <MUITableCell sx={tablecellLabelStyle}>
                              <Typography style={field}>
                                Service Type:
                              </Typography>
                            </MUITableCell>
                            <MUITableCell sx={tablecellValueStyle}>
                              <Typography
                                className="data-field"
                                style={fieldValueStyle}
                              >
                                {serviceType}
                              </Typography>
                            </MUITableCell>
                          </TableRow>

                          {formData?.specifications
                            ?.anyOtherServiceProvided && (
                            <TableRow>
                              <MUITableCell
                                sx={{
                                  textAlign: "right",
                                  width: "50%",
                                  paddingRight: theme.spacing(4),
                                }}
                              >
                                <Typography style={field}>
                                  Other Services:
                                </Typography>
                              </MUITableCell>
                              <MUITableCell
                                sx={{
                                  textAlign: "left",
                                  width: "50%",
                                  paddingLeft: theme.spacing(2),
                                }}
                              >
                                <Typography
                                  className="data-field"
                                  style={fieldValueStyle}
                                >
                                  {
                                    formData?.specifications
                                      ?.anyOtherServiceProvided
                                  }
                                </Typography>
                              </MUITableCell>
                            </TableRow>
                          )}

                          <TableRow>
                            <MUITableCell sx={tablecellLabelStyle}>
                              <Typography style={field}>Priority:</Typography>
                            </MUITableCell>
                            <MUITableCell sx={tablecellValueStyle}>
                              <Typography
                                className="data-field"
                                style={fieldValueStyle}
                              >
                                {priorityName}
                              </Typography>
                            </MUITableCell>
                          </TableRow>

                          <TableRow>
                            <MUITableCell sx={tablecellLabelStyle}>
                              <Typography style={field}>Budget:</Typography>
                            </MUITableCell>
                            <MUITableCell sx={tablecellValueStyle}>
                              <Typography
                                className="data-field"
                                style={fieldValueStyle}
                              >

                                {formData?.budget?.rangeFrom}{" "}
                                {formData?.budget?.rangeTo && "-"}{" "}
                                {formData?.budget?.rangeTo}{" "}
                                {
                                  listValues?.find(
                                    (item) =>
                                      item.id === formData?.budget?.units
                                  )?.name
                                }{" "}
                                {
                                  listValues?.find(
                                    (item) =>
                                      item.id === formData?.budget?.condition
                                  )?.name
                                }
                              </Typography>
                            </MUITableCell>
                          </TableRow>

                          <TableRow>
                            <MUITableCell sx={tablecellLabelStyle}>
                              <Typography style={field}>
                                Requirement Dead Line:
                              </Typography>
                            </MUITableCell>
                            <MUITableCell sx={tablecellValueStyle}>
                              <Typography
                                className="data-field"
                                style={fieldValueStyle}
                              >
                                {formData?.requirementDeadLine}
                              </Typography>
                            </MUITableCell>
                          </TableRow>

                          <TableRow>
                            <MUITableCell sx={tablecellLabelStyle}>
                              <Typography style={field}>
                                Society Remarks:
                              </Typography>
                            </MUITableCell>
                            <MUITableCell sx={tablecellValueStyle}>
                              <Typography
                                className="data-field"
                                style={fieldValueStyle}
                              >
                                {formData?.remarks}
                              </Typography>
                            </MUITableCell>
                          </TableRow>

                          {/* Displaying the specifications section */}
                          <TableRow>
                            <MUITableCell sx={tablecellValueStyle} colSpan={2}>
                              <Typography
                                className="data-field"
                                style={fieldValueStyle}
                                sx={{ backgroundColor: "#f2f7f2" }}
                              >
                                Specifications
                              </Typography>
                            </MUITableCell>
                          </TableRow>

                          {
                            formData?.specifications?.sections
                              ?.map((section) => {
                                // Filter fields to include only those with values
                                const fieldsWithValues = section.fields?.filter(
                                  (field) => {
                                    return (
                                      ((field.componentId ===
                                        authConfig.textFieldComponentId ||
                                        field.componentId ===
                                          authConfig.textAreaComponentId ||
                                        field.componentId ===
                                          authConfig.numberTextFieldComponentId) &&
                                        field.providedTextValue) || // Check if providedTextValue exists and is not empty
                                      field.selectedDropdownValues?.length > 0 // Check if selectedDropdownValues exists and has items
                                    );
                                  }
                                );

                                // Only render the section if it has fields with values
                                if (!fieldsWithValues?.length) return null;

                                return (
                                  <Fragment key={section.id}>
                                    {/* Display section ID */}
                                    <TableRow>
                                      <MUITableCell colSpan={2}>
                                        <Typography
                                          variant="body2"
                                          fontWeight={"bold"}
                                          sx={{ mt: 0, ml: 2 }}
                                        >
                                          {
                                            listValues?.find(
                                              (item) => item.id === section.id
                                            )?.name
                                          }
                                        </Typography>
                                      </MUITableCell>
                                    </TableRow>

                                    {fieldsWithValues.map((field) => (
                                      <TableRow key={field.id}>
                                        <MUITableCell sx={tablecellLabelStyle}>
                                          <Typography style={field}>
                                            {
                                              listNames?.find(
                                                (item) =>
                                                  item.id === field.labelId
                                              )?.name
                                            }
                                            {":"}
                                          </Typography>
                                        </MUITableCell>
                                        <MUITableCell sx={tablecellValueStyle}>
                                          <Typography
                                            className="data-field"
                                            style={fieldValueStyle}
                                          >
                                            {
                                              field.componentId ===
                                                authConfig.textFieldComponentId ||
                                              field.componentId ===
                                                authConfig.textAreaComponentId ||
                                              field.componentId ===
                                                authConfig.numberTextFieldComponentId
                                                ? field.providedTextValue // Show providedTextValue if the componentId matches
                                                : field.selectedDropdownValues
                                                    ?.map((item) => item.key)
                                                    .join(", ") // Show the keys as a comma-separated string
                                            }
                                          </Typography>
                                        </MUITableCell>
                                      </TableRow>
                                    ))}
                                  </Fragment>
                                );
                              })
                              .filter(Boolean) // Remove null entries (sections with no fields)
                          }
                        </TableBody>
                      </Table>
                    </TableContainer>
                  </>
                }
                expanded={false}
              />
            </Card>
            {user?.organisationCategory === "EMPLOYEE" && (
              <Card>
                <Grid
                  sx={{
                    backgroundColor: "#f2f7f2",
                    mt: 4,
                    paddingTop: 0,
                    height: "36px",
                    display: "flex",
                    alignItems: "center",
                  }}
                >
                  <Typography
                    variant="body1"
                    fontWeight={"bold"}
                    sx={{ mt: 0, ml: 2 }}
                  >
                    Service Providers Information
                  </Typography>
                  <Divider />
                </Grid>
                <Divider />

                <Grid container spacing={5} style={{ padding: "16px" }}>
                  <Grid item xs={12} sm={4}>
                    <FormControl
                      fullWidth
                      error={Boolean(errors.spOrganizationName)}
                    >
                      <Controller
                        name="spOrganizationName"
                        control={control}
                        // defaultValue={formData?.senderDetails?.organisationId}
                        rules={{
                          required: "Sender Organisation name is required",
                        }}
                        render={({ field }) => (
                          <>
                            <SelectAutoComplete
                              id="spOrganizationName"
                              label="SP Organization Name"
                              nameArray={spOrganizationName?.map((type) => ({
                                key: type.organisationName,
                                value: type.organisationId,
                              }))}
                              value={orgName}
                              onChange={(event) => {
                                field.onChange(event.target.value);
                                setOrgName(event.target.value);
                              }}
                            />
                            {errors.spOrganizationName && (
                              <FormHelperText sx={{ color: "error.main" }}>
                                {errors.spOrganizationName.message}
                              </FormHelperText>
                            )}
                          </>
                        )}
                      />
                    </FormControl>
                  </Grid>
                </Grid>
              </Card>
            )}

            {templateData?.sections?.length > 0 && (
              <BOQWizard
                data={templateData?.sections}
                updatedData={updatedData}
                setUpdatedData={setUpdatedData}
                handleUpdate={update}
                formData={templateData}
                dateOfSubmission={dateOfSubmission}
              />
            )}
          </>
        </DialogContent>
      </Dialog>

      <Dialog
        open={openDialogContent}
        onClose={handleDialogClose}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
        PaperProps={{
          sx: {
            p: (theme) => `${theme.spacing(2.5)} !important`,
            backgroundColor: (theme) => theme.palette.primary.background,
          },
        }}
      >
        <Box
          sx={{
            width: "100%",
            borderRadius: 1,
            textAlign: "center",
            border: (theme) => `1px solid ${theme.palette.divider}`,
            borderColor: "primary.main",
          }}
        >
          <DialogContent>
            <DialogContentText
              id="alert-dialog-description"
              color="primary.main"
            >
              <div dangerouslySetInnerHTML={{ __html: dialogMessage }} />
            </DialogContentText>
          </DialogContent>
          <DialogActions>
            <Button
              variant="contained"
              onClick={handleDialogClose}
              sx={{ margin: "auto", width: 100 }}
            >
              Okay
            </Button>
          </DialogActions>
        </Box>
      </Dialog>
    </>
  );
};
export default SubmitBoqDialog;