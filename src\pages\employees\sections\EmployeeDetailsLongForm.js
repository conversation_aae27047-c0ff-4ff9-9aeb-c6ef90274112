// ** React Imports

// ** MUI Imports
import Button from "@mui/material/Button";
import FormControl from "@mui/material/FormControl";
import Grid from "@mui/material/Grid";
import TextField from "@mui/material/TextField";
import axios from "axios";
// ** Third Party Imports
import { getAuthorizationHeaders, getUrl } from "src/helpers/utils";

import { Controller, useForm } from "react-hook-form";

// ** Hooks
// ** Icon Imports
import { yupResolver } from "@hookform/resolvers/yup";
import {
  DialogActions,
  Divider,
  FormControlLabel,
  FormHelperText,
  InputLabel,
  MenuItem,
  Select,
  Switch,
  Typography,
  Card,
} from "@mui/material";
import { Box } from "@mui/system";
import MobileNumberValidation from "src/@core/components/custom-components/MobileNumberValidation";
import NameTextField from "src/@core/components/custom-components/NameTextField";
import { useAuth } from "src/hooks/useAuth";
import EmployeeValidations from "./EmployeeValidations";
import { useContext, useEffect, useState } from "react";
import SelectCategory from "src/@core/components/custom-components/SelectCategory";

import authConfig from "src/configs/auth";
import { AuthContext } from "src/context/AuthContext";
import SelectAutoComplete from "src/@core/components/custom-components/SelectAutoComplete";

const EmployeeDetailsLongForm = ({ onCancel, formData, fetchUsers }) => {
  const [isActive, setIsActive] = useState(formData?.status);

  const handleOnChange = (event) => {
    if (event.target.checked) {
      setIsActive("ACTIVE");
    } else {
      setIsActive("INACTIVE");
    }
  };

  const { getAllListValuesByListNameId } = useContext(AuthContext);

  const [employeeId, setEmployeeId] = useState(formData?.reportingTo);
  const [departmentId, setDepartmentId] = useState(formData?.department);
  const [selectedLocationId, setSelectedLocationId] = useState(
    formData?.employeeMetaData?.employeeData?.workLocation
  );
  const [locationsData, setLocationsData] = useState(null);
  const [selectedDesignationId, setSelectedDesignationId] = useState(
    formData?.designation
  );
  const [designationsData, setDesignationsData] = useState(null);

  console.log("DEP_INITAL ID", formData?.department);

  //Hooks
  const auth = useAuth();
  const fields = ["firstName", "lastName", "mobileNumber"];

  const {
    register,
    handleSubmit,
    setError,
    clearErrors,
    control,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(EmployeeValidations(fields)),
    mode: "onChange",
  });

  async function onSubmit(data) {
    const fields = {
      id: formData.id,
      firstName: data.firstName,
      lastName: data.lastName,
      mobileNumber: data.mobileNumber,
      email: data.email,
      designation: selectedDesignationId,
      reportingTo: employeeId,
      department: departmentId,
      status: isActive,
      employeeMetaData: {
        employeeData: {
          address: {
            street1: data.street1 || "",
            street2: data.street2 || "",
            city: data.city || "",
            state: data.state || "",
            country: data.country || "",
            pinCode: data.pinCode || "",
          },
          workLocation: selectedLocationId,
        },
      },
    };

    const response = await auth.patchEmployee(fields, () => {
      console.error(" Employee Details failed");
    });

    const currentPage = 1;
    const currentPageSize = 5;

    fetchUsers(currentPage, currentPageSize);
    onCancel();
  }

  const [listOfEmployees, setListOfEmployees] = useState([]);
  const [employeesOptions, setEmployeesOptions] = useState([]);

  const [listOfRoleTypes, setListOfRoleTypes] = useState([]);
  const [departmentOptions, setDepartmentOptions] = useState([]);

  useEffect(() => {
    // Fetch employees //to do as we fetching same data in edit. define in auth context to use in both files
    axios({
      method: "get",
      url: getUrl(authConfig.selectDropdownNew) + "?selectionType=EMPLOYEES",
      headers: getAuthorizationHeaders(),
    })
      .then((res) => {
        setListOfEmployees(res.data.data);
      })
      .catch((err) => console.log("Employees error", err));

    axios({
      method: "get",
      url:
        getUrl(authConfig.selectDropdown) +
        "?selectionType=EMPLOYEE_SUBCATEGORIES",
      headers: getAuthorizationHeaders(),
    })
      .then((res) => {
        setListOfRoleTypes(res.data.data);
      })
      .catch((err) => console.log("Role Types error", err));
  }, []);

  useEffect(() => {
    if (!!authConfig) {
      getAllListValuesByListNameId(
        authConfig.workLocation,
        (data) =>
          setLocationsData(
            data?.listValues.map((item) => ({
              value: item.id,
              key: item.listValue,
            }))
          ),
        handleError
      );

      getAllListValuesByListNameId(
        authConfig.designationId,
        (data) =>
          setDesignationsData(
            data?.listValues.map((item) => ({
              value: item.id,
              key: item.listValue,
            }))
          ),
        handleError
      );
    }
  }, [authConfig]);

  useEffect(() => {
    if (!!listOfEmployees) {
      let data = [];
      listOfEmployees.map((entry) => {
        data.push({ value: entry.id, key: entry.name });
      });
      setEmployeesOptions(data);
    }
  }, [listOfEmployees]);

  useEffect(() => {
    if (!!listOfRoleTypes) {
      let data = [];
      listOfRoleTypes[0]?.metaData?.subRoleTypes?.forEach((entry) => {
        data.push({ value: entry.id, key: entry.name });
      });
      setDepartmentOptions(data);
    }
  }, [listOfRoleTypes]);

  const handleEmployeeChange = (newValue) => {
    setEmployeeId(newValue);
  };

  const handleDepartmentChange = (newValue) => {
    console.log("DEP _ID", newValue);
    setDepartmentId(newValue);
  };

  const handleLocationSuccess = (data) => {
    setLocationsData(data?.listValues);
  };

  const handleDesignationSuccess = (data) => {
    setDesignationsData(data?.listValues);
  };

  const handleError = (error) => {
    console.error("Employees page:", error);
  };

  const handleSelectChange = (event) => {
    const selectedId = event.target.value;
    setSelectedLocationId(selectedId);
  };

  const handleSelectDesignationChange = (event) => {
    const selectedId = event.target.value;
    setSelectedDesignationId(selectedId);
  };

  return (
    <>
    <Grid container spacing={5}>
      <Grid item  xs={12}
        sx={{
          backgroundColor: "#f2f7f2",
          paddingTop: 0,
          ml:5,
          height: "36px",
          display: "flex",
          alignItems: "center",
          justifyContent: "space-between",
        }}
      >
        <Typography variant="body1" fontWeight={"bold"} sx={{ mt: 0, ml: -3, mb: 5  }}>
          Employee Profile
        </Typography>
      </Grid>
      
     
          <Grid item xs={12} sm={4}>
            <FormControl fullWidth>
              <Controller
                name="firstName"
                control={control}
                defaultValue={formData?.firstName}
                render={({ field }) => (
                  <NameTextField
                    size="small"
                    label="First Name"
                    InputLabelProps={{ shrink: true }}
                    placeholder="Enter your First Name"
                    error={Boolean(errors.firstName)}
                    helperText={errors.firstName?.message}
                    {...register("firstName", {
                      required: "First Name is required",
                    })} // Add required validation
                  />
                )}
              />
            </FormControl>
          </Grid>

          <Grid item xs={12} sm={4}>
            <FormControl fullWidth>
              <Controller
                name="lastName"
                control={control}
                defaultValue={formData?.lastName}
                render={({ field }) => (
                  <NameTextField
                    {...field}
                    size="small"
                    label="Last Name"
                    InputLabelProps={{ shrink: true }}
                    placeholder="Enter your Last Name"
                    error={Boolean(errors.lastName)}
                    helperText={errors.lastName?.message}
                    aria-describedby="Section1-lastName"
                  />
                )}
              />
            </FormControl>
          </Grid>
          <Grid item xs={12} sm={4}>
            <FormControl
              fullWidth
              error={Boolean(errors.selectedDesignationId)}
            >
              <Controller
                name="selectedDesignationId"
                control={control}
                rules={{ required: "Designation is required" }}
                defaultValue={selectedDesignationId}
                render={({ field }) => (
                  <SelectAutoComplete
                    id="designation"
                    label="Designation"
                    nameArray={designationsData}
                    value={field.value}
                    onChange={(e) => field.onChange(e.target.value)}
                  />
                )}
              />
              {errors.selectedDesignationId && (
                <FormHelperText
                  sx={{ color: "error.main" }}
                  id="validation-selectedDesignationId"
                >
                  {errors.selectedDesignationId.message}
                </FormHelperText>
              )}
            </FormControl>
          </Grid>

          <Grid item xs={12} sm={4}>
            <SelectAutoComplete
              register={register}
              clearErrors={clearErrors}
              id={"employeeId-select"}
              label={"Reporting to "}
              name="employeeId-select"
              nameArray={employeesOptions}
              defaultValue={employeeId}
              value={employeeId}
              onChange={(event) => handleEmployeeChange(event.target.value)}
              aria-describedby="employeeId-select"
            />
          </Grid>

          <Grid item xs={12} sm={4}>
            <SelectAutoComplete
              register={register}
              clearErrors={clearErrors}
              id={"departmentId-select"}
              label={"Department"}
              name="departmentId-select"
              nameArray={departmentOptions}
              defaultValue={departmentId}
              value={departmentId}
              onChange={(event) => handleDepartmentChange(event.target.value)}
              aria-describedby="departmentId-select"
            />
          </Grid>

          <Grid item xs={12} sm={4}>
            <FormControl fullWidth>
              <Controller
                name="workLocation"
                control={control}
                defaultValue={formData?.workLocation || ""}
                render={({ field }) => (
                  <SelectAutoComplete
                    {...field}
                    labelId="location-select-label"
                    id="location-select"
                    size="small"
                    value={selectedLocationId}
                    label="Work Location"
                    nameArray={locationsData}
                    onChange={(e) => {
                      field.onChange(e);
                      handleSelectChange(e);
                    }}
                  />
                )}
              />
            </FormControl>
            {errors.selectedLocationId && (
              <FormHelperText
                sx={{ color: "error.main" }}
                id="validation-selectedLocationId"
              >
                {errors.selectedLocationId?.message}
              </FormHelperText>
            )}
          </Grid>

          <Grid item xs={12} sm={4}>
            <FormControl fullWidth>
              <Controller
                name="mobileNumber"
                control={control}
                defaultValue={formData?.mobileNumber}
                render={({ field }) => (
                  <MobileNumberValidation
                    {...field}
                    size="small"
                    type="tel"
                    label="Mobile Number"
                    InputLabelProps={{ shrink: true }}
                    error={Boolean(errors.mobileNumber)}
                    placeholder="Enter 10 digit Mobile Number"
                    helperText={errors.mobileNumber?.message}
                    aria-describedby="Section1-contactNumber"
                  />
                )}
              />
            </FormControl>
          </Grid>
          <Grid item xs={12} sm={5}>
            <FormControl fullWidth>
              <Controller
                name="email"
                control={control}
                defaultValue={formData?.email}
                render={({ field }) => (
                  <NameTextField
                    {...field}
                    size="small"
                    label=" Email"
                    InputLabelProps={{ shrink: true }}
                    placeholder="Enter your Email"
                    error={Boolean(errors.firstName)}
                    helperText={errors.firstName?.message}
                    aria-describedby="Section1-firstName"
                  />
                )}
              />
            </FormControl>
          </Grid>
          <Grid item xs={6} sm={1}>
            <Button variant="contained">Verify</Button>
          </Grid>
          
           
           
            <Grid item  xs={12}
              sx={{
                backgroundColor: "#f2f7f2",
                paddingTop: 0,
                height: "36px",
                display: "flex",
                alignItems: "center",
                justifyContent: "space-between",
                ml:5,
                mt :3
      
  
              }}
            >
              <Typography
                variant="body1"
                fontWeight={"bold"}
                sx={{ mt: 0, ml: -3, mb: 5 ,  textAlign: "center",}}
              >
                Address
              </Typography>
              <Divider/>
            </Grid>
            
        

          <Grid item xs={12} sm={6}>
            <FormControl fullWidth>
              <Controller
                name="street1"
                control={control}
                defaultValue={
                  formData?.employeeMetaData?.employeeData?.address?.street1
                }
                render={({ field }) => (
                  <TextField
                    {...field}
                    size="small"
                    label="Street1"
                    InputLabelProps={{ shrink: true }}
                    error={Boolean(errors.street1)}
                    helperText={errors.street1?.message}
                    aria-describedby="Section1-street1"
                  />
                )}
              />
            </FormControl>
          </Grid>
          <Grid item xs={12} sm={6}>
            <FormControl fullWidth>
              <Controller
                name="street2"
                control={control}
                defaultValue={
                  formData?.employeeMetaData?.employeeData?.address?.street2
                }
                render={({ field }) => (
                  <TextField
                    {...field}
                    size="small"
                    label="Street2"
                    InputLabelProps={{ shrink: true }}
                    error={Boolean(errors.street2)}
                    helperText={errors.street2?.message}
                    aria-describedby="Section1-street2"
                  />
                )}
              />
            </FormControl>
          </Grid>
          <Grid item xs={12} sm={6}>
            <FormControl fullWidth>
              <Controller
                name="pinCode"
                control={control}
                defaultValue={
                  formData?.employeeMetaData?.employeeData?.address?.pinCode
                }
                rules={{
                  // required: "This field is required",
                  minLength: {
                    value: 6,
                    message: "Enter a 6 digit pincode",
                  },
                }}
                render={({ field: { value, onChange } }) => (
                  <TextField
                    value={value}
                    type="text"
                    size="small"
                    inputMode="numeric"
                    pattern="[0-9]*"
                    maxLength={6}
                    label="Pin Code"
                    InputLabelProps={{ shrink: true }}
                    onChange={onChange}
                    onKeyDown={(event) => {
                      if (
                        !/^\d+$/.test(event.key) &&
                        event.key !== "Backspace" &&
                        event.key !== "Delete"
                      ) {
                        event.preventDefault();
                      }
                      if (
                        value &&
                        value.length === 6 &&
                        event.key !== "Backspace" &&
                        event.key !== "Delete"
                      ) {
                        event.preventDefault();
                      }
                    }}
                    placeholder="Enter Pin Code"
                    error={Boolean(errors.pinCode)}
                    helperText={errors.pinCode?.message}
                    aria-describedby="validation-basic-pin-code"
                  />
                )}
              />
            </FormControl>
          </Grid>

          <Grid item xs={12} sm={6}>
            <FormControl fullWidth>
              <Controller
                name="city"
                control={control}
                defaultValue={
                  formData?.employeeMetaData?.employeeData?.address?.city
                }
                render={({ field }) => (
                  <NameTextField
                    {...field}
                    size="small"
                    label="City"
                    InputLabelProps={{ shrink: true }}
                    placeholder="Enter your city"
                    error={Boolean(errors.city)}
                    helperText={errors.city?.message}
                    aria-describedby="Section1-city"
                  />
                )}
              />
            </FormControl>
          </Grid>

          <Grid item xs={12} sm={6}>
            <FormControl fullWidth>
              <Controller
                name="state"
                control={control}
                defaultValue={
                  formData?.employeeMetaData?.employeeData?.address?.state
                }
                render={({ field }) => (
                  <NameTextField
                    {...field}
                    size="small"
                    label="State"
                    InputLabelProps={{ shrink: true }}
                    placeholder="Enter your state"
                    error={Boolean(errors.state)}
                    helperText={errors.state?.message}
                    aria-describedby="Section1-state"
                  />
                )}
              />
            </FormControl>
          </Grid>

          <Grid item xs={12} sm={6}>
            <FormControl fullWidth>
              <Controller
                name="country"
                control={control}
                defaultValue={
                  formData?.employeeMetaData?.employeeData?.address?.country
                }
                render={({ field }) => (
                  <NameTextField
                    {...field}
                    size="small"
                    label="Country"
                    InputLabelProps={{ shrink: true }}
                    placeholder="Enter your country"
                    error={Boolean(errors.country)}
                    helperText={errors.country?.message}
                    aria-describedby="Section1-country"
                  />
                )}
              />
            </FormControl>
          </Grid>
    </Grid>
   
     
    </>
  );
};

export default EmployeeDetailsLongForm;
