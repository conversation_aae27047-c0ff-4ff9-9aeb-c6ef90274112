import { Menu, MenuItem, Tooltip } from "@mui/material";
import { useContext, useState } from "react";
import Icon from "src/@core/components/icon";
import CustomAvatar from "src/@core/components/mui/avatar";
import { AuthContext } from "src/context/AuthContext";

const useColumns = ({
  setLocation,
  setCurrentRow,
  currentRow,
  setOpenViewDialog,
  setOpenEditDialog,
  setOpenDeleteDialog,
  setDialogOpen
}) => {
  const { user, setWorkOrderId, workOrderId } = useContext(AuthContext);

  const [anchorEl, setAnchorEl] = useState(null);

  const handleMenuClose = () => {
    setAnchorEl(null); 
  };

  const handleViewDialog = () => {
    setOpenViewDialog(true);
    handleMenuClose();
  };

  const handleEditDialog = () => {
    setOpenEditDialog(true);
    handleMenuClose();
  };

  const onClickToggleStatus = () => {
    setOpenDeleteDialog(true);
    handleMenuClose();
  };

  const handleClick = (event, row) => {
    event.preventDefault();
    setDialogOpen(true);
    setCurrentRow(row);
  };

  return [
    user.organisationCategory !== "SERVICE_PROVIDER"
      ? {
          field: "workOrdersSystemCode",
          minWidth: 135,
          headerName: "WO Number",
          flex: 0.11,
          renderCell: ({ row }) => (
            <Tooltip title={row.workOrdersSystemCode}>
              <a
                href="#"
                style={{
                  color: "blue",
                  textDecoration: "underline",
                }}
                onClick={(event) => handleClick(event, row)}
              >
                {row.workOrdersSystemCode}
              </a>
            </Tooltip>
          ),
        }
      : null,
    user.organisationCategory !== "SERVICE_PROVIDER"
      ? {
          field: "systemCode",
          minWidth: 135,
          headerName: "SR No.",
          flex: 0.12,
          renderCell: (params) => (
            <Tooltip title={params.value}>
              <span>{params.value}</span>
            </Tooltip>
          ),
        }
      : null,
    {
      field: "societyName", // New Field: Society Name
      minWidth: 135,
      headerName: "Society Name",
      flex: 0.13,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <span>{params.value}</span>
        </Tooltip>
      ),
    },
    {
      field: "confirmedSpName",
      minWidth: 130,
      headerName: "Confirmed SP",
      flex: 0.13,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <span>{params.value}</span>
        </Tooltip>
      ),
    },
    {
      field: "fileLocation", // New Field: Society Name
      minWidth: 135,
      headerName: "Signed Work Order",
      flex: 0.13,
      renderCell: (params) => {
        // Extract the file name from the full location string
        const location = params.value || ""; // Handle null/undefined gracefully
        const parts = location.split("/");
        const fileName = parts[parts?.length - 1]; // Get the last part of the path
        const snapshotData = { id: params.row?.id, location };

        return (
          <Tooltip title={fileName}>
            <a
              // href="#"
              onClick={(e) => {
                e.preventDefault(); // Prevent default anchor behavior
                setLocation(snapshotData); // Call setLocation with the snapshotData object
              }}
              style={{
                textDecoration: "underline",
                color: "blue",
                cursor: "pointer",
              }}
            >
              {fileName}
            </a>
          </Tooltip>
        );
      },
    },
    {
      field: "createdByEmail",
      minWidth: 95,
      headerName: "Created By",
      flex: 0.04,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <span>{params.value}</span>
        </Tooltip>
      ),
    },
    {
      field: "isActive",
      minWidth: 70,
      headerName: "Is Active",
      flex: 0.04,
      renderCell: ({ row }) => {
        const status = row.isActive;

        return (
          <Tooltip title={status ? "ACTIVE" : "INACTIVE"}>
            <CustomAvatar
              skin="none"
              sx={{
                width: status ? 13 : 13,
                height: status ? 13 : 13,
                // m: 5,
                m: "auto",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
              }}
              color={status ? "success" : "error"}
            >
              <Icon
                icon={
                  status
                    ? "fluent-emoji-flat:green-circle"
                    : "fluent-emoji-flat:red-circle"
                }
                style={{ width: 15, height: 15 }}
              />
            </CustomAvatar>
          </Tooltip>
        );
      },
    },
    {
      flex: 0.01,
      field: "edit",
      headerName: "Actions",
      sortable: false,
      minWidth: 100,
      disableClickEventBubbling: true,
      renderCell: (params) => {
        const handleClick = (params, event) => {
          const row = params.row;
          setAnchorEl(event.currentTarget);
          setWorkOrderId({
            ...workOrderId,
            id: params.row.id,
          });
          setCurrentRow(params.row);
        };

        return (
          <div
            style={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
            }}
          >
            <CustomAvatar
              skin="light"
              variant="rounded"
              sx={{
                mr: { xs: 2, lg: 4 },
                width: 34,
                height: 34,
                cursor: "pointer",
              }}
              onClick={(event) => handleClick(params, event)}
            >
              <Icon icon="bi:three-dots-vertical" />
            </CustomAvatar>

            <Menu
              anchorEl={anchorEl}
              open={Boolean(anchorEl)}
              onClose={handleMenuClose}
            >
              <MenuItem onClick={handleViewDialog}>View</MenuItem>

              {user.organisationCategory !== "SERVICE_PROVIDER" && (
                <MenuItem onClick={handleEditDialog}>Edit</MenuItem>
              )}

              {user.organisationCategory === "EMPLOYEE" && (
                <MenuItem onClick={onClickToggleStatus}>
                  {currentRow?.isActive ? "Deactivate" : "Activate"}
                </MenuItem>
              )}
            </Menu>
          </div>
        );
      },
    },
  ].filter(Boolean); // Filters out any null values
};

export default useColumns;