import { useContext, useEffect, useState } from "react";
import {
  Grid,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  IconButton,
  Button,
  FormControl,
  FormHelperText,
  TextField,
  DialogContentText, Box,
  Card,
  TableContainer,
  Table,
  TableBody,
  TableRow,
  Typography,
  Divider,
} from "@mui/material";
import { useForm, Controller } from "react-hook-form";
import SelectAutoComplete from "src/@core/components/custom-components/SelectAutoComplete";
import axios from "axios";
import { getAuthorizationHeaders, getUrl } from "src/helpers/utils";
import authConfig from "src/configs/auth";
import Icon from "src/@core/components/icon";
import AccordionBasic from "src/@core/components/custom-components/AccordionBasic";
import MUITableCell from "../SP/MUITableCell";
import { useTheme } from "@emotion/react";
import { AuthContext } from "src/context/AuthContext";
const field = {
  fontSize: "12.75px",
};
const fieldValueStyle = {
  fontWeight: "bold",
  wordWrap: "break-word",
  whiteSpace: "pre-wrap",
  // color: "#66BB6A", // Sets color for the TableCell
  color:'#108A00',
  fontSize: "14px",
  lineHeight: "1.2"
};
const tablecellLabelStyle = {
  width: { xs: "50%", sm: "50%", md: "40%" }, // Responsive width adjustment
  padding: "4px", // Ensure padding for better alignment
  textAlign: "right", // Align the text to the left
  verticalAlign: "middle", // Vertically align the text to the middle
  textIndent: {
    lg: "80px",
    md: "80px",
    sm: "100px",
    xs: "0",
  },
};
const tablecellValueStyle = {
  justifyContent: "flex-start",
  alignItems: "center",
  width: { xs: "50%", sm: "50%", md: "60%" }, // Responsive width for small and medium screens
  height: "10px",
};

const SelectOrApproveSpDialog = ({ open, onClose, data }) => {
  const { control, handleSubmit, formState: { errors }, watch, reset, setValue } = useForm();
  const [serviceProviders, setServiceProviders] = useState([]);
  const selectedL1 = watch("l1ServiceProvider");
  const [openDialogContent, setOpenDialogContent] = useState(false);
  const [dialogMessage, setDialogMessage] = useState("Are you sure you want to approve the SPs & update the remarks?");
  const [openSuccessDialog, setOpenSuccessDialog] = useState(false);
  const [failureDialog, setOpenFailureDialog] = useState(false);
  const { user, listValues } = useContext(AuthContext); 

  // Filter L2 options based on the selected L1 option
  const l2Options = serviceProviders.filter((sp) => sp.value !== selectedL1);


  useEffect(() => {
    if (data?.id) {
      // Ensure that the approvedServiceProvidersList is correctly mapped
      if (data?.confirmedSpDTO?.approvedServiceProvidersList) {
        const mappedServiceProviders = data?.confirmedSpDTO?.approvedServiceProvidersList.reduce((acc, sp) => {
          if (sp?.level === 'L1') {
            acc.L1 = sp.spOrganisationId;
          } else if (sp?.level === 'L2') {
            acc.L2 = sp?.spOrganisationId;
          }
          return acc;
        }, {});

        // Set the values dynamically using setValue
        setValue("l1ServiceProvider", mappedServiceProviders.L1 || '');
        setValue("l2ServiceProvider", mappedServiceProviders.L2 || '');
      }

      // Prepopulate the remarks field
      setValue("remarks", data?.confirmedSpDTO?.remarks || '');

      axios({
        method: "get",
        url: `${getUrl(authConfig.quotationsEndpoint)}/${data.id}/quotation-type?srQuotationEnum=ALL`,
        headers: getAuthorizationHeaders(authConfig.SR_QUOTATIONS_GET_BROAD_CASTED_SP_RES_V1),
      })
        .then((res) => {
          setServiceProviders(
            res.data?.map((item) => ({
              key: item.organisationName,
              value: item.organisationId,
            }))
          );
        })
        .catch((err) => console.error("Error fetching service providers:", err));
    }
  }, [data?.id, data?.confirmedSpDTO, setValue]);

  // Close the dialog and reset the form
  const handleClose = () => {
    reset();
    onClose();
  };

  const onSubmit = async (formData) => {
    const approvedServiceProvidersList = [
      {
        id: crypto.randomUUID(),
        spOrganisationId: formData?.l1ServiceProvider,
        level: "L1",
      },
    ];
  
    if (formData?.l2ServiceProvider) {
      approvedServiceProvidersList.push({
        id: crypto.randomUUID(),
        spOrganisationId: formData?.l2ServiceProvider,
        level: "L2",
      });
    }
  
    const payload = {
      approvedServiceProvidersList,
      remarks: formData?.remarks,
    };

    try {
      const response = await axios.patch(
        `${getUrl(authConfig.serviceRequisitionsEndpoint)}/${data.id}/confirmed-sp/`,
        payload,
        {
          headers: {
            ...getAuthorizationHeaders(),
            "Content-Type": authConfig.SERVICE_REQUISITIONS_CONFIRMED_SP_UPDATE_REQ_V1,
            Accept: authConfig.SERVICE_REQUISITIONS_CONFIRMED_SP_UPDATE_RES_V1,
          },
        }
      );
      setOpenDialogContent(false);
      setOpenSuccessDialog(true);
    } catch (error) {
      console.error("Submission Failed:", error);
      setOpenDialogContent(false);
      setOpenFailureDialog(true);
    }
    handleClose();
  };
const theme = useTheme();
  const handleApprove = (formData) => {
    setOpenDialogContent(true); // Open confirmation dialog
  };
  const serviceType = data?.serviceTypeId
    ? listValues?.find((item) => item.id === data?.serviceTypeId)
        ?.name
    : null;

  const priorityName = data?.priority
    ? listValues?.find((item) => item.id === data?.priority)
        ?.name
    : null;
    const [specifications, setSpecifications] = useState([]);

  return (
    <>
     <Dialog open={open} onClose={handleClose} fullScreen>
  <DialogTitle
    sx={{
      position: "relative",
      borderBottom: (theme) => `1px solid ${theme.palette.divider}`,
      p: (theme) => `${theme.spacing(1.75, 4)} !important`,
      display: "flex",
      alignItems: "center",
      justifyContent: "start",
      fontSize: { xs: 19, md: 20 },
      height: "50px", // height
    }}
    textAlign={"center"}
  >
    <Box sx={{ ml: { xs: 0, xl: 3,lg:3,md:3,sm:3 } }}>
      Select / Approve Service Providers&nbsp;
    </Box>
    <Box sx={{ position: "absolute", top: "9px", right: "30px", mr: { xs: 4, xl: 8 } }}>
      {/* Add your IconButton or custom actions here */}
    </Box>
    <Box sx={{ position: "absolute", top: "9px", right: "12px", mr: { xs: 3, sm: 5, xl: 5 } }}>
      <IconButton
        size="small"
        onClick={handleClose}
        sx={{
          borderRadius: 1,
          color: "common.white",
          backgroundColor: "primary.main",
          "&:hover": {
            backgroundColor: "#66BB6A",
            transition: "background 0.5s ease, transform 0.5s ease",
          },
        }}
      >
        <Icon icon="tabler:x" fontSize="1rem" />
      </IconButton>
    </Box>
  </DialogTitle>

  <DialogContent
    sx={{
      position: "relative",
      pt: (theme) => `${theme.spacing(8)} !important`,
      pb: (theme) => `${theme.spacing(5)} !important`,
      px: (theme) => [`${theme.spacing(8)} !important`],
    }}
  >

<Card>
              <AccordionBasic
                id={"panel-header-2"}
                ariaControls={"panel-content-2"}
                heading={"Requisition Details"}
                body={
                  <>
                    <TableContainer
                      sx={{ padding: "4px 6px", paddingTop: "0px !important" }}
                      className="tableBody"
                    >
                      <Table>
                        <TableBody
                          sx={{
                            "& .MuiTableCell-root": {
                              p: `${theme.spacing(1.35, 1.125)} !important`,
                            },
                          }}
                        >
                           <TableRow>
                            <MUITableCell sx={tablecellLabelStyle}>
                              <Typography style={field}>
                                Service Requisition No:
                              </Typography>
                            </MUITableCell>
                            <MUITableCell sx={tablecellValueStyle}>
                              <Typography
                                className="data-field"
                                style={fieldValueStyle}
                              >
                                {data?.systemCode}
                              </Typography>
                            </MUITableCell>
                          </TableRow>
                          <TableRow>
                            <MUITableCell sx={tablecellLabelStyle}>
                              <Typography style={field}>
                                Society Name:
                              </Typography>
                            </MUITableCell>
                            <MUITableCell sx={tablecellValueStyle}>
                              <Typography
                                className="data-field"
                                style={fieldValueStyle}
                              >
                                {data?.initiatingEntity?.orgName}
                              </Typography>
                            </MUITableCell>
                          </TableRow>
                          <TableRow>
                            <MUITableCell sx={tablecellLabelStyle}>
                              <Typography style={field}>
                                Service Type:
                              </Typography>
                            </MUITableCell>
                            <MUITableCell sx={tablecellValueStyle}>
                              <Typography
                                className="data-field"
                                style={fieldValueStyle}
                              >
                                {serviceType}
                              </Typography>
                            </MUITableCell>
                          </TableRow>

                          {data?.specifications
                            ?.anyOtherServiceProvided && (
                            <TableRow>
                              <MUITableCell
                                sx={{
                                  textAlign: "right",
                                  width: "50%",
                                  paddingRight: theme.spacing(4),
                                }}
                              >
                                <Typography style={field}>
                                  Other Services:
                                </Typography>
                              </MUITableCell>
                              <MUITableCell
                                sx={{
                                  textAlign: "left",
                                  width: "50%",
                                  paddingLeft: theme.spacing(2),
                                }}
                              >
                                <Typography
                                  className="data-field"
                                  style={fieldValueStyle}
                                >
                                  {
                                    data?.specifications
                                      ?.anyOtherServiceProvided
                                  }
                                </Typography>
                              </MUITableCell>
                            </TableRow>
                          )}

                          <TableRow>
                            <MUITableCell sx={tablecellLabelStyle}>
                              <Typography style={field}>Priority:</Typography>
                            </MUITableCell>
                            <MUITableCell sx={tablecellValueStyle}>
                              <Typography
                                className="data-field"
                                style={fieldValueStyle}
                              >
                                {priorityName}
                              </Typography>
                            </MUITableCell>
                          </TableRow>

                          <TableRow>
                            <MUITableCell sx={tablecellLabelStyle}>
                              <Typography style={field}>Budget:</Typography>
                            </MUITableCell>
                            <MUITableCell sx={tablecellValueStyle}>
                              <Typography
                                className="data-field"
                                style={fieldValueStyle}
                              >
                                 {data?.budget?.rangeFrom} {" "}
                              {data?.budget?.rangeTo && ("-")} {" "}
                              {data?.budget?.rangeTo}{" "}
                              {
                                listValues?.find(
                                  (item) => item.id === data?.budget?.units
                                )?.name
                              }{" "}
                              {
                                listValues?.find(
                                  (item) => item.id === data?.budget?.condition
                                )?.name
                              }
                              </Typography>
                            </MUITableCell>
                          </TableRow>

                          <TableRow>
                            <MUITableCell sx={tablecellLabelStyle}>
                              <Typography style={field}>
                                Requirement Dead Line:
                              </Typography>
                            </MUITableCell>
                            <MUITableCell sx={tablecellValueStyle}>
                              <Typography
                                className="data-field"
                                style={fieldValueStyle}
                              >
                                {data?.requirementDeadLine}
                              </Typography>
                            </MUITableCell>
                          </TableRow>

                          <TableRow>
                            <MUITableCell sx={tablecellLabelStyle}>
                              <Typography style={field}>
                                Society Remarks:
                              </Typography>
                            </MUITableCell>
                            <MUITableCell sx={tablecellValueStyle}>
                              <Typography
                                className="data-field"
                                style={fieldValueStyle}
                              >
                                {data?.remarks}
                              </Typography>
                            </MUITableCell>
                          </TableRow>

                          {specifications
                            ?.slice()
                            ?.sort((a, b) => a.listSequence - b.listSequence)
                            ?.map((category) =>
                              category.values.length > 0 ||
                              category.otherValue ? (
                                <TableRow key={category.name}>
                                  <MUITableCell
                                    sx={{
                                      textAlign: "right",
                                      width: "50%",
                                      paddingRight: theme.spacing(4),
                                    }}
                                  >
                                    <Typography style={field}>
                                      {category.name}
                                    </Typography>
                                  </MUITableCell>
                                  <MUITableCell
                                    sx={{
                                      textAlign: "left",
                                      width: "50%",
                                      paddingLeft: theme.spacing(2),
                                    }}
                                  >
                                    {category?.values?.length > 0
                                      ? category?.values?.map(
                                          (value, index) => (
                                            <Typography
                                              key={index}
                                              className="data-field"
                                              style={{ lineHeight: "1.2" }}
                                            >
                                              {value.name}
                                            </Typography>
                                          )
                                        )
                                      : category?.otherValue && (
                                          <Typography
                                            className="data-field"
                                            style={{ lineHeight: "1.2" }}
                                          >
                                            {category.otherValue}
                                          </Typography>
                                        )}
                                  </MUITableCell>
                                </TableRow>
                              ) : null
                            )}
                        </TableBody>
                      </Table>
                    </TableContainer>
                  </>
                }
                expanded={false}
              />
            </Card>
            <Card>
              
    <Grid
                  sx={{
                    backgroundColor: "#f2f7f2",
                    mt: 4,
                    paddingTop: 0,
                    height: "36px",
                    display: "flex",
                    alignItems: "center",
                  }}
                >
                  <Typography
                    variant="body1"
                    fontWeight={"bold"}
                    sx={{ mt: 0, ml: 2 }}
                  >
                    Select / Approve Service Providers
                  </Typography>
                  <Divider />
                </Grid>
                <Divider />
    <Grid container spacing={2} sx={{mt:2}}>

      {/* L1 Service Provider */}
      <Grid item xs={12} sm={6}>
        <Controller
          name="l1ServiceProvider"
          control={control}
          rules={{ required: "L1 Service Provider is required" }}
          render={({ field }) => (
            <FormControl fullWidth error={Boolean(errors.l1ServiceProvider)}>
              <SelectAutoComplete
                id="l1ServiceProvider"
                label="L1 Service Provider"
                nameArray={serviceProviders}
                value={field.value}
                onChange={(e) => field.onChange(e.target.value)}
              />
              {errors.l1ServiceProvider && (
                <FormHelperText>{errors.l1ServiceProvider.message}</FormHelperText>
              )}
            </FormControl>
          )}
        />
      </Grid>

      {/* L2 Service Provider */}
      {selectedL1 && (
        <Grid item xs={12} sm={6}>
          <Controller
            name="l2ServiceProvider"
            control={control}
            render={({ field }) => (
              <FormControl fullWidth>
                <SelectAutoComplete
                  id="l2ServiceProvider"
                  label="L2 Service Provider"
                  nameArray={l2Options}
                  value={field.value}
                  onChange={(e) => field.onChange(e.target.value)}
                />
              </FormControl>
            )}
          />
        </Grid>
      )}

      {/* Remarks */}
      <Grid item xs={12}>
        <Controller
          name="remarks"
          control={control}
          defaultValue={data?.confirmedSpDTO?.remarks || ''}
          render={({ field }) => (
            <TextField
              label="Remarks"
              multiline
              rows={4}
              fullWidth
              {...field}
            />
          )}
        />
      </Grid>
    </Grid>
    </Card>
  </DialogContent>

  <DialogActions
    sx={{
      justifyContent: "end",
      borderTop: (theme) => `1px solid ${theme.palette.divider}`,
      p: (theme) => `${theme.spacing(2.5)} !important`,
      height: "50px", // height
      mr: { xs: 3, sm: 5, xl: 5.5 }
    }}
  >
    <Button onClick={handleClose} variant="outlined">
      Cancel
    </Button>
    <Button
      onClick={handleSubmit(handleApprove)}
      variant="contained"
      color="primary"
    >
      Approve
    </Button>
  </DialogActions>
</Dialog>


      {/* Confirmation Dialog */}
      <Dialog
        open={openDialogContent}
        onClose={() => setOpenDialogContent(false)}
        aria-labelledby="confirmation-dialog-title"
        aria-describedby="confirmation-dialog-description"
        PaperProps={{
          sx: {
            p: (theme) => `${theme.spacing(2.5)} !important`,
            backgroundColor: (theme) => theme.palette.primary.background,
          },
        }}
      >
        <Box
          sx={{
            width: "100%",
            borderRadius: 1,
            textAlign: "center",
            border: (theme) => `1px solid ${theme.palette.divider}`,
            borderColor: "primary.main",
          }}
        >
          <DialogContent>
            <DialogContentText
              id="confirmation-dialog-description"
              color="primary.main"
            >
              {dialogMessage}
            </DialogContentText>
          </DialogContent>
          <DialogActions>
            <Button
              onClick={handleSubmit(onSubmit)}
              variant="contained"
              color="primary"
              sx={{ margin: "auto", width: 100 }}
            >
              Yes
            </Button>
            <Button
              onClick={() => setOpenDialogContent(false)}
              variant="text"
              sx={{ margin: "auto", width: 100 }}
            >
              No
            </Button>
          </DialogActions>
        </Box>
      </Dialog>

      {/* Success Dialog */}
      <Dialog
        open={openSuccessDialog}
        onClose={() => setOpenSuccessDialog(false)}
        aria-labelledby="success-dialog-title"
        aria-describedby="success-dialog-description"
        PaperProps={{
          sx: {
            p: (theme) => `${theme.spacing(2.5)} !important`,
            backgroundColor: (theme) => theme.palette.primary.background,
          },
        }}
      >
        <Box
          sx={{
            width: "100%",
            borderRadius: 1,
            textAlign: "center",
            border: (theme) => `1px solid ${theme.palette.divider}`,
            borderColor: "primary.main",
          }}
        >
          <DialogContent>
            <DialogContentText
              id="success-dialog-description"
              color="primary.main"
            >
               Approved/Updated successfully!
            </DialogContentText>
          </DialogContent>
          <DialogActions>
            <Button
              onClick={() => setOpenSuccessDialog(false)}
              variant="contained"
              color="primary"
              sx={{ margin: "auto", width: 100 }}
            >
              Okay
            </Button>
          </DialogActions>
        </Box>
      </Dialog>
    </>
  );
};

export default SelectOrApproveSpDialog;