import { FormControl, Grid, TextField } from "@mui/material";
import Box from "@mui/material/Box";
import Button from "@mui/material/Button";
import Drawer from "@mui/material/Drawer";
import IconButton from "@mui/material/IconButton";
import { styled } from "@mui/material/styles";
import Tooltip from "@mui/material/Tooltip";
import Typography from "@mui/material/Typography";
import { useEffect, useState } from "react";
import { Controller, useForm } from "react-hook-form";
import PerfectScrollbar from "react-perfect-scrollbar";
import MobileNumberValidation from "src/@core/components/custom-components/MobileNumberValidation";
import SelectClearAutoComplete from "src/@core/components/custom-components/SelectClearAutoComplete";
import Icon from "src/@core/components/icon";
import CustomAvatar from "src/@core/components/mui/avatar";
 
const Header = styled(Box)(({ theme }) => ({
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
  position: "relative",
  padding: theme.spacing(3),
  borderBottom: `1px solid ${theme.palette.divider}`,
}));
 
const AdvancedSearch = (props) => {
  const {
    open,
    toggle,
    selectedFilters,
    setSearchingState,
    convertedArray,
    clearAllFilters,
    onApplyFilters,
  } = props;
 
  const [selectRoleId, setSelectRoleId] = useState(null);
 
  const {
    reset,
    control,
    setValue,
    handleSubmit,
    formState: { errors },
  } = useForm();
 
  useEffect(() => {
    // Create a map of filter keys for easy lookup
    const filterMap = new Map(selectedFilters?.map((filter) => [filter.key, filter.value]));
 
    // Set values based on filters or clear them if the filter is not present
    setValue("name", filterMap.get("nameFilter") || "");
    setValue("email", filterMap.get("emailFilter") || "");
    setValue("mobileNumber", filterMap.get("mobileFilter") || "");
    setSelectRoleId(filterMap.get("roleFilter") || null);
    setValue("companyName",filterMap.get("searchByCompanyName") || "");
  }, [selectedFilters, setValue]);
 
  const handleCancel = () => {
    reset();
    setSelectRoleId("");
    setValue("mobileNumber", "");
    setSearchingState(false);
    clearAllFilters();
  };
 
  const handleClose = () => {
    toggle();
  };
 
 
  const handleApply = (data) => {

    // Prepare selected filters for chips
    const filters = [];
    if (data?.name) {
      filters.push({ key: "nameFilter", label: "Name", value: data?.name });
    }
    if (data?.email) {
      filters.push({ key: "emailFilter", label: "Email", value: data?.email });
    }
    if (data?.mobileNumber) {
      filters.push({
        key: "mobileFilter",
        label: "Mobile Number",
        value: data?.mobileNumber,
      });
    }
    if (selectRoleId) {
      filters.push({ key: "roleFilter", label: "Role", value: selectRoleId });
    }


    if(data?.companyName){
       filters.push({ key: "searchByCompanyName", label: "Company Name", value: data?.companyName });
    }
 
    // Call the parent callback with selected filters searchByCompanyName
    onApplyFilters(filters);
    setSearchingState(true); // Update searching state in parent if needed
    toggle(); // Close the drawer
  };
 
  const handleRoleChange = (value) => {
    const selectedRole = convertedArray.find((role) => role.value === value);
    if (selectedRole) {
      setSelectRoleId(selectedRole.value);
    }
  };
 
  return (
    <>
      <Tooltip title="Advanced Search">
        <CustomAvatar
          variant="rounded"
          sx={{ width: 36, height: 36, cursor: "pointer" }}
          onClick={toggle}
        >
          <Icon icon="tabler:filter" fontSize={27} />
        </CustomAvatar>
      </Tooltip>
 
      <Drawer
        open={open}
        anchor="right"
        variant="temporary"
        onClose={handleClose}
        ModalProps={{ keepMounted: true }}
        sx={{ "& .MuiDrawer-paper": { width: { xs: "85%", sm: 500 } } }}
      >
        <Header 
          sx={{position: "relative", 
            display: "flex",
            alignItems: "center", 
            justifyContent: "space-between"
           }}
           >
          <Typography variant="h5" sx={{
            ml:{
              xs :3,
              sm:3,

              xl :3
            }}}
            
            
            > Advanced Search&nbsp;   </Typography>
          <Box sx={{ position: "absolute", top: "8px", right: "14px", 
              mt:{
                xs:1,
              xl:1.5,
              lg:1.5,
              md:1.5,
              sm:1
            },
              mr:{
                xs:2.5,
                xl:2.5,
                lg:2.5,
                md:2.5,
                sm:2.5
              },
            

           }}>
            <IconButton
              size="small"
              onClick={handleClose}
              sx={{
                borderRadius: 1,
                color: "common.white",
                backgroundColor: "primary.main",
                "&:hover": {
                  backgroundColor: "#66BB6A",
                  transition: "background 0.5s ease, transform 0.5s ease",
                },
              }}
            >
              <Icon icon="tabler:x" fontSize="1rem" />
            </IconButton>
          </Box>
        </Header>
 
        <PerfectScrollbar options={{ wheelPropagation: false }}>
          <Box sx={{ p: (theme) => theme.spacing(4, 6) }}>
            <Grid container spacing={3} alignItems={"center"}>
              <Grid item xs={12} md={12}>
                <FormControl fullWidth>
                  <Controller
                    name="name"
                    control={control}
                    defaultValue=""
                    render={({ field }) => (
                      <TextField
                        {...field}
                        size="small"
                        label=" Search by Name"
                        placeholder="Search By Name"
                        InputLabelProps={{ shrink: true }}
                        aria-describedby="users"
                        value={field.value}
                      />
                    )}
                  />
                </FormControl>
              </Grid>
               <Grid item xs={12} md={12}>
                <FormControl fullWidth>
                  <Controller
                    name="companyName"
                    control={control}
                    defaultValue=""
                    render={({ field }) => (
                      <TextField
                        {...field}
                        size="small"
                        label=" Search by Company Name"
                        placeholder="Search By Company Name"
                        InputLabelProps={{ shrink: true }}
                        aria-describedby="users"
                        value={field.value}
                      />
                    )}
                  />
                </FormControl>
              </Grid>
              <Grid item xs={12} md={12}>
                <FormControl fullWidth>
                  <Controller
                    name="email"
                    control={control}
                    defaultValue=""
                    render={({ field }) => (
                      <TextField
                        {...field}
                        size="small"
                        label=" Search by Email"
                        placeholder="Search By Email"
                        InputLabelProps={{ shrink: true }}
                        aria-describedby="users"
                        value={field.value}
                      />
                    )}
                  />
                </FormControl>
              </Grid>
              <Grid item xs={12}>
                <FormControl fullWidth>
                  <Controller
                    name="mobileNumber"
                    control={control}
                    render={({ field }) => (
                      <MobileNumberValidation
                        {...field}
                        type="tel"
                        label="Mobile Number"
                        size="small"
                        placeholder="Contact Number"
                        error={Boolean(errors.mobileNumber)}
                        helperText={errors.mobileNumber?.message}
                        aria-describedby="validation-Mobile Number"
                      />
                    )}
                  />
                </FormControl>
              </Grid>
              <Grid item xs={12}>
                <SelectClearAutoComplete
                  id={"selectRoleId"}
                  label={"Select Role"}
                  name="selectRoleId"
                  nameArray={convertedArray}
                  defaultValue={selectRoleId}
                  value={selectRoleId}
                  onChange={(event) => handleRoleChange(event.target.value)} // Use the updated handler
                  aria-describedby="selectRoleId"
                />
              </Grid>
            </Grid>
          </Box>
        </PerfectScrollbar>
 
        <Box
          sx={{
            borderTop: (theme) => `1px solid ${theme.palette.divider}`,
            p: (theme) => theme.spacing(2),
            justifyContent: "flex-end",
            display: "flex",
            alignItems: "center",
          }}
        >
          <Button variant="tonal" sx={{ mr: 3 }} onClick={handleCancel}>
            Clear All
          </Button>
          <Button variant="contained" onClick={handleSubmit(handleApply)} sx={{ mr: {
                xs: 4, 
                sm: 4, 
                md: 4, 
                lg: 4, 
                xl: 4,
              },} }>
            Apply
          </Button>
        </Box>
      </Drawer>
    </>
  );
};
 
export default AdvancedSearch;