// ** MUI Imports
import Typography from "@mui/material/Typography";
import { useContext, useEffect, useState } from "react";

// ** Custom Components Imports
import axios from "axios";
import AccordionBasic from "src/@core//components/custom-components/AccordionBasic";
import authConfig from "src/configs/auth";
import { getAuthorizationHeaders, getUrl } from "src/helpers/utils";
// ** Demo Components Imports
import { useTheme } from "@emotion/react";

import Icon from "src/@core/components/icon";

// ** Styled Component
import {
  Box,
  Grid,
  Divider,
  TableCell,
  Button,
  Card,
  Dialog,
  TextField,
  Select,
  DialogActions,
  DialogContent,
  DialogTitle,
  IconButton,
  Table,
  TableBody,
  TableHead,
  TableContainer,
  TableRow,
} from "@mui/material";
import MenuItem from "@mui/material/MenuItem";

import { AuthContext } from "src/context/AuthContext";
import MUITableCell from "src/pages/SP/MUITableCell";

import UploadFile from "src/@core/components/custom-components/UploadFile";


const field = {
  fontWeight: 500,
  fontSize: "12.75px",
};

const tableCellStyle = {
  paddingLeft: 60,
  fontWeight: "bold",
  fontSize: "12px",
  height: "10px",
  paddingTop: "1px",
  paddingBottom: "1px",
};

const SubmitBoqDialog = ({
  open,
  onClose,
  role,
  data,
  fetchRequisitions,
  employeesData,
}) => {
  // ** Hook
  const theme = useTheme();

  const [dataView, setDataView] = useState({});

  const [designation, setDesignation] = useState("");

  const [subCategories, setSubCategories] = useState([]);
  const [listOfSubCategories, setListOfSubCategories] = useState([]);
  const [selectedFiles, setSelectedFiles] = useState([]);

  useEffect(() => {
    const fetchSocieties = async () => {
      axios({
        method: "get",
        url: getUrl(authConfig.selectDropdownNew) + "?selectionType=SOCIETY_NAME",
        headers: getAuthorizationHeaders(),
      })
        .then((res) => {
          const metadataArray = res.data?.data?.map((item) => item?.metaData);
          const matchingSociety = metadataArray.find(
            (society) => society?.userId === data?.userId
          );

          // Set the matching location as the defaultValue for the SelectCategory
          if (matchingSociety) {
            setDataView({
              ...dataView, // This keeps the existing properties
              ...matchingSociety, // This adds/updates properties from newValue
            });

            const loc = matchingSociety?.designation
              ? listOfSubCategories.find(
                  (item) => item.value === matchingSociety?.designation
                )?.key
              : null;

            setDesignation(loc);
          }
        })
        .catch((err) => console.log("error", err));
    };
    fetchSocieties();
  }, [data]);

  const [rows, setRows] = useState([
    { date: "", description: "", amount: "" },
  ]);

  const handleAddRow = () => {
    setRows([...rows, { date: "", description: "", amount: "" }]);
  };

  const handleDeleteRow = (index) => {
    setRows(rows.filter((_, rowIndex) => rowIndex !== index));
  };

  const handleInputChange = (index, field, value) => {
    const newRows = [...rows];
    newRows[index][field] = value;
    setRows(newRows);
  };

  const handleClosePayment = () => {
    onClose();
    setRows([{ date: "", description: "", amount: "" }]); // Reset rows
  };

  const { user, listValues,getAllListValuesByListNameId } = useContext(AuthContext);

    const handleError = (error) => console.error("Error:", error);
  
    useEffect(() => {
      if (!!authConfig) {
        getAllListValuesByListNameId(
          authConfig.ChsDesignation,
          (data) =>
            setListOfSubCategories(
              data?.listValues?.map((item) => ({
                value: item.id,
                key: item.listValue,
              }))
            ),
          handleError
        );
      }
    }, [authConfig]);

  const [openEditDialog, setOpenEditDialog] = useState(false);
  const [conversation, setConversation] = useState({});

  const handleCloseEditDialog = () => {
    setOpenEditDialog(false);
  };

  const [openMoreInfoDialog, setOpenMoreInfoDialog] = useState(false);

  const handleDialogClose = () => {
    setOpenMoreInfoDialog(false);
  };

  const handleOpenEditDialog = () => {
    setOpenEditDialog(true);
  };

  const serviceType = data?.requisitionData?.serviceType
    ? listValues?.find((item) => item.id === data?.requisitionData?.serviceType)
        ?.name
    : null;

  const priorityName = data?.requisitionData?.priority
    ? listValues?.find((item) => item.id === data?.requisitionData?.priority)
        ?.name
    : null;

  const getNamesFromIds = (ids, listValues) => {
    return ids?.map((id) => {
      const foundItem = listValues?.find((item) => item?.id === id);
      return foundItem ? foundItem?.name : null;
    });
  };

  const [specifications, setSpecifications] = useState([]);

  const [userList, setUserList] = useState([]);

  useEffect(() => {
    if (data?.requisitionData?.serviceType) {
      const fetchAll = async (serviceId, data) => {
        const url = `${getUrl(
          authConfig.getAllServiceProfiles
        )}/${serviceId}/requisitionFields`;
        const headers = getAuthorizationHeaders();

        try {
          const response = await axios({
            method: "get",
            url: url,
            headers: headers,
            data: data,
          });

          if (response.data) {
            setUserList(response.data);
          } else {
            console.error("Unexpected API response format:", response);
          }
        } catch (error) {
          console.error("Error fetching users:", error);
        }
      };

      const serviceId = data?.requisitionData?.serviceType;
      fetchAll(serviceId);
    }
  }, [data]);

  useEffect(() => {
    if (data?.requisitionData?.specifications?.listNames.length > 0) {
      // Create a mapping of listValueId to listValue
      const listValueMap = userList?.reduce((map, item) => {
        item?.values?.forEach((value) => {
          map[value.id] = { id: value.id, name: value.name };
        });
        return map;
      }, {});

      // Get all list names from specifications
      const listNames = userList?.map((item) => {
        const metadataItem =
          data?.requisitionData?.specifications?.listNames?.find(
            (list) => list.listNameId === item.id
          );
        const otherValue = metadataItem ? metadataItem.otherValue : null;

        return {
          id: item.id,
          name: item.name,
          otherValue: otherValue,
          values:
            metadataItem && metadataItem.listValues.length
              ? metadataItem.listValues.map(
                  (value) => listValueMap[value.listValueId]
                )
              : [],
        };
      });
      setSpecifications(listNames);
    }
  }, [data, userList]);

  // Using the function to get names
  const names = getNamesFromIds(data?.requisitionData?.subServices, listValues);

  const subServices = names?.filter((name) => name !== null).join(", ");

  const assignedName = data?.assignedTo
    ? employeesData?.find((item) => item.id === data?.assignedTo)?.name
    : null;

  const status = data?.status
    ? listValues?.find((item) => item.id === data?.status)?.name
    : null;

  const teamMember = data?.teamMember
    ? employeesData?.find((item) => item.id === data?.teamMember)?.name
    : null;

  const handleClose = () => {
    onClose();
    setUserList([]);
    setSpecifications([]);
  };

  return (
    <>
      <Dialog open={open} onClose={handleClose} fullScreen>
        <DialogTitle
          sx={{
            position: "relative",
            borderBottom: (theme) => `1px solid ${theme.palette.divider}`,
            p: (theme) => `${theme.spacing(1.75, 4)} !important`,
            display: "flex",
            alignItems: "center",
            justifyContent: "start",
            fontSize: { xs: 19, md: 20 },
          }}
          textAlign={"center"}
        >
          Submit Revised / Final (BOQ) quote
          <Box sx={{ position: "absolute", top: "4px", right: "12px" }}>
            <IconButton
              size="small"
              onClick={handleClose}
              sx={{
                // p: "0.438rem",
                borderRadius: 1,
                color: "common.white",
                backgroundColor: "primary.main",
                "&:hover": {
                  backgroundColor: "#66BB6A",
                  transition: "background 0.5s ease, transform 0.5s ease",
                },
              }}
            >
              <Icon icon="tabler:x" fontSize="1rem" />
            </IconButton>
          </Box>
        </DialogTitle>
        <DialogContent
          sx={{
            position: "relative",
            pb: (theme) => `${theme.spacing(5)} !important`,
            px: (theme) => [`${theme.spacing(8)} !important`],
          }}
        >
          <>

          
          <Card>
              <AccordionBasic
                id={"panel-header-2"}
                ariaControls={"panel-content-2"}
                heading={"Requisition Details"}
                body={
                  <>
                    <TableContainer
                      sx={{ padding: "4px 6px", paddingTop: "0px !important" }}
                      className="tableBody"
                    >
                      <Table>
                        <TableBody
                          sx={{
                            "& .MuiTableCell-root": {
                              p: `${theme.spacing(1.35, 1.125)} !important`,
                            },
                          }}
                        >
                           <TableRow>
                            <MUITableCell
                              sx={{
                                textAlign: "right",
                                width: "50%",
                                paddingRight: theme.spacing(4),
                              }}
                            >
                              <Typography style={field}>
                                Service Requisition No:
                              </Typography>
                            </MUITableCell>
                            <MUITableCell
                              sx={{
                                textAlign: "left",
                                width: "50%",
                                paddingLeft: theme.spacing(2),
                              }}
                            >
                              <Typography
                                className="data-field"
                                style={{ lineHeight: "1.2" }}
                              >
                                SR1234567890123
                              </Typography>
                            </MUITableCell>
                          </TableRow>
                          <TableRow>
                            <MUITableCell
                              sx={{
                                textAlign: "right",
                                width: "50%",
                                paddingRight: theme.spacing(4),
                              }}
                            >
                              <Typography style={field}>
                                Society Name:
                              </Typography>
                            </MUITableCell>
                            <MUITableCell
                              sx={{
                                textAlign: "left",
                                width: "50%",
                                paddingLeft: theme.spacing(2),
                              }}
                            >
                              <Typography
                                className="data-field"
                                style={{ lineHeight: "1.2" }}
                              >
                                Manjeera Trinity Society
                              </Typography>
                            </MUITableCell>
                          </TableRow>
                          <TableRow>
                            <MUITableCell
                              sx={{
                                textAlign: "right",
                                width: "50%",
                                paddingRight: theme.spacing(4),
                              }}
                            >
                              <Typography style={field}>
                                Service Type:
                              </Typography>
                            </MUITableCell>
                            <MUITableCell
                              sx={{
                                textAlign: "left",
                                width: "50%",
                                paddingLeft: theme.spacing(2),
                              }}
                            >
                              <Typography
                                className="data-field"
                                style={{ lineHeight: "1.2" }}
                              >
                                CCTV & Security Devices
                              </Typography>
                            </MUITableCell>
                          </TableRow>
                          
                     

                          {data?.requisitionData?.subServices?.length > 0 && (
                            <TableRow>
                              <MUITableCell
                                sx={{
                                  textAlign: "right",
                                  width: "50%",
                                  paddingRight: theme.spacing(4),
                                }}
                              >
                                <Typography style={field}>
                                  Sub Services:
                                </Typography>
                              </MUITableCell>
                              <MUITableCell
                                sx={{
                                  textAlign: "left",
                                  width: "50%",
                                  paddingLeft: theme.spacing(2),
                                }}
                              >
                                <Typography
                                  className="data-field"
                                  style={{ lineHeight: "1.2" }}
                                >
                                  {subServices}
                                </Typography>
                              </MUITableCell>
                            </TableRow>
                          )}

                          {data?.requisitionData?.anyOtherServices && (
                            <TableRow>
                              <MUITableCell
                                sx={{
                                  textAlign: "right",
                                  width: "50%",
                                  paddingRight: theme.spacing(4),
                                }}
                              >
                                <Typography style={field}>
                                  Other Services:
                                </Typography>
                              </MUITableCell>
                              <MUITableCell
                                sx={{
                                  textAlign: "left",
                                  width: "50%",
                                  paddingLeft: theme.spacing(2),
                                }}
                              >
                                <Typography
                                  className="data-field"
                                  style={{ lineHeight: "1.2" }}
                                >
                                  {data?.requisitionData?.anyOtherServices}
                                </Typography>
                              </MUITableCell>
                            </TableRow>
                          )}

                          <TableRow>
                            <MUITableCell
                              sx={{
                                textAlign: "right",
                                width: "50%",
                                paddingRight: theme.spacing(4),
                              }}
                            >
                              <Typography style={field}>Priority:</Typography>
                            </MUITableCell>
                            <MUITableCell
                              sx={{
                                textAlign: "left",
                                width: "50%",
                                paddingLeft: theme.spacing(2),
                              }}
                            >
                              <Typography
                                className="data-field"
                                style={{ lineHeight: "1.2" }}
                              >
                                {priorityName}
                              </Typography>
                            </MUITableCell>
                          </TableRow>

                          <TableRow>
                            <MUITableCell
                              sx={{
                                textAlign: "right",
                                width: "50%",
                                paddingRight: theme.spacing(4),
                              }}
                            >
                              <Typography style={field}>Budget:</Typography>
                            </MUITableCell>
                            <MUITableCell
                              sx={{
                                textAlign: "left",
                                width: "50%",
                                paddingLeft: theme.spacing(2),
                              }}
                            >
                              <Typography
                                className="data-field"
                                style={{ lineHeight: "1.2" }}
                              >
                                {data?.requisitionData?.budget}
                              </Typography>
                            </MUITableCell>
                          </TableRow>

                          <TableRow>
                            <MUITableCell
                              sx={{
                                textAlign: "right",
                                width: "50%",
                                paddingRight: theme.spacing(4),
                              }}
                            >
                              <Typography style={field}>
                                Requirement Dead Line:
                              </Typography>
                            </MUITableCell>
                            <MUITableCell
                              sx={{
                                textAlign: "left",
                                width: "50%",
                                paddingLeft: theme.spacing(2),
                              }}
                            >
                              <Typography
                                className="data-field"
                                style={{ lineHeight: "1.2" }}
                              >
                                {data?.requisitionData?.requirementDeadLine}
                              </Typography>
                            </MUITableCell>
                          </TableRow>

                          <TableRow>
                            <MUITableCell
                              sx={{
                                textAlign: "right",
                                width: "50%",
                                paddingRight: theme.spacing(4),
                              }}
                            >
                              <Typography style={field}>
                                Society Remarks:
                              </Typography>
                            </MUITableCell>
                            <MUITableCell
                              sx={{
                                textAlign: "left",
                                width: "50%",
                                paddingLeft: theme.spacing(2),
                              }}
                            >
                              <Typography
                                className="data-field"
                                style={{ lineHeight: "1.2" }}
                              >
                                {data?.requisitionData?.societyRemarks}
                              </Typography>
                            </MUITableCell>
                          </TableRow>

                          {specifications
                            ?.slice()
                            ?.sort((a, b) => a.listSequence - b.listSequence)
                            ?.map((category) =>
                              category.values.length > 0 ||
                              category.otherValue ? (
                                <TableRow key={category.name}>
                                  <MUITableCell
                                    sx={{
                                      textAlign: "right",
                                      width: "50%",
                                      paddingRight: theme.spacing(4),
                                    }}
                                  >
                                    <Typography style={field}>
                                      {category.name}
                                    </Typography>
                                  </MUITableCell>
                                  <MUITableCell
                                    sx={{
                                      textAlign: "left",
                                      width: "50%",
                                      paddingLeft: theme.spacing(2),
                                    }}
                                  >
                                    {category?.values?.length > 0
                                      ? category?.values?.map(
                                          (value, index) => (
                                            <Typography
                                              key={index}
                                              className="data-field"
                                              style={{ lineHeight: "1.2" }}
                                            >
                                              {value.name}
                                            </Typography>
                                          )
                                        )
                                      : category?.otherValue && (
                                          <Typography
                                            className="data-field"
                                            style={{ lineHeight: "1.2" }}
                                          >
                                            {category.otherValue}
                                          </Typography>
                                        )}
                                  </MUITableCell>
                                </TableRow>
                              ) : null
                            )}
                        </TableBody>
                      </Table>
                    </TableContainer>
                  </>
                }
                expanded={false}
              />
            </Card>
            
            <AccordionBasic
              id={"panel-header-2"}
              ariaControls={"panel-content-2"}
              heading={"Payment schedule"}
              body={
                <TableContainer>
                  <Table sx={{ minWidth: 650 }}>
                    <TableBody>
                      {rows.map((row, index) => (
                        <TableRow key={index}>
                          <TableCell>
                            <TextField
                              type="date"
                              value={row.date}
                              onChange={(e) =>
                                handleInputChange(index, "date", e.target.value)
                              }
                              fullWidth
                              InputProps={{
                                sx: { height: '40px' } // Adjust the height
                              }}
          
                              
                            />
                          </TableCell>
                          <TableCell>
                            <TextField
                              type="text"
                              value={row.description}
                              onChange={(e) =>
                                handleInputChange(
                                  index,
                                  "description",
                                  e.target.value
                                )
                              }
                              fullWidth
                              placeholder="Description"
                              InputProps={{
                                sx: { height: '40px' } // Adjust the height
                              }}
                            />
                          </TableCell>
                          <TableCell>
                            <TextField
                              type="number"
                              value={row.amount}
                              onChange={(e) =>
                                handleInputChange(index, "amount", e.target.value)
                              }
                              fullWidth
                              placeholder="Amount"
                              InputProps={{
                                sx: { height: '40px' } // Adjust the height
                              }}
                            />
                          </TableCell>
                          <TableCell>
                            <IconButton
                              size="small"
                              onClick={() => handleDeleteRow(index)}
                              // disabled={disableDelete}
                              color="error"
                            >
                              <Icon icon="tabler:trash" />
                            </IconButton>
                            <IconButton
                              size="small"
                              onClick={handleAddRow}
                              color="success"
                            >
                              <Icon icon="tabler:plus" />
                            </IconButton>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              }
            />
         


{/* <SubmitBoqWizard/> */}




































{/*  */}
            {/* <Card>
              <AccordionBasic
                id={"panel-header-2"}
                ariaControls={"panel-content-2"}
                heading={" Section 1 (Wi-Fi Camera)"}
                body={
                  <>
                    <TableContainer>
                      <Table>
                        <TableHead>
                          <TableRow>
                            <TableCell
                              style={{
                                paddingLeft: 5,
                                fontWeight: "bold",
                                fontSize: "12px",
                                height: "10px",
                                paddingTop: "1px",
                                paddingBottom: "1px",
                              }}
                            >
                              S.No.
                            </TableCell>
                            <TableCell
                              style={{
                                paddingLeft: 5,
                                fontWeight: "bold",
                                fontSize: "12px",
                                height: "10px",
                                paddingTop: "1px",
                                paddingBottom: "1px",
                              }}
                            >
                              Description
                            </TableCell>
                            <TableCell style={tableCellStyle}>Brand</TableCell>
                            <TableCell style={tableCellStyle}>Model</TableCell>
                            <TableCell style={tableCellStyle}>Memory</TableCell>
                            <TableCell style={tableCellStyle}>
                              Quantity
                            </TableCell>
                            <TableCell style={tableCellStyle}>Rate</TableCell>
                          </TableRow>
                        </TableHead>
                        <TableBody>
                          {[
                            "Wifi Camera",
                            "Memory Card",
                            "HD TV Monitor",
                            "4K HDMI Extender",
                            "Installation Charges",
                          ].map((description, index) => (
                            <TableRow key={index}>
                              <TableCell
                                style={{
                                  paddingLeft: 5,
                                  fontWeight: "bold",
                                  fontSize: "12px",
                                  height: "10px",
                                  paddingTop: "1px",
                                  paddingBottom: "1px",
                                }}
                              >
                                {index + 1}
                              </TableCell>
                              <TableCell
                                style={{
                                  paddingLeft: 5,
                                  fontWeight: "bold",
                                  fontSize: "12px",
                                  height: "10px",
                                  paddingTop: "1px",
                                  paddingBottom: "1px",
                                }}
                              >
                                {description}
                              </TableCell>
                              <TableCell style={tableCellStyle}>
                                <TextField
                                  placeholder="Brand"
                                  size="small"
                                  variant="outlined"
                                  fullWidth
                                  sx={{ width: "100px" }}
                                />
                              </TableCell>
                              <TableCell style={tableCellStyle}>
                                <TextField
                                  placeholder="Model"
                                  size="small"
                                  variant="outlined"
                                  fullWidth
                                  sx={{ width: "100px" }}
                                />
                              </TableCell>
                              <TableCell style={tableCellStyle}>
                                <TextField
                                  placeholder="Memory"
                                  size="small"
                                  variant="outlined"
                                  fullWidth
                                  sx={{ width: "100px" }}
                                />
                              </TableCell>
                              <TableCell style={tableCellStyle}>
                                <TextField
                                  placeholder="Quantity"
                                  size="small"
                                  variant="outlined"
                                  fullWidth
                                  type="number"
                                  sx={{ width: "100px" }}
                                />
                              </TableCell>
                              <TableCell style={tableCellStyle}>
                                <TextField
                                  placeholder="Rate"
                                  size="small"
                                  variant="outlined"
                                  fullWidth
                                  type="number"
                                  sx={{ width: "100px" }}
                                />
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </TableContainer>
                  </>
                }
              /> */}
              {/* <Grid
    sx={{
      backgroundColor: "#f2f7f2",
      mt: 4,
      paddingTop: 0,
      height: "36px",
      display: "flex",
      alignItems: "center",
    }}
  >
    <Typography variant="body1" fontWeight={"bold"} sx={{ mt: 0, ml: 2 }}>
      Section 1 (Wi-Fi Camera)
    </Typography>
    <Divider />
  </Grid> */}
              {/* <Divider /> */}
            {/* </Card> */}

            {/* <Card> */}
              {/* <Grid
                sx={{
                  backgroundColor: "#f2f7f2",
                  mt: 4,
                  paddingTop: 0,
                  height: "36px",
                  display: "flex",
                  alignItems: "center",
                }}
              >
                <Typography
                  variant="body1"
                  fontWeight={"bold"}
                  sx={{ mt: 0, ml: 2 }}
                >
                  Section 2 (DVR/NVR Camera)
                </Typography>
                <Divider />
              </Grid>
              <Divider /> */}

              {/* <AccordionBasic
                id={"panel-header-2"}
                ariaControls={"panel-content-2"}
                heading={"Section 2 (DVR/NVR Camera)"}
                body={
                  <>
                    <TableContainer>
                      <Table>
                        <TableHead>
                          <TableRow>
                            <TableCell style={{ width: "60px" }}>
                              S.No.
                            </TableCell>
                            <TableCell style={{ width: "250px" }}>
                              Description
                            </TableCell>
                            <TableCell>Type of System</TableCell>
                            <TableCell>Megapixel</TableCell>
                            <TableCell>Ampere</TableCell>
                            <TableCell>Brand</TableCell>
                            <TableCell>Model</TableCell>
                            <TableCell>Memory</TableCell>
                            <TableCell>Inches</TableCell>
                            <TableCell>Channel</TableCell>
                            <TableCell>Recording</TableCell>
                            <TableCell>Units</TableCell>
                            <TableCell>Quantity</TableCell>
                            <TableCell>Rate(₹)</TableCell>
                          </TableRow>
                        </TableHead>
                        <TableBody>
                          {[
                            "Bullet Camera",
                            "Dome Camera",
                            "Supply with Spike",
                            "Hard Disk",
                            "4 X 4 Junction Box",
                            "4 + 1 Cable Supply",
                            "Installation Charges",
                            "Rack",
                            "HD TV Monitor",
                            "4K HDMI Extender",
                            "Cat 6 Cable",
                          ].map((description, index) => (
                            <TableRow key={index}>
                              <TableCell>{index + 1}</TableCell>
                              <TableCell
                                style={{
                                  fontWeight: "bold",
                                  // backgroundColor: "#f7f2e0",
                                }}
                              >
                                {description}
                              </TableCell>
                              <TableCell>
                                <Select
                                  size="small"
                                  variant="outlined"
                                  fullWidth
                                >
                                  <MenuItem value="DVR">DVR</MenuItem>
                                  <MenuItem value="NA">NA</MenuItem>
                                </Select>
                              </TableCell>
                              <TableCell>
                                <TextField
                                  placeholder="Megapixel"
                                  size="small"
                                  variant="outlined"
                                  fullWidth
                                  sx={{ width: "100px" }}
                                />
                              </TableCell>
                              <TableCell>
                                <TextField
                                  placeholder="Ampere"
                                  size="small"
                                  variant="outlined"
                                  fullWidth
                                  sx={{ width: "100px" }}
                                />
                              </TableCell>
                              <TableCell>
                                <TextField
                                  placeholder="Brand"
                                  size="small"
                                  variant="outlined"
                                  fullWidth
                                  sx={{ width: "100px" }}
                                />
                              </TableCell>
                              <TableCell>
                                <TextField
                                  placeholder="Model"
                                  size="small"
                                  variant="outlined"
                                  fullWidth
                                  sx={{ width: "100px" }}
                                />
                              </TableCell>
                              <TableCell>
                                <TextField
                                  placeholder="Memory"
                                  size="small"
                                  variant="outlined"
                                  fullWidth
                                  sx={{ width: "100px" }}
                                />
                              </TableCell>
                              <TableCell>
                                <TextField
                                  placeholder="Inches"
                                  size="small"
                                  variant="outlined"
                                  fullWidth
                                  sx={{ width: "70px" }}
                                />
                              </TableCell>
                              <TableCell>
                                <TextField
                                  placeholder="Channel"
                                  size="small"
                                  variant="outlined"
                                  fullWidth
                                  sx={{ width: "100px" }}
                                />
                              </TableCell>
                              <TableCell>
                                <Select
                                  size="small"
                                  variant="outlined"
                                  fullWidth
                                >
                                  <MenuItem value="Both">Both</MenuItem>

                                  <MenuItem value="NA">NA</MenuItem>
                                </Select>
                              </TableCell>
                              <TableCell>
                                <TextField
                                  placeholder="Units"
                                  size="small"
                                  variant="outlined"
                                  fullWidth
                                  sx={{ width: "70px" }}
                                />
                              </TableCell>
                              <TableCell>
                                <TextField
                                  placeholder="Quantity"
                                  size="small"
                                  variant="outlined"
                                  type="number"
                                  fullWidth
                                  sx={{ width: "100px" }}
                                />
                              </TableCell>
                              <TableCell>
                                <TextField
                                  placeholder="Rate"
                                  type="number"
                                  size="small"
                                  variant="outlined"
                                  fullWidth
                                  sx={{ width: "100px" }}
                                />
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </TableContainer>
                  </>
                }
              />
            </Card> */}
{/*  */}
            
          </>
        </DialogContent>
        {/* <DialogActions
          sx={{
            justifyContent: "end",
            borderTop: (theme) => `1px solid ${theme.palette.divider}`,
            p: (theme) => `${theme.spacing(2.5)} !important`,
          }}
        >
          <Button
            display="flex"
            justifyContent="center"
            variant="outlined"
            color="primary"
            onClick={handleClose}
          >
            Cancel
          </Button>
          <Button
            display="flex"
            justifyContent="center"
            variant="contained"
            color="primary"
            //onClick={handleClose}
          >
            Submit
          </Button>
        </DialogActions> */}
      </Dialog>
    </>
  );
};
export default SubmitBoqDialog;
