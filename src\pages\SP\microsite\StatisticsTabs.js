import React, { useState, useEffect } from "react";
import Tab from "@mui/material/Tab";
import TabList from "@mui/lab/TabList";
import TabPanel from "@mui/lab/TabPanel";
import TabContext from "@mui/lab/TabContext";
import { Grid } from "@mui/material";
import StatCard from "./StatCard";

const StatisticsTabs = (props) => {
  const { tabContents, statisticsData, onTabChange, listNames } = props;
  const [value, setValue] = useState("1");

  useEffect(() => {
    if (tabContents && tabContents.length > 0) {
      setValue(tabContents[0].id.toString());
      onTabChange(tabContents[0].id);
    }
  }, [tabContents]);

  const handleServiceChange = (event, newValue) => {
    setValue(newValue);
    const selectedService = tabContents.find((tab) => tab.id === newValue);
    if (selectedService) {
      onTabChange(selectedService.id);
    }
  };

  const getNameById = (id) => {
    const matchedName = listNames.find((item) => item.id === id);

    return matchedName ? matchedName.name : "";
  };

  const imagePaths = [
    "/images/microsite/totalNoProjects.png",
    "/images/microsite/totalCatered.png",
    "/images/microsite/constructedAreaOnGoingProj.png",
    "/images/microsite/noOfOnGoingProj.png",
  ];

  let imageIndex = 0;

  const getNextImagePath = () => {
    const nextImagePath = imagePaths[imageIndex % imagePaths.length];
    imageIndex++;

    return nextImagePath;
  };

  return (
    <TabContext value={value}>
      <TabList
        variant="scrollable"
        scrollButtons="auto"
        aria-label="statistics tabs"
      >
        {tabContents?.map((tabContent) => (
          <Tab
            key={tabContent.id}
            value={tabContent.id}
            label={tabContent.name}
            onClick={(e) => handleServiceChange(e, tabContent.id)}
          />
        ))}
      </TabList>

      {tabContents?.map((tabContent) => (
        <TabPanel
          key={tabContent.id}
          value={tabContent.id}
          sx={{ minHeight: '150px', height: 'auto', marginTop: '40px' }}
        >
          {statisticsData?.metadata?.listNames?.length ? (
            <Grid container spacing={3}>
              {statisticsData.metadata.listNames.map(
                (data) =>
                  data.otherValue != null &&
                  data.otherValue !== "" && (
                    <Grid item xs={12} sm={6} lg={3} key={data.listNameId}>
                      <StatCard
                        icon={getNextImagePath()}
                        title={getNameById(data.listNameId)}
                        value={data.otherValue}
                      />
                    </Grid>
                  )
              )}
            </Grid>
          ) : (
            <div>No data found</div>
          )}
        </TabPanel>
      ))}
    </TabContext>
  );
};

export default StatisticsTabs;
