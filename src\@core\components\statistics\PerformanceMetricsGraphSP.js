import { Card, useMediaQuery, useTheme } from "@mui/material";
import axios from "axios";
import dynamic from "next/dynamic";
import { useRouter } from "next/router";
import { useEffect, useState } from "react";
import authConfig from "src/configs/auth";
import { getAuthorizationHeaders, getUrl } from "src/helpers/utils";

// Dynamically import 'react-apexcharts' to avoid SSR issues
const ApexChart = dynamic(() => import("react-apexcharts"), { ssr: false });

const PerformanceMetricsGraph = () => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("sm")); // Check if screen size is mobile
  const isTablet = useMediaQuery(theme.breakpoints.between("sm", "md")); // Check if screen size is tablet

   const router = useRouter();
  // Metrics for the x-axis
  const metrics = ["Today", "This Week", "This Month", "Uptil Now"];

  const [sampleData, setSampleData] = useState({
    createdCount: {},
    assignedCount: {},
  }); // Properly initialize as an object

  useEffect(() => {
    axios({
      method: "get",
      url:
        getUrl(authConfig.statisticsEndpointGraphs) +
        "/individual-count-as-created-by-assigned-to?/role=SERVICE_PROVIDER",
      headers: getAuthorizationHeaders(),
    })
      .then((res) => {
        setSampleData(res?.data || { createdCount: {}, assignedCount: {} }); // Provide fallback data
      })
      .catch((err) => console.log("Employees error", err));
  }, []);

  // Safely extract data for Created and Assigned
  const createdData =
    Object.values(sampleData?.createdCount || {})?.length > 0
      ? Object.values(sampleData.createdCount)
      : [0, 0, 0, 0]; // Fallback data
  const assignedData =
    Object.values(sampleData?.assignedCount || {})?.length > 0
      ? Object.values(sampleData.assignedCount)
      : [0, 0, 0, 0]; // Fallback data

  const handleBarClick = (event, chartContext, config) => {
    const clickedIndex = config.dataPointIndex;

    const clickedSeriesName = config.seriesIndex === 0 ? 'createdData' : 'assignedData';

    // Initialize the query parameters
    const queryParams = {};

    // Determine whether to pass createdBy or assignedTo
    if (clickedSeriesName === 'createdData') {
      queryParams.seriesName = "createdBy";
    } else if (clickedSeriesName === 'assignedData') {
      queryParams.seriesName = "assignedTo";
    }

    // Add specific duration-based parameters based on the clicked index
    if (clickedIndex === 0) {
      queryParams.duration = "daily";
    } else if (clickedIndex === 1) {
      queryParams.duration = "weekly";
    } else if (clickedIndex === 2) {
      queryParams.duration = "monthly";
    }

    // Navigate to the SP page with the constructed query
    router.push({
      pathname: "/SP",
      query: queryParams,
    });
  };

  const state = {
    series: [
      {
        name: "Created",
        data: createdData,
        color: "rgba(141, 223, 141, 1)", 
      },
      {
        name: "Assigned",
        data: assignedData,
        color: "rgba(16, 138, 0, 1)", 
      },
    ],
    options: {
      chart: {
        type: "bar",
        stacked: false,
        toolbar: {
          show: false, // Hide toolbar for cleaner UI
        },
        id: "performance-metrics-SP-bar-chart",
        // events: {
        //   dataPointSelection: handleBarClick, // Add click event handler
        // },
      },
      plotOptions: {
        bar: {
          horizontal: false, // Vertical bars
          columnWidth: isMobile ? "70%" : "50%", // Responsive column width
        },
      },
      stroke: {
        width: 1,
        colors: ["#fff"], // White strokes for better bar separation
      },
      title: {
        text: "SP's Managed by Me",
        align: "center",
        style: {
          fontSize: isMobile ? "14px" : isTablet ? "15px" : "16px",
        },
      },
      xaxis: {
        categories: metrics,
        labels: {
          rotate: isMobile ? -45 : 0, // Rotate labels for better readability on mobile
          style: {
            fontSize: isMobile ? "8px" : isTablet ? "10px" : "11px",
          },
        },
      },
      yaxis: {
        title: {
          text: "No. of SP's",
          style: {
            fontSize: isMobile ? "10px" : isTablet ? "11px" : "12px",
          },
        },
        labels: {
          style: {
            fontSize: isMobile ? "10px" : isTablet ? "11px" : "12px",
          },
        },
      },
      tooltip: {
        y: {
          formatter: (val) => val, // Tooltip shows raw values
        },
      },
      fill: {
        opacity: 1, // Solid fill
      },
      legend: {
        position: "top",
        horizontalAlign: "right",
        offsetX: 40,
        labels: {
          style: {
            fontSize: isMobile ? "10px" : isTablet ? "11px" : "12px",
          },
        },
      },
      dataLabels: {
        enabled: true,
        style: {
          fontSize: isMobile ? "10px" : isTablet ? "11px" : "12px",
          colors: ["#000"], // Black text for better contrast
        },
      },
    },
  };

  return (
    <Card sx={{ p: 3 }}>
      <ApexChart
        options={state.options}
        series={state.series}
        type="bar"
        height={isMobile ? 300 : isTablet ? 350 : 400}
      />
    </Card>
  );
};

export default PerformanceMetricsGraph;
