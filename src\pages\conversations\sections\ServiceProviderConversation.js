import React, { useContext, useEffect, useState, useRef } from "react";
import Typography from "@mui/material/Typography";
import {
  Button,
  Table,
  TableBody,
  TableContainer,
  TableRow,
  Box,
  IconButton,
  Divider,
  DialogContentText,
  Tooltip,
  
} from "@mui/material";
import Grid from "@mui/material/Grid";
import FormControl from "@mui/material/FormControl";
import InputLabel from "@mui/material/InputLabel";
import MenuItem from "@mui/material/MenuItem";
import Select from "@mui/material/Select";
import { useForm } from "react-hook-form";
import FormHelperText from "@mui/material/FormHelperText";
import AccordionBasic from "src/@core/components/custom-components/AccordionBasic";
import MUITableCell from "src/pages/SP/MUITableCell";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import Icon from "src/@core/components/icon";
import authConfig from "src/configs/auth";
import TextField from "@mui/material/TextField";
import CloseIcon from "@mui/icons-material/Close";
import Dialog from "@mui/material/Dialog";
import DialogActions from "@mui/material/DialogActions";
import DialogContent from "@mui/material/DialogContent";
import DialogTitle from "@mui/material/DialogTitle";
import { Controller } from "react-hook-form";

import { AuthContext } from "src/context/AuthContext";
import { useAuth } from "src/hooks/useAuth";
import SelectAutoComplete from "src/@core/components/custom-components/SelectAutoComplete";

const ServiceProviderConversation = ({
  userId,
  data,
  conversationTypeData,
  outcomeConversationData,
  targetData,
  shallRemindData,
  handleOpenConversationDialog,
  openConversationDialog,
  fetchUsers,
  setOpenConversationDialog,
}) => {
  const auth = useAuth();

  const {
    register,
    control,
    setValue,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm();

  const {
    getAllListValuesByListNameId,
    basicProfileDataAllProfiles,
    setBasicProfileAllProfiles,
  } = useContext(AuthContext);

  const [selectedConversationId, setSelectedConversationId] = useState(null);
  const [selectedOutcomeConversationId, setSelectedOutcomeConversationId] =
    useState(null);
  const [targetId, setTargetId] = useState(null);
  const [shallRemindId, setShallRemindId] = useState(null);
  const [conversationDate, setConversationDate] = useState(null);
  const [nextConversationDate, setNextConversationDate] = useState(null);

  const [openDialogContent, setOpenDialogContent] = useState(false);
  const [dialogMessage, setDialogMessage] = useState("");
  const [submitSuccess, setSubmitSuccess] = useState(false);

  const [leadStatus, setLeadStatus] = useState(data?.leadStatus);

  const [openDialog, setOpenDialog] = useState();

  const handleLeadStatusChange = (event) => {
    const selectedId = event.target.value;
    setLeadStatus(selectedId);
  };

  const [leadPriority, setLeadPriority] = useState(data?.leadPriority);
  const handleLeadPriorityChange = (event) => {
    const selectedId = event.target.value;
    setLeadPriority(selectedId);
  };

  const handleAddDialogClose = () => {
    reset({
      typeOfConversation: [],
      conversationDate: "",
      outcomeOfConversation: [],
      nextConversationDate: "",
      nextConversationTime: "",
      target: [],
      shallRemind: [],
      remarks: "",
    });
    setSelectedConversationId(null);
    setConversationDate(null);
    setNextConversationDate(null);
    setShallRemindId(null);
    setTargetId(null);
    setSelectedOutcomeConversationId(null);
    setOpenConversationDialog(false);
    setLeadStatus(data?.leadStatus);
    setLeadPriority(data?.leadPriority);
  };

  const handleCancel = () => {
    const message = `
      <div> 
        <h3> Are you sure you want to leave the page? Unsaved changes may be lost</h3>
      </div>
    `;
    setDialogMessage(message);
    setOpenDialog(true);
  };

  const handleCancelDialog = () => {
    setOpenDialog(false);
    handleAddDialogClose();
  };

  const handleCancelClose = () => {
    setOpenDialog(false);
  };

  const handleDialogClose = () => {
    reset({
      typeOfConversation: [],
      conversationDate: "",
      outcomeOfConversation: [],
      nextConversationDate: "",
      nextConversationTime: "",
      target: [],
      shallRemind: [],
      remarks: "",
    });
    setSelectedConversationId(null);
    setConversationDate(null);
    setNextConversationDate(null);
    setShallRemindId(null);
    setTargetId(null);
    setSelectedOutcomeConversationId(null);
    setOpenConversationDialog(false);
  };


  const handleShallRemindChange = (event) => {
    const selectedId = event.target.value;
    setShallRemindId(selectedId);
  };

  const handleSelectChange = (event) => {
    const selectedId = event.target.value;
    setSelectedConversationId(selectedId);
  };
  const handleTargetChange = (event) => {
    const selectedId = event.target.value;
    setTargetId(selectedId);
  };

  const handleSelectOutcomeConversation = (event) => {
    const selectedId = event.target.value;
    setSelectedOutcomeConversationId(selectedId);
  };

  const [leadStatusData, setLeadStatusData] = useState(null);

  const [leadPriorityData, setLeadPriorityData] = useState(null);


  useEffect(() => {
    if (!!authConfig) {
      getAllListValuesByListNameId(
        authConfig.leadStatusListNamesId,
        (data) =>
          setLeadStatusData(
            data?.listValues.map((item) => ({
              value: item.id,
              key: item.listValue,
            }))
          ),
        handleError
      );
      getAllListValuesByListNameId(
        authConfig.leadPriorityListNamesId,
        (data) =>
          setLeadPriorityData(
            data?.listValues.map((item) => ({
              value: item.id,
              key: item.listValue,
            }))
          ),
        handleError
      );
    }
  }, [authConfig]);

  const handleError = (error) => {
    console.error("Basic profile: All Services:", error);
  };

  const apiCall = useRef(false);
  async function submit(formData) {
    if (apiCall.current) {
      // API call is already in progress, return early
      return;
    }
    apiCall.current = true;

    const nextTimeFormatted = convertTo12HourFormat(formData.nextConversationTime);

    const fields = {
      userId: userId,
      leadStatus: leadStatus,
      leadPriority: leadPriority,
      smartSummary: formData?.smartSummary,
      typeOfConversation: formData?.typeOfConversation,
      conversationDate: formData?.conversationDate,
      outcomeOfConversation: formData?.outcomeOfConversation,
      nextConversationDate: formData?.nextConversationDate,
      nextConversationTime: nextTimeFormatted,
      target: formData?.target,
      shallRemind: formData?.shallRemind,
      remarks: formData?.remarks,
    };

    try {
     
      await auth.conversationPost(fields, handleFailure, handleSuccess);
      fetchUsers();
      handleDialogClose();
      setSubmitSuccess(true);
    } catch (error) {
      console.error("Master Data Creation failed:", error);
      handleFailure();
    }
    apiCall.current = false;
  }
 

  function convertTo12HourFormat(timeStr) {
    const [hours, minutes] = timeStr.split(":").map(Number);
    const period = hours >= 12 ? "PM" : "AM";
    const adjustedHours = hours % 12 || 12;
    return `${adjustedHours}:${
      minutes < 10 ? "0" + minutes : minutes
    } ${period}`;
  }

  const handleSuccess = () => {
    const message = `
    <div> 
      <h3>Conversation added Successfully.</h3>
    </div>
  `;
    setDialogMessage(message);
    setOpenDialogContent(true);
  };

  const handleFailure = () => {
    const message = `
    <div> 
      <h3> Failed to Add Conversation. Please try again later.</h3>
    </div>
  `;
    setDialogMessage(message);
    setOpenDialogContent(true);
  };

  const handleButtonClick = () => {
    setOpenDialogContent(false);
    setBasicProfileAllProfiles(null);
  };  

  useEffect(() => {
    setValue("smartSummary", basicProfileDataAllProfiles?.remarks);
    setValue("leadStatus", basicProfileDataAllProfiles?.leadStatus);
    setValue("leadPriority", basicProfileDataAllProfiles?.leadPriority);
  }, [
    basicProfileDataAllProfiles?.remarks,
    basicProfileDataAllProfiles?.leadStatus,
    basicProfileDataAllProfiles?.leadPriority,
    setValue,
  ]);
  useEffect(() => {
    const handleBeforeUnload = (event) => {
      if (openConversationDialog) {
        event.preventDefault();
        event.returnValue = "";
      }
    };

    window.addEventListener("beforeunload", handleBeforeUnload);

    return () => {
      window.removeEventListener("beforeunload", handleBeforeUnload);
    };
  }, [openConversationDialog]);

  return (
    <>
      <Button
        color="primary"
        size="medium"
        variant="contained"
        onClick={handleOpenConversationDialog}
      >
        Add Conversation
      </Button>

      <Dialog
        open={openConversationDialog}
        onClose={handleCancel}
        maxWidth="lg"
      >
        <DialogTitle
          sx={{
            position: "relative",
            borderBottom: (theme) => `1px solid ${theme.palette.divider}`,
            p: (theme) => `${theme.spacing(1.5)} !important`,
            display: "flex",
            flexDirection: "column",
            alignItems: "start",
            margin: 3,
            fontSize: {
              xs: "19px",
              md: "20px",
              
            },
            height:"50px",
            // fontWeight: "bold",
          }}
        >
          <Box sx={{
            marginLeft:2
          }}>
            Add SP Conversation Details&nbsp;
          </Box>
        </DialogTitle>
        <DialogActions>
          <Box sx={{ position: "absolute", top: "16px", right: "14px", marginRight:{
            xs:3,
            xl:6  ,
            lg:3,
            sm :3.5
          } }}>
            <IconButton
                size="small"
                onClick={handleCancel}
                sx={{
                  // p: "0.438rem",
                  borderRadius: 1,
              
                  color: "common.white",
                  backgroundColor: "primary.main",
                  "&:hover": {
                    backgroundColor: "#66BB6A",
                    transition: "background 0.5s ease, transform 0.5s ease",
                  },
                }}
              >
                <Icon icon="tabler:x" fontSize="1rem" />
              </IconButton>
          </Box>
        </DialogActions>
        <DialogContent>
          
          <Grid container spacing={5}>
        <Grid
          item
          xs={12}
          sx={{
            backgroundColor: "#f2f7f2",
            mt: 1,
            ml:5,
            paddingTop: 0,  
            height: "36px",
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between"
          }}
        >
          <Typography
            variant="body1"
            fontWeight={"bold"}
            sx={{ mt: 0, ml: -3, mb: 5 ,
              textAlign: "center", // Aligns text in the center
              
            }}
            
          >
            Conversation details
          </Typography>
          <Divider />
        </Grid>
           
            <Grid item xl={2.5} xs={12} sm={4} md={3} lg={2.5}>
              <FormControl fullWidth error={Boolean(errors.typeOfConversation)}>
                <Controller
                  name="typeOfConversation"
                  control={control}
                  defaultValue={""}
                  render={({ field }) => (
                    <SelectAutoComplete
                      id="conversation-type-select-label"
                      label="Type"
                      value={selectedConversationId}
                      nameArray={conversationTypeData}
                      onChange={(e) => {
                        field.onChange(e);
                        handleSelectChange(e);
                      }}
                    />
                  )}
                />
              </FormControl>
            </Grid>
            <Grid item sx={12} sm={4} md={3}  lg={2.35}>
              <FormControl>
                <Controller
                  name="conversationDate"
                  control={control}
                  defaultValue={new Date().toISOString().split("T")[0]}
                  rules={{ required: "Date is required" }} // Add validation rules here
                  render={({ field }) => (
                    <TextField
                      label="date"
                      {...field}
                      fullWidth
                      size="small"
                      type="date"
                      InputLabelProps={{
                        shrink: true,
                      }}
                      sx={{ minWidth: { xl:210,lg:210,md:210, sm:210,xs:240} }} 
                     
                      error={Boolean(errors.conversationDate)}
                      helperText={errors.conversationDate?.message}
                    />
                  )}
                />
              </FormControl>
            </Grid>
            <Grid item  xl={2.5} xs={12} sm={4} md={3}  lg={2.5}>
              <FormControl
                fullWidth
                error={Boolean(errors.outcomeOfConversation)}
              >
                <Controller
                  name="outcomeOfConversation"
                  control={control}
                  defaultValue={""}
                  render={({ field }) => (
                    <SelectAutoComplete
                      id="outcome-conversation-type-select"
                      label="OutCome"
                      value={selectedOutcomeConversationId}
                      nameArray={outcomeConversationData}
                      onChange={(e) => {
                        field.onChange(e);
                        handleSelectOutcomeConversation(e);
                      }}
                    />
                  )}
                />
              </FormControl>
            </Grid>
         

            <Grid item xl={2.5} xs={12} sm={4} md={3}  lg={2.5}>
              <FormControl fullWidth>
                <Controller
                  name="nextConversationTime"
                  control={control}
                  defaultValue=""
                  rules={{ required: "Next Conversation Time is required" }}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      size="small"
                      label="Next Conversation Time"
                      type="time"
                      InputLabelProps={{ shrink: true }}
                      
                      error={Boolean(errors.nextConversationTime)}
                      helperText={errors.nextConversationTime?.message}
                      aria-describedby="nextConversationTime"
                    />
                  )}
                />
              </FormControl>
            </Grid>
            <Grid item xl={2.5} sm={4} md={3}   lg={2.5}>
              <FormControl>
                <Controller
                  name="nextConversationDate"
                  control={control}
                  defaultValue=""
                  rules={{ required: "Next Conversation Date is required" }}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      size="small"
                      fullWidth
                      label="Next Conversation Date"
                      type="date"
                      InputLabelProps={{ shrink: true }}
                      sx={{ minWidth: { xl:220, sm :210, lg:225, xs:240} }} 
                      error={Boolean(errors.nextConversationDate)}
                      helperText={errors.nextConversationDate?.message}
                      aria-describedby="nextConversationDate"
                    />
                  )}
                />
              </FormControl>
            </Grid>

            <Grid item xl={2.356} xs={12} sm={4} md={3}  lg={2.35}>
              <FormControl fullWidth error={Boolean(errors.target)}>
                <Controller
                  name="target"
                  control={control}
                  defaultValue={""}
                  render={({ field }) => (
                    <SelectAutoComplete
                      id="target-select"
                      label="Target"
                      value={targetId}
                      nameArray={targetData}
                      onChange={(e) => {
                        field.onChange(e);
                        handleTargetChange(e);
                      }}
                    />
                  )}
                />
              </FormControl>
            </Grid>
            <Grid item xs={12} xl={2.5} sm={4} md={3}  lg={2.5}>
              <FormControl fullWidth error={Boolean(errors.shallRemind)}>
                <Controller
                  name="shallRemind"
                  control={control}
                  defaultValue={""}
                  render={({ field }) => (
                    <SelectAutoComplete
                      id="shall-remind-select"
                      label="Shall Remind"
                      value={shallRemindId}
                      nameArray={shallRemindData}
                      onChange={(e) => {
                        field.onChange(e);
                        handleShallRemindChange(e);
                      }}
                    />
                    
                  )}
                  
                />
              </FormControl>
            </Grid>
            <Grid item xs={12}>
              <FormControl fullWidth>
                <Controller
                  name="remarks"
                  control={control}
                  defaultValue={""}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      rows={4}
                      multiline
                      label="Comments"
                      InputLabelProps={{ shrink: true }}
                      error={Boolean(errors.remarks)}
                      helperText={errors.remarks?.message}
                      aria-describedby="statusAssignmentDetails_remarks"
                    />
                  )}
                />
              </FormControl>
            </Grid>
            <Grid
                item
                xs={12}
                sx={{
                  backgroundColor: "#f2f7f2",
                  mt: 4,
                  ml:5,
                  paddingTop: 0,  
                  height: "36px",
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "space-between"
                }}
              >
                <Typography
                  variant="body1"
                  fontWeight={"bold"}
                  sx={{ mt: 0, ml: -3, mb: 5 ,
                    textAlign: "center", // Aligns text in the center
                    
                  }}
                  
                >
                  Do you want to update - Basic Profile details
                </Typography>
                <Divider />
              </Grid>

            <Grid item xs={12}  sm={4} md={3} lg={2.5}>
              {/* Lead Status Field */}
              <FormControl fullWidth>
                <Controller
                  name="status"
                  control={control}
                  size="small"
                  defaultValue={data?.leadStatus}
                  render={({ field }) => (
                    <SelectAutoComplete
                      id="status"
                      label="Lead Status"
                      nameArray={leadStatusData}
                      value={leadStatus}
                      onChange={(e) => {
                        field.onChange(e);
                        handleLeadStatusChange(e);
                      }}
                    />
                  )}
                />
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={4} md={3} lg={2.5}>
              {/* Lead Priority Field */}
              <FormControl fullWidth>
                <Controller
                  name="lead-priority"
                  control={control}
                  size="small"
                  defaultValue={data?.leadPriority}
                  // InputLabelProps={{ shrink: true }}
                  render={({ field }) => (
                    <SelectAutoComplete
                      id="lead-priority"
                      label="Lead Priority"
                      nameArray={leadPriorityData}
                      value={leadPriority}
                      onChange={(e) => {
                        field.onChange(e);
                        handleLeadPriorityChange(e);
                      }}
                    />
                  )}
                />
              </FormControl>
            </Grid>

            <Grid item xs={12} >
              <FormControl fullWidth>
                <Controller
                  name="smartSummary"
                  control={control}
                  defaultValue={data?.individualRemarks}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      rows={4}
                      multiline
                      label="Smart Summary"
                      InputLabelProps={{ shrink: true }}
                      error={Boolean(errors.smartSummary)}
                      helperText={errors.smartSummary?.message}
                      aria-describedby="statusAssignmentDetails_smartSummary"
                    />
                  )}
                />
              </FormControl>
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions
          sx={{
            justifyContent: "end",
            borderTop: (theme) => `1px solid ${theme.palette.divider}`,
            p: (theme) => `${theme.spacing(2.5)} !important`,
            height:"50px",
          }}
        >
          <Button
            size="medium"
            sx={{ mr: 1 }}
            onClick={handleCancel}
            variant="outlined"
            color="primary"
          >
            Cancel
          </Button>
          <Button
            size="medium"
            onClick={handleSubmit(submit)}
            variant="contained"
            sx={{
              marginRight: {xs:3.5, xl:7.5}
            }}
            color="primary"
          >
            Submit
          </Button>
        </DialogActions>
      </Dialog>

      <Dialog
        open={openDialogContent}
        onClose={handleButtonClick}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
        PaperProps={{
          sx: {
            p: (theme) => `${theme.spacing(2.5)} !important`,
            backgroundColor: (theme) => theme.palette.primary.background,
          },
        }}
      >
        <Box
          sx={{
            width: "100%",
            borderRadius: 1,
            textAlign: "center",
            border: (theme) => `1px solid ${theme.palette.divider}`,
            borderColor: "primary.main",
          }}
        >
          <DialogContent>
            <DialogContentText
              id="alert-dialog-description"
              color="primary.main"
            >
              <div dangerouslySetInnerHTML={{ __html: dialogMessage }} />
            </DialogContentText>
          </DialogContent>
          <DialogActions>
            <Button
              variant="contained"
              onClick={handleButtonClick}
              sx={{ margin: "auto", width: 100 }}
            >
              Okay
            </Button>
          </DialogActions>
        </Box>
      </Dialog>

      <Dialog
        open={openDialog}
        onClose={handleCancelClose}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
        PaperProps={{
          sx: {
            p: (theme) => `${theme.spacing(2.5)} !important`,
            backgroundColor: (theme) => theme.palette.primary.background,
          },
        }}
      >
        <Box
          sx={{
            width: "100%",
            borderRadius: 1,
            textAlign: "center",
            border: (theme) => `1px solid ${theme.palette.divider}`,
            borderColor: "primary.main",
          }}
        >
          <DialogContent>
            <DialogContentText
              id="alert-dialog-description"
              color="primary.main"
            >
              <div dangerouslySetInnerHTML={{ __html: dialogMessage }} />
            </DialogContentText>
          </DialogContent>
          <DialogActions>
            <Button
              variant="contained"
              onClick={handleCancelDialog}
              sx={{ margin: "auto" }}
            >
              Discard Changes
            </Button>
            <Button
              variant="contained"
              onClick={handleCancelClose}
              sx={{ margin: "auto" }}
            >
              Return to Form
            </Button>
          </DialogActions>
        </Box>
      </Dialog>
    </>
  );
};

export default ServiceProviderConversation;
