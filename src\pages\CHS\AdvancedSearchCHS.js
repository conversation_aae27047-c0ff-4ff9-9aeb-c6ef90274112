import { useContext, useEffect, useState } from "react";
import {<PERSON>, Slider} from "@mui/material";
import Drawer from "@mui/material/Drawer";
import Button from "@mui/material/Button";
import MenuItem from "@mui/material/MenuItem";
import { styled } from "@mui/material/styles";
import IconButton from "@mui/material/IconButton";
import Typography from "@mui/material/Typography";
import Box from "@mui/material/Box";
import Tooltip from "@mui/material/Tooltip";
import CustomAvatar from "src/@core/components/mui/avatar";
import { Grid, FormControl, InputLabel, Select, FormHelperText, FormControlLabel, Checkbox } from "@mui/material";
import { Controller, useForm } from "react-hook-form";
import PerfectScrollbar from "react-perfect-scrollbar";
import authConfig from "src/configs/auth";
import axios from "axios";
import { getAuthorizationHeaders, getUrl } from "src/helpers/utils";
import Icon from "src/@core/components/icon";
import { AuthContext } from "src/context/AuthContext";
import MultiSelectAutoComplete from "src/@core/components/custom-components/MultiSelectAutoComplete";
import { useTheme, useMediaQuery ,  TextField,  InputAdornment,
} from "@mui/material";
import SelectProject from "src/@core/components/custom-components/SelectProject";

import SearchIcon from "@mui/icons-material/Search";
import MultiSelectAutoCompleteStatic from "src/@core/components/custom-components/MultiSelectAutoCompleteStatic";
import SelectAutoComplete from "src/@core/components/custom-components/SelectAutoComplete";
const Header = styled(Box)(({ theme }) => ({
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
  position: "relative",
  padding: theme.spacing(3),
  borderBottom: `1px solid ${theme.palette.divider}`,
}));

const AdvancedSearchCHS = (props) => {
  const { open, toggle, searchKeyword,setSearchKeyword,roleFilter, searchData,setSearchData, fetchUsers, page, pageSize,  searchingState,
    setSearchingState } = props;
  const { getAllListValuesByListNameId } = useContext(AuthContext);
  const theme = useTheme();
  const { control, setValue, register,reset, getValues, formState: { errors } } = useForm({
    defaultValues: {
      serviceTypeUUIDs: [],
      locationUUIDs: [],
      leadStatusUUIDs: [],
      leadPriorityUUIDs: [],
      isMicroSiteEmpanelled: null,
      isListingEmpanelled: null,
       buildingAge:[],
       searchKeywordByEmail: "",
       searchKeywordBySocietyName: "",
       searchKeywordByMobile: "",
       searchKeywordBySocietyAddress:"",
       searchKeyword: "",// for search by name 
       searchingState:true
    },
  });
  const [useCustomRange, setUseCustomRange] = useState(false); // State to track custom range usage

  
  const handleCancel = () => {
    console.log("Resetting all fields..."); // Debugging log
    setSelectedTypes([]);
    setBuildingAge([0,0]);  // Reset buildingAge state
    setUseCustomRange(false); // Hide custom range slider
    setAgeValue([0, 0]); // Reset slider values
    setSearchKeyword("");
    setSearchingState(false)
    reset({
      serviceTypeUUIDs: [],
      locationUUIDs: [],
      leadStatusUUIDs: [],
      leadPriorityUUIDs: [],
      isMicroSiteEmpanelled: null,
      isListingEmpanelled: null,
      buildingAge:"", // Reset buildingAge in the form
      searchKeywordByEmail: "",
      searchKeywordBySocietyName: "",
      searchKeywordByMobile: "",
      searchKeywordBySocietyAddress: "",
      searchKeyword: "",
    });

    const buildingAgeField = document.querySelector("#buildingAge-select input");
    if (buildingAgeField) {
      buildingAgeField.value = ""; // Directly clear input field value
    }
  

    // Ensure searchData state is also reset
    setSearchData({
      serviceTypeUUIDs: [],
      locationUUIDs: [],
      leadStatusUUIDs: [],
      leadPriorityUUIDs: [],
      isMicroSiteEmpanelled: null,
      isListingEmpanelled: null,
      buildingAge:[], // Reset buildingAge in search data to an empty string
      searchKeywordByEmail: "",
      searchKeywordBySocietyName: "",
      searchKeywordByMobile: "",
      searchKeywordBySocietyAddress: "",
      searchKeyword: "",
    });

    fetchUsers(page, pageSize,roleFilter, searchKeyword, searchData);
    console.log("Fields after reset:", getValues(), buildingAge);
  };

  useEffect(() => {
    reset(searchData);
  }, [searchData, reset]);

  const handleApply =() =>{
    setSearchingState(true);    
      toggle();
  }
  const handleClose = () => {
    toggle();
  };

  const [listOfEmployees, setListOfEmployees] = useState([]);
  const [employeesOptions, setEmployeesOptions] = useState([]);
  const [employeeId, setEmployeeId] = useState("");
  const isMobile = useMediaQuery(theme.breakpoints.down(435));
  const [placeholderText, setPlaceholderText] = useState(
    "Search by Society name"
  );
  const [keyword, setKeyword] = useState("");
  useEffect(() => {
    axios({
      method: "get",
      url: getUrl(authConfig.selectDropdownNew) + "?selectionType=EMPLOYEES",
      headers: getAuthorizationHeaders(),
    })
      .then((res) => {
        setListOfEmployees(res.data.data);
      })
      .catch((err) => console.log("Employees error", err));

      const phrases = ["Society name", "name", "email", "mobile number"];
    let phraseIndex = 0;
    const intervalId = setInterval(() => {
      phraseIndex = (phraseIndex + 1) % phrases.length;
      setPlaceholderText(`Search by ${phrases[phraseIndex]}`);
    }, 1000);

    return () => clearInterval(intervalId);
  
  }, []);

  const [listOfTypes, setListOfTypes] = useState([]); // State for storing list of types
  const [selectedTypes, setSelectedTypes] = useState([]); // State for selected types

  useEffect(() => {
    // Fetch the list of types for "Select Society Type"
    axios({
      method: "post",
      url: getUrl(authConfig.masterDataGetAllEndpoint),
      headers: getAuthorizationHeaders(),
      data: { masterDataType: "TYPE_FSI" },
    })
      .then((res) => {
        setListOfTypes(
          res.data.masterDataResponse.map((item) => ({
            value: item.id,
            key: item.name,
          }))
        );
      })
      .catch((err) => console.log("Error fetching types:", err));
  }, []);

  useEffect(() => {
    if (listOfEmployees.length) {
      const data = listOfEmployees.map((entry) => ({ value: entry.id, key: entry.name }));
      setEmployeesOptions(data);
    }
  }, [listOfEmployees]);

  const updateFilters = (data) => {
    setSearchData(data);
    fetchUsers(page, pageSize,roleFilter, searchKeyword, data);
  };

  useEffect(() => {
    if (!!authConfig) {
      getAllListValuesByListNameId(
        authConfig.allServicesListNameId,
        (data) =>
          setServices(
            data?.listValues.map((item) => ({
              value: item.id,
              key: item.listValue,
            }))
          ),
        handleError
      );
    }
    if (!!authConfig) {
      getAllListValuesByListNameId(
        authConfig.locationlistNameId,
        (data) =>
          setLocationsData(
            data?.listValues.map((item) => ({
              value: item.id,
              key: item.listValue,
            }))
          ),
        handleError
      );
    }
    if (!!authConfig) {
      getAllListValuesByListNameId(
        authConfig.leadStatusListNamesId,
        (data) =>
          setLeadStatusData(
            data?.listValues.map((item) => ({
              value: item.id,
              key: item.listValue,
            }))
          ),
        handleError
      );
    }
    if (!!authConfig) {
      getAllListValuesByListNameId(
        authConfig.leadPriorityListNamesId,
        (data) =>
          setLeadPriorityData(
            data?.listValues.map((item) => ({
              value: item.id,
              key: item.listValue,
            }))
          ),
        handleError
      );
    }
    
  }, [authConfig]);

  const [leadStatusData, setLeadStatusData] = useState(null);
  const [leadPriorityData, setLeadPriorityData] = useState(null);
  const [locationsData, setLocationsData] = useState(null);
  const [services, setServices] = useState(null);
  const [age, setAge] = useState("");
  const [ageValue, setAgeValue] = useState([0, 0]);
  const [buildingAge, setBuildingAge] = useState(""); 

  const handleChange = (event, newValue) => {
    setAgeValue(newValue);
    updateFilters({ ...getValues(), buildingAge: newValue });
  };

  const marks = Array.from({ length: 20 }, (_, i) => ({
    value: (i + 1) * 5,
    label: `${(i + 1) * 5}`,
  }));
  function valuetext(value) {
    return `${value}`;
  }
  
  

  const handleLeadStatusSuccess = (data) => setLeadStatusData(data?.listValues);
  const handleLeadPrioritySuccess = (data) => setLeadPriorityData(data?.listValues);
  const handleLocationsSuccess = (data) => setLocationsData(data?.listValues);
  const handleServices = (data) => setServices(data?.listValues);
  const handleError = (error) => console.error("Error:", error);



  const buildingAges = [
    {
      value: "LESS_THAN_5",
      key: "Less than 5 years",
    },
    {
      value: "5_TO_10",
      key: "5 to 10 years",
    },
    {
      value: "10_TO_20",
      key: "10 to 20 years",
    },
    {
      value: "20_TO_30",
      key: "20 to 30 years",
    },
    {
      value: "ABOVE_30",
      key: "Above 30 years",
    },
    {
      value: "CUSTOM_RANGE",
      key: "Custom Range" // Custom range selection option
    }
  ];

  const handleBuildingAgeChange = (event) => {
    const selectedValue = event.target.value;
    console.log("Building Age Selected:", selectedValue);
  
    if (selectedValue === "CUSTOM_RANGE") {
      setUseCustomRange(true); // Show custom range slider
      setBuildingAge("CUSTOM_RANGE");
    } else {
      setUseCustomRange(false); // Hide custom range slider
      setBuildingAge(selectedValue); // Update the state variable
    }
  
    // Update form value with the selected building age
    setValue("buildingAge", selectedValue);
  
    // Update searchData state to reflect the selected building age
    setSearchData((prevData) => ({
      ...prevData,
      buildingAge: selectedValue,
    }));
  };
  

  // Handle Slider Change
  const handleSliderChange = (event, newValue) => {
    setAgeValue(newValue); // Update slider values
    const customRangeValue = `Custom Range: ${newValue[0]} - ${newValue[1]} years`;

    setBuildingAge(customRangeValue); // Update the buildingAge state
    setValue("buildingAge", customRangeValue); // Update form value
    setSearchData((prevData) => ({
      ...prevData,
      buildingAge: customRangeValue,
    }));
  };
  
  function valuetext(value) {
    return `${value} years`;
  }
  
    return (
      <>
        <Tooltip title="Advanced Search">
          <CustomAvatar
            variant="rounded"
            sx={{ width: 36, height: 36, cursor: "pointer" }}
            onClick={toggle}
          >
            <Icon icon="tabler:filter" fontSize={27} />
          </CustomAvatar>
        </Tooltip>
        
        <Drawer
          open={open}
          anchor="right"
          variant="temporary"
          onClose={handleClose}
          ModalProps={{ keepMounted: true }}
          sx={{ "& .MuiDrawer-paper": { width: { xs: "90%", sm: 500 } } }}
        >
          <Header
          sx={{
            position: "relative", 
            display: "flex",
            alignItems: "center", 
            justifyContent: "space-between", 
          }}
        >
          <Typography variant="h5" sx={{
            marginLeft: "12px",
          }}>Advanced Search</Typography>
          <Box sx={{ position: "absolute", top: "8px", right: "26px" }}>
            <IconButton
              size="small"
              onClick={handleClose}
              sx={{
                borderRadius: 1,
                color: "common.white",
                backgroundColor: "primary.main",
                "&:hover": {
                  backgroundColor: "#66BB6A",
                  transition: "background 0.5s ease, transform 0.5s ease",
                },
                mt: 2,
              }}
            >
              <Icon icon="tabler:x" fontSize="1rem" />
            </IconButton>
          </Box>
        </Header>
          <PerfectScrollbar options={{ wheelPropagation: false }}>
          <Box sx={{ p: (theme) => theme.spacing(4, 6) }}>
          <Grid container spacing={2} direction="column">
              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
                  <Controller
                    name="mainSearch"
                    control={control}
                    render={({ field: { onChange } }) => (
                      <TextField
                        id="mainSearch"
                        placeholder="Search by Name"
                        value={searchKeyword}
                        label="Name"
                        variant="outlined"
                        InputLabelProps={{
                          shrink: true,
                        }}
                        onChange={(e) => {
                          onChange(e.target.value);
                          setSearchKeyword(e.target.value);
                        }}
                        onKeyDown={(e) => {
                          if (e.key === "Enter") {
                            setSearchKeyword(searchKeyword);
                            fetchServiceProviders(
                              page,
                              pageSize,
                              searchKeyword,
                              searchData
                            );
                          }
                        }}
                        sx={{
                          "& .MuiInputBase-root": {
                            height: "40px",
                          },
                          "& .MuiInputBase-input::placeholder": {
                            fontSize: "0.9rem",
                          },
                        }}
                        InputProps={{
                          endAdornment: (
                            <InputAdornment position="start">
                              <SearchIcon
                                sx={{
                                  cursor: "pointer",
                                  marginRight: "-15px",
                                }}
                                onClick={() => {
                                  setSearchKeyword(searchKeyword);
                                  fetchServiceProviders(
                                    page,
                                    pageSize,
                                    searchKeyword,
                                    searchData
                                  );
                                }}
                              />{" "}
                            </InputAdornment>
                          ),
                        }}
                      />
                    )}
                  />
                </FormControl>
              </Grid>

              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
                  <Controller
                    name="searchKeywordByEmail"
                    control={control}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        id="searchKeywordByEmail"
                        placeholder="Search by Email"
                        label="Email"
                        variant="outlined"
                        InputLabelProps={{
                          shrink: true,
                        }}
                        value={field.value || ""}
                        onChange={(event) => {
                          updateFilters({
                            ...getValues(),
                            searchKeywordByEmail: event.target.value,
                          });
                        }}
                        onKeyDown={(e) => {
                          if (e.key === "Enter") {
                            fetchServiceProviders(
                              page,
                              pageSize,
                              searchKeyword,
                              searchData
                            );
                          }
                        }}
                        sx={{
                          "& .MuiInputBase-root": {
                            height: "40px",
                          },
                          "& .MuiInputBase-input::placeholder": {
                            fontSize: "0.9rem",
                          },
                        }}
                        InputProps={{
                          endAdornment: (
                            <InputAdornment position="start">
                              <SearchIcon
                                sx={{
                                  cursor: "pointer",
                                  marginRight: "-15px",
                                }}
                                onClick={(event) => {
                                  updateFilters({
                                    ...getValues(),
                                    searchKeywordByEmail: event.target.value,
                                  });
                                }}
                              />{" "}
                            </InputAdornment>
                          ),
                        }}
                      />
                    )}
                  />
                </FormControl>
              </Grid>

              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
                  <Controller
                    name="searchKeywordBySocietyName"
                    control={control}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        id="searchKeywordBySocietyName"
                        placeholder="Search by Society Name"
                        label="Society Name"
                        variant="outlined"
                        InputLabelProps={{
                          shrink: true,
                        }}
                        value={field.value || ""}
                        onChange={(event) => {
                          updateFilters({
                            ...getValues(),
                            searchKeywordBySocietyName: event.target.value,
                          });
                        }}
                        onKeyDown={(e) => {
                          if (e.key === "Enter") {
                            fetchServiceProviders(
                              page,
                              pageSize,
                              searchKeyword,
                              searchData
                            );
                          }
                        }}
                        sx={{
                          "& .MuiInputBase-root": {
                            height: "40px",
                          },
                          "& .MuiInputBase-input::placeholder": {
                            fontSize: "0.9rem",
                          },
                        }}
                        InputProps={{
                          endAdornment: (
                            <InputAdornment position="start">
                              <SearchIcon
                                sx={{
                                  cursor: "pointer",
                                  marginRight: "-15px",
                                }}
                                onClick={(event) => {
                                  updateFilters({
                                    ...getValues(),
                                    searchKeywordBySocietyName: event.target.value,
                                  });
                                }}
                              />{" "}
                            </InputAdornment>
                          ),
                        }}
                      />
                    )}
                  />
                </FormControl>
              </Grid>

              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
                  <Controller
                    name="searchKeywordByMobile"
                    control={control}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        id="searchKeywordByMobile"
                        placeholder="Search by Mobile No."
                        label="Mobile No."
                        variant="outlined"
                        InputLabelProps={{
                          shrink: true,
                        }}
                        value={field.value || ""}
                        onChange={(event) => {
                          updateFilters({
                            ...getValues(),
                            searchKeywordByMobile: event.target.value,
                          });
                        }}
                        onKeyDown={(e) => {
                          if (e.key === "Enter") {
                            fetchServiceProviders(
                              page,
                              pageSize,
                              searchKeyword,
                              searchData
                            );
                          }
                        }}
                        sx={{
                          "& .MuiInputBase-root": {
                            height: "40px",
                          },
                          "& .MuiInputBase-input::placeholder": {
                            fontSize: "0.9rem",
                          },
                        }}
                        InputProps={{
                          endAdornment: (
                            <InputAdornment position="start">
                              <SearchIcon
                                sx={{
                                  cursor: "pointer",
                                  marginRight: "-15px",
                                }}
                                onClick={(event) => {
                                  updateFilters({
                                    ...getValues(),
                                    searchKeywordByMobile: event.target.value,
                                  });
                                }}
                              />{" "}
                            </InputAdornment>
                          ),
                        }}
                      />
                    )}
                  />
                </FormControl>
              </Grid>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
                  <Controller
                    name="searchKeywordBySocietyAddress"
                    control={control}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        id="searchKeywordBySocietyAddress"
                        placeholder="Search by Society Address."
                        label="Society Address"
                        variant="outlined"
                        InputLabelProps={{
                          shrink: true,
                        }}
                        value={field.value || ""}
                        onChange={(event) => {
                          updateFilters({
                            ...getValues(),
                            searchKeywordBySocietyAddress: event.target.value,
                          });
                        }}
                        onKeyDown={(e) => {
                          if (e.key === "Enter") {
                            fetchServiceProviders(
                              page,
                              pageSize,
                              searchKeyword,
                              searchData
                            );
                          }
                        }}
                        sx={{
                          "& .MuiInputBase-root": {
                            height: "40px",
                          },
                          "& .MuiInputBase-input::placeholder": {
                            fontSize: "0.9rem",
                          },
                        }}
                        InputProps={{
                          endAdornment: (
                            <InputAdornment position="start">
                              <SearchIcon
                                sx={{
                                  cursor: "pointer",
                                  marginRight: "-15px",
                                }}
                                onClick={(event) => {
                                  updateFilters({
                                    ...getValues(),
                                    searchKeywordBySocietyAddress: event.target.value,
                                  });
                                }}
                              />{" "}
                            </InputAdornment>
                          ),
                        }}
                      />
                    )}
                  />
                </FormControl>
              </Grid>

                {/* <Grid item xs={12} sm={12}>
                  <FormControl fullWidth>
                    <InputLabel id="services-select-label">Services</InputLabel>
                    <Controller
                      name="serviceTypeUUIDs"
                      control={control}
                      render={({ field }) => (
                        <Select
                          {...field}
                          labelId="services-select-label"
                          id="services-select"
                          size="small"
                          multiple
                          value={field.value || []}
                          label="Services"
                          onChange={(e) => {
                            field.onChange(e);
                            updateFilters({ ...getValues(), serviceTypeUUIDs: e.target.value });
                          }}
                        >
                          {services?.map((service) => (
                            <MenuItem key={service.id} value={service.id}>
                              {service.listValue}
                            </MenuItem>
                          ))}
                        </Select>
                      )}
                    />
                  </FormControl>
                  {errors.service && (
                    <FormHelperText sx={{ color: "error.main" }} id="validation-service">
                      {errors.service?.message}
                    </FormHelperText>
                  )}
                </Grid> */}

<Grid item xs={12} md={6}>
              <FormControl fullWidth>
                <Controller
                  name="serviceTypeUUIDs"
                  control={control}
                  render={({ field }) => (
                    <MultiSelectAutoComplete
                      id="autocomplete-type-select"
                      label="Select Society Type" // Label for the field
                      nameArray={listOfTypes || []} // Options for selection
                      value={field.value || []} // Selected values
                      onChange={(e) => {
                        field.onChange(e);
                        updateFilters({
                          ...getValues(),
                          serviceTypeUUIDs: e.target.value,
                        });
                      }}
                    />
                  )}
                />
              </FormControl>
            </Grid>

            {/* <Grid item xs={12} md={6}>
              <FormControl fullWidth>
                      <Controller
                        name="serviceTypeUUIDs"
                        control={control}
                        rules={{ required: false }}
                        render={({ field }) => (
                          <MultiSelectAutoComplete
                            size="small"
                            id="serviceTypeUUIDs"
                            label="Society Type"
                            nameArray={listOfTypes|| []}
                            value={field.value || []}
                            onChange={(e) => {
                              field.onChange(e);
                              updateFilters({
                                ...getValues(),
                                leadStatusUUIDs: e.target.value,
                              });
                            }}
    
                            error={Boolean(errors.serivcesProvided)}
                          />
                        )}
                      />

                </FormControl>
                {errors.leadStatus && (
                  <FormHelperText
                    sx={{ color: "error.main" }}
                    id="validation-leadStatus"
                  >
                    {errors.leadStatus?.message}
                  </FormHelperText>
                )}
              </Grid> */}

            <Grid item xs={12} md={6}>
              <FormControl fullWidth>
                      <Controller
                        name="leadStatusUUIDs"
                        control={control}
                        rules={{ required: false }}
                        render={({ field }) => (
                          <MultiSelectAutoComplete
                            size="small"
                            id="leadStatus"
                            label="Lead Status"
                            nameArray={leadStatusData|| []}
                            value={field.value || []}
                            onChange={(e) => {
                              field.onChange(e);
                              updateFilters({
                                ...getValues(),
                                leadStatusUUIDs: e.target.value,
                              });
                            }}
    
                            error={Boolean(errors.serivcesProvided)}
                          />
                        )}
                      />

                </FormControl>
                {errors.leadStatus && (
                  <FormHelperText
                    sx={{ color: "error.main" }}
                    id="validation-leadStatus"
                  >
                    {errors.leadStatus?.message}
                  </FormHelperText>
                )}
              </Grid>

              <Grid item xs={12} md={6}>
              <FormControl fullWidth>
                  <Controller
                    name="leadPriorityUUIDs"
                    control={control}
                    rules={{ required: false }}
                    render={({ field }) => (
                      <MultiSelectAutoComplete
                        id="lead-priority-select"
                        size="small"
                        label="Lead Priority"
                        nameArray={leadPriorityData || []}
                        value={field.value || []}
                        onChange={(e) => {
                          field.onChange(e);
                          updateFilters({
                            ...getValues(),
                            leadPriorityUUIDs: e.target.value,
                          });
                        }}

                        error={Boolean(errors.service)}
                      />
                    )}
                  />
                </FormControl>

                {errors.leadPriority && (
                  <FormHelperText
                    sx={{ color: "error.main" }}
                    id="validation-leadPriority"
                  >
                    {errors.leadPriority?.message}
                  </FormHelperText>
                )}
              </Grid>

              <Grid item xs={12} md={6}>
              <FormControl fullWidth>
                  <Controller
                    name="locationUUIDs"
                    control={control}
                    rules={{ required: false }}
                    render={({ field }) => (
                      <MultiSelectAutoComplete
                        id="locations-select"
                        size="small"
                        label="Location"
                        nameArray={locationsData || []}
                        value={field.value || []}
                        
                        // onChange={(e) => {
                        //   field.onChange(e);
                        //   updateFilters({
                        //     ...getValues(),
                        //     locationUUIDs: e.target.value,
                        //   });
                        // }}
                        onChange={(event) => {
                          field.onChange(event.target.value);
                          updateFilters({ ...getValues(), locationUUIDs: event.target.value });
                        }}

                        error={Boolean(errors.location)}
                      />
                    )}
                  />
                </FormControl>
                {errors.location && (
                  <FormHelperText
                    sx={{ color: "error.main" }}
                    id="validation-location"
                  >
                    {errors.location?.message}
                  </FormHelperText>
                )}
              </Grid>
              <Grid item xs={12} sm={12}>
                <FormControl fullWidth>
                <Typography variant="body1" gutterBottom>
                    Building Age
                  </Typography>
                  <Controller
                    name="buildingAge"
                    control={control}
                    render={({ field }) => (
                      <Slider
                        value={ageValue}
                        onChange={handleSliderChange}
                        valueLabelDisplay="auto"
                        aria-labelledby="range-slider"
                        getAriaValueText={valuetext}
                        step={5}
                        marks={marks}
                        min={5}
                        max={100}
                      />
                    )}
                  />
                </FormControl>
              </Grid>
              </Grid>
            </Box>
          </PerfectScrollbar>
  
          <Box sx={{ 
            borderTop: theme => `1px solid ${theme.palette.divider}`,
            p: (theme) => theme.spacing(2),justifyContent:"flex-end",
            display: "flex", alignItems: "center" 
          }}>
             
             <Button variant="tonal" sx={{ mr: 2 }} onClick={handleCancel}>
            Clear All
          </Button>
          <Button
          sx={{ mr: {
            xs: 4, 
            sm: 4, 
            md: 4, 
            lg: 4, 
            xl: 4,
          },} }
            variant="contained"
            //  onClick={handleSubmit(handleApply)}
            onClick={toggle}
          >
            Apply
          </Button>
          </Box>
        </Drawer>
      </>
    );
  };
  
  export default AdvancedSearchCHS;
