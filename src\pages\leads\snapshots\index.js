import { DataGrid } from "@mui/x-data-grid";
import { useContext, useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import {
  <PERSON><PERSON>,
  Card,
  CardContent,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  FormControl,
  Grid,
  MenuItem,
  Select,
  Typography,
  Box,
  Divider,
  DialogContentText,
  Tooltip,
  Menu,
  Chip,
} from "@mui/material";
import CustomChip from "src/@core/components/mui/chip";
import axios from "axios";
import { getAuthorizationHeaders, getUrl } from "src/helpers/utils";
import authConfig from "src/configs/auth";
import { AuthContext } from "src/context/AuthContext";
import SnapshotUploadDialog from "src/@core/components/custom-components/SnapshotUploadDialog";
import Icon from "src/@core/components/icon";
import CustomAvatar from "src/@core/components/mui/avatar";
import DeleteDialog from "./DeleteDialog";
import SnapshotEditDialog from "./SnapshotEditDialog";
import ActivateDialog from "./ActivateDialog";
import { useRBAC } from "src/pages/permission/RBACContext";
import FallbackSpinner from "src/@core/components/spinner";
import ViewSnapshotByLocation from "src/@core/components/custom-components/ViewSnapshotByLocation";
import AdvancedSearch from "./AdvancedSearch";
import { useRouter } from "next/router";

const userStatusObj = {
  true: "Active",
  false: "InActive",
};

const LeadSnapshots = ({ category, type, setActiveTab }) => {
  const { can } = useRBAC();

  const [searchData, setSearchData] = useState({});

    const router = useRouter();
    const { id } = router.query; 

      useEffect(() => {
        if (id) {
          setSearchData((prev) => ({ ...prev, id: id }));
        }
      }, [id]);

  const {
    uploadSnapshots,
    updateSnapshots,
    documentDelete,
    allCategories,
    allSubCategories,
    user,
    listValues,
    snapshotsData,
    setSnapshotsData,
    snapshotsDataDetails,
  } = useContext(AuthContext);

  const [userList, setUserList] = useState([]);
  const [loading, setLoading] = useState(false);
  const [allLoading, setAllLoading] = useState(true);
  const [rowCount, setRowCount] = useState(0);
  const rowsPerPageOptions = [10, 15, 20, 25, 50, 100];

  
  const [searchingState, setSearchingState]= useState(false);
  const [addUserOpen, setAddUserOpen] = useState(false);
  const toggleAddUserDrawer = () => setAddUserOpen(!addUserOpen);
  const [error, setError] = useState(false);

  const [pageSize, setPageSize] = useState(rowsPerPageOptions[0]);
  const [page, setPage] = useState(1);
  const [searchKeyword, setSearchKeyword] = useState("");
  const [initialRowCount, setInitialRowCount] = useState(null);
  const [currentRow, setCurrentRow] = useState();
  const [openDialog, setOpenDialog] = useState(false);
  const [dialogSuccess, setDialogSuccess] = useState(false);
  const [dialogMessage, setDialogMessage] = useState("");
  const [dialogFailure, setDialogFailure] = useState(false);
  const [selectedFiles, setSelectedFiles] = useState([]);
  const [disableButton, setDisableButton] = useState(false);
  const [employeesData, setEmployeesData] = useState([]);

  const [categoryId, setCategoryId] = useState("");

  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [openDeleteDialog, setOpenDeleteDialog] = useState(false);
  const [openActivateDialog, setOpenActivateDialog] = useState(false);

  const [leadStatus, setLeadStatus] = useState("");
  const [leadPriority, setLeadPriority] = useState("");
  const [status, setStatus] = useState("");
  const [assignedTo, setAssignedTo] = useState("");

  const [isShortForm,setIsShortForm] = useState(false)

  const handleCloseDeleteDialog = () => {
    setOpenDeleteDialog(false);
    fetchUserList(page, pageSize);
  };

  const handleCloseActivateDialog = () => {
    setOpenActivateDialog(false);
    fetchUserList(page, pageSize);
  };

  const handleAddSnapshots = () => {
    setIsShortForm(false)
    setOpenDialog(true);
    setAnchorEl(null);
  };

  const handleAddSnapshotsShortForm = () => {
    setIsShortForm(true)
    setOpenDialog(true);
    setAnchorEl(null);
  };

  const handlePageChange = (direction) => {
    if (direction === page) {
      setPage(page + 1);
    } else {
      setPage(page - 1);
    }
  };

  const handlePageSizeChange = (params) => {
    if (params) {
      setPageSize(params);
      setPage(1);
    }
  };


  useEffect(()=>{
    setLeadPriority("")
    setLeadStatus("")
    setAssignedTo("")
    setStatus("")
  },[category])

  const [matchedLeadStatus, setMatchedLeadStatus] = useState([]);
  const [matchedLeadPriority, setMatchedLeadPriority] = useState([]);
  const [matchedAssignedTo, setMatchedAssignedTo] = useState([]);
  const [showAllLeadStatus, setShowAllLeadStatus] = useState(false);
  const [showAllLeadPriority, setShowAllLeadPriority] = useState(false);
  const [showAllAssignedTo, setShowAllAssignedTo] = useState(false);


  useEffect(() => {
    const leadStatusUUIDsKeys =
      searchData?.leadStatusUUIDs?.map((item) => item.key) || [];
    setMatchedLeadStatus(leadStatusUUIDsKeys);

    const leadPriorityUUIDsKeys =
      searchData?.leadPriorityUUIDs?.map((item) => item.key) || [];
    setMatchedLeadPriority(leadPriorityUUIDsKeys);

    const assignedToUUIDsKeys =
      searchData?.assignedToUUIDs?.map((item) => item.key) || [];
      setMatchedAssignedTo(assignedToUUIDsKeys);

  }, [searchData, listValues]);

  const getVisibleChips = (dataArray, showAll) => {
    return showAll ? dataArray : dataArray.slice(0, 5);
  };

  const renderFilterSection = (
    label,
    data,
    matchedItems,
    showAll,
    setShowAll,
    handleRemoveFilter,
    UUIDs
  ) => {
    
    if (!data || data.length === 0) {
      return null;
    }
  
    const commonStyle = {
      fontSize: '12px', 
      height: '24px', 
      '& .MuiChip-label': {
        padding: '0 8px',
      },
      '& .MuiSvgIcon-root': {
        fontSize: '16px', 
      },
    };
  
    return (
      <Box display="flex" flexDirection="column" gap={1}>
        <Box display="flex" flexWrap="wrap" gap={1} sx={{ marginTop: '6px' }}>
          {/* Label */}
          <Typography variant="subtitle1"  sx={{
            fontSize: '12px',
            fontWeight: 'bold',
            height: '24px',
            '& .MuiChip-label': {
              padding: '0 8px',
            },
            '& .MuiSvgIcon-root': {
              fontSize: '16px',
            },
          }}>
            {label}:
          </Typography>
          {/* Chips */}
          {getVisibleChips(matchedItems, showAll).map((item, index) => (
            <Chip
              key={index}
              label={item}
              onDelete={() => handleRemoveFilter(UUIDs, item)}
              variant="outlined"
              sx={commonStyle}
            />
          ))}
          {/* View All / View Less Button */}
          {matchedItems.length > 5 && (
            <Button size="small" onClick={() => setShowAll(!showAll)} sx={commonStyle}>
              {showAll ? 'View Less ↑' : 'View All ↓'}
            </Button>
          )}
        </Box>
      </Box>
    );
  };

  const handleRemoveFilter = (filterType, value = null) => {
    
    const updatedData = { ...searchData };
 
    if (typeof updatedData[filterType] === "boolean") {
      updatedData[filterType] = false; 
    } 
    else if(typeof updatedData[filterType] === "string") {
      updatedData[filterType] = ""; 
    }
    else{
      if (value !== null) {
        updatedData[filterType] = updatedData[filterType].filter(
          (item) => item.key !== value
        );
      }
    }
   
    setSearchData(updatedData);    
    fetchUserList(page, pageSize, searchKeyword,searchData);
  };

  const handleRemoveMultipleFilters = (filterTypes) => {
    const updatedData = { ...searchData };

    

    
    filterTypes.forEach(filterType => {
      if (typeof updatedData[filterType] === "boolean") {
        updatedData[filterType] = false; 
      } 
      else if (typeof updatedData[filterType] === "string") {
        updatedData[filterType] = ""; 
      } 
      else {
        updatedData[filterType] = []; // Reset the array to empty
      }
    });
  
    setSearchData(updatedData);
    setError(false);
    fetchUserList(page, pageSize, searchKeyword);
  };


  const fetchUserList = async (currentPage, currentPageSize,searchKeyword, searchData) => {

      let url;
        if (user?.roleId === authConfig?.superAdminRoleId) {
          url = getUrl(authConfig.snapshots) + "/allLeadSnapshotsAdmin/" + category;
        } else {
          url = getUrl(authConfig.snapshotsGetAll) + "/" + category;
        }
    
        const headers = getAuthorizationHeaders();
    

    const data = {
      page: currentPage,
      pageSize: currentPageSize,
      leadSnapShotId: searchData?.id,
    };

    try {
      const response = await axios({
        method: "post",
        url: url,
        headers: headers,
        data: data,
      });

      if (initialRowCount == null) {
        setInitialRowCount(response.data.rowCount || 0);
      }

      if (response.data) {
        setUserList(response.data?.leadSnapshotDTOResponses || []);
        setRowCount(response.data?.rowCount || 0);
      } else {
        console.error("Unexpected API response format:", response);
      }
    } catch (error) {
      console.error("Error fetching users:", error);
    } finally {
      setAllLoading(false);
    }
  };
  

  useEffect(() => {
    fetchUserList(page, pageSize,searchKeyword, searchData);
  }, [page, pageSize,searchKeyword, searchData]);

  const documentDetails = {
    documentCategory: "240ab5e5-48b3-4897-a1cc-66a98792ef25",
    documentSubCategory: "7bf03e27-502a-45e5-a897-871e59f39073",
    categoryTypeEnum: category,
    documentFrom: "EMPLOYEE",
    documentTo: "HOUZER",
  };

  const handleSave = async () => {
    setDisableButton(true);
    setLoading(true);

    const formData = new FormData();
    selectedFiles.forEach((file) => {
      formData.append("files", file);
    });
    
    formData.append("documentDetails", JSON.stringify(documentDetails));
    formData.append(
      "leadSnapshotResponseDTO",
      JSON.stringify({
        assignedTo: assignedTo,
        status: status,
        leadStatus: leadStatus,
        leadPriority: leadPriority,
      })
    );

    // API call
    await uploadSnapshots(
      formData,
      () => {
        const message = `Successfully Uploaded`;
        setDialogMessage(message);
        setDialogSuccess(true);

        setSelectedFiles([]);
      },
      () => {
        const message = `Failed to upload`;
        setDialogMessage(message);
        setDialogSuccess(true);
      }
    );

    setSelectedFiles([]);
    setLeadPriority("");
    setLeadStatus("");
    setStatus("");
    setAssignedTo("");
    fetchUserList();
    setLoading(false);
    setDisableButton(false);
    setOpenDialog(false);
    
  };

  const handlePatchSave = async () => {
    setDisableButton(true);
    setLoading(true);

    const formData = new FormData();
    if (selectedFiles.length > 0) {
      selectedFiles.forEach((file) => {
        formData.append("file", file);
      });
    }
    formData.append("documentDetails", JSON.stringify(documentDetails));
    formData.append(
      "leadSnapshotResponseDTO",
      JSON.stringify({
        assignedTo: assignedTo,
        status: status,
        leadStatus: leadStatus,
        leadPriority: leadPriority,
      })
    );

    // API call
    await updateSnapshots(
      snapshotsDataDetails?.id,
      formData,
      () => {
        setEditDialogOpen(false);
        const message = `Updated Successfully`;
        setDialogMessage(message);
        setDialogSuccess(true);
        setSelectedFiles([]);
      },
      () => {
        const message = `Failed to Update`;
        setDialogMessage(message);
        setDialogFailure(true);
      }
    );

    setSelectedFiles([]);
    setLeadPriority("");
    setLeadStatus("");
    setStatus("");
    setAssignedTo("");
    fetchUserList();
    setLoading(false);
    setDisableButton(false);
    setEditDialogOpen(false);
  };

  useEffect(() => {
    // Fetch all employees
    axios({
      method: "get",
      url: getUrl(authConfig.selectDropdownNew) + "?selectionType=EMPLOYEES",
      headers: getAuthorizationHeaders(),
    })
      .then((res) => {
        setEmployeesData(res?.data?.data);
      })
      .catch((err) => console.log("Employees error", err));
  }, []);

  const handleClose = () => {
    setDialogSuccess(false);
  };

  const handleFailureClose = () => {
    // New function to close failure dialog
    setDialogFailure(false);
  };

  const [menu, setMenu] = useState(null);

  const handleCloseMenuItems = () => {
    setMenu(null);
  };

  const mapIsActiveToLabel = (isActive) => {
    return userStatusObj[isActive] || "Unknown";
  };

  const [location,setLocation] = useState(null)

  const columns = [
    {
      field: "location",
      headerName: "File Name",
      flex: 0.3,
      minWidth: 180,
      valueGetter: (params) => {
        const metaData = params?.row?.metaData;
        if (Array.isArray(metaData)) {
          
          return metaData
            .filter((item) => item?.isActive) // Filter by isActive property
            .map((item) => {
              const location = item?.location || "";
              const parts = location.split("/");
              return parts[parts.length - 1]; // Return just the file name
            })
            
        }
        return "";
      },
      renderCell: (params) => {
        const metaData = params?.row?.metaData;
        if (Array.isArray(metaData)) {
          const activeMetaData = metaData.filter((item) => item?.isActive);
          return (
            <div
              style={{
                whiteSpace: "normal",
                wordWrap: "break-word",
                lineHeight: 1.5,
              }}
            >
              {metaData
                .filter((item) => item?.isActive) // Filter by isActive property
                .map((item, index) => {
                  const location = item?.location || "";
                  const parts = location.split("/");
                  const fileName = parts[parts.length - 1];
  
                  // Object containing both id and location
                  const snapshotData = { id: item.id, location: item.location };
  
                  // Hyperlink to trigger ViewSnapshotByLocation with id and location
                  return (
                    <span key={index}>
                      <a
                        href="#"
                        onClick={(e) => {
                          e.preventDefault(); // Prevent default anchor behavior
                          // Call the ViewSnapshotByLocation component with id and location
                          setLocation(snapshotData)
                        }}
                        style={{
                          color: "blue", // Styling for the hyperlink
                          textDecoration: "underline", // Underline text to show as a link
                          cursor: "pointer",
                        }}
                      >
                        {fileName}
                      </a>
                      {index < activeMetaData.length - 1 ? ", " : ""}
                    </span>
                  );
                })}
            </div>
          );
        }
        return "";
      },
    },
    {
      field: "leadStatus",
      headerName: "Status",
      flex: 0.13,
      minWidth: 140,
      renderCell: (params) => {
        const assignedTo = listValues?.find(
          (item) => item?.id === params?.row?.status
        );
        return <span>{assignedTo ? assignedTo?.name : ""}</span>;
      },
    },
    {
      field: "leadPriority",
      headerName: "Priority",
      flex: 0.07,
      minWidth: 80,
      renderCell: (params) => {
        const assignedTo = listValues?.find(
          (item) => item?.id === params?.row?.leadPriority
        );
        return <span>{assignedTo ? assignedTo?.name : ""}</span>;
      },
    },
    {
      field: "assignedTo",
      headerName: "Assigned To",
      flex: 0.13,
      minWidth: 140,
      renderCell: (params) => {
        const assignedTo = employeesData?.find(
          (item) => item?.id === params?.row?.assignedTo
        );
        return <span>{assignedTo ? assignedTo?.name : ""}</span>;
      },
    },
    {
      field: "status",
      headerName: "Active Status",
      flex: 0.1,
      minWidth: 110,
      renderCell: ({ row }) => {
        return (
          <CustomChip
            rounded={true}
            skin="light"
            size="small"
            label={mapIsActiveToLabel(row.isActive)}
            color={row.isActive === true ? "success" : "error"}
            sx={{ textTransform: "capitalize" }}
          />
        );
      },
    },
    {
      flex: 0.05,
      field: "actions",
      headerName: "Actions",
      sortable: false,
      minWidth: 95,
      disableClickEventBubbling: true,
      renderCell: (params) => {
        const handleClickMenu = (event) => {
          event.stopPropagation();
          setMenu(event.currentTarget);
          const row = params.row;
          setCurrentRow(row);
          setSnapshotsData({
            ...snapshotsData,
            id: row.id,
          });
        };

        const onClickViewProfile = () => {
          setEditDialogOpen(true);
          handleCloseMenuItems();
        };
        const onClickDeleteProfile = () => {
          setOpenDeleteDialog(true);
          handleCloseMenuItems();
        };

        const onClickActivateProfile = () => {
          setOpenActivateDialog(true);
          handleCloseMenuItems();
        };

        return (
          <div
            style={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
            }}
          >
            <Tooltip title="Actions">
              <CustomAvatar
                skin="light"
                variant="rounded"
                sx={{
                  mr: { xs: 2, lg: 4 },
                  width: 34,
                  height: 34,
                  cursor: "pointer",
                }}
                onClick={handleClickMenu}
              >
                <Icon icon="bi:three-dots-vertical" />
              </CustomAvatar>
            </Tooltip>
            <Menu
              id="actions-menu"
              anchorEl={menu}
              keepMounted
              open={Boolean(menu)}
              onClose={handleCloseMenuItems}
            >
              <MenuItem onClick={onClickViewProfile}>Edit</MenuItem>
              {currentRow?.isActive ? (
                <MenuItem onClick={onClickDeleteProfile}>DeActivate</MenuItem>
              ) : (
                <MenuItem onClick={onClickActivateProfile}>Activate</MenuItem>
              )}
            </Menu>
          </div>
        );
      },
    },
  ];

  const [anchorEl, setAnchorEl] = useState(null);
  const open = Boolean(anchorEl);


  const handleMenuClick = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const handleSnapshotClearFilter = () => {
    // Clear the status filter from selected filters
    setSearchData((prev) => ({ ...prev, id: undefined }));

    // Update the URL to remove the status query parameter
    router.replace(
      {
        pathname: router.pathname,
        query: {}, // Clear all query parameters
      },
      undefined,
      { shallow: false } // Allow page reload if necessary
    );
    setActiveTab("snapshots")
  };


  return (
    <>
     
        <Box
          sx={{
            py: 3,
            px: 6,
            rowGap: 2,
            columnGap: 4,
            display: "flex",
            flexWrap: "wrap",
            alignItems: "center",
            justifyContent: "space-between",
          }}
        >
          <Grid container spacing={3} alignItems="center">
            <Grid item xs={12} sm={4} sx={{ textAlign: "flex-start" }}>
              <Typography variant="h6" fontWeight={"600"}>
                {type} - Lead Snapshots
              </Typography>
            </Grid>

            <Grid item xs={12} sm={8}>
              <Grid
                container
                spacing={2}
                alignItems="center"
                justifyContent="flex-end"
              >

<Grid
                      item
                      // xs={3}
                      // sm="auto"
                      sx={{
                        paddingTop: { xs: "15px", sm: "25px" },
                        mr: "6px",
                        ml: "6px",
                        marginTop: { xs: "0.5rem", sm: "0rem" },
                      }}
                    >
                      {/* <AdvancedSearch
                        open={addUserOpen}
                        toggle={toggleAddUserDrawer}
                        searchKeyword={searchKeyword}
                        searchData={searchData}
                        setSearchKeyword={setSearchKeyword}
                        setSearchData={setSearchData}
                        fetchUserList={fetchUserList}
                        page={page}
                        pageSize={pageSize}
                        searchingState={searchingState}
                        setSearchingState = {setSearchingState}
                      /> */}
                    </Grid>
                <Grid item xs="auto" sm="auto">
                    <Button
                    variant="contained"
                      onClick={handleMenuClick}
                    >
                      Add Snapshots&nbsp;
                       <Icon icon='tabler:chevron-down'/>
                    </Button>
                    <Menu
                      id="split-button-menu"
                      anchorEl={anchorEl}
                      open={open}
                      onClose={handleMenuClose}
                      PaperProps={{
                        style: {
                          width: anchorEl ? anchorEl.clientWidth : undefined,
                        },
                      }}
                    >
                      <MenuItem onClick={handleAddSnapshotsShortForm}>
                        Short Form
                      </MenuItem>
                      <MenuItem onClick={handleAddSnapshots}>
                        Long Form
                      </MenuItem>
                    </Menu>
                </Grid>
              </Grid>
            </Grid>
          </Grid>
        </Box>

        <Divider/>
      <CardContent>

            <div style={{ fontSize: "13px", marginBottom: "12px" }}>
            {searchingState && (
            (searchKeyword ||
              searchData?.leadStatusUUIDs?.length>0 ||
              searchData?.leadPriorityUUIDs?.length>0 ||
              searchData?.assignedToUUIDs?.length>0
            ) ? (
                <div sx={{marginBottom: "16px"}}>
                  Showing {rowCount} of {initialRowCount} Results | 
                  <>
                    {!(searchKeyword==="") && 
                      (
                      <Chip
                          label={
                            <span>
                              <span style={{ fontWeight: 'bold' }}>File Name:</span> {searchKeyword}
                            </span>
                          }
                          onDelete={() => setSearchKeyword("")}
                          variant="outlined"
                          sx={{
                            fontSize: '12px',
                            height: '24px',
                            '& .MuiChip-label': {
                              padding: '0 8px',
                            },
                            '& .MuiSvgIcon-root': {
                              fontSize: '16px',
                            },
                          }}
                        />

                      )
                    }
                      {renderFilterSection(
                        "Status",
                        searchData?.leadStatusUUIDs,
                        matchedLeadStatus,
                        showAllLeadStatus,
                        setShowAllLeadStatus,
                        handleRemoveFilter,
                        "leadStatusUUIDs"
                      )}
                      
                      {renderFilterSection(
                        "Lead Priority",
                        searchData?.leadPriorityUUIDs,
                        matchedLeadPriority,
                        showAllLeadPriority,
                        setShowAllLeadPriority,
                        handleRemoveFilter,
                        "leadPriorityUUIDs"
                      )}
                      
                      {renderFilterSection(
                        "Assigned To",
                        searchData?.assignedToUUIDs,
                        matchedAssignedTo,
                        showAllAssignedTo,
                        setShowAllAssignedTo,
                        handleRemoveFilter,
                        "assignedToUUIDs"
                      )}
                  </>
                </div>
              
              ):setSearchingState(false)
            )}
          </div> 

          {id && (
            <Chip
              label={
                <>
                  Snapshot assigned recently
                </>
              }
              color="primary"
              variant="outlined"
              sx={{ mb: 3 }}
              onDelete={handleSnapshotClearFilter}
            />
          )}


            <div style={{ height: 380, width: "100%" }}>
              {allLoading ? (
                <Box
                  display="flex"
                  justifyContent="center"
                  alignItems="center"
                  height="60vh"
                >
                  <FallbackSpinner />
                </Box>
              ) : (
                 <DataGrid
                rows={userList}
                columns={columns}
                pageSize={pageSize}
                page={page - 1}
                rowsPerPageOptions={
                  userList && userList.length > 0 ? rowsPerPageOptions : []
                }
                rowCount={rowCount}
                paginationMode="server"
                onPageChange={handlePageChange}
                onPageSizeChange={handlePageSizeChange}
                // rowHeight={40}
                disableSelectionOnClick
                pagination={rowCount > 0}
                headerHeight={40}
              />
              )}
            </div>
          </CardContent>



      <ViewSnapshotByLocation
        location={location}
        setSelectedLocation={setLocation}
      />

      <SnapshotUploadDialog
        open={openDialog}
        onClose={() => setOpenDialog(false)}
        onSave={() => handleSave()}
        selectedFiles={selectedFiles}
        setSelectedFiles={setSelectedFiles}
        isShortForm={isShortForm}
        leadStatus={leadStatus}
        setLeadStatus={setLeadStatus}
        leadPriority={leadPriority}
        setLeadPriority={setLeadPriority}
        status={status}
        setStatus={setStatus}
        assignedTo={assignedTo}
        setAssignedTo={setAssignedTo}
        loading={loading}
        disableButton={disableButton}
      />

      <SnapshotEditDialog
        open={editDialogOpen}
        onClose={() => setEditDialogOpen(false)}
        onSave={() => handlePatchSave()}
        data={snapshotsDataDetails}
        selectedFiles={selectedFiles}
        loading={loading}
        fetchUserList={fetchUserList}
        setSelectedFiles={setSelectedFiles}
        disableButton={disableButton}
        leadStatus={leadStatus}
        setLeadStatus={setLeadStatus}
        leadPriority={leadPriority}
        setLeadPriority={setLeadPriority}
        status={status}
        setStatus={setStatus}
        assignedTo={assignedTo}
        setAssignedTo={setAssignedTo}
      />

      <DeleteDialog
        open={openDeleteDialog}
        onClose={handleCloseDeleteDialog}
        data={currentRow}
      />

      <ActivateDialog
        open={openActivateDialog}
        onClose={handleCloseActivateDialog}
        data={currentRow}
      />

      <Dialog
        open={dialogSuccess}
        onClose={handleClose}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
        PaperProps={{
          sx: {
            p: (theme) => `${theme.spacing(2.5)} !important`,
            backgroundColor: (theme) => theme.palette.primary.background,
          },
        }}
      >
        <Box
          sx={{
            width: "100%",
            borderRadius: 1,
            textAlign: "center",
            border: (theme) => `1px solid ${theme.palette.divider}`,
            borderColor: "primary.main",
          }}
        >
          <DialogContent>
            <DialogContentText
              id="alert-dialog-description"
              color="primary.main"
            >
              <div dangerouslySetInnerHTML={{ __html: dialogMessage }} />
            </DialogContentText>
          </DialogContent>
          <DialogActions>
            <Button
              variant="contained"
              onClick={handleClose}
              sx={{ margin: "auto", width: 100 }}
            >
              Ok
            </Button>
          </DialogActions>
        </Box>
      </Dialog>
    </>
  );
};

export default LeadSnapshots;
