// ** MUI Imports
import Grid from "@mui/material/Grid";

// ** Custom Components Imports
import { useEffect, useState } from "react";

// ** Styled Component
import axios from "axios";
import DatePickerWrapper from "src/@core/styles/libs/react-datepicker";
import authConfig from "src/configs/auth";
import { getAuthorizationHeaders, getUrl } from "src/helpers/utils";
import LongFormContent from "../sections-longform/LongFormContent";

const initialFormData = () => ({
  societyDetails: {
    name: "",
    googleMapLocation: "",
    enrolledDate: "",
    registeredFor: [],
    societyAddress: "",
    roadWidth: "",
    grossPlotArea: "",
    authority: "",
    teamMember: "",
    plotCTSNo: "",
    locationId: "",
    zone: "",
    pinCode: "",
  },
  societyMemberInformation: {
    societyMemberName: "",
    societyMemberDesignation: "",
    societyMemberContactNumber: "",
    loginEmail: "",
    alternateNumber: "",
    fromDate: "",
    toDate: "",
    societyCommitteeMemberInformationList: [],
  },
  businessInformation: {
    bankName: "",
    branch: "",
    accountNumber: "",
    ifscCode: "",
    gstNo: "",
    panNo: "",
    stateName: "",
    doYouHaveGstNo: "",
  },
  landDetails: {
    builtUpAreaResidential: "",
    builtUpAreaCommercial: "",
    noOfResidence: "",
    noOfCommercial: "",
  },
  fsi: {
    buildingAge: "",
    fsiConsumedFsi: "",
    fsi_AvailableFsi: "",
    fsi_PermissibleFsi: "",
    heightRestriction: "",
    scheme: "",
    dpRestrictions: "",
    litigationsOrEncroachment: "",
  },
  requirements: {
    requirements_ExtraArea: "",
    requirements_Rent: "",
    requirements_Corpus: "",
    notes: "",
    leadGivenTo: "",
  },
});

const RegistrationForm = () => {
  const [employeesData, setEmployeesData] = useState(null);

  useEffect(() => {
    // Fetch all employees
    axios({
      method: "get",
      url: getUrl(authConfig.selectDropdownNew) + "?selectionType=EMPLOYEES",
      headers: getAuthorizationHeaders(),
    })
      .then((res) => {
        setEmployeesData(res.data.data);
      })
      .catch((err) => console.log("Employees error", err));
  }, []);

  return (
    <div>
      <>
        <style>
          {`
          .tableBody:hover {
            background-color: #f6f6f7;
          }
      `}
        </style>
        <DatePickerWrapper>
          <Grid container spacing={2} className="match-height">
            <LongFormContent employeesData={employeesData} initialFormData={initialFormData} />
          </Grid>
        </DatePickerWrapper>
      </>
    </div>
  );
};

export default RegistrationForm;
