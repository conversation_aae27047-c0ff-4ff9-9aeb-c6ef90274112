import React, { useContext } from "react";
import { Card, Grid, Typography, TextField } from "@mui/material";
import AccordionBasic from "src/@core/components/custom-components/AccordionBasic";
import { AuthContext } from "src/context/AuthContext";

const field = {
  fontWeight: 500,
  fontSize: "12.75px",
};

const ContactCollapsibleCard = ({ data, errors, handleSocietyName, handleName, handleContactNoChange }) => {

  const { user } = useContext(AuthContext)

  return (
    <AccordionBasic
      id={"basic-schema-1"}
      ariaControls={"basic-schema-1"}
      heading={"Contact Details"}
      expanded={false}  // Ensuring the accordion is always expanded
      body={
        <Card
          sx={{
            padding: 2,
            display: "flex",
            flexDirection: "column",
          }}
        >
          <Grid container spacing={1} alignItems="center">
            <Grid item xs={12} sm={2.5} mt={1}>
              <TextField
                fullWidth
                type="text"
                value={data?.needAssessment?.societyName}
                label="Society Name"
                sx={{ margin: 0, width: "100% !important" }}
                size="small"
                onChange={handleSocietyName}
                error={Boolean(errors.societyName) || (data?.needAssessment?.societyName && data.needAssessment.societyName.length < 3)}
                helperText={
                  (Boolean(errors.societyName) && "Society Name is required.") ||
                  (data?.needAssessment?.societyName && data.needAssessment.societyName.length < 3 && "Society Name should be at least 3 characters long.")
                }
                placeholder="Enter Society Name"
                inputProps={{ maxLength: 30 }}
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
            <Grid item xs={12} sm={2.5} mt={1}>
              <TextField
                fullWidth
                type="text"
                value={data?.needAssessment?.name}
                label="Contact Person Name"
                size="small"
                sx={{ margin: 0, width: "100% !important" }}
                onChange={handleName}
                error={Boolean(errors.name) || (data?.needAssessment?.name && data.needAssessment.name.length < 3)}
                helperText={
                  (Boolean(errors.name) && "Name is required.") ||
                  (data?.needAssessment?.name && data.needAssessment.name.length < 3 && "Name should be at least 3 characters long.")
                }
                placeholder="Enter your Name"
                inputProps={{ maxLength: 30 }}
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
            <Grid item xs={12} sm={2} mt={1}>
              <TextField
                fullWidth
                type="tel"
                value={data?.financialClosure?.contactNumber}
                label="Contact Number"
                sx={{ margin: 0, width: "100% !important" }}
                size="small"
                onChange={handleContactNoChange}
                error={Boolean(errors.contactNumber)}
                placeholder="+911234567890"
                InputLabelProps={{ shrink: true }}
                inputMode="text"
              />
            </Grid>
            <Grid item xs={12} sm={4} mt={1}>
              <Grid container spacing={0.5} alignItems="center" sx={{ width: { xs:'300px !important', md: '300px !important', sm: '300px !important' } }}>
                <Grid item xs={2}>
                  <Typography size="small">Email:</Typography>
                </Grid>
                <Grid item xs={10}>
                  <Typography variant="h6" sx={{ fontWeight: "bold", fontSize:{ xs: "0.8rem !important", lg:'1rem !important'}, paddingLeft:'10px !important' }}>
                    {data?.financialClosure?.emailId ? data?.financialClosure?.emailId : user?.email}
                  </Typography>
                </Grid>
              </Grid>
            </Grid>
          </Grid>
        </Card>
      }
    />
  );
};

export default ContactCollapsibleCard;
