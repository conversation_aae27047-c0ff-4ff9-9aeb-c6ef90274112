import { yupResolver } from "@hookform/resolvers/yup";
import CheckCircleIcon from "@mui/icons-material/CheckCircle";
import {
  Box,
  Card,
  CardContent,
  CircularProgress,
  DialogContentText,
  Divider,
  Tooltip,
  InputAdornment,
  InputLabel,
  Select,
  MenuItem,
  Menu,
  ListItemIcon,
  Link,
} from "@mui/material";
import SelectAutoComplete from "src/@core/components/custom-components/SelectAutoComplete";
import SearchIcon from "@mui/icons-material/Search";
import Button from "@mui/material/Button";
import Dialog from "@mui/material/Dialog";
import DialogActions from "@mui/material/DialogActions";
import DialogContent from "@mui/material/DialogContent";
import DialogTitle from "@mui/material/DialogTitle";
import FormControl from "@mui/material/FormControl";
import Grid from "@mui/material/Grid";
import IconButton from "@mui/material/IconButton";
import TextField from "@mui/material/TextField";
import Typography from "@mui/material/Typography";
import { DataGrid } from "@mui/x-data-grid";
import axios from "axios";
import { useContext, useEffect, useState, useRef } from "react";
import { Controller, useForm } from "react-hook-form";
import EmailTextField from "src/@core/components/custom-components/EmailTextField";
import MobileNumberValidation from "src/@core/components/custom-components/MobileNumberValidation";
import NameTextField from "src/@core/components/custom-components/NameTextField";
import Icon from "src/@core/components/icon";
import CustomAvatar from "src/@core/components/mui/avatar";
import authConfig from "src/configs/auth";
import { AuthContext } from "src/context/AuthContext";
import { getAuthorizationHeaders, getUrl } from "src/helpers/utils";
import { useAuth } from "src/hooks/useAuth";
import DeleteDialog from "./sections/DeleteDialog";
import UpdateDialog from "./sections/UpdateDialog";
import EmployeeDetailsView from "./sections/EmployeeDetailsView";
import EmployeeValidations from "./sections/EmployeeValidations";
import CustomChip from "src/@core/components/mui/chip";
import { useRBAC } from "src/pages/permission/RBACContext";
import { useRouter } from "next/router";
import SelectCategory from "src/@core/components/custom-components/SelectCategory";
import FallbackSpinner from "src/@core/components/spinner";
import EmployeeDetailsEdit from "./sections/EmployeeDetailsEdit";
import EmployeeDetailsLongForm from "./sections/EmployeeDetailsLongForm";
import { MENUS, PAGES, PERMISSIONS } from "src/constants";
import { fetchIpAddress } from "src/@core/components/custom-components/FetchIpAddress";

import SelectAutoCompleteRole from "src/@core/components/custom-components/SelectAutoCompleteRole";

const userStatusObj = {
  ACTIVE: "Active",
  INACTIVE: "Inactive",
  REGISTERED: "Registered",
};

const Employees = ({ formData, setValue, data, onCancel }) => {
  const [userList, setUserList] = useState([]);
  const [isEmailVerified, setIsEmailVerified] = useState(false);
  const [isOtpVerified, setIsOtpVerified] = useState(false);
  const [isVerified, setIsVerified] = useState(false);
  const [openDeleteDialog, setOpenDeleteDialog] = useState(false);
  const [openDialogContent, setOpenDialogContent] = useState(false);
  const [openUpdateDialog, setOpenUpdateDialog] = useState(false);

  const [allLoading, setAllLoading] = useState(true);

  const [name, setName] = useState("");
  const [email, setEmail] = useState("");
  const [status, setStatus] = useState("");
  const [id, setId] = useState(null);
  const [editIndex, setEditIndex] = useState(null);
  const [openDialog, setOpenDialog] = useState(false);
  const [openEditDialog, setOpenEditDialog] = useState(false);
  const auth = useAuth();

  const [viewProfileDialogOpen, setViewProfileDialogOpen] = useState(false);

  const {
    employeeProfile,
    setEmployeeProfile,
    employeeData,
    getAllListValuesByListNameId,
  } = useContext(AuthContext);

  const [keyword, setKeyword] = useState("");
  const [searchKeyword, setSearchKeyword] = useState("");
  const [resendOtpDisabled, setResendOtpDisabled] = useState(false);
  const [expanded, setExpanded] = useState(true);
  const [resendOtpCountdown, setResendOtpCountdown] = useState(0);
  const [otp, setOTP] = useState("");
  const rowsPerPageOptions = [10, 15, 20, 25, 50, 100];
  const [loading, setLoading] = useState(false);
  const [saveLoading, setSaveLoading] = useState(false);
  const [pageSize, setPageSize] = useState(rowsPerPageOptions[0]);
  const [page, setPage] = useState(1);
  const [currentRow, setCurrentRow] = useState();
  const [rowCount, setRowCount] = useState(0);
  const [showOTPOptions, setShowOTPOptions] = useState(false);
  const [isChecked, setIsChecked] = useState(false);
  const [dialogMessage, setDialogMessage] = useState("");
  const [disableVerifyEmailButton, setDisableVerifyEmailButton] =
    useState(false);
  const [disableSaveButton, setDisableSaveButton] = useState(false);

  const [employeeId, setEmployeeId] = useState("");
  const [departmentId, setDepartmentId] = useState("");
  const [roleId, setRoleId] = useState("");
  const [selectedLocationId, setSelectedLocationId] = useState({});
  const [locationsData, setLocationsData] = useState(null);
  const [selectedDesignationId, setSelectedDesignationId] = useState("");
  const [designationsData, setDesignationsData] = useState(null);

  const handleClose = () => setOpenDialogContent(false);

  const fields = ["firstName", "lastName", "email", "contactNumber"];

  const {
    register,
    handleSubmit,
    setError,
    clearErrors,
    control,
    reset,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(EmployeeValidations(fields)),
    mode: "onChange",
  });

  const [employeesData, setEmployeesData] = useState(null);

  useEffect(() => {
    if (showOTPOptions) {
      setOTP("");
    }
  }, [showOTPOptions]);

  async function submit(data) {
    setSaveLoading(true);
    setDisableSaveButton(true);
    if (!isEmailVerified) {
      const message = `
          <div>
          <h3>
          Email Address need to be verified
          </h3>
          </div>
        `;
      setDialogMessage(message);
      setOpenDialogContent(true);
    } else {
      const fields = {
        firstName: data.firstName,
        lastName: data.lastName,
        mobileNumber: data.contactNumber,
        email: data.email,
        designation: data?.selectedDesignationId,
        reportingTo: employeeId,
        department: departmentId,
        rolesId:roleId,
        userType: "EMPLOYEE",
        status: "REGISTERED",
        employeeMetaData: {
          employeeData: {
            workLocation: selectedLocationId,
          },
        },
      };
      try {
        const response = await auth.postEmployee(
          fields,
          handleFailure,
          handleSuccess
        );
        setSaveLoading(false);
      } catch (error) {
        console.error("Employee Creation failed:", error);
        handleFailure();
      }

      setOpenDialog(false);
      reset();
      setSelectedDesignationId("");
      setEmployeeId("");
      setSelectedLocationId("");
      setDepartmentId("");
      setRoleId("");
      setIsVerified(false);
      setIsOtpVerified(false);
      setOTP("");
      fetchUsers(page, pageSize);
    }
    setIsEmailVerified(false);
    setSaveLoading(false);
  }

  const handleSuccess = () => {
    const message = `
    <div> 
      <h3> Employee Created Successfully.</h3>
    </div>
  `;
    setDialogMessage(message);
    setOpenDialogContent(true);
    setDisableSaveButton(false);
  };

  const handleFailure = () => {
    const message = `
    <div> 
      <h3> Failed to Add Employee Data. Please try again later.</h3>
    </div>
  `;
    setDialogMessage(message);
    setOpenDialogContent(true);
    setDisableSaveButton(false);
  };

  const handleButtonClick = () => {
    setOpenDialogContent(false);
  };

  const handleOpenDialog = () => {
    setOpenDialog(true);
    handleFormMenuClose();
  };

  const handleOpenEditDialog = () => {
    setOpenEditDialog(true);
    handleFormMenuClose(); // Close the menu
  };

  const handleCloseEditDialog = () => {
    setOpenEditDialog(false);
  };
  const [anchorEl, setAnchorEl] = useState(null);
  const [menuPosition, setMenuPosition] = useState(null);

  // Update the handleRowClick function to open EmployeeDetailsView
  const handleRowClick = (params) => {
    setCurrentRow(params.row); // Save the clicked row data
    setViewProfileDialogOpen(true); // Open the EmployeeDetailsView dialog
  };

  const handleMenuClick = (event, params) => {
    event.stopPropagation();
    setAnchorEl(event.currentTarget); // Set the anchor element to the clicked icon
    setMenuPosition({ mouseX: event.clientX, mouseY: event.clientY }); // Capture mouse click position
    setCurrentRow(params.row); // Save the row details for later actions
    setEmployeeProfile({
      ...employeeProfile,
      id: params.row.id,
    });
  };

  const handleMenuClose = () => {
    setAnchorEl(null); // Close menu
    setMenuPosition(null); // Reset menu position
  };

  const onClickViewProfile = () => {
    setViewProfileDialogOpen(true);
    handleMenuClose(); // Close menu after action
  };

  const onClickToggleStatus = () => {
    if (currentRow.status === "ACTIVE") {
      setOpenDeleteDialog(true);
    } else {
      setOpenUpdateDialog(true);
    }
    handleMenuClose(); // Close menu after action
  };

  const handleSelectChange = (event) => {
    const selectedId = event.target.value;
    setSelectedLocationId(selectedId);
  };

  const handleSelectDesignationChange = (event) => {
    const selectedId = event.target.value;
    setSelectedDesignationId(selectedId);
  };

  const isapicalling = useRef(false);
  const handleEmailVerification = async (formData) => {
    if (isapicalling.current) {
      // API call is already in progress, return early
      return;
    }
    isapicalling.current = true;
    setLoading(true);
    setDisableVerifyEmailButton(true);
   
   
    try {
      const ipAddress = await fetchIpAddress();
      const fields = {
        contactType: "EMAIL",
        contactValue: formData?.email,
        ipAddress: ipAddress,
      };
      const response = await auth.postOTP(fields);
      if (response.isVerified) {
        const message = `
        <div>
        <h3>
        Email already exists!!!
        </h3>
        </div>
        `;
        setLoading(false);
        setDialogMessage(message);
        setOpenDialogContent(true);
        setDisableVerifyEmailButton(false);
      } else {
        setEmail(fields?.contactValue);
        const message = `
          <div>
          <h3>
          OTP has been sent to your Email for verification. Please check.
          </h3>
          </div>
        `;
        setLoading(false);
        setDialogMessage(message);
        setOpenDialogContent(true);
        setShowOTPOptions(true);
        startResendCountdown();
      }

    } catch (error) {
      console.error("An error occurred during email verification:", error);
      const message = `
          <div>
          <h3>
          Error sending OTP to your Email. Please try again.
          </h3>
          </div>
        `;
      setLoading(false);
      setDialogMessage(message);
      setOpenDialogContent(true);
      setDisableVerifyEmailButton(false);
    }
    setLoading(false);
    isapicalling.current = false;
  };

  const handleOtpVerification = async (formData) => {

    const data = { contactType: "EMAIL", contactValue: formData?.email, otp: otp };
      try {
        const response = await auth.verifyOTP(data);
        if (response) {
          const message = `
          <div>
          <h3>
          Email has been verified successfully.
          </h3>
          </div>
        `;
        setLoading(false);
        setIsEmailVerified(true);
        setDialogMessage(message);
        setOpenDialogContent(true);
        setIsOtpVerified(true);
        setIsVerified(true);
        setShowOTPOptions(false);
        setDisableVerifyEmailButton(false);
        setDisableSaveButton(false);
        }
      } catch (error) {
        const message = `
        <div>
        <h3>
        OTP doesn't match. Please try again.
        </h3>
        </div>

      `;
      setLoading(false);
      setDialogMessage(message);
      setOpenDialogContent(true);
      }
  };
  const startResendCountdown = () => {
    setResendOtpDisabled(true);
    setResendOtpCountdown(30);

    const countdownInterval = setInterval(() => {
      setResendOtpCountdown((prevCountdown) => {
        if (prevCountdown > 0) {
          return prevCountdown - 1;
        } else {
          clearInterval(countdownInterval);
          setResendOtpDisabled(false);
          return 0;
        }
      });
    }, 1000);
  };

  const handleCloseDialog = () => {
    reset();
    setOpenDialog(false);
    setSelectedDesignationId("");
    setEmployeeId("");
    setSelectedLocationId("");
    setDepartmentId("");
    setRoleId("");
    setShowOTPOptions(false);
    setStatus("");
    setEditIndex(null);
    setId(null);
    setSaveLoading(false);
    setDisableVerifyEmailButton(false);
  };

  const [listOfEmployees, setListOfEmployees] = useState([]);
  const [employeesOptions, setEmployeesOptions] = useState([]);

  const [departmentOptions, setDepartmentOptions] = useState([]);

  const [formAnchorEl, setFormAnchorEl] = useState(null);

  // Handler to open the form menu
  const handleFormMenuOpen = (event) => {
    setFormAnchorEl(event.currentTarget);
  };

  // Handler to close the form menu
  const handleFormMenuClose = () => {
    setFormAnchorEl(null);
  };

  useEffect(() => {
    // Fetch employees
    axios({
      method: "get",
      url: getUrl(authConfig.selectDropdownNew) + "?selectionType=EMPLOYEES",
      headers: getAuthorizationHeaders(),
    })
      .then((res) => {
        setListOfEmployees(res.data.data);
      })
      .catch((err) => console.log("Employees error", err));
  }, [userList]);

  useEffect(() => {
    if (!!authConfig) {
      getAllListValuesByListNameId(
        authConfig.workLocation,
        (data) =>
          setLocationsData(
            data?.listValues?.map((item) => ({
              value: item.id,
              key: item.listValue,
            }))
          ),
        handleError
      );
      getAllListValuesByListNameId(
        authConfig.designationId,
        (data) =>
          setDesignationsData(
            data?.listValues?.map((item) => ({
              value: item.id,
              key: item.listValue,
            }))
          ),
        handleError
      );
      getAllListValuesByListNameId(
        authConfig.departmentId,
        (data) =>
          setDepartmentOptions(
            data?.listValues?.map((item) => ({
              value: item.id,
              key: item.listValue,
            }))
          ),
        handleError
      );
      getAllListValuesByListNameId(
        authConfig.departmentId,
        (data) =>
          setDepartmentOptions(
            data?.listValues.map((item) => ({
              value: item.id,
              key: item.listValue,
            }))
          ),
        handleError
      );
    }
  }, [authConfig]);

  useEffect(() => {
    if (!!listOfEmployees) {
      let data = [];
      listOfEmployees?.map((entry) => {
        data.push({ value: entry.id, key: entry.name });
      });
      setEmployeesOptions(data);
    }
  }, [listOfEmployees]);


  const handleError = (error) => {
    console.error("Employees page:", error);
  };

  const fetchUsers = async (currentPage, currentPageSize, searchKeyword) => {
    const url = getUrl(authConfig.employeeGetAllEndpoint);

    const headers = getAuthorizationHeaders();

    const data = {
      page: currentPage,
      pageSize: currentPageSize,
      searchKeyword: searchKeyword,
    };

    try {
      const response = await axios({
        method: "post",
        url: url,
        headers: headers,
        data: data,
      });

      if (response.data) {
        setUserList(response.data?.employees || []);
        setRowCount(response.data?.rowCount || 0);
      } else {
        console.error("Unexpected API response format:", response);
      }
    } catch (error) {
      console.error("Error fetching users:", error);
    } finally {
      setAllLoading(false);
    }
  };

  const closeViewProfileDialog = () => {
    setEmployeeProfile(null);
    setViewProfileDialogOpen(false);
  };
  const handleCloseDeleteDialog = () => {
    setOpenDeleteDialog(false);
    fetchUsers(page, pageSize);
  };

  const handleCloseUpdateDialog = () => {
    setOpenUpdateDialog(false);
    fetchUsers(page, pageSize);
  };

  useEffect(() => {
    fetchUsers(page, pageSize, searchKeyword);
  }, [page, pageSize, searchKeyword]);

  const [roles, setRoles] = useState([]);
  const rolesArray = roles?.map((item) => ({
    value: item?.id,
    key: item?.name,
    description:item?.description
  }));

  useEffect(() => {
    if (authConfig?.defaultEmployeeRoleEndpoint) {
      axios({
        method: "get",
        url:
          getUrl(authConfig.individualEndpoint) +
          "/roles-hierarchy/" +
          authConfig?.defaultEmployeeRoleEndpoint,
        headers: getAuthorizationHeaders(
          authConfig.INDIVIDUAL_GET_ROLE_HIERARCHY_MIMETYPE
        ),
      })
        .then((res) => {
          setRoles(res.data.rolesGetResponse);
        })
        .catch((err) => console.log("error", err));
    }
  }, [authConfig?.defaultEmployeeRoleEndpoint]);

  const handleEmployeeChange = (newValue) => {
    setEmployeeId(newValue);
  };
  const handleRole = (newValue) => {
    setRoleId(newValue);
  };

  const handleDepartmentChange = (newValue) => {
    setDepartmentId(newValue);
  };

  const handlePageChange = (direction) => {
    if (direction === page) {
      setPage(page + 1);
    } else {
      setPage(page - 1);
    }
  };

  const handlePageSizeChange = (params) => {
    if (params) {
      setPageSize(params);
      setPage(1);
    }
  };

  const mapIsActiveToLabel = (status) => {
    return userStatusObj[status] || "Unknown";
  };

  const columns = [
    {
      field: "name",
      headerName: "Name",
      flex: 2.5,
      minWidth: 185,
      valueGetter: (params) => {
        const row = params.row;
        return `${row.firstName} ${row.lastName}`;
      },
      renderCell: (params) => {
        const name = params.value;
        return (
          <Tooltip title={name}>
            <span>{name?.length > 21 ? `${name}` : name}</span>
          </Tooltip>
        );
      },
    },
    {
      field: "designation",
      headerName: "Designation",
      flex: 1.9,
      minWidth: 160,
      valueGetter: (params) => {
        const row = params.row;
        const designationData = designationsData?.find(
          (designation) => designation.value === row.designation
        );
        return designationData ? designationData.key : "N/A";
      },
      renderCell: (params) => {
        const designation = params.value || "N/A";
        return (
          <Tooltip title={designation}>
            <span>
              {designation?.length > 21 ? `${designation}` : designation}
            </span>
          </Tooltip>
        );
      },
    },
    {
      field: "reportingTo",
      headerName: "Reporting To",
      flex: 2.3,
      minWidth: 95,
      valueGetter: (params) => {
        const row = params?.row;
        const reportingToId = row?.reportingTo;
        const reportingToEmployee = listOfEmployees?.find(
          (employee) => employee?.id === reportingToId
        );
        return reportingToEmployee ? `${reportingToEmployee?.name}` : "";
      },
      renderCell: (params) => {
        const reportingTo = params.value;
        return (
          <Tooltip title={reportingTo}>
            <span>
              {reportingTo?.length > 21
                ? `${reportingTo?.substring(0, 21)}...`
                : reportingTo}
            </span>
          </Tooltip>
        );
      },
    },
    {
      field: "email",
      minWidth: 180,
      headerName: "Email",
      flex: 3,
      renderCell: (params) => {
        const email = params?.value;

        return email?.length > 21 ? (
          <Tooltip title={email}>
            <Link
              href={`mailto:${email}`}
              target="_blank"
              rel="noopener noreferrer"
              sx={{ color: "#6666ff" }}
            >
              <span>{email}</span>
            </Link>
          </Tooltip>
        ) : (
          <Link
            href={`mailto:${email}`}
            target="_blank"
            rel="noopener noreferrer"
            sx={{ color: "#6666ff" }}
          >
            {email}
          </Link>
        );
      },
    },
    {
      field: "mobileNumber",
      minWidth: 135,
      headerName: "Mobile No",
      flex: 0.1,
      renderCell: (params) => {
        const mobileNumber = params?.value;

        return mobileNumber?.length > 9 ? (
          <Tooltip title={mobileNumber}>
            <Link
              href={`mailto:${mobileNumber}`}
              target="_blank"
              rel="noopener noreferrer"
              sx={{ color: "#6666ff" }}
            >
              <span>{mobileNumber}</span>
            </Link>
          </Tooltip>
        ) : (
          <Link
            href={`mailto:${mobileNumber}`}
            target="_blank"
            rel="noopener noreferrer"
            sx={{ color: "#6666ff" }}
          >
            {mobileNumber}
          </Link>
        );
      },
    },
    {
      field: "status",
      headerName: "Status",
      flex: 0.13,
      minWidth: 115,
      renderCell: ({ row }) => {
        return (
          <CustomChip
            rounded={true}
            skin="light"
            size="small"
            label={mapIsActiveToLabel(row.status)}
            color={
              row.status === "ACTIVE" || row.status === "REGISTERED"
                ? "success"
                : "error"
            }
            sx={{ textTransform: "capitalize" }}
          />
        );
      },
    },
    {
      field: "actions",
      headerName: "Actions",
      width: 100,
      sortable: false,
      renderCell: (params) => (
        <>
          <CustomAvatar
            skin="light"
            variant="rounded"
            sx={{
              mr: { xs: 2, lg: 4 },
              width: 34,
              height: 34,
              cursor: "pointer",
            }}
            onClick={(event) => handleMenuClick(event, params)}
          >
            <Icon icon="bi:three-dots-vertical" />
          </CustomAvatar>
          <Menu
            anchorEl={anchorEl}
            open={Boolean(anchorEl)}
            onClose={handleMenuClose}
            anchorReference="anchorPosition"
            anchorPosition={
              menuPosition
                ? { top: menuPosition.mouseY, left: menuPosition.mouseX }
                : undefined
            }
          >
            <MenuItem onClick={onClickViewProfile}>
              <Icon icon="iconamoon:edit" style={{ marginRight: 8 }} />
              Edit
            </MenuItem>
            <MenuItem onClick={onClickToggleStatus}>
              <ListItemIcon>
                <Icon
                  icon={
                    currentRow?.status === "ACTIVE"
                      ? "iconamoon:trash"
                      : "tabler:circle-check"
                  }
                />
              </ListItemIcon>
              {currentRow?.status === "ACTIVE" ? "Deactivate" : "Activate"}
            </MenuItem>
          </Menu>
        </>
      ),
    },
  ];

  const { canMenuPage, rbacRoles } = useRBAC();

  const router = useRouter();

  const canAccessEmployees = (requiredPermission) =>
    canMenuPage(MENUS.LEFT, PAGES.EMPLOYEES, requiredPermission);

  useEffect(() => {
    if (rbacRoles != null && rbacRoles?.length > 0) {
      if (!canAccessEmployees(PERMISSIONS.READ)) {
        router.push("/401");
      }
    }
  }, [rbacRoles]);

  if (canAccessEmployees(PERMISSIONS.READ)) {
    return (
      <>
        <Grid>
          <>
            <Box
              sx={{
                py: 3,
                px: 6,
                rowGap: 2,
                columnGap: 4,
                display: "flex",
                flexWrap: "wrap",
                alignItems: "center",
                justifyContent: "space-between",
              }}
            >
              <Grid container spacing={3} alignItems="center">
                <Grid item xs={12} sm={4} sx={{ textAlign: "flex-start" }}>
                  <Typography variant="h6" fontWeight={"600"}>
                    Employee List
                  </Typography>
                </Grid>

                <Grid item xs={12} sm={8}>
                  <Grid
                    container
                    spacing={2}
                    alignItems="center"
                    justifyContent="flex-end"
                  >
                    <Grid item xs={12} sm="auto">
                      <FormControl>
                        <Controller
                          name="mainSearch"
                          control={control}
                          render={({ field: { onChange } }) => (
                            <TextField
                              id="mainSearch"
                              placeholder="Search by name & email"
                              value={keyword}
                              onChange={(e) => {
                                onChange(e.target.value);
                                setKeyword(e.target.value);
                                setSearchKeyword(e.target.value);
                              }}
                              onKeyDown={(e) => {
                                if (e.key === "Enter") {
                                  setSearchKeyword(keyword);
                                  fetchUsers(page, pageSize, searchKeyword);
                                }
                              }}
                              sx={{
                                "& .MuiInputBase-root": {
                                  height: "40px",
                                },
                              }}
                              InputProps={{
                                endAdornment: (
                                  <InputAdornment position="start">
                                    <SearchIcon
                                      sx={{
                                        cursor: "pointer",
                                        marginRight: "-15px",
                                      }}
                                      onClick={() => {
                                        setSearchKeyword(keyword);
                                        fetchUsers(
                                          page,
                                          pageSize,
                                          searchKeyword
                                        );
                                      }}
                                    />{" "}
                                  </InputAdornment>
                                ),
                              }}
                            />
                          )}
                        />
                      </FormControl>
                    </Grid>

                    <Grid item xs="auto" sm="auto">
                      <Button variant="contained" onClick={handleFormMenuOpen}>
                        Add New Employee&nbsp;
                        <Icon icon="tabler:chevron-down" />
                      </Button>
                      <Menu
                        anchorEl={formAnchorEl} // Use new state
                        open={Boolean(formAnchorEl)}
                        onClose={handleFormMenuClose} // Use new handler
                        PaperProps={{
                          style: {
                            width: formAnchorEl
                              ? formAnchorEl.clientWidth
                              : undefined,
                          },
                        }}
                      >
                        <MenuItem onClick={handleOpenDialog}>
                          Short Form
                        </MenuItem>
                        {/* <MenuItem onClick={handleOpenEditDialog}>
                          Long Form
                        </MenuItem> */}
                      </Menu>
                    </Grid>
                  </Grid>
                </Grid>
              </Grid>
            </Box>

            {/* <Divider /> */}

            {/* Long form */}
            <>
              <Dialog
                open={openEditDialog}
                scroll="paper"
                fullScreen
              >
                <DialogTitle
                  sx={{
                    position: "relative",
                    borderBottom: (theme) =>
                      `1px solid ${theme.palette.divider}`,
                    p: (theme) => `${theme.spacing(1.75, 4)} !important`,
                    display: "flex",
                    alignItems: "center",
                    justifyContent: { xs: "start" },
                    fontSize: { xs: 19, md: 20 },
                    height: "50px", // height
                  }}
                  textAlign={"center"}
                >
                  <Box
                    sx={{
                      ml: {
                        xs: 6,
                        sm: 6,
                        xl: 6,
                      },
                    }}
                  >
                    Add New Employee
                  </Box>
                  <Box
                    sx={{
                      position: "absolute",
                      top: "9px",
                      right: "10px",
                      mr: {
                        xs: 6,
                        sm: 6,
                        md: 6,
                        lg: 6,
                        xl: 6,
                      },
                    }}
                  >
                    <IconButton
                      size="small"
                      onClick={handleCloseEditDialog}
                      sx={{
                        // p: "0.438rem",
                        borderRadius: 1,
                        color: "common.white",
                        backgroundColor: "primary.main",
                        "&:hover": {
                          backgroundColor: "#66BB6A",
                          transition:
                            "background 0.5s ease, transform 0.5s ease",
                    
                        },
                      }}
                    >
                      <Icon icon="tabler:x" fontSize="1rem" />
                    </IconButton>
                  </Box>
                </DialogTitle>

                <DialogContent
                  sx={{
                    position: "relative",
                    p: (theme) => `${theme.spacing(1.75, 4)} !important`,
                  }}
                >
                  <EmployeeDetailsLongForm
                    formData={{}} // Pass necessary formData here
                    onCancel={handleCloseEditDialog}
                    fetchUsers={fetchUsers}
                  />
                </DialogContent>
                <DialogActions
                  sx={{
                    justifyContent: "end",
                    borderTop: (theme) => `1px solid ${theme.palette.divider}`,
                    p: (theme) => `${theme.spacing(2.5,4)} !important`,
                  }}
                >
                  <Button onClick={handleCloseEditDialog} color="primary">
                    Cancel
                  </Button>
                  <Button
                    variant="contained"
                    sx={{
                      mr: {
                        xs: 5.5,
                        sm: 5.5,
                        md: 5.5,
                        lg: 5.5,
                        xl: 6,
                      },
                    }}
                  >
                    Save
                  </Button>
                </DialogActions>
              </Dialog>
            </>

            <Dialog
              open={openDialog}
              onClose={handleCloseDialog}
               maxWidth="xs"
            >
              <DialogTitle
                sx={{
                  position: "relative",
                  borderBottom: (theme) => `1px solid ${theme.palette.divider}`,
                  p: (theme) => `${theme.spacing(1.75, 4)} !important`,
                  display: "flex",
                  alignItems: "center",
                  justifyContent: { xs: "start" },
                  fontSize: { xs: 19, md: 20 },
                  height: "50px", // Set fixed height for header
                }}
                textAlign={"center"}
              >
                <Box
                  sx={{
                    fontSize: {
                      xs: 14, // Smaller font size for mobile
                      sm: 15, // Slightly larger font size
                      md: 17, // Default font size for larger screens
                      lg: 16,
                    },
                    fontWeight: 600, // Bold text if needed
                    ml: {
                      xs: 3,
                      xl: 3,
                    },
                  }}
                >
                  Add New Employee
                </Box>
                <Box
                  sx={{
                    position: "absolute",
                    top: "9px",
                    right: "10px",
                    mr: {
                      xs: 5.5,
                      sm: 5.5,
                      md: 5.5,
                      lg: 5.5,
                      xl: 5.5,
                    },
                  }}
                >
                  <IconButton
                    size="small"
                    onClick={handleCloseDialog}
                    sx={{
                      // p: "0.438rem",
                      borderRadius: 1,
                      color: "common.white",
                      backgroundColor: "primary.main",
                      "&:hover": {
                        backgroundColor: "#66BB6A",
                        transition: "background 0.5s ease, transform 0.5s ease",
                      },
                    }}
                  >
                    <Icon icon="tabler:x" fontSize="1rem" />
                  </IconButton>
                </Box>
              </DialogTitle>
              <DialogContent
                sx={{
                  position: "relative",
                  p: (theme) => `${theme.spacing(6, 8)} !important`,
                }}
              >
                <Grid container spacing={2}>
                  <Grid item xs={12} xl ={12}>
                    <FormControl fullWidth>
                      <Controller
                        name="firstName"
                        control={control}
                        defaultValue={formData?.firstName}
                        render={({ field }) => (
                          <NameTextField
                            {...field}
                            size="small"
                            label=" First Name"
                            InputLabelProps={{ shrink: true }}
                            placeholder="Enter your firstName"
                            error={Boolean(errors.firstName)}
                            helperText={errors.firstName?.message}
                            aria-describedby="Section1-firstName"
                          />
                        )}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item xs={12} xl ={12}>
                    <FormControl fullWidth>
                      <Controller
                        name="lastName"
                        control={control}
                        defaultValue={formData?.lastName}
                        render={({ field }) => (
                          <NameTextField
                            {...field}
                            size="small"
                            label="Last Name"
                            InputLabelProps={{ shrink: true }}
                            placeholder="Enter your Last name"
                            error={Boolean(errors.lastName)}
                            helperText={errors.lastName?.message}
                            aria-describedby="Section1-lastName"
                          />
                        )}
                      />
                    </FormControl>
                  </Grid>

                  <Grid item xs={12} xl ={12} >
                    <FormControl
                      fullWidth
                      error={Boolean(errors.selectedDesignationId)}
                    >
                      <Controller
                        name="selectedDesignationId"
                        control={control}
                        rules={{ required: "Designation is required" }}
                        render={({ field }) => (
                          <SelectAutoComplete
                            id="designation"
                            label="Designation"
                            nameArray={designationsData}
                            value={field.value}
                            onChange={(e) => field.onChange(e.target.value)}
                          />
                        )}
                      />
                      {errors.selectedDesignationId && (
                        <FormHelperText
                          sx={{ color: "error.main" }}
                          id="validation-selectedDesignationId"
                        >
                          {errors.selectedDesignationId.message}
                        </FormHelperText>
                      )}
                    </FormControl>
                  </Grid>

                  <Grid item xs={12} xl ={12}>
                    <SelectAutoCompleteRole
                      register={register}
                      clearErrors={clearErrors}
                      id={"roleId"}
                      label={"Role"}
                      name="roleId"
                      nameArray={rolesArray}
                      defaultValue={roleId}
                      value={roleId}
                      onChange={(event) => handleRole(event.target.value)}
                      aria-describedby="roleId"
                    />
                  </Grid>

                  <Grid item xs={12} xl ={12}>
                    <SelectAutoComplete
                      register={register}
                      clearErrors={clearErrors}
                      id={"employeeId-select"}
                      label={"Reporting to "}
                      name="employeeId-select"
                      nameArray={employeesOptions}
                      defaultValue={employeeId}
                      value={employeeId}
                      onChange={(event) =>
                        handleEmployeeChange(event.target.value)
                      }
                      aria-describedby="employeeId-select"
                    />
                  </Grid>

                  <Grid item xs={12} xl ={12}>
                    <FormControl fullWidth>
                      <Controller
                        name="workLocation"
                        control={control}
                        defaultValue={formData?.workLocation || ""}
                        render={({ field }) => (
                          <SelectAutoComplete
                            {...field}
                            labelId="location-select-label"
                            id="location-select"
                            size="small"
                            value={selectedLocationId}
                            label="Work Location"
                            nameArray={locationsData}
                            onChange={(e) => {
                              field.onChange(e);
                              handleSelectChange(e);
                            }}
                          />
                        )}
                      />
                    </FormControl>
                    {errors.selectedLocationId && (
                      <FormHelperText
                        sx={{ color: "error.main" }}
                        id="validation-selectedLocationId"
                      >
                        {errors.selectedLocationId?.message}
                      </FormHelperText>
                    )}
                  </Grid>
                  <Grid item xs={12} xl ={12}>
                    <FormControl fullWidth>
                      <Controller
                        name="contactNumber"
                        control={control}
                        defaultValue={formData?.contactNumber}
                        render={({ field }) => (
                          <MobileNumberValidation
                            {...field}
                            size="small"
                            type="tel"
                            label="Contact Number"
                            InputLabelProps={{ shrink: true }}
                            error={Boolean(errors.contactNumber)}
                            placeholder="Enter 10 digit Mobile Number"
                            helperText={errors.contactNumber?.message}
                            aria-describedby="Section1-contactNumber"
                          />
                        )}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item xs={12} xl ={12} >
                    <SelectAutoComplete
                      register={register}
                      clearErrors={clearErrors}
                      id={"departmentId-select"}
                      label={"Department"}
                      name="departmentId-select"
                      nameArray={departmentOptions}
                      defaultValue={departmentId}
                      value={departmentId}
                      onChange={(event) =>
                        handleDepartmentChange(event.target.value)
                      }
                      aria-describedby="departmentId-select"
                    />
                  </Grid>
                  <Grid item xs={12} xl={9.7} lg={9.8} sm={9} md={9.4}>
                    {!isEmailVerified && (
                      <>
                        <FormControl fullWidth>
                          <Controller
                            name="email"
                            control={control}
                            render={({ field }) => (
                              <EmailTextField
                                {...field}
                                size="small"
                                type="email"
                                label="Email address"
                                error={Boolean(errors.email)}
                                inputProps={{ maxLength: 50 }}
                                InputLabelProps={{ shrink: true }}
                                placeholder="Enter email address"
                                helperText={errors.email?.message}
                              />
                            )}
                          />
                        </FormControl>
                      </>
                    )}
                    {isEmailVerified && (
                      <>
                        <FormControl fullWidth>
                          <div
                            style={{
                              display: "flex",
                              alignItems: "baseline",
                            }}
                          >
                            <Typography
                              sx={{ marginBottom: 0, fontWeight: "bold" }}
                            >
                              Email:
                            </Typography>
                            <Typography
                              variant="body2"
                              sx={{
                                marginRight: "4px",
                                marginLeft: "6px",
                              }}
                            >
                              {email}
                            </Typography>
                          </div>
                        </FormControl>

                        <Box sx={{ display: "flex", alignItems: "center" }}>
                          <CheckCircleIcon
                            sx={{
                              color: "green",
                              marginBottom: "-10px",
                              paddingBottom: 2,
                              marginLeft: "55px",
                            }}
                          />
                          <Typography
                            sx={{
                              marginLeft: "6px",
                              marginBottom: "-10px",
                              paddingBottom: 2,
                            }}
                          >
                            Verified
                          </Typography>
                        </Box>
                      </>
                    )}
                  </Grid>
                  {!isEmailVerified && (
                    <Grid item xs={4} sm={3} lg={2} md={2.3} xl={2}>
                      <Button
                        display="flex"
                        justifyContent="center"
                        color="primary"
                        variant="contained"
                        onClick={() => {
                          handleSubmit(handleEmailVerification)();
                        }}
                        disabled={disableVerifyEmailButton}
                        sx={{
                          px: { lg: 2.08 },
                        }}
                      >
                        {loading ? (
                          <CircularProgress color="inherit" size={22} />
                        ) : (
                          "Verify"
                        )}
                      </Button>
                    </Grid>
                  )}
                  {showOTPOptions && !isVerified && (
                    <>
                      <Grid item xs={12} sm={4} sx={{ marginLeft: "0.1rem" }}>
                        <TextField
                          fullWidth
                          margin="dense"
                          label="OTP"
                          name="otp"
                          type="text"
                          size="small"
                          placeholder="OTP(6 Digits)"
                          inputProps={{ maxLength: 6 }}
                          value={otp}
                          onChange={(e) => {
                            setOTP(
                              e.target.value
                                .replace(/[^\d]/g, "")
                                .substring(0, 6)
                            );
                          }}
                          required
                          sx={{
                            "& .MuiInputBase-input.MuiOutlinedInput-input": {
                              padding: "8px",
                            },
                          }}
                        />
                      </Grid>
                      {!isVerified && (
                        <Grid item xs={4} sm={3} md={2.5}>
                          <FormControl fullWidth>
                            <Button
                              variant="contained"
                              color="primary"
                              onClick={handleSubmit(handleOtpVerification)}
                              style={{
                                marginTop: "8px"
                              }}
                            >
                              Verify
                            </Button>
                          </FormControl>
                        </Grid>
                      )}
                      {!isVerified && (
                        <Grid item xs={6} sm={3.5} md={4}>
                          <FormControl fullWidth>
                            <Button
                              variant="outlined"
                              color="primary"
                              onClick={() => {
                                handleSubmit(handleEmailVerification)();
                              }}
                              disabled={resendOtpDisabled}
                              style={{
                                marginTop: "9px",
                              }}
                            >
                              Resend&nbsp;OTP&nbsp;({resendOtpCountdown}s)
                            </Button>
                          </FormControl>
                        </Grid>
                      )}
                    </>
                  )}
                </Grid>
              </DialogContent>
              <DialogActions
                sx={{
                  justifyContent: "end",
                  borderTop: (theme) => `1px solid ${theme.palette.divider}`,
                  p: (theme) => `${theme.spacing(2.5,4)} !important`,
                  height: "50px", // Set fixed height for footer
                }}
              >
                <Button
                  display="flex"
                  justifyContent="center"
                  variant="outlined"
                  color="primary"
                  onClick={() => {
                    handleCloseDialog();
                    setDisableVerifyEmailButton(false);
                  }}
                >
                  Cancel
                </Button>

                <Button
                  display="flex"
                  justifyContent="center"
                  variant="contained"
                  color="primary"
                  disabled={disableSaveButton}
                  onClick={handleSubmit(submit)}
                  sx={{
                    mr: {
                      xs: 4,
                      sm: 4,
                      md: 4,
                      lg: 4,
                      xl: 4,
                    },
                  }}
                >
                  {saveLoading ? (
                    <CircularProgress color="inherit" size={22} />
                  ) : (
                    "Save"
                  )}
                </Button>
              </DialogActions>
            </Dialog>
            <Divider />
            <Dialog
              open={viewProfileDialogOpen}
              onClose={closeViewProfileDialog}
              fullScreen
              fullWidth
              maxWidth="xl"
            >
              <DialogTitle
                sx={{
                  position: "relative",
                  borderBottom: (theme) => `1px solid ${theme.palette.divider}`,
                  p: (theme) => `${theme.spacing(1.75, 4)} !important`,
                  display: "flex",
                  alignItems: "center",
                  justifyContent: { xs: "start" },
                  fontSize: { xs: 19, md: 20 },
                  height: "50px", // height
                }}
                textAlign={"center"}
              >
                <Typography
                  variant="h6"
                  sx={{
                    marginLeft: {
                      xs: 2,
                      sm: 6,
                    },
                  }}
                >
                  Update Employee Profile &nbsp;
                </Typography>
                <Box
                  sx={{
                    position: "absolute",
                    top: "9px",
                    right: "10px",
                    mr: {
                      xs: 3.5,
                      sm: 7.5,
                      md: 7,
                      lg: 7,
                      xl: 12,
                    },
                  }}
                >
                  <IconButton
                    size="small"
                    onClick={closeViewProfileDialog}
                    sx={{
                      borderRadius: 1,
                      color: "common.white",
                      backgroundColor: "primary.main",
                      "&:hover": {
                        backgroundColor: "#66BB6A",
                        transition: "background 0.5s ease, transform 0.5s ease",
                      },
                    }}
                  >
                    <Icon icon="tabler:x" fontSize="1rem" />
                  </IconButton>
                </Box>
              </DialogTitle>
              <DialogContent
                sx={{
                  position: "relative",
                  ":playing": (theme) => `${theme.spacing(4)} !important`,
                  px: (theme) => [
                    `${theme.spacing(6)} !important`,
                    `${theme.spacing(10)} !important`,
                  ],
                }}
              >
                <EmployeeDetailsView
                  data={employeeData} // Pass currentRow as the data prop
                  employeesOptions={employeesOptions}
                  departmentOptions={departmentOptions}
                  expanded={expanded}
                  onCancel={closeViewProfileDialog}
                  fetchUsers={fetchUsers}
                  rolesArray={rolesArray}
                />
              </DialogContent>
              <DialogActions
                sx={{
                  justifyContent: "end",
                  borderTop: (theme) => `1px solid ${theme.palette.divider}`,
                  p: (theme) => `${theme.spacing(1.85)} !important`,
                  height: "50px", // height
                }}
              >
                <Button
                  onClick={closeViewProfileDialog}
                  sx={{
                    mr: {
                      xs: 4,
                      sm: 8,
                      xl: 12,
                    },
                  }}
                >
                  Close
                </Button>
              </DialogActions>
            </Dialog>

            <CardContent>
              <div style={{ height: 380, width: "100%" }}>
                {allLoading ? (
                  <Box
                    display="flex"
                    justifyContent="center"
                    alignItems="center"
                    height="60vh"
                  >
                    <FallbackSpinner />
                  </Box>
                ) : (
                  <>
                    <DataGrid
                      rows={userList}
                      columns={columns}
                      pagination
                      pageSize={pageSize}
                      page={page - 1}
                      rowsPerPageOptions={rowsPerPageOptions}
                      rowCount={rowCount}
                      paginationMode="server"
                      onPageChange={handlePageChange}
                      onPageSizeChange={handlePageSizeChange}
                      rowHeight={38}
                      headerHeight={38}
                      components={{
                        NoRowsOverlay: () => (
                          <Typography
                          variant="body1"
                          align="center"
                          sx={{ marginTop: "120px" }}
                          >
                            {userList?.length === 0 ? "No Data" : "No Rows"}
                          </Typography>
                        )
                      }}
                    />
                  </>
                )}
              </div>
            </CardContent>
            <Divider />

            <DeleteDialog
              open={openDeleteDialog}
              onClose={handleCloseDeleteDialog}
              data={currentRow}
            />

            <UpdateDialog
              open={openUpdateDialog}
              onClose={handleCloseUpdateDialog}
              data={currentRow}
            />
          </>
        </Grid>
        <Dialog
          open={openDialogContent}
          onClose={handleClose}
          aria-labelledby="alert-dialog-title"
          aria-describedby="alert-dialog-description"
          PaperProps={{
            sx: {
              p: (theme) => `${theme.spacing(2.5)} !important`,
              backgroundColor: (theme) => theme.palette.primary.background,
            },
          }}
        >
          <Box
            sx={{
              width: "100%",
              borderRadius: 1,
              textAlign: "center",
              border: (theme) => `1px solid ${theme.palette.divider}`,
              borderColor: "primary.main",
            }}
          >
            <DialogContent>
              <DialogContentText
                id="alert-dialog-description"
                color="primary.main"
              >
                <div dangerouslySetInnerHTML={{ __html: dialogMessage }} />
              </DialogContentText>
            </DialogContent>
            <DialogActions>
              <Button
                variant="contained"
                onClick={handleButtonClick}
                sx={{ margin: "auto", width: 100 }}
              >
                Okay
              </Button>
            </DialogActions>
          </Box>
        </Dialog>
      </>
    );
  } else {
    return null;
  }
};

export default Employees;
