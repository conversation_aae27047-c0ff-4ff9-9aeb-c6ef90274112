import React from "react";
import { DataGrid } from "@mui/x-data-grid";
import { Box } from "@mui/system";
import FallbackSpinner from "src/@core/components/spinner";

const WorkOrdersDataGrid = ({
  columns,
  workOrdersList,
  loading,
  page,
  pageSize,
  rowCount,
  setPage,
  setPageSize,
  rowsPerPageOptions,
}) => {
  const handlePageChange = (direction) => {
    if (direction === page) {
      setPage(page + 1);
    } else {
      setPage(page - 1);
    }
  };

  const handlePageSizeChange = (params) => {
    if (params) {
      setPageSize(params);
      setPage(1);
    }
  };

  return (
    <div style={{ height: 380, width: "100%", padding: "10px" }}>
      {loading ? (
        <Box
          display="flex"
          justifyContent="center"
          alignItems="center"
          height="60vh"
        >
          <FallbackSpinner />
        </Box>
      ) : (
        <>
          <DataGrid
            rows={workOrdersList || []}
            columns={columns}
            pagination
            pageSize={pageSize}
            page={page - 1}
            rowsPerPageOptions={
              workOrdersList?.length > 0 ? rowsPerPageOptions : []
            }
            rowCount={rowCount}
            paginationMode="server"
            onPageChange={handlePageChange}
            onPageSizeChange={handlePageSizeChange}
            rowHeight={38}
            headerHeight={38}
          />
        </>
      )}
    </div>
  );
};

export default WorkOrdersDataGrid;
