import React, { useState } from "react";
import {
  ClickAwayListener,
  Typography,
  Table,
  TableHead,
  Paper,
  TableBody,
  TableRow,
  TableCell,
  TableContainer,
  Box,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  useMediaQuery,
  Popover,
} from "@mui/material";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import { useRouter } from "next/router";
import TopNavigation from "src/top-menu";
import IconButton from "@mui/material";
import ArrowRightAltIcon from '@mui/icons-material/ArrowRightAlt';

function MegaMenu() {
  const [isOpen, setIsOpen] = useState(true);
  const [anchorEl, setAnchorEl] = useState(null);
  const [hoveredCategory, setHoveredCategory] = useState(null);

  // Detect screen sizes
  const isMobile = useMediaQuery("(max-width: 700px)");

  const dataFromBackend = TopNavigation();


  // Group menu items by category
  const groupedMenu = {};
  dataFromBackend?.forEach((item) => {
    groupedMenu[item.title] = item.children;
  });

  const categories = Object.keys(groupedMenu);

  const handleClickAway = () => {
    setIsOpen(false);
    setAnchorEl(null);
  };

  const router = useRouter();

  const handleCellClick = (category, index) => {
    const url = groupedMenu[category][index]?.path;
    if (url) {
      setIsOpen(false);
      router.push(url);
    }
  };

  const handleSubheadingClick = (url) => {
    if (url) {
      setIsOpen(false);
      router.push(url);
    }
  };

  const handleMouseEnter = (event, category) => {
    setHoveredCategory(category);
    setAnchorEl(event.currentTarget);
  };

  const handleMouseLeave = () => {
    setHoveredCategory(null);
    setAnchorEl(null);
  };

  const open = Boolean(anchorEl);

  return (
    <ClickAwayListener onClickAway={handleClickAway}>
      <div style={{ position: "relative" }}>
        {isOpen && (
          <Box
            sx={{
              top: 0,
              left: 0,
              zIndex: 1100,
              background: "white",
              borderRadius: "10px",
              boxShadow: "0 8px 16px rgba(0,0,0,0.1)",
              width: isMobile ? "100%" : "80%",
              height: isMobile ? "100vh" : "auto", // Full screen height in mobile view
              overflow: "hidden",
              position: "fixed",
              marginLeft: isMobile ? 0 : 40,
              marginTop: 12.5,
            }}
          >
            {isMobile ? (
              // Mobile view using Accordion
              categories.map((category, index) => (
                <Accordion key={index} sx={{ borderBottom: "1px solid #ddd" }}>
                  <AccordionSummary
                    expandIcon={<ExpandMoreIcon />}
                    aria-controls={`panel-${index}-content`}
                    id={`panel-${index}-header`}
                    sx={{
                      padding: "10px", // Default padding
                      "@media (max-width: 768px)": {
                        padding: "4px 8px", // Reduced padding in mobile view
                      },
                    }}
                  >
                    <Typography
                      sx={{
                        fontWeight: "bold",
                        whiteSpace: "normal",
                        wordWrap: "break-word",
                        maxWidth: "200px",
                        overflowX: "hidden",
                        textOverflow: "ellipsis",
                        padding: "10px", // Default padding
                        "@media (max-width: 768px)": {
                          padding: "2px 4px", // Reduced padding in mobile view
                          fontSize: "0.875rem", // Reduced font size in mobile view
                        },
                      }}
                    >
                      {category.replace(/_/g, " ")}
                    </Typography>
                  </AccordionSummary>
                  <AccordionDetails>
                    {groupedMenu[category]?.map((subItem, subIndex) => (
                      <Typography
                        key={subIndex}
                        onClick={() => handleSubheadingClick(subItem.path)}
                        sx={{
                          padding: "8px 16px",
                          cursor: "pointer",
                          "&:hover": {
                            background: "#f2f2f2",
                          },
                        }}
                      >
                        {subItem.title}
                      </Typography>
                    ))}
                  </AccordionDetails>
                </Accordion>
              ))
            ) : (
              // Tablet and Desktop/Laptop view using Table with Hover
              <TableContainer
                component={Paper}
                sx={{
                  width: "100%",
                  overflowX: "hidden",
                  "@media (max-width: 1024px)": {
                    overflowX: "auto", // Enable horizontal scrolling in tablet view
                  },
                }}
              >
                <Table sx={{ minWidth: "100%" }}>
                  <TableHead sx={{ border: '0 !important', padding: '0 !important' }}>
                    <TableRow sx={{ border: 'none !important' }}>
                      {categories.map((category, index) => (
                        <TableCell
                          key={index}
                          onMouseEnter={(event) => handleMouseEnter(event, category)}
                          onMouseLeave={handleMouseLeave}
                          sx={{
                            position: "relative",
                            whiteSpace: "normal", // Allow line breaks
                            wordWrap: "break-word", // Handle long words
                            maxWidth: "200px",
                            overflowX: "hidden",
                            textOverflow: "ellipsis",
                            border: 'none !important',
                            padding: '10px 10px 10px 16px',
                            lineHeight: '0.8rem !important',
                            "&::after": {
                              content: '""',
                              position: "absolute",
                              bottom: 0,
                              left: "50%",
                              transform: "translateX(-50%)",
                              height: "2px",
                              width: "0%",
                              backgroundColor: "#ddd",
                              transition: "width 0.3s ease",
                            },
                            "&:hover::after": {
                              width: "20%",
                            },
                            "@media (max-width: 1024px)": {
                            },
                          }}
                        >
                          {category.replace(/_/g, " ")}
                        </TableCell>
                      ))}
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {Array.from({ length: Math.max(...categories.map(category => groupedMenu[category]?.length)) }).map((_, rowIndex) => (
                      <TableRow key={rowIndex}>
                        {categories.map((category, colIndex) => (
                          groupedMenu[category]?.[rowIndex] ? (
                            <TableCell
                              key={colIndex}
                              onClick={() => handleCellClick(category, rowIndex)}
                              sx={{
                                padding: "8px", // Adjust padding to reduce space
                                cursor: "pointer",
                                whiteSpace: "normal", // Allow line breaks
                                overflow: "hidden",
                                textOverflow: "ellipsis",
                                border: 'none !important',
                                padding: '8px 10px 10px 20px !important',
                                "&:hover": {
                                  background: "#f2f2f2",
                                },
                                "@media (max-width: 1024px)": {
                                  padding: "2px 2px", // Reduce padding in tablet view
                                  fontSize: "0.7rem", // Reduce font size in tablet view
                                },
                              }}
                            >
                              {groupedMenu[category]?.[rowIndex] && (
                                <Typography
                                  variant="body1"
                                  component="span"
                                  sx={{
                                    fontWeight: 200,
                                    fontSize: "0.8rem",
                                    fontWeight:"650",
                                    whiteSpace: "normal", // Allow line breaks
                                    wordWrap: "break-word", // Handle long words
                                    overflow: "hidden",
                                    textOverflow: "ellipsis",
                                    border: 'none !important',
                                    "@media (max-width: 1024px)": {
                                      fontSize: "0.7rem", // Reduce font size in tablet view
                                    },
                                  }}
                                >
                                  {groupedMenu[category][rowIndex].title}
                                </Typography>
                              )}
                            </TableCell>
                          ) : (
                            <TableCell key={colIndex} sx={{ border: 'none !important', padding: '8px 10px 10px 20px !important' }} />
                          )
                        ))}
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            )}

            {/* Popover for hover */}
            {hoveredCategory && (
              <Popover
                open={open}
                anchorEl={anchorEl}
                onClose={handleMouseLeave}
                anchorOrigin={{
                  vertical: 'bottom',
                  horizontal: 'left',
                }}
                transformOrigin={{
                  vertical: 'top',
                  horizontal: 'left',
                }}
                PaperProps={{
                  sx: { pointerEvents: 'none', borderRadius: '10px' },
                }}
              >
                <Box sx={{ padding: '10px', width: '200px' }}>
                  {groupedMenu[hoveredCategory]?.map((subItem, subIndex) => (
                    <Typography
                      key={subIndex}
                      onClick={() => handleSubheadingClick(subItem.path)}
                      sx={{
                        padding: "8px 16px",
                        cursor: "pointer",
                        "&:hover": {
                          background: "#f2f2f2",
                        },
                      }}
                    >
                      {subItem.title}
                    </Typography>
                  ))}
                </Box>
              </Popover>
            )}
          </Box>
        )}
      </div>
    </ClickAwayListener>
  );
}

export default MegaMenu;
