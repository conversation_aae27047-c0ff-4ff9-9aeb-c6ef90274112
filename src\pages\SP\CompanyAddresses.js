import {
    Box,
    <PERSON>ton,
    Dialog,
    <PERSON>alogA<PERSON>,
    DialogContent,
    DialogTitle,
    FormControl,
    Grid,
    IconButton,
    TextField
} from "@mui/material";
import { useEffect, useState } from "react";
import { Controller, useForm } from "react-hook-form";
import SelectAutoComplete from "src/@core/components/custom-components/SelectAutoComplete";
import SelectAutoCompleteLocation from "src/@core/components/custom-components/SelectAutoCompleteLocation";
import Icon from "src/@core/components/icon";

const CompanyAddresses = ({
  rowData,
  addresses,
  setAddresses,
  open,
  onClose,
  locationsData,
  addressTypeList,
}) => {

  const [selectedLocationId, setSelectedLocationId] = useState();
  const [selectedAddressId, setSelectedAddressId] = useState();


  const {
    control,
    handleSubmit,
    setValue,
    reset,
    formState: { errors },
  } = useForm();

  const handleCloseDialog = () => {
    onClose();
    reset();
    setSelectedAddressId("")
    setSelectedLocationId("")
    reset();
  };

  useEffect(() => {
    setValue("address", rowData?.address);
    setSelectedLocationId(rowData?.locationId);
    setSelectedAddressId(rowData?.addressType)
  }, [rowData]);

  const handleSelectChange = (event) => {
    const selectedId = event.target.value;
    setSelectedLocationId(selectedId);
  };

  const submit = (data) => {

    const fields = {
      id: crypto.randomUUID(),
      address: data?.address,
      locationId: selectedLocationId,
      addressType: selectedAddressId,
    };
    setAddresses((prevAddresses) => [
      ...(prevAddresses || []),
      fields,
    ]);
    handleCloseDialog();
  };

  const update = (data) => {
   
    const updatedMembers = addresses?.map((member) =>
      member.id === rowData?.id
        ? {
            ...member,
            address: data?.address,
            locationId: selectedLocationId,
            addressType: selectedAddressId,
          }
        : member
    );

    setAddresses(updatedMembers);
    handleCloseDialog();
  };

  return (
    <>
      <Dialog open={open} onClose={handleCloseDialog} maxWidth="sm" fullWidth>
        <DialogTitle
          sx={{
            position: "relative",
            borderBottom: (theme) => `1px solid ${theme.palette.divider}`,
            p: (theme) => `${theme.spacing(1.75, 4)} !important`,
            display: "flex",
            alignItems: "center",
            justifyContent: { xs: "start" },
            fontSize: { xs: 19, md: 20 },
            margin: "2px 2px 2px 2px",
          }}
          textAlign={"center"}
        >
          <Box
            sx={{
              marginLeft: 1.5,
            }}
          >
            Company Addresses
          </Box>
          <Box
            sx={{
              position: "absolute",
              top: "4px",
              right: "14px",
              marginRight: 2,
            }}
          >
            <IconButton
              id="closeDialog"
              size="small"
              onClick={handleCloseDialog}
              sx={{
                p: "0.2rem",
                borderRadius: 1,
                color: "common.white",
                backgroundColor: "primary.main",
                "&:hover": {
                  backgroundColor: "#66BB6A",
                  transition: "background 0.5s ease, transform 0.5s ease",
                },
              }}
            >
              <Icon icon="tabler:x" fontSize="1rem" />
            </IconButton>
          </Box>
        </DialogTitle>
        <DialogContent>
          <Grid
            container
            alignItems="center"
            justifyContent="center"
            spacing={3}
            sx={{ marginBottom: 2 }}
          >
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth error={Boolean(errors.selectedLocationId)}>
                <Controller
                  name="selectedLocationId"
                  control={control}
                  render={({ field }) => (
                    <SelectAutoCompleteLocation
                      id="location"
                      label="Location"
                      nameArray={locationsData}
                      value={selectedLocationId}
                      onChange={(e) => {
                        field.onChange(e);
                        handleSelectChange(e);
                      }}
                    />
                  )}
                />
                {errors.selectedLocationId && (
                  <FormHelperText sx={{ color: "error.main" }}>
                    {errors.selectedLocationId.message}
                  </FormHelperText>
                )}
              </FormControl>
            </Grid>

            <Grid item xs={12} sm={6}>
              <FormControl fullWidth error={Boolean(errors.selectedAddressId)}>
                <Controller
                  name="selectedAddressId"
                  control={control}
                  render={({ field }) => (
                    <SelectAutoComplete
                      id="addressType"
                      label="Address Type"
                      nameArray={addressTypeList}
                      value={selectedAddressId}
                      onChange={(e) => {
                        field.onChange(e);
                        setSelectedAddressId(e.target.value)
                      }}
                    />
                  )}
                />
                {errors.selectedAddressId && (
                  <FormHelperText sx={{ color: "error.main" }}>
                    {errors.selectedAddressId.message}
                  </FormHelperText>
                )}
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={12}>
              <FormControl fullWidth>
                <Controller
                  name="address"
                  control={control}
                  rules={{ required: "Address is required" }}
                  render={({ field }) => (
                    <TextField
                      id="address"   
                      rows={4}
                      multiline
                      {...field}
                      value={field.value || ""}
                      label="Address"
                      InputLabelProps={{ shrink: true }}
                      size="small"
                      placeholder="Enter address"
                      error={Boolean(errors.address)}
                      helperText={errors.address?.message}
                      aria-describedby="validation-basic-address"
                      inputProps={{ maxLength: 150 }}
                    />
                  )}
                />
              </FormControl>
            </Grid>        
            <Grid item xs={12} sm={6}></Grid>
          </Grid>
        </DialogContent>
        <DialogActions
          sx={{
            justifyContent: "end",
            borderTop: (theme) => `1px solid ${theme.palette.divider}`,
            p: (theme) => `${theme.spacing(2.5)} !important`,
          }}
        >
          <Button onClick={handleCloseDialog}>Cancel</Button>

          {rowData ? (
            <Button
              id="update"
              onClick={handleSubmit(update)}
              variant="contained"
              sx={{
                marginRight: 3.5,
              }}
            >
              Update
            </Button>
          ) : (
            <Button
              id="add"
              onClick={handleSubmit(submit)}
              variant="contained"
              sx={{
                marginRight: 3.5,
              }}
            >
              Add
            </Button>
          )}
        </DialogActions>
      </Dialog>
    </>
  );
};

export default CompanyAddresses;
