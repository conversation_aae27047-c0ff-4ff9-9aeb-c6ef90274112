// ** React Imports
import { forwardRef, useContext, useEffect } from "react";

// ** MUI Imports
import Grid from "@mui/material/Grid";
import Button from "@mui/material/Button";
import TextField from "@mui/material/TextField";
import FormControl from "@mui/material/FormControl";
import { useAuth } from "src/hooks/useAuth";
import { Box } from "@mui/system";
import { useState } from "react";
import IconButton from "@mui/material/IconButton";
import toast, { Toaster } from "react-hot-toast";

// ** Third Party Imports
import DatePicker from "react-datepicker";
import { useForm, Controller } from "react-hook-form";
import {
  FormHelperText,
  Typography,
  InputLabel,
  Select,
  MenuItem,
  InputAdornment,
} from "@mui/material";
import SelectMultipleBasic from "src/@core/components/custom-components/SelectMultipleBasic";
import NameTextField from "src/@core/components/custom-components/NameTextField";
import SocietyValidationsSection1 from "./SocietyValidationsSection1";
import { yupResolver } from "@hookform/resolvers/yup";
import "react-datepicker/dist/react-datepicker.css";
import { format } from "date-fns";
import CustomAutocomplete from "src/@core/components/custom-components/CustomAutoComplete";
import CustomTextField from "src/@core/components/custom-components/CustomTextField";
import axios from "axios";
import SelectAutoComplete from "src/@core/components/custom-components/SelectAutoComplete";
import SelectCategory from "src/@core/components/custom-components/SelectCategory";
import GoogleMapsIconButton from "src/@core/components/custom-components/toastDisplay.js";

import authConfig from "src/configs/auth";
import { getAuthorizationHeaders, getUrl } from "src/helpers/utils";
import { AuthContext } from "src/context/AuthContext";
import MultiSelectAutoComplete from "src/@core/components/custom-components/MultiSelectAutoComplete";
import MultiSelectAutoCompleteStatic from "src/@core/components/custom-components/MultiSelectAutoCompleteStatic";


const owner = [
  { key: "Mhada", value: "MHADA" },
  { key: "Society", value: "SOCIETY" },
  { key: "Landlord", value: "LANDLORD" },
  { key: "Collector", value: "COLLECTOR" },
];

const CustomInput = forwardRef(({ ...props }, ref) => {
  return <TextField inputRef={ref} {...props} sx={{ width: "100%" }} />;
});

const Section1 = ({
  onCancel,
  formData,
  userData,
  locId,
  employeesData,
  handleAssignedToChange,
  assignedTo,
}) => {
  //Hooks
  const auth = useAuth();
  const fields = [
    "name",
    "plotCTSNo",
    "teamMember",
    "pinCode",
    "reference",
    "roadWidth",
    "authority",
    "grossPlotArea",
    "societyAddress",
  ];

  const today = new Date();
  const maxDate = format(today, "yyyy-MM-dd");
  const { user, listValues } = useContext(AuthContext);

  const {
    register,
    handleSubmit,
    control,
    formState: { errors },
    setValue,
    clearErrors,
    formState: { isValid },
  } = useForm({
    resolver: yupResolver(SocietyValidationsSection1(fields)),
    mode: "onChange",
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  async function submit(data) {
    setIsSubmitting(true)
      try {
        if (Array.isArray(data?.owner)) {
          data.owner = data?.owner
            .map((service) => service.value)
            .join(",");
        }

        const trimmedData = Object.fromEntries(
          Object.entries(data).map(([key, value]) => [
            key,
            typeof value === "string" ? value.trim() : value,
          ])
        );

        
        const hasWhiteSpace = Object.values(trimmedData).some(
          (value) => typeof value === "string" && value === ""
        );
        // if (hasWhiteSpace) {
        //   toast.error("Fields cannot contain only white spaces");
        //   return;
        // }
        const locationName = locationId
          ? listValues.find((item) => item.id === locationId)?.name
          : null;

        const fields = {
          location: locationName,
          name: trimmedData?.name,
          authority: trimmedData?.authority,
          owner: trimmedData?.owner,
          googleMapLocation: trimmedData?.googleMapLocation,
          pinCode: trimmedData?.pinCode,
          plotCTSNo: trimmedData?.plotCTSNo,
          reference: trimmedData?.reference,
          roadWidth: trimmedData?.roadWidth,
          assignedTo: assignedTo,
          grossPlotArea: trimmedData?.grossPlotArea,
          societyAddress: trimmedData?.societyAddress,
          teamMember: trimmedData?.teamMember,
          ward: null,
          zone: zone,
          type: selectedType?.name,
        };

        // Conditionally add 'ward' to fields if selectedWard?.name is not undefined
        if (selectedWard?.name !== undefined && selectedWard.name !== "") {
          fields.ward = selectedWard.name;
        }

        const userUniqueId =
          formData && formData.userId !== undefined ? formData?.userId : user?.id;
        const response = await auth.updateEntity(fields, userUniqueId);
        onCancel();
    } catch {
      console.error("Submission failed:", errors);
      toast.error("An error occurred while submitting the form.");
    } finally {
      setIsSubmitting(false)
    }
  }

  const selectedValues = formData?.owner
    ? formData.owner
        .split(",")
        .map((value) =>
          owner.find(
            (option) => option.value === value.trim()
          )
        )
        .filter(Boolean)
    : [];
  useEffect(() => {
    if (formData?.owner) {
      const valuesArray = formData?.owner.split(",");
      const mapped =
      valuesArray.map((value) =>
          owner.find((line) => line.value === value)
        );
      setValue("owner", mapped);
    }
  }, [formData, setValue]);

  const [locationId, setLocationId] = useState(locId);
  const [zone, setZone] = useState(formData?.zone);

  const [listOfLocations, setListOfLocations] = useState([]);
  const [locationsOptions, setLocationsOptions] = useState([]);

  useEffect(() => {
    axios({
      method: "get",
      url: getUrl(authConfig.selectDropdownNew) + "?selectionType=LOCATIONS",
      headers: getAuthorizationHeaders(),
    })
      .then((res) => {
        setListOfLocations(res.data.data);
      })
      .catch((err) => console.log("Locations error", err));
  }, []);

  useEffect(() => {
    if (!!listOfLocations) {
      let data = [];
      listOfLocations.map((entry) => {
        data.push({ value: entry.id, key: entry.name });
      });
      setLocationsOptions(data);
    }
  }, [listOfLocations]);

  const [selectedWard, setSelectedWard] = useState({
    name: formData?.ward || "",
    id: formData?.id || "",
  });
  const [listOfWards, setListOfWards] = useState([]);

  useEffect(() => {
    const fetchWards = async () => {
      const data = {
        masterDataType: "WARD_FSI",
      };
      axios({
        method: "post",
        url: getUrl(authConfig.masterDataGetAllEndpoint),
        headers: getAuthorizationHeaders(),
        data: data,
      })
        .then((res) => {
          setListOfWards(res.data.masterDataResponse);
        })
        .catch((err) => console.log("error", err));
    };

    fetchWards();
  }, []);

  const handleWardChange = (newValue) => {
    setSelectedWard(newValue);
  };

  const [selectedType, setSelectedType] = useState({
    name: formData?.type || "",
    id: formData?.id || "",
  });
  const [listOfTypes, setListOfTypes] = useState([]);

  useEffect(() => {
    const fetchTypes = async () => {
      const data = {
        masterDataType: "TYPE_FSI",
      };
      axios({
        method: "post",
        url: getUrl(authConfig.masterDataGetAllEndpoint),
        headers: getAuthorizationHeaders(),
        data: data,
      })
        .then((res) => {
          setListOfTypes(res.data.masterDataResponse);
        })
        .catch((err) => console.log("error", err));
    };

    fetchTypes();
  }, []);

  const handleTypeChange = (newValue) => {
    setSelectedType(newValue);
  };

  const handleLocationChange = (newValue) => {
    setLocationId(newValue);
    const selectedLocation = listOfLocations.find(
      (location) => location.id === newValue
    );
    if (selectedLocation) {
      const zone = selectedLocation.zoneId
        ? listValues.find((item) => item.id === selectedLocation.zoneId)?.name
        : null;
      setZone(zone);
    }
  };
  const validateLocationUrl = (value) => {
    const urlPattern =
      /^https:\/\/(www\.)?(maps\.app\.goo\.gl|google\.com\/maps)\/.+$/;
    return urlPattern.test(value) || "Please enter a valid Google Maps URL";
  };

  return (
    <>
      <Box sx={{ pt: 3 }}>
        <Grid container spacing={5}>
          <Grid item xs={12} sm={6}>
            <FormControl fullWidth>
              <Controller
                name="name"
                control={control}
                defaultValue={formData?.name}
                render={({ field }) => (
                  <NameTextField
                    {...field}
                    label="Society Name"
                    size="small"
                    InputLabelProps={{ shrink: true }}
                    placeholder="Enter Society Name"
                    error={Boolean(errors.name)}
                    helperText={errors.name?.message}
                    aria-describedby="validation-basic-society-name"
                  />
                )}
              />
            </FormControl>
          </Grid>
          <Grid item xs={12} sm={6}>
            <FormControl fullWidth>
              <Typography className="data-field">System Code</Typography>
              <Typography style={{ fontWeight: "bold" }}>
                {formData?.systemCode}
              </Typography>
            </FormControl>
          </Grid>

          <Grid item xs={12} sm={6}>
            <FormControl fullWidth>
              <Controller
                name="plotCTSNo"
                control={control}
                defaultValue={formData?.plotCTSNo}
                render={({ field }) => (
                  <TextField
                    {...field}
                    label="Plot CTS No"
                    placeholder="Identification number given to property"
                    InputLabelProps={{ shrink: true }}
                    error={Boolean(errors.plotCTSNo)}
                    helperText={errors.plotCTSNo?.message}
                    size="small"
                    aria-describedby="validation-basic-plot-cts-no"
                  />
                )}
              />
            </FormControl>
          </Grid>
          <Grid item xs={12} sm={6}>
            <FormControl fullWidth>
              <Controller
                name="teamMember"
                control={control}
                defaultValue={formData?.teamMember}
                render={({ field }) => (
                  <TextField
                    {...field}
                    label="*Team Member"
                    InputLabelProps={{ shrink: true }}
                    placeholder="Team Member"
                    error={Boolean(errors.teamMember)}
                    helperText={errors.teamMember?.message}
                    size="small"
                    aria-describedby="validation-basic-team-member"
                  />
                )}
              />
            </FormControl>
          </Grid>
          <Grid item xs={12}>
            <FormControl fullWidth>
              <Controller
                name="societyAddress"
                control={control}
                defaultValue={formData?.societyAddress}
                render={({ field }) => (
                  <TextField
                    rows={4}
                    multiline
                    {...field}
                    label="*Society Address"
                    error={Boolean(errors.societyAddress)}
                    InputLabelProps={{ shrink: true }}
                    helperText={errors.societyAddress?.message}
                    aria-describedby="validation-basic-society-address"
                    InputProps={{
                      title: "Enter society address",
                    }}
                  />
                )}
              />
            </FormControl>
          </Grid>
          {/* <Grid item xs={12} sm={6}>
                  <CustomAutocomplete
                    autoHighlight
                    id="autocomplete-location-select"
                    label="Location"
                    options={listOfLocations}
                    getOptionLabel={(option) => option.name || ""}
                    value={selectedLocation}
                    getOptionSelected={(option, value) =>
                      option.id === value.id
                    }
                    defaultValue={{
                      name: formData?.location || "",
                      id: formData?.id || "",
                    }}
                    onChange={(event, newValue) =>
                      handleLocationChange(newValue)
                    }
                    renderInput={(params) => (
                      <CustomTextField
                        {...params}
                        placeholder="Select Location"
                        label="Select Location"
                      />
                    )}
                  />
                </Grid> */}
          <Grid item xs={12} sm={6}>
            <SelectAutoComplete
              register={register}
              clearErrors={clearErrors}
              id={"location-select"}
              label={" Select Society Location "}
              name="location-select"
              nameArray={locationsOptions}
              defaultValue={locationId}
              size="small"
              value={locationId}
              onChange={(event) => handleLocationChange(event.target.value)}
              aria-describedby="location-select"
            />
          </Grid>
          <Grid container item xs={12} sm={6} spacing={2}>
            <Grid item>
              <Typography className="data-field"> Society Zone:</Typography>
            </Grid>
            <Grid item>
              <Typography style={{ fontWeight: "bold" }}>{zone}</Typography>
            </Grid>
          </Grid>

          <Grid item xs={12} sm={6}>
            <CustomAutocomplete
              autoHighlight
              id="autocomplete-type-select"
              label="Type"
              size="small"
              options={listOfTypes}
              getOptionLabel={(option) => option.name || ""}
              value={selectedType}
              getOptionSelected={(option, value) => option.id === value.id}
              defaultValue={{
                name: formData?.type || "",
                id: formData?.id || "",
              }}
              onChange={(event, newValue) => handleTypeChange(newValue)}
              renderInput={(params) => (
                <CustomTextField
                  {...params}
                  placeholder="Select Society Type"
                  label="Select Society Type"
                />
              )}
            />
          </Grid>
          <Grid item xs={12} sm={6}>
            <CustomAutocomplete
              autoHighlight
              id="autocomplete-ward-select"
              label="Ward"
              size="small"
              options={listOfWards}
              getOptionLabel={(option) => option.name || ""}
              value={selectedWard}
              getOptionSelected={(option, value) => option.id === value.id}
              defaultValue={{
                name: formData?.ward || "",
                id: formData?.id || "",
              }}
              onChange={(event, newValue) => handleWardChange(newValue)}
              renderInput={(params) => (
                <CustomTextField
                  {...params}
                  placeholder="Choose Your Ward"
                  label="Choose Your Ward"
                />
              )}
            />
          </Grid>
          <Grid item xs={12} sm={6}>
            <FormControl fullWidth>
              <Controller
                name="pinCode"
                control={control}
                size="small"
                defaultValue={formData?.pinCode}
                rules={{
                  required: "This field is required",
                  minLength: {
                    value: 6,
                    message: "Enter a 6 digit pincode",
                  },
                }}
                render={({ field: { value, onChange } }) => (
                  <TextField
                    value={value}
                    type="text"
                    inputMode="numeric"
                    pattern="[0-9]*"
                    size="small"
                    maxLength={6}
                    label="*Pin Code"
                    InputLabelProps={{ shrink: true }}
                    onChange={onChange}
                    onKeyDown={(event) => {
                      if (
                        !/^\d+$/.test(event.key) &&
                        event.key !== "Backspace" &&
                        event.key !== "Delete"
                      ) {
                        event.preventDefault();
                      }
                      if (
                        value &&
                        value.length === 6 &&
                        event.key !== "Backspace" &&
                        event.key !== "Delete"
                      ) {
                        event.preventDefault();
                      }
                    }}
                    placeholder="Enter Pin Code"
                    error={Boolean(errors.pinCode)}
                    helperText={errors.pinCode?.message}
                    aria-describedby="validation-basic-pin-code"
                  />
                )}
              />
            </FormControl>
          </Grid>
          <Grid item xs={12} sm={6}>
            <FormControl fullWidth>
              <Controller
                name="roadWidth"
                control={control}
                size="small"
                rules={{
                  required: "This field is required",
                  maxLength: {
                    value: 100,
                    message: "Must not exceed 100 characters",
                  },
                }}
                defaultValue={formData?.roadWidth}
                render={({ field }) => (
                  <TextField
                    {...field}
                    type="text"
                    label="Road Width"
                    placeholder="road width"
                    size="small"
                    InputLabelProps={{ shrink: true }}
                    error={Boolean(errors.roadWidth)}
                    helperText={errors.roadWidth?.message}
                    aria-describedby="validation-basic-road-width"
                  />
                )}
              />
            </FormControl>
          </Grid>

          <Grid item xs={12} sm={6}>
            <FormControl fullWidth>
              <Controller
                name="grossPlotArea"
                control={control}
                defaultValue={formData?.grossPlotArea}
                render={({ field }) => (
                  <TextField
                    {...field}
                    type="text"
                    label="Gross Plot Area (Sq Mtrs.)"
                    size="small"
                    InputLabelProps={{ shrink: true }}
                    error={Boolean(errors.grossPlotArea)}
                    helperText={errors.grossPlotArea?.message}
                    aria-describedby="validation-basic-gross-plot-area"
                  />
                )}
              />
            </FormControl>
          </Grid>

          <Grid item xs={12} sm={6}>
            <FormControl fullWidth>
              <Controller
                name="authority"
                control={control}
                defaultValue={formData?.authority}
                render={({ field }) => (
                  <TextField
                    {...field}
                    type="text"
                    label="Authority"
                    size="small"
                    InputLabelProps={{ shrink: true }}
                    placeholder="Enter Authority"
                    error={Boolean(errors.authority)}
                    helperText={errors.authority?.message}
                    aria-describedby="validation-basic-society-authority"
                  />
                )}
              />
            </FormControl>
          </Grid>
          {userData && userData.id !== undefined && (
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <Controller
                  name="assignedTo"
                  control={control}
                  defaultValue={assignedTo}
                  render={({ field }) => (
                    <SelectAutoComplete
                      id="assignedTo"
                      label="Assigned To"
                      value={assignedTo}
                      nameArray={employeesData.map((emp) => ({
                        value: emp.id,
                        key: emp.name,
                      }))}
                      onChange={(e) => {
                        field.onChange(e);
                        handleAssignedToChange(e);
                      }}
                    />
                  )}
                />
              </FormControl>
            </Grid>
          )}
          <Grid item xs={12} sm={6}>
            <FormControl fullWidth>
              <Controller
                name="reference"
                control={control}
                defaultValue={formData?.reference}
                render={({ field }) => (
                  <TextField
                    {...field}
                    label="Referral Source"
                    placeholder="Reference"
                    size="small"
                    InputLabelProps={{ shrink: true }}
                    error={Boolean(errors.reference)}
                    helperText={errors.reference?.message}
                    aria-describedby="validation-basic-reference"
                  />
                )}
              />
            </FormControl>
          </Grid>

          <Grid item xs={12} sm={6}>
            <FormControl fullWidth error={Boolean(errors.owner)}>
              <Controller
                name="owner"
                control={control}
                defaultValue={selectedValues} // Default value as an empty array if formData is undefined
                render={({ field }) => (
                  <MultiSelectAutoCompleteStatic
                    id="owner" // Unique identifier for the component
                    label="Property Owner" // Label to be displayed
                    nameArray={owner} // Array of options to select from
                    value={field.value} // Bind the value to the field's current value
                    onChange={(newValue) => field.onChange(newValue)} // Pass the change handler to update form state
                  />
                )}
              />
            </FormControl>
          </Grid>
          <Grid item xs={12} sm={6}>
            <FormControl fullWidth>
              <Controller
                name="googleMapLocation"
                control={control}
                defaultValue={formData?.googleMapLocation}
                rules={{ required: true, validate: validateLocationUrl }}
                render={({ field }) => (
                  <TextField
                    {...field}
                    label="Google location"
                    InputLabelProps={{ shrink: true }}
                    size="small"
                    placeholder="Click on the icon to navigate & paste the url here"
                    error={Boolean(errors.googleMapLocation)}
                    helperText={errors.googleMapLocation?.message}
                    aria-describedby="validation-basic-googleMapLocation"
                    InputProps={{
                      endAdornment: (
                        <InputAdornment position="end">
                          <GoogleMapsIconButton />
                        </InputAdornment>
                      ),
                    }}
                  />
                )}
              />
            </FormControl>
          </Grid>

          <Grid item xs={12}>
            <center>
              <Button
                size="medium"
                sx={{ mr: 3 }}
                variant="outlined"
                color="primary"
                onClick={() => onCancel()}
              >
                Cancel
              </Button>
              <Button
                size="medium"
                type="button"
                variant="contained"
                disabled={isSubmitting}
                onClick={handleSubmit(submit)}
              >
                Save
              </Button>
            </center>
          </Grid>
        </Grid>
      </Box>
    </>
  );
};

export default Section1;
