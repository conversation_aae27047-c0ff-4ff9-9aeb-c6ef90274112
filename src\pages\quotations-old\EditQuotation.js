import React, { useContext,useState,useEffect } from "react";
import Dialog from "@mui/material/Dialog";
import DialogActions from "@mui/material/DialogActions";
import MUITableCell from "src/pages/SP/MUITableCell";
import { useTheme } from "@emotion/react";
import CloseExpandIcons from "../../@core/components/custom-components/CloseExpandIcons";
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableRow,
  TableHead,
  Card,
  TextField,
  Paper,
} from "@mui/material";
import DialogContent from "@mui/material/DialogContent";
import {
  Button,
  DialogTitle,
  IconButton,
  Typography,
} from "@mui/material";
import axios from "axios";
import authConfig from "src/configs/auth";
import { getAuthorizationHeaders, getUrl } from "src/helpers/utils";
import { AuthContext } from "src/context/AuthContext";
import Box from "@mui/material/Box";
import Icon from "src/@core/components/icon";
import AccordionBasic from "src/@core/components/custom-components/AccordionBasic";
import { useForm } from "react-hook-form";
import UploadFile from "./UploadFileEdit";

const field = {
  fontWeight: 500,
  fontSize: "12.75px",
};

const innerTableStylings = {
  fontWeight: "bold", // Make text bold
  padding: "6px 16px", // Reduce padding to decrease cell height
};
const EditQuotationDetails = ({ open,onClose, data,disableDelete = false  }) => {
  const theme = useTheme();

  const [dataView, setDataView] = useState({});

  const [designation, setDesignation] = useState("");

  const [subCategories, setSubCategories] = useState([]);
  const [listOfSubCategories, setListOfSubCategories] = useState([]);
  // const [selectedFiles, setSelectedFiles] = useState([]);
  const [selectedFiles, setSelectedFiles] = useState([
    {
      name: "SampleDocument.pdf",
      type: "application/pdf",
      // size: 1024 * 1024,
      url: "/pdf/SampleDocument.pdf"
      , 
    },
   
  ]);
  const sampleData = {
    serviceType: "Plumbing",
    priorityName: "High",
    budget: "300000",
    requirementDeadLine: "28-09-2024",
    societyRemarks: "No Remarks",
  };

  const paymentData = [
    {
      date: "1-Oct-2024",
      description: "Monthly Payment",
      amount: 40000,
      gst: "0.00",
    },
    {
      date: "1-Nov-2024",
      description: "Monthly Payment",
      amount: 40000,
      gst: "0.00",
    },
    {
      date: "1-Dec-2024",
      description: "Monthly Payment",
      amount: 40000,
      gst: "0.00",
    },
    // Continue adding all other rows similarly for each month
    {
      date: "1-Jan-2025",
      description: "Monthly Payment",
      amount: 40000,
      gst: "0.00",
    },
  ];
  const [pdfUrl, setPdfUrl] = useState(null);

  // Custom function to handle static files
  const handleOpenDialog = (index) => {
    const file = selectedFiles[index];
    if (file.url) {
      // If the file has a static URL, set it directly
      setPdfUrl(file.url);
    } else {
      // Otherwise, handle dynamic files using default logic
      openDialog(index);
    }
  };

  const handleCloseDialog = () => {
    setPdfUrl(null);
  };

  // Dummy function to demonstrate the default openDialog logic
  const [selectedFileIndex, setSelectedFileIndex] = useState(null);
  const openDialog = async (index) => {
    setSelectedFileIndex(index);
    const file = selectedFiles[index];

    if (
      file.type ===
      "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
    ) {
      try {
        const arrayBuffer = await file.arrayBuffer();
        const result = await mammoth.convertToHtml({ arrayBuffer });
        setDocxContent(result.value);
      } catch (error) {
        console.error("Error converting DOCX to HTML:", error);
      }
    }

    if (file.type === "application/pdf") {
      try {
        const url = URL.createObjectURL(file);
        setPdfUrl(url);
      } catch (error) {
        console.error("Error creating object URL for PDF:", error);
      }
    }
  };
  useEffect(() => {
    const fetchSocieties = async () => {
      axios({
        method: "get",
        url: getUrl(authConfig.selectDropdownNew) + "?selectionType=SOCIETY_NAME",
        headers: getAuthorizationHeaders(),
      })
        .then((res) => {
          const metadataArray = res.data?.data?.map((item) => item?.metaData);
          const matchingSociety = metadataArray.find(
            (society) => society?.userId === data?.userId
          );

          // Set the matching location as the defaultValue for the SelectCategory
          if (matchingSociety) {
            setDataView({
              ...dataView, // This keeps the existing properties
              ...matchingSociety, // This adds/updates properties from newValue
            });

            const loc = matchingSociety?.designation
              ? listOfSubCategories.find(
                  (item) => item.value === matchingSociety?.designation
                )?.key
              : null;

            setDesignation(loc);
          }
        })
        .catch((err) => console.log("error", err));
    };
    fetchSocieties();
  }, [data]);


  const { user, listValues,getAllListValuesByListNameId } = useContext(AuthContext);

     const handleError = (error) => console.error("Error:", error);
    
      useEffect(() => {
        if (!!authConfig) {
          getAllListValuesByListNameId(
            authConfig.ChsDesignation,
            (data) =>
              setListOfSubCategories(
                data?.listValues?.map((item) => ({
                  value: item.id,
                  key: item.listValue,
                }))
              ),
            handleError
          );
        }
      }, [authConfig]);

  const [openEditDialog, setOpenEditDialog] = useState(false);
  const [conversation, setConversation] = useState({});

  const handleCloseEditDialog = () => {
    setOpenEditDialog(false);
  };

  const [openMoreInfoDialog, setOpenMoreInfoDialog] = useState(false);

  const handleDialogClose = () => {
    setOpenMoreInfoDialog(false);
  };

  const handleOpenEditDialog = () => {
    setOpenEditDialog(true);
  };

  const serviceType = data?.requisitionData?.serviceType
    ? listValues?.find((item) => item.id === data?.requisitionData?.serviceType)
        ?.name
    : null;

  const priorityName = data?.requisitionData?.priority
    ? listValues?.find((item) => item.id === data?.requisitionData?.priority)
        ?.name
    : null;

  const getNamesFromIds = (ids, listValues) => {
    return ids?.map((id) => {
      const foundItem = listValues?.find((item) => item?.id === id);
      return foundItem ? foundItem?.name : null;
    });
  };

  const [specifications, setSpecifications] = useState([]);

  const [userList, setUserList] = useState([]);

  useEffect(() => {
    if (data?.requisitionData?.serviceType) {
      const fetchAll = async (serviceId, data) => {
        // const url = `${getUrl(
        //   authConfig.getAllServiceProfiles
        // )}/${serviceId}/requisitionFields`;
        const headers = getAuthorizationHeaders();

        try {
          const response = await axios({
            method: "get",
            url: url,
            headers: headers,
            data: data,
          });

          if (response.data) {
            setUserList(response.data);
          } else {
            console.error("Unexpected API response format:", response);
          }
        } catch (error) {
          console.error("Error fetching users:", error);
        }
      };

      const serviceId = data?.requisitionData?.serviceType;
      fetchAll(serviceId);
    }
  }, [data]);

  useEffect(() => {
    if (data?.requisitionData?.specifications?.listNames.length > 0) {
      // Create a mapping of listValueId to listValue
      const listValueMap = userList?.reduce((map, item) => {
        item?.values?.forEach((value) => {
          map[value.id] = { id: value.id, name: value.name };
        });
        return map;
      }, {});

      // Get all list names from specifications
      const listNames = userList?.map((item) => {
        const metadataItem =
          data?.requisitionData?.specifications?.listNames?.find(
            (list) => list.listNameId === item.id
          );
        const otherValue = metadataItem ? metadataItem.otherValue : null;

        return {
          id: item.id,
          name: item.name,
          otherValue: otherValue,
          values:
            metadataItem && metadataItem.listValues.length
              ? metadataItem.listValues.map(
                  (value) => listValueMap[value.listValueId]
                )
              : [],
        };
      });
      setSpecifications(listNames);
    }
  }, [data, userList]);

  // Using the function to get names
  const names = getNamesFromIds(data?.requisitionData?.subServices, listValues);

  const subServices = names?.filter((name) => name !== null).join(", ");

  const assignedName = data?.assignedTo
    ? employeesData?.find((item) => item.id === data?.assignedTo)?.name
    : null;

  const status = data?.status
    ? listValues?.find((item) => item.id === data?.status)?.name
    : null;

  const teamMember = data?.teamMember
    ? employeesData?.find((item) => item.id === data?.teamMember)?.name
    : null;

  const handleClose = () => {
    onClose();
    setUserList([]);
    setSpecifications([]);
  };

  const [rows, setRows] = useState([
    { date: "", description: "", amount: "" },
  ]);

  const handleAddRow = () => {
    setRows([...rows, { date: "", description: "", amount: "" }]);
  };

  const handleDeleteRow = (index) => {
    setRows(rows.filter((_, rowIndex) => rowIndex !== index));
  };

  const handleInputChange = (index, field, value) => {
    const newRows = [...rows];
    newRows[index][field] = value;
    setRows(newRows);
  };

  const handleClosePayment = () => {
    onClose();
    setRows([{ date: "", description: "", amount: "" }]); // Reset rows
  };


  return (
    <>
      <Dialog open={open} onClose={handleClose} fullScreen>
        <DialogTitle
          sx={{
            position: "relative",
            borderBottom: (theme) => `1px solid ${theme.palette.divider}`,
            p: (theme) => `${theme.spacing(1.75, 4)} !important`,
            display: "flex",
            alignItems: "center",
            justifyContent: "start",
            fontSize: { xs: 19, md: 20 },
          }}
          textAlign={"center"}
        >
          Edit Quotations
          <Box sx={{ position: "absolute", top: "4px", right: "12px" }}>
            <IconButton
              size="small"
              onClick={handleClose}
              sx={{
                // p: "0.438rem",
                borderRadius: 1,
                color: "common.white",
                backgroundColor: "primary.main",
                "&:hover": {
                  backgroundColor: "#66BB6A",
                  transition: "background 0.5s ease, transform 0.5s ease",
                },
              }}
            >
              <Icon icon="tabler:x" fontSize="1rem" />
            </IconButton>
          </Box>
        </DialogTitle>
        <DialogContent
          sx={{
            position: "relative",
            pb: (theme) => `${theme.spacing(5)} !important`,
            px: (theme) => [`${theme.spacing(8)} !important`],
          }}
        >
          <>
            <Card>
              <AccordionBasic
                id={"panel-header-2"}
                ariaControls={"panel-content-2"}
                heading={"Requisition Details"}
                body={
                  <>
                    <TableContainer
                      sx={{ padding: "4px 6px", paddingTop: "0px !important" }}
                      className="tableBody"
                    >
                      <Table>
                        <TableBody
                          sx={{
                            "& .MuiTableCell-root": {
                              p: `${theme.spacing(1.35, 1.125)} !important`,
                            },
                          }}
                        >
                          <TableRow>
                            <MUITableCell sx={{ padding: "4px 8px" }}>
                              {/* Adjust padding here */}
                              <Typography
                                style={{
                                  ...field,
                                  lineHeight: "1.2",
                                }}
                              >
                                Service Type:
                              </Typography>
                            </MUITableCell>
                            <MUITableCell sx={{ padding: "4px 8px" }}>
                              {/* Adjust padding here */}
                              <Typography
                                className="data-field"
                                style={{
                                  lineHeight: "1.2",
                                }}
                              >
                                {sampleData.serviceType}
                              </Typography>
                            </MUITableCell>
                          </TableRow>

                          {data?.requisitionData?.subServices?.length > 0 && (
                            <TableRow>
                              <MUITableCell sx={{ padding: "4px 8px" }}>
                                {" "}
                                {/* Adjust padding here */}
                                <Typography
                                  style={{
                                    ...field,
                                    lineHeight: "1.2", // Adjust line height
                                  }}
                                >
                                  Sub Services:
                                </Typography>
                              </MUITableCell>
                              <MUITableCell sx={{ padding: "4px 8px" }}>
                                {" "}
                                {/* Adjust padding here */}
                                <Typography
                                  className="data-field"
                                  style={{ lineHeight: "1.2" }}
                                >
                                  {subServices}
                                </Typography>
                              </MUITableCell>
                            </TableRow>
                          )}
                          {data?.requisitionData?.anyOtherServices && (
                            <TableRow>
                              <MUITableCell sx={{ padding: "4px 8px" }}>
                                {" "}
                                {/* Adjust padding here */}
                                <Typography
                                  style={{
                                    ...field,
                                    lineHeight: "1.2", // Adjust line height
                                  }}
                                >
                                  Other Services:
                                </Typography>
                              </MUITableCell>
                              <MUITableCell sx={{ padding: "4px 8px" }}>
                                {" "}
                                {/* Adjust padding here */}
                                <Typography
                                  className="data-field"
                                  style={{ lineHeight: "1.2" }}
                                >
                                  {data?.requisitionData?.anyOtherServices}
                                </Typography>
                              </MUITableCell>
                            </TableRow>
                          )}

                          <TableRow>
                            <MUITableCell sx={{ padding: "4px 8px" }}>
                              {" "}
                              {/* Adjust padding here */}
                              <Typography
                                style={{ ...field, lineHeight: "1.2" }}
                              >
                                Priority:
                              </Typography>
                            </MUITableCell>
                            <MUITableCell sx={{ padding: "4px 8px" }}>
                              {" "}
                              {/* Adjust padding here */}
                              <Typography
                                className="data-field"
                                style={{ lineHeight: "1.2" }}
                              >
                                {sampleData.priorityName}
                              </Typography>
                            </MUITableCell>
                          </TableRow>
                          <TableRow>
                            <MUITableCell sx={{ padding: "4px 8px" }}>
                              {" "}
                              {/* Adjust padding here */}
                              <Typography
                                style={{ ...field, lineHeight: "1.2" }}
                              >
                                Budget:
                              </Typography>
                            </MUITableCell>
                            <MUITableCell sx={{ padding: "4px 8px" }}>
                              {" "}
                              {/* Adjust padding here */}
                              <Typography
                                className="data-field"
                                style={{ lineHeight: "1.2" }}
                              >
                                {sampleData?.budget}
                              </Typography>
                            </MUITableCell>
                          </TableRow>

                          <TableRow>
                            <MUITableCell sx={{ padding: "4px 8px" }}>
                              {" "}
                              {/* Adjust padding here */}
                              <Typography
                                style={{ ...field, lineHeight: "1.2" }}
                              >
                                Requirement Dead Line
                              </Typography>
                            </MUITableCell>
                            <MUITableCell sx={{ padding: "4px 8px" }}>
                              {" "}
                              {/* Adjust padding here */}
                              <Typography
                                className="data-field"
                                style={{ lineHeight: "1.2" }}
                              >
                                {sampleData?.requirementDeadLine}
                              </Typography>
                            </MUITableCell>
                          </TableRow>
                          <TableRow>
                            <MUITableCell sx={{ padding: "4px 8px" }}>
                              {" "}
                              {/* Adjust padding here */}
                              <Typography
                                style={{ ...field, lineHeight: "1.2" }}
                              >
                                Society Remarks:
                              </Typography>
                            </MUITableCell>
                            <MUITableCell sx={{ padding: "4px 8px" }}>
                              {" "}
                              {/* Adjust padding here */}
                              <Typography
                                className="data-field"
                                style={{ lineHeight: "1.2" }}
                              >
                                {sampleData?.societyRemarks}
                              </Typography>
                            </MUITableCell>
                          </TableRow>
                          {specifications
                            ?.slice()
                            ?.sort((a, b) => a.listSequence - b.listSequence)
                            ?.map((category) =>
                              category.values.length > 0 ||
                              category.otherValue ? (
                                <TableRow key={category.name}>
                                  <MUITableCell sx={{ padding: "4px 8px" }}>
                                    {" "}
                                    {/* Adjust padding here */}
                                    <Typography
                                      style={{ ...field, lineHeight: "1.2" }}
                                    >
                                      {category.name}
                                    </Typography>
                                  </MUITableCell>
                                  <MUITableCell sx={{ padding: "4px 8px" }}>
                                    {" "}
                                    {/* Adjust padding here */}
                                    {category?.values?.length > 0
                                      ? category?.values?.map(
                                          (value, index) => (
                                            <Typography
                                              key={index}
                                              className="data-field"
                                              style={{ lineHeight: "1.2" }}
                                            >
                                              {value.name}
                                            </Typography>
                                          )
                                        )
                                      : category?.otherValue && (
                                          <Typography
                                            className="data-field"
                                            style={{ lineHeight: "1.2" }}
                                          >
                                            {category.otherValue}
                                          </Typography>
                                        )}
                                  </MUITableCell>
                                </TableRow>
                              ) : null
                            )}
                        </TableBody>
                      </Table>
                    </TableContainer>
                  </>
                }
                
              />
            </Card>
       
            <AccordionBasic
              id={"panel-header-2"}
              ariaControls={"panel-content-2"}
              heading={"Payment schedule"}
             
              body={
        <TableContainer component={Paper}>
                <Table aria-label="payment schedule" size="small">
                  <TableHead>
                    <TableRow>
                      <TableCell style={innerTableStylings}>Date</TableCell>
                      <TableCell style={innerTableStylings}>
                        Payment Description
                      </TableCell>
                      <TableCell style={innerTableStylings}>
                        Amount(₹)
                      </TableCell>
                      <TableCell style={innerTableStylings}>GST</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {paymentData.map((row, index) => (
                      <TableRow key={index}>
                        <TableCell style={innerTableStylings}>
                          {row.date}
                        </TableCell>
                        <TableCell style={innerTableStylings}>
                          {row.description}
                        </TableCell>
                        <TableCell style={innerTableStylings}>{`${Number(
                          row.amount
                        ).toFixed(2)}`}</TableCell>
                        <TableCell style={innerTableStylings}>
                          {row.gst}
                        </TableCell>
                      </TableRow>
                    ))}

                    {/* Add a row for Advance Payment */}
                    <TableRow>
                      <TableCell style={innerTableStylings}></TableCell>
                      <TableCell style={innerTableStylings}>
                        <strong>Advance Payment (10%):</strong>
                      </TableCell>
                      <TableCell style={innerTableStylings}>
                        <strong>{40000.0}</strong>
                      </TableCell>
                      <TableCell style={innerTableStylings}></TableCell>
                    </TableRow>

                    {/* Add a row for Total Amount */}
                    <TableRow>
                      <TableCell style={innerTableStylings}></TableCell>
                      <TableCell style={innerTableStylings}>
                        <strong>Total Amount (₹):</strong>
                      </TableCell>
                      <TableCell style={innerTableStylings}>
                        <strong>{200000.0}</strong>
                      </TableCell>
                      <TableCell style={innerTableStylings}></TableCell>
                    </TableRow>
                  </TableBody>
                </Table>
              </TableContainer>
              }
              />
             
              <Card sx={{ p: 2 }}>
          <Typography variant="h6" sx={{ mb: 2,ml:3 }}>
            Uploaded Files
          </Typography>
          {/* Pass the 'hideUpload' prop as true to disable upload UI */}
          <UploadFile
            selectedFiles={selectedFiles}
            setSelectedFiles={setSelectedFiles}
            hideUpload={true} // Prop to hide the upload functionality
            disableDelete={disableDelete}
          />
        </Card>
        

          </>
        </DialogContent>
    
      </Dialog>
    </>
  );
};

export default EditQuotationDetails;
