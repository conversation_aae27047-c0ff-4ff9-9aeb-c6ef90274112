const staticLeftMenu = [
  { name: "Dashboard", title: "Dashboard", icon: "tabler:layout-dashboard", path: "/dashboard" },
  {
    name: "Growth",
    title: "Growth",
    icon: "tabler:growth",
    path: null,
    children: [
      { name: "Service_Providers_SP", title: "Service Providers SP", icon: "tabler:users", path: "/SP" },
      { name: "Society_CHS", title: "Societies CHS", icon: "tabler:buildings", path: "/CHS" },    
    ]
  },
  {
    name: "SP_Profile",
    title: "SP Profile",
    icon: "tabler:user-circle",
    path: null,
    children: [
      { name: "Basic_Profile", title: "Basic Profile", icon: "tabler:user", path: "/SP/basic-profile" },
      { name: "Microsite", title: "For Microsite", icon: "tabler:smart-home", path: "/SP/microsite" },
      { name: "Service_Profile", title: "Service Profile", icon: "tabler:user-check", path: "/SP/service-profile" }
    ]
  },
  {
    name: "Projects",
    title: "Projects",
    icon: "tabler:brand-producthunt",
    path: null,
    children: [
  { name: "Service_Requisition", title: "Service Requisitions", icon: "tabler:checklist", path: "/service-requisitions" },
  { name: "Site_Visits", title: "Site Visits", icon: "tabler:map-pin", path: "/site-visits" },
  { name: "Quotations", title: "Quotations", icon: "tabler:file-text", path: "/quotations" },
  { name: "Work_Orders", title: "Work Orders", icon: "tabler:hammer", path: "/work-orders" }
 
    ]
  },
  { name: "Appointments", title: "Calendar", icon: "tabler:calendar-week", path: "/appointments-calendar" },
  {
    name: "Tools",
    title: "Tools",
    icon: "tabler:tools",
    path: null,
    children: [
      { name: "Contact_Groups", title: "Contact Groups", icon: "tabler:users-group", path: "/contact-groups" },
      { name: "Appointments", title: "Appointments", icon: "tabler:calendar-week", path: "/appointments-calendar" },
      { name: "Tasks", title: "Tasks", icon: "tabler:clipboard-list", path: "/tasks" },
      { name: "All_Notifications", title: "All Notifications", icon: "tabler:notification", path: "/notifications" },    
    ]
  },
  {
    name: "Redevelopment",
    title: "Redevelopment",
    icon: "tabler:building-skyscraper",
    path: null,
    children: [
      { name: "CHS_Readiness_Report", title: "CHS Readiness Report", icon: "tabler:report", path: "/CHS/readiness" },
      { name: "FSI_Calculator", title: "FSI Calculator", icon: "tabler:calculator", path: "/CHS/fsi-calculator" },
      { name: "CHS_Readiness_Reports", title: "CHS Readiness Reports", icon: "tabler:clipboard-data", path: "/CHS/all-readiness-reports" },
      { name: "CHS_Calculated_FSI", title: "CHS Calculated FSI's", icon: "tabler:calculator", path: "/CHS/fsi-calculations" }, 
    ]
  },
  {
    name: "Engage",
    title: "Engage",
    icon: "tabler:plug-connected",
    path: null,
    children: [
      { name: "SP_Conversations", title: "SP Conversations", icon: "tabler:brand-wechat", path: "/SP/conversations" },
      { name: "CHS_Conversations", title: "CHS Conversations", icon: "tabler:brand-wechat", path: "/CHS/conversations" },
    ]
  },
  { name: "Employees", title: "Employees", icon: "tabler:briefcase", path: "/employees" },
  { name: "Invoices", title: "Payments", icon: "tabler:currency-rupee", path: "/invoices" },
  { name: "Chat", title: "Chat", icon: "tabler:messages", path: "/chat" },
  { name: "WhatsApp", title: "WhatsApp", icon: "tabler:brand-whatsapp", path: "https://wa.me/918928497829" },
  { name: "Documents", title: "Documents", icon: "tabler:files", path: "/documents" },
  { name: "Testimonial_Center", title: "Testimonial Center", icon: "tabler:blockquote", path: "/testimonial-center" },
  { name: "Planing_And_Billing_Info", title: "Subscription", icon: "tabler:package", path: "/subscription/plans-billing" },
  { name: "Society_Profile", title: "CHS Profile", icon: "tabler:address-book", path: "/CHS/profile" },
    // { name: "Reports", title: "Reports", icon: "tabler:forms", path: null },
];

export default staticLeftMenu;
