import React, { useContext, useEffect, useState } from "react";
import {
  Box,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  IconButton,
  Grid,
  DialogContentText,
  FormControl,
  MenuItem,
  Select,
  InputLabel,
  FormControlLabel,
  Typography,
  Switch,
} from "@mui/material";
import SelectAutoComplete from "src/@core/components/custom-components/SelectAutoComplete";
import CloseIcon from "@mui/icons-material/Close";
import AccordionBasic from "src/@core/components/custom-components/AccordionBasic";
import CloseExpandIcons from "../all-profiles-old/CloseExpandIcons";
import SiteMapData from "src/@core/components/custom-components/SiteMapData";
import { Controller, useForm } from "react-hook-form";
import authConfig from "src/configs/auth";
import { AuthContext } from "src/context/AuthContext";
import * as yup from "yup";
import { yupResolver } from "@hookform/resolvers/yup";
import MultiSelectAutoComplete from "src/@core/components/custom-components/MultiSelectAutoComplete";

const schema = yup.object().shape({});
const accessList = [
  { name: "Create", value: "CREATE" },
  { name: "Read", value: "READ" },
  { name: "Update", value: "UPDATE" },
  { name: "Delete", value: "DELETE" },
];

const AddDialog = ({ employeesData, roles, open, onClose }) => {
  const handleClose = () => {
    onClose();
    reset();
    setRoleData(null);
  };

  const {
    postAssignRole,
    setRoleUser,
    roleData,
    setRoleData,
    allSiteMapDataFromSettings,
    getAllListValuesByListNameId,
    allCategories,
  } = useContext(AuthContext);

  const [dialogMessage, setDialogMessage] = useState("");
  const [expanded, setExpanded] = useState(true);
  const [email, setEmail] = useState(null);
  const [mobileNumber, setMobileNumber] = useState(null);
  const [selectedRole, setSelectedRole] = useState(""); //RoleId
  const [rolesSiteMapData, setRolesSiteMapData] = useState([]);
  const [openDialogContent, setOpenDialogContent] = useState(false);

  const [societiesData, setSocietiesData] = useState(null);

  // ServiceProviders States. This is the data from the listValues. For the Options.
  const [allServicesList, setAllServicesList] = useState([]);
  const [allBusinessLine, setAllBusinessLine] = useState([]);

  // start For Document Categories data.
  const [selectedDocumentCategoryId, setSelectedDocumentCategoryId] = useState(
    []
  );

  const handleSelectDocumentCategory = (event) => {
    setSelectedDocumentCategoryId(event.target.value);
  };
  // end For Document Categories data.

  const {
    setValue,
    control,
    getValues,
    handleSubmit,
    register,
    reset,
    clearErrors,
    formState: { errors },
  } = useForm({
    defaultValues: {
      name: "",
      servicesAccessGivenTo: [],
      societiesData: "No",
      businessLine: [],
    },
    mode: "onChange",
    resolver: yupResolver(schema),
  });

  const [isActive, setIsActive] = useState("");

  const handleOnChange = (event) => {
    if (event.target.checked) {
      setIsActive("ACTIVE");
    } else {
      setIsActive("INACTIVE");
    }
  };

  useEffect(() => {
    if (!!authConfig) {
      getAllListValuesByListNameId(
        authConfig.societiesId,
        handleSocietiesChange,
        handleError
      );
      getAllListValuesByListNameId(
        authConfig.allBusinessLineId,
        (data) =>
          setAllBusinessLine(
            data?.listValues.map((item) => ({
              value: item.id,
              key: item.listValue,
            }))
          ),
        handleError
      );
      getAllListValuesByListNameId(
        authConfig.allServicesListNameId,
        (data) =>
          setAllServicesList(
            data?.listValues.map((item) => ({
              value: item.id,
              key: item.listValue,
            }))
          ),
        handleError
      );
    }
  }, [authConfig]);

  const handleSocietiesChange = (data) => {
    setSocietiesData(data?.listValues);
  };

  const handleServicesSuccess = (data) => {
    setAllServicesList(data?.listValues);
  };

  const handleBusinessLine = (data) => {
    setAllBusinessLine(data?.listValues);
  };

  const handleError = (error) => {
    console.error("All Services:", error);
  };

  const [employeeUserId, setEmployeeUserId] = useState("");

  const handleEmployeeChange = (e) => {
    const selectedData = e.target.value;
    setEmployeeUserId(selectedData.id);
    setEmail(selectedData.metaData.email);
    setMobileNumber(selectedData.metaData.mobileNumber);
  };

  // For Accoridians close/open
  const handleToggle = (value) => {
    setExpanded(value);
  };
  // useEffect(() => {
  //   setIsNameDropdownEnabled(selectedUserType?.userType !== "");
  //   setEmail("");
  // }, [selectedUserType?.userType, profileType]);

  const [isAPISuccess, setIsAPISuccess] = useState(false);

  const handleErrorAPI = () => {
    setOpenDialogContent(false);
  };

  const handleSuccessAPI = () => {
    handleClose();
  };

  const handleSave = async (submittedData) => {
    // Flag to check if any checkbox is selected
    let noCheckBoxSelected = true;
    const transformedData = allSiteMapDataFromSettings.map((page) => {
      const pageData = {
        name: page.name,
        code: page.code,
        children: [],
        permissions: accessList
          .filter((theme) => getValues(`${page.code}.${theme.value}`))
          .map((theme) => theme.value),
      };

      if (pageData.permissions.length) {
        noCheckBoxSelected = false;
      }

      if (page.children) {
        pageData.children = page.children.map((child) => {
          return {
            code: child.code,
            permissions: accessList
              .filter((theme) => getValues(`${child.code}.${theme.value}`))
              .map((theme) => theme.value),
          };
        });
      }

      return pageData;
    });
    console.log("submittedData",submittedData);

    let message = "Failed to Save:";

    if (noCheckBoxSelected) {
      message += `<h3 style="text-align: left;">
      Select atleast one permissions.
      </h3>`;
    }
    if (!employeeUserId) {
      message += `<h3 style="text-align: left;">
      No Employee is Selected.
      </h3>`;
    }
    if (!selectedRole && !selectedRole?.id) {
      message += `<h3 style="text-align: left;">
      No Role Selected.
      </h3>`;
    }

    if (
      noCheckBoxSelected ||
      !employeeUserId ||
      (!selectedRole && !selectedRole?.id)
    ) {
      setDialogMessage(message);
      setOpenDialogContent(true);
    } else {
      const jsonToBackend = {
        referenceId: employeeUserId,
        roleID: selectedRole?.id,
        additionalPrivileges: {
          societiesData: submittedData?.societiesData == "Yes" ? true : false,
          businessLine: submittedData?.businessLine.map(
            (business) => business.value
          ),
          services: submittedData?.servicesAccessGivenTo.map(
            (service) => service.value
          ),
          documentCategoryIds: selectedDocumentCategoryId,
        },
        pages: {
          pages: transformedData,
        },
        status: isActive,
      };
      console.log("JSON To backend", jsonToBackend);

      const response = await postAssignRole(jsonToBackend, () => {});

      if (response) {
        const message = `
          <div style="text-align: left;">
          <h3>
          Role Assigned successfully.
          </h3>
          </div>
        `;
        setDialogMessage(message);
        setOpenDialogContent(true);
        setIsAPISuccess(true);
        reset();
      } else {
        const message = `
          <div>
          <h3>
          Failed to Assign Role to User.
          </h3>
          </div>
        `;
        setDialogMessage(message);
        setOpenDialogContent(true);
        setIsAPISuccess(false);
        reset();
      }
    }
  };

  useEffect(() => {
    // Your mount code here. This code runs when the component is mounted.

    // Return a function from here. This function will be called when the component is about to unmount.
    return () => {
      setEmployeeUserId("")
      setSelectedRole("");
      setEmail("");
      setMobileNumber("");
      setOpenDialogContent(false);

      // Need to reset these values as well, thet are defined in the authcontext.
      setRoleData(null);
      setRoleUser(null);
      console.log("AddDialog Component will unmount");
      // Your unmount code here. Put your clean-up logic here.
    };
  }, []); // The empty array means this effect runs once on mount and once on unmount.

  // When the setRoleUser is set, API call is made to fetch the role based on the roleId and the data is set to the roleData state,
  // Both the states roleUser and roleData is present in the Authcontext
  useEffect(() => {
    console.log("1/2. selectedRole changed............", selectedRole);
    if (!!selectedRole) {
      console.log("2/2. selectedRole changed............", selectedRole);
      setRoleUser(selectedRole);
    }
  }, [selectedRole]);

  //Actually this is written when the user changes the role from select dropdown we fetch the pages assigned to the role and use it to pre-populate.
  // And at pre-population we check an conditions whether it's changed from the select dropdown, or if in the set when in the edit mode when we pre-populate the data for the user.
  useEffect(() => {
    if (!!roleData) {
      console.log("RoleData is set:", roleData);
      setRolesSiteMapData(roleData?.roleMetaData?.siteMapData?.pages);
      setValue(
        "servicesAccessGivenTo",
        roleData?.additionalPrivileges?.services || []
      );
      setValue(
        "societiesData",
        roleData?.additionalPrivileges?.societiesData ? "Yes" : "No"
      );
      setValue(
        "businessLine",
        roleData?.additionalPrivileges?.businessLine || []
      );
    }
  }, [roleData]);

  return (
    <>
      <Dialog
        fullScreen
        maxWidth="md"
        scroll="paper"
        open={open}
        onClose={(_event, reason) => {
          if (reason !== "backdropClick") {
            handleClose();
          }
        }}
      >
        <DialogTitle
          sx={{
            position: "relative",
            borderBottom: (theme) => `1px solid ${theme.palette.divider}`,
            p: (theme) => `${theme.spacing(1.5)} !important`,
            display: "flex",
            flexDirection: "column",
            alignItems: "start",
            margin: 3,
            fontSize: {
              xs: "24px",
              md: "30px",
            },
            height:"50px", // height
          }}
        >
          <Box
            sx={{
              marginBottom: 1,
            }}
          >
            Assign Privileges to User
          </Box>
          <Box
            sx={{
              position: "absolute",
              top: 0,
              right: 0,
              display: "flex",
              alignItems: "center",
            }}
          >
            <CloseExpandIcons
              expanded={expanded}
              onToggle={handleToggle}
              sx={{ marginRight: 1 }}
            />
            <IconButton
              size="small"
              onClick={handleClose}
              sx={{
                p: "0.438rem",
                borderRadius: 1,
                color: "common.white",
                backgroundColor: "primary.main",
                "&:hover": {
                  backgroundColor: "#66BB6A",
                  transition: "background 0.5s ease, transform 0.5s ease",
                },
              }}
            >
              <CloseIcon fontSize="1rem" />
            </IconButton>
          </Box>
        </DialogTitle>

        <DialogContent>
          <AccordionBasic
            id={"user-details"}
            ariaControls={"user-details"}
            heading={"User Details"}
            body={
              <>
                <Grid
                  marginTop={5}
                  id="userDetails"
                  container
                  spacing={2}
                  style={{
                    alignItems: "start",
                    width: "100%",
                  }}
                >
                  <Grid item xs={12} md={2}>
                    <Box>
                      <Typography variant="body1">User Category:</Typography>
                      <Typography variant="body1" fontWeight={500}>
                        Employees
                      </Typography>
                    </Box>
                  </Grid>
                  <Grid item xs={12} md={2} sx={{ mr: 2 }}>
                    <Box>
                      <FormControl fullWidth>
                        <InputLabel id="name-label">Name</InputLabel>
                        <Select
                        label="Name"
                          defaultValue=""
                          size="small"
                          labelId="name-label"
                        onChange={handleEmployeeChange}
                        >
                          {employeesData?.map((data) => (
                            <MenuItem key={data.id} value={data}>
                              {data.name}
                            </MenuItem>
                          ))}
                        </Select>
                      </FormControl>
                    </Box>
                  </Grid>
                  <Grid item xs={12} md={2}>
                    <Box>
                      <FormControl fullWidth>
                        <InputLabel id="userRole-label">User Role</InputLabel>
                        <Select
                          label="User Role"
                          labelId="userRole-label"
                          size="small"
                          value={selectedRole}
                          onChange={(e) => {
                            const role = e.target.value;
                            console.log("Role Changed::::", role);
                            setSelectedRole(role);
                          }}
                        >
                          <MenuItem key="none" value="">
                            <em>Please Select</em>
                          </MenuItem>
                          {roles?.map((role) => (
                            <MenuItem key={role?.id} value={role}>
                              {role?.name}
                            </MenuItem>
                          ))}
                        </Select>
                      </FormControl>
                    </Box>
                  </Grid>

                  <Grid item xs={12} md={5} alignItems="flex-start">
                    {" "}
                    {/* Increased md to allow more space */}
                    <Grid container alignItems="center" spacing={2}>
                      {/* Email Block */}
                      {(email) && (
                        <Grid
                          item
                          xs={12}
                          md={6}
                          style={{ paddingRight: "8px" }}
                        >
                          {" "}
                          {/* Adjusted grid sizing for both xs and md */}
                          <Typography variant="body1" sx={{marginLeft:"30px"}}>Email:</Typography>
                          <Typography
                            variant="body2"
                            style={{ wordBreak: "break-all" }}
                            fontWeight={"bold"}
                            sx={{marginLeft:"30px"}}
                          >
                            {" "}
                            {/* Allows words to break and wrap */}
                            {email}
                          </Typography>
                        </Grid>
                      )}

                      {/* Mobile Number Block */}
                      {(mobileNumber) && (
                        <Grid
                          item
                          xs={12}
                          md={6}
                          style={{ paddingLeft: "8px" }}
                        >
                          {" "}
                          {/* Adjusted grid sizing for both xs and md */}
                          <Typography variant="body1">
                            Mobile Number:
                          </Typography>
                          <Typography
                            variant="body2"
                            style={{ wordBreak: "break-all" }}
                            fontWeight={"bold"}
                          >
                            {" "}
                            {/* Allows words to break and wrap */}
                            {mobileNumber}
                          </Typography>
                        </Grid>
                      )}
                    </Grid>
                  </Grid>
                </Grid>
                <Grid
                  marginTop={5}
                  id="userStatus"
                  container
                  spacing={2}
                  style={{
                    alignItems: "start",
                    width: "100%",
                  }}
                >
                  {(isActive == "ACTIVE" || isActive == "INACTIVE") && (
                    <Grid item xs={12} md={4}>
                      <Box>
                        <FormControlLabel
                          control={
                            <Switch
                              checked={isActive == "ACTIVE" ? true : false}
                              onChange={handleOnChange}
                              name="isActive"
                            />
                          }
                          label="Make This Role Active."
                        />
                      </Box>
                    </Grid>
                  )}
                </Grid>
              </>
            }
            expanded={expanded}
          />
              
          <>

            {/* <AccordionBasic
              id={"data-access-societies-and-service-providers"}
              ariaControls={"data-access-societies-and-service-providers"}
              heading={"Data Access"}
              body={
                <>
                  <Grid
                    marginTop={5}
                    id="data-access-societies-and-service-providers"
                    container
                    spacing={2}
                    style={{
                      alignItems: "start",
                      width: "100%",
                    }}
                  >
                    <Grid item xs={12} sm={4}>
                      <FormControl fullWidth>
                        <Controller
                          name="servicesAccessGivenTo"
                          control={control}
                          render={({ field }) => (
                            <MultiSelectAutoComplete
                              id="service-providers"
                              label="Select Services Providers"
                              nameArray={allServicesList}
                              value={field.value}
                              onChange={(e) => field.onChange(e.target.value)}
                              error={Boolean(errors.serivcesProvided)}
                            />
                          )}
                        /> */}
                        {/* {errors.servicesProvided && (
      <FormHelperText>{errors.servicesProvided.message}</FormHelperText>
    )} */}
                      {/* </FormControl>
                    </Grid>
                    <Grid item xs={12} sm={2}>
                    <FormControl
                        fullWidth>
                        <InputLabel id="societiesData-select-label">
                          Societies Data
                        </InputLabel>
                        <Controller
                          name="societiesData"
                          control={control}
                          render={({ field }) => (
                            <Select
                              {...field}
                              labelId="societiesData-select-label"
                              id="societiesData-select"
                              size="small"
                              label="Societies Data"
                              onChange={(event) => {
                                field.onChange(event.target.value);
                              }}
                                renderValue={(selected) => (
                                <span>
                                  {societiesData?.find((service) =>
                                            service?.listValue === selected
                                        )?.listValue
                                        }
                                </span>
                              )}
                            >
                              <MenuItem key="none" value="">
                                  Please Select.
                                </MenuItem>
                              {societiesData?.map((data) => (
                                <MenuItem key={data.id} value={data.listValue}>
                                  {data.listValue}
                                </MenuItem>
                              ))}
                            </Select>
                          )}
                        />
                        {errors.selectedSocietiesDataId && (
                          <FormHelperText>
                            {errors.selectedSocietiesDataId?.message}
                          </FormHelperText>
                        )}
                      </FormControl>
                    </Grid>

                    <Grid item xs={12} md={2}>
                      <Box>
                        <FormControl fullWidth>
                          <Controller
                            name="businessLine"
                            control={control}
                            rules={{ required: true }}
                            render={({ field }) => (
                              <MultiSelectAutoComplete
                                id="businessLine"
                                label="Business Line"
                                nameArray={allBusinessLine}
                                value={field.value}
                                onChange={(e) => field.onChange(e.target.value)}
                              />
                            )}
                          /> */}
                          {/* {errors.servicesProvided && (
      <FormHelperText>{errors.servicesProvided.message}</FormHelperText>
    )} */}
                        {/* </FormControl>
                      </Box>
                    </Grid>
                  </Grid>
                  <Grid
                    marginTop={5}
                    id="userStatus"
                    container
                    spacing={2}
                    style={{
                      alignItems: "start",
                      width: "100%",
                    }}
                  >
                    {(isActive == "ACTIVE" || isActive == "INACTIVE") && (
                      <Grid item xs={12} md={4}>
                        <Box>
                          <FormControlLabel
                            control={
                              <Switch
                                checked={isActive == "ACTIVE" ? true : false}
                                onChange={handleOnChange}
                                name="isActive"
                              />
                            }
                            label="Make This Role Active."
                          />
                        </Box>
                      </Grid>
                    )}
                  </Grid>
                </>
              }
              expanded={expanded}
            /> */}
          </>

          {/* Temp removing it. Pre-population and CRUD need to verify. */}
          {/* <AccordionBasic
            id="document-access"
            ariaControls="document-access"
            heading="Document Access"
            body={
              <>
                <Grid
                  marginTop={5}
                  id="document-access"
                  container
                  spacing={2}
                  style={{
                    alignItems: "start",
                    width: "100%",
                  }}
                >
                  <Grid item xs={12} sm={2}>
                    <FormControl
                      fullWidth
                      error={!!errors.selectedDocumentCategoryId}
                    >
                      <InputLabel id="documentsCategories-select-label">
                        Document Categories
                      </InputLabel>
                      <Select
                        labelId="documentsCategories-select-label"
                        id="documentsCategories-select"
                        multiple
                        size="small"
                        value={selectedDocumentCategoryId || []}
                        onChange={handleSelectDocumentCategory}
                        label="Document Categories"
                      >
                        {allCategories.map((category) => (
                          <MenuItem key={category.id} value={category.id}>
                            {category.documentCategory}
                          </MenuItem>
                        ))}
                      </Select>
                      {errors.selectedDocumentCategoryId && (
                        <FormHelperText
                          id="validation-selectedDocumentCategoryId"
                          error
                        >
                          {errors.selectedDocumentCategoryId.message}
                        </FormHelperText>
                      )}
                    </FormControl>
                  </Grid>
                </Grid>
              </>
            }
            expanded={expanded}
          /> */}

          {/* {selectedRole?.name && ( */}
          <AccordionBasic
            id={"siteMapData"}
            ariaControls={"siteMapData"}
            heading={"SiteMap Data"}
            body={
              <>
                <SiteMapData
                  setValue={setValue}
                  control={control}
                  getValues={getValues}
                  defaultData={rolesSiteMapData}
                />
              </>
            }
            expanded={expanded}
          />
          {/* )} */}
        </DialogContent>
        <DialogActions
          sx={{
            justifyContent: "end",
            borderTop: (theme) => `1px solid ${theme.palette.divider}`,
            p: (theme) => `${theme.spacing(1.5)} !important`,
            height:"50px",
          }}
        >
          <Button onClick={handleClose}>Close</Button>

          <Button variant="contained" onClick={handleSubmit(handleSave)}>
            Submit
          </Button>
        </DialogActions>
      </Dialog>
      <Dialog
        open={openDialogContent}
        onClose={isAPISuccess ? handleSuccessAPI : handleErrorAPI}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
        PaperProps={{
          sx: {
            p: (theme) => `${theme.spacing(2.5)} !important`,
            backgroundColor: (theme) => theme.palette.primary.background,
          },
        }}
      >
        <Box
          sx={{
            width: "100%",
            borderRadius: 1,
            textAlign: "center",
            border: (theme) => `1px solid ${theme.palette.divider}`,
            borderColor: "primary.main",
          }}
        >
          <DialogContent>
            <DialogContentText
              id="alert-dialog-description"
              color="primary.main"
            >
              <div dangerouslySetInnerHTML={{ __html: dialogMessage }} />
            </DialogContentText>
          </DialogContent>
          <DialogActions>
            <Button
              variant="contained"
              onClick={isAPISuccess ? handleSuccessAPI : handleErrorAPI}
              sx={{ margin: "auto", width: 100 }}
            >
              Okay
            </Button>
          </DialogActions>
        </Box>
      </Dialog>
    </>
  );
};

export default AddDialog;
