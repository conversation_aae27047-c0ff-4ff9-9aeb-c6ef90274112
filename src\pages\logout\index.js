import {
  <PERSON>,
  Button,
  Container,
  <PERSON>rid,
  <PERSON>u,
  MenuItem,
  Typography,
} from "@mui/material";
import { useState } from "react";
import BlankLayout from "src/@core/layouts/BlankLayout";

const LogoutPage = () => {
  const [anchorEl, setAnchorEl] = useState(null);

  const handleOpenMenu = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleCloseMenu = () => {
    setAnchorEl(null);
  };

  const handleMenuItemClick = (role) => {
    localStorage.setItem("role", role);
    setAnchorEl(null);
    window.location.href = "/register?role=" + role;
    handleCloseMenu();
  };

  return (
    <>
      <Container
        maxWidth="xs"
        sx={{
          height: "90vh",
          display: "flex",
          flexDirection: "row",
          alignItems: "center",
          justifyContent: "start",
          boxShadow: 3,
          p: 8,
          mt: 8,
          mb: 8,
          borderRadius: 6,
          bgcolor: "white",
          position: "relative",
          overflowY: "auto",
          flexGrow: 1,
          transform: {
            xs: "scale(0.9)", // 90% of original size on extra-small screens (mobile)
            sm: "none", // Normal size for tablets and above
          },
        }}
      >
        <Box>
          <Grid
            container
            spacing={3}
            sx={{ alignItems: "center", justifyContent: "center" }}
          >
            <Grid item xs={9.2}>
              <Typography
                variant="h5"
                fontWeight={500}
                color="primary"
                gutterBottom
              >
                {` Successfully logged out 👋🏻`}
              </Typography>
            </Grid>

            <Grid item xs={9.2} sx={{ marginBottom: 2 }}>
              <Button
                href="/login"
                variant="contained"
                color="primary"
                sx={{ mt: 4, width: "100%" }}
              >
                Login
              </Button>
            </Grid>

            <Grid item xs={9.2} sx={{ marginBottom: 2 }}>
              <Button
                variant="outlined"
                color="primary"
                sx={{ mt: 4, width: "100%" }}
                onClick={handleOpenMenu}
              >
                Create New Account
              </Button>
              <Menu
                anchorEl={anchorEl}
                open={Boolean(anchorEl)}
                onClose={handleCloseMenu}
                anchorReference="anchorPosition"
                anchorPosition={{
                  top: anchorEl ? anchorEl.getBoundingClientRect().bottom : 0,
                  left: anchorEl ? anchorEl.getBoundingClientRect().left : 0,
                }}
              >
                <MenuItem
                  onClick={() => handleMenuItemClick("service-provider")}
                  style={{ padding: "3px 6px" }}
                >
                  Service Provider
                </MenuItem>
                <MenuItem
                  onClick={() => handleMenuItemClick("society")}
                  style={{ padding: "3px 6px" }}
                >
                  Society Member
                </MenuItem>
              </Menu>
            </Grid>
          </Grid>
        </Box>
      </Container>
    </>
  );
};

LogoutPage.getLayout = (page) => <BlankLayout>{page}</BlankLayout>;
LogoutPage.guestGuard = true;

export default LogoutPage;
