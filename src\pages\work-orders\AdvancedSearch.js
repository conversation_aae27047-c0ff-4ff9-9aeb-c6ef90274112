import { FormControl, Grid, TextField } from "@mui/material";
import Box from "@mui/material/Box";
import Button from "@mui/material/Button";
import Drawer from "@mui/material/Drawer";
import IconButton from "@mui/material/IconButton";
import { styled } from "@mui/material/styles";
import Tooltip from "@mui/material/Tooltip";
import Typography from "@mui/material/Typography";
import axios from "axios";
import { useEffect, useState } from "react";
import { Controller, useForm } from "react-hook-form";
import PerfectScrollbar from "react-perfect-scrollbar";
import SelectClearAutoComplete from "src/@core/components/custom-components/SelectClearAutoComplete";
import Icon from "src/@core/components/icon";
import CustomAvatar from "src/@core/components/mui/avatar";
import { getAuthorizationHeaders, getUrl } from "src/helpers/utils";
import authConfig from "src/configs/auth";

const Header = styled(Box)(({ theme }) => ({
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
  position: "relative",
  padding: theme.spacing(3),
  borderBottom: `1px solid ${theme.palette.divider}`,
}));

const AdvancedSearch = (props) => {
  const {
    open,
    toggle,
    selectedFilters,
    setSearchingState,
    societyOptions,
    clearAllFilters,
    onApplyFilters,
    spsList,
    setSpsList
  } = props;

  const {
    setValue,
    control,
    reset,
    handleSubmit,
    formState: { errors },
  } = useForm();

  const [selectSocietyId, setSelectSocietyId] = useState("");
  const [confirmedSPId,setConfirmedSPId] = useState("")

  useEffect(() => {
    // Create a map of filter keys for easy lookup
    const filterMap = new Map(selectedFilters?.map((filter) => [filter.key, filter.value]));

    // Set values based on filters or clear them if the filter is not present
    setValue("workOrderNo", filterMap.get("workOrdersNoFilter") || "");
    setValue("reqNo", filterMap.get("requisitionNoFilter") || "");
    setSelectSocietyId(filterMap.get("societyNameFilter") || null);
    setConfirmedSPId(filterMap.get("confirmedSpNameFilter") || null);

  }, [selectedFilters, setValue]);

  useEffect(()=>{
    if (selectSocietyId) {
      axios({
        method: "get",
        url:
          getUrl(authConfig.serviceRequisitionsEndpoint) +
          "/get-service-type-names/" +
          selectSocietyId,
        headers: getAuthorizationHeaders(),
      })
        .then((res) => {
          setSpsList(res.data);
        })
        .catch((err) => console.log("Categories error", err));
    }
  },[selectSocietyId])

  const handleCancel = () => {
    reset();
    setSelectSocietyId("");
    setConfirmedSPId("")
    setSearchingState(false);
    clearAllFilters();
    
  };

  const handleClose = () => {
    toggle();
  };

  const handleApply = (data) => {
    // Prepare selected filters for chips
    const filters = [];
    if (data?.workOrderNo) {
      filters.push({ key: "workOrdersNoFilter", label: "Work Order No", value: data?.workOrderNo });
    }
    if (data?.reqNo) {
      filters.push({ key: "requisitionNoFilter", label: "Requisition No", value: data?.reqNo });
    }
    if (selectSocietyId) {
      filters.push({ key: "societyNameFilter", label: "Society Name", value: selectSocietyId });
    }
    // if(confirmedSPId){
    //   filters.push({ key: "confirmedSpNameFilter", label: "Confirmed SP", value: confirmedSPId });
    // }

    // Call the parent callback with selected filters
    onApplyFilters(filters);
    setSearchingState(true); // Update searching state in parent if needed
    toggle(); // Close the drawer
  };

  return (
    <>
      <Tooltip title="Advanced Search">
        <CustomAvatar
          variant="rounded"
          sx={{ width: 36, height: 36, cursor: "pointer" }}
          onClick={toggle}
        >
          <Icon icon="tabler:filter" fontSize={27} />
        </CustomAvatar>
      </Tooltip>

      <Drawer
        open={open}
        anchor="right"
        variant="temporary"
        onClose={handleClose}
        ModalProps={{ keepMounted: true }}
        sx={{ "& .MuiDrawer-paper": { width: { xs: "85%", sm: 500 } } }}
      >
      <Header
          sx={{
            position: "relative", 
            display: "flex",
            alignItems: "center", 
            justifyContent: "space-between", 
          }}
        >
          <Typography variant="h5" sx={{
            marginLeft: "12px",
          }}>Advanced Search</Typography>
          <Box sx={{ position: "absolute", top: "8px", right: "26px" }}>
            <IconButton
              size="small"
              onClick={handleClose}
              sx={{
                borderRadius: 1,
                color: "common.white",
                backgroundColor: "primary.main",
                "&:hover": {
                  backgroundColor: "#66BB6A",
                  transition: "background 0.5s ease, transform 0.5s ease",
                },
                mt: 2,
              }}
            >
              <Icon icon="tabler:x" fontSize="1rem" />
            </IconButton>
          </Box>
        </Header>

        <PerfectScrollbar options={{ wheelPropagation: false }}>
          <Box sx={{ p: (theme) => theme.spacing(4, 6) }}>
            <Grid container spacing={3} alignItems={"center"}>
              <Grid item xs={12} md={12}>
                <FormControl fullWidth>
                  <Controller
                    name="workOrderNo"
                    control={control}
                    defaultValue=""
                    render={({ field }) => (
                      <TextField
                        {...field}
                        size="small"
                        label=" Search by Work Order No"
                        placeholder="Search By Work Order No"
                        InputLabelProps={{ shrink: true }}
                        aria-describedby="workOrderNo"
                        value={field.value}
                      />
                    )}
                  />
                </FormControl>
              </Grid>

              <Grid item xs={12} md={12}>
                <FormControl fullWidth>
                  <Controller
                    name="reqNo"
                    control={control}
                    defaultValue=""
                    render={({ field }) => (
                      <TextField
                        {...field}
                        size="small"
                        label=" Search by Requisition No"
                        placeholder="Search By Requisition No"
                        InputLabelProps={{ shrink: true }}
                        aria-describedby="reqNo"
                        value={field.value}
                      />
                    )}
                  />
                </FormControl>
              </Grid>

              <Grid item xs={12} md={12}>
                <FormControl fullWidth>
                      <Controller
                        name="societyId"
                        control={control}
                        defaultValue=""
                        render={({ field }) => (
                          <SelectClearAutoComplete
                            id="societyId"
                            label="Select Society"
                            value={selectSocietyId}
                            nameArray={societyOptions?.map((option) => ({
                              value: option.orgId,
                              key: option.societyName,
                            }))}
                            onChange={(event) => {
                              field.onChange(event.target.value);
                              setSelectSocietyId(event.target.value);
                            }}
                          />
                        )}
                      />
                    </FormControl>
              </Grid>
                  {/* {selectSocietyId && (
                     <Grid item xs={12} md={12}>
                     <FormControl fullWidth>
                       <Controller
                         name="confirmedSP"
                         control={control}
                         defaultValue=""
                         render={({ field }) => (
                           <SelectClearAutoComplete
                             size="small"
                             id="confirmedSPId"
                             label="Select Service Provider"
                             nameArray={spsList?.map((option) => ({
                              value: option.srId,
                              key: option.serviceTypeAndCreatedBy,
                            }))}
                             value={confirmedSPId}
                             onChange={(event) => {
                               field.onChange(event.target.value);
                               setConfirmedSPId(event.target.value);
                             }}
                           />
                         )}
                       />
                     </FormControl>
                   </Grid>
                  )} */}
            </Grid>
          </Box>
        </PerfectScrollbar>

        <Box
          sx={{
            borderTop: (theme) => `1px solid ${theme.palette.divider}`,
            p: (theme) => theme.spacing(2),
            justifyContent: "flex-end",
            display: "flex",
            alignItems: "center",
          }}
        >
          <Button variant="tonal" sx={{ mr: 2 }} onClick={handleCancel}>
            Clear All
          </Button>
          <Button
          sx={{ mr: {
            xs: 4, 
            sm: 4, 
            md: 4, 
            lg: 4, 
            xl: 4,
          },} }
            variant="contained"
            onClick={handleSubmit(handleApply)}
          
          >
            Apply
          </Button>
        </Box>
      </Drawer>
    </>
  );
};

export default AdvancedSearch;
