import { useContext, useEffect, useState } from "react";

// ** MUI Imports
import Box from "@mui/material/Box";
import Button from "@mui/material/Button";
import Grid from "@mui/material/Grid";
import Menu from "@mui/material/Menu";
import MenuItem from "@mui/material/MenuItem";
import Tooltip from "@mui/material/Tooltip";
import { DataGrid } from "@mui/x-data-grid";
import Icon from "src/@core/components/icon";
import CustomAvatar from "src/@core/components/mui/avatar";
// ** Third Party Imports
import { useForm } from "react-hook-form";
import { AuthContext } from "src/context/AuthContext";
import SPMembers from "./SPMembers";
import CustomChip from "src/@core/components/mui/chip";
const userStatusObj = {
  true: "Active",
  false: "InActive",
};

const SPMemberInformation = ({
  formData,
  designationList,
  spMembers,
  setSpMembers,
}) => {
  const {
    control,
    watch,
    formState: { errors },
  } = useForm({
    defaultValues: formData?.serviceProviderList || {}, // Initialize form fields with existing data
  });

  const rowsPerPageOptions = [10, 15, 20, 25, 50, 100];
  const [pageSize, setPageSize] = useState(rowsPerPageOptions[0]);
  const [page, setPage] = useState(1);
  const [rowCount, setRowCount] = useState(0);

  useEffect(() => {
    setSpMembers(formData?.serviceProviders || []);
  }, [formData]);

  const [currentRow, setCurrentRow] = useState(null);
  const [societyMemberOpen, setSocietyMemberOpen] = useState(false);

  const handleSocietyMemberDialogOpen = () => {
    setCurrentRow(null);
    setSocietyMemberOpen(true);
  };
  const handleSocietyMemberDialogClose = () => {
    setSocietyMemberOpen(false);
    setCurrentRow(null);
  };

  const [menu, setMenu] = useState(null);

  const handleCloseMenuItems = () => {
    setMenu(null);
  };

  const mapIsActiveToLabel = (isActive) => {
    return userStatusObj[isActive] || "Unknown";
  };

  const cols = [
    {
      field: "name",
      headerName: "Name",
      minWidth: 105,
      flex: 1,
      renderCell: (params) => (
        <Tooltip title={params.row.name}>
          <span>{params.row.name}</span>
        </Tooltip>
      ),
    },
    {
      field: "mobileNumber",
      headerName: "Contact No.",
      minWidth: 105,
      flex: 1,
      renderCell: (params) => (
        <Tooltip title={params.row.mobileNumber}>
          <a
            href={`tel:${params.row.mobileNumber}`}
            style={{ textDecoration: "none", color: "blue" }}
          >
            {params.row.mobileNumber}
          </a>
        </Tooltip>
      ),
    },
    {
      field: "alternateMobileNumber",
      headerName: "Alternate No.",
      minWidth: 105,
      flex: 1,
      renderCell: (params) => (
        <Tooltip title={params.row.alternateMobileNumber}>
          <a
            href={`tel:${params.row.alternateMobileNumber}`}
            style={{ textDecoration: "none", color: "blue" }}
          >
            {params.row.alternateMobileNumber}
          </a>
        </Tooltip>
      ),
    },
    {
      field: "email",
      headerName: "Email",
      minWidth: 110,
      flex: 1,
      renderCell: (params) => (
        <Tooltip title={params.row.email}>
          <a
            href={`mailto:${params.row.email}`}
            style={{ textDecoration: "none", color: "blue" }}
          >
            {params.row.email}
          </a>
        </Tooltip>
      ),
    },
    {
      field: "designation",
      headerName: "Designation",
      minWidth: 95,
      flex: 1,
      renderCell: (params) => {
        const designation = designationList?.find(
          (item) => item?.value === params?.row?.designation
        );
        return (
          <Tooltip title={designation?.key || ""}>
            <span>{designation ? designation?.key : ""}</span>
          </Tooltip>
        );
      },
    },
    {
      field: "isActive",
      headerName: "Is Active",
      flex: 1,
      minWidth: 100,
      renderCell: ({ row }) => {
        return (
          <CustomChip
            rounded={true}
            skin="light"
            size="small"
            label={mapIsActiveToLabel(row.isActive)}
            color={row.isActive === true ? "success" : "error"}
            sx={{ textTransform: "capitalize" }}
          />
        );
      },
    },
    {
      field: "actions",
      headerName: "Actions",
      sortable: false,
      minWidth: 80,
      flex: 1,
      disableClickEventBubbling: true,
      renderCell: (params) => {
        const onClick = (event) => {
          event.stopPropagation();
          setMenu(event.currentTarget);
          const row = params.row;
          setCurrentRow(row);
        };

        const onClickViewProfile = () => {
          setSocietyMemberOpen(true);
          handleCloseMenuItems();
        };
        const onClickDeleteProfile = () => {
          const updatedMembers = spMembers?.map((member) =>
            member?.id === currentRow?.id
              ? { ...member, isActive: false }
              : member
          );
          setSpMembers(updatedMembers);
          handleCloseMenuItems();
        };

        const onClickActivateProfile = () => {
          const updatedMembers = spMembers?.map((member) =>
            member?.id === currentRow?.id
              ? { ...member, isActive: true }
              : member
          );
          setSpMembers(updatedMembers);
          handleCloseMenuItems();
        };

        return (
          <div
            style={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
            }}
          >
            <CustomAvatar
              skin="light"
              variant="rounded"
              sx={{
                mr: { xs: 2, lg: 4 },
                width: 34,
                height: 34,
                cursor: "pointer",
              }}
              onClick={onClick}
            >
              <Icon icon="bi:three-dots-vertical" />
            </CustomAvatar>
            <Menu
              id="actions-menu"
              anchorEl={menu}
              keepMounted
              open={Boolean(menu)}
              onClose={handleCloseMenuItems}
            >
              <MenuItem onClick={onClickViewProfile}>Edit</MenuItem>
              {currentRow?.isActive ? (
                <MenuItem onClick={onClickDeleteProfile}>DeActivate</MenuItem>
              ) : (
                <MenuItem onClick={onClickActivateProfile}>Activate</MenuItem>
              )}
            </Menu>
          </div>
        );
      },
    },
  ];

  const handlePageChange = (direction) => {
    if (direction === page) {
      setPage(page + 1);
    } else {
      setPage(page - 1);
    }
  };

  const handlePageSizeChange = (params) => {
    if (params) {
      setPageSize(params);
      setPage(1);
    }
  };

  return (
    <>
      <Box
        sx={{
          display: "flex",
          justifyContent: "left",
        }}
      >
        <div>
          <Button
            sx={{ marginBottom: "15px" }}
            variant="contained"
            onClick={handleSocietyMemberDialogOpen}
          >
            Add&nbsp;a&nbsp;SP&nbsp;member
          </Button>
        </div>
      </Box>
      <Grid item xs={12} sm={4}>
        <>
          {currentRow ? (
            <SPMembers
              open={societyMemberOpen}
              onClose={handleSocietyMemberDialogClose}
              spMembers={spMembers}
              setSpMembers={setSpMembers}
              designationList={designationList}
              rowData={currentRow}
            />
          ) : (
            <SPMembers
              open={societyMemberOpen}
              onClose={handleSocietyMemberDialogClose}
              spMembers={spMembers}
              setSpMembers={setSpMembers}
              designationList={designationList}
            />
          )}
        </>
      </Grid>
      {spMembers?.length > 0 && (
        <div style={{ height: "50%", width: "100%" }}>
          <DataGrid
            rows={spMembers || []}
            columns={cols}
            autoHeight
            checkboxSelection
            rowHeight={38}
            headerHeight={38}
          />
        </div>
      )}
    </>
  );
};

export default SPMemberInformation;
