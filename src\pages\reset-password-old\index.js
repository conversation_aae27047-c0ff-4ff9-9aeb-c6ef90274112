// ** React Imports
import { useState } from "react";
import { useRouter } from "next/router";

// ** Next Import
import Link from "next/link";
import { useAuth } from "src/hooks/useAuth";

// ** MUI Components
import Button from "@mui/material/Button";
import Typography from "@mui/material/Typography";
import Box from "@mui/material/Box";
import useMediaQuery from "@mui/material/useMediaQuery";
import { styled, useTheme } from "@mui/material/styles";
import InputLabel from "@mui/material/InputLabel";
import IconButton from "@mui/material/IconButton";
import FormControl from "@mui/material/FormControl";
import FormHelperText from "@mui/material/FormHelperText";
import InputAdornment from "@mui/material/InputAdornment";
import OutlinedInput from "@mui/material/OutlinedInput";

// ** React Imports
import { useEffect } from "react";

// ** Icon Imports
import Icon from "src/@core/components/icon";

// ** Layout Import
import BlankLayout from "src/@core/layouts/BlankLayout";
import authConfig from "src/configs/auth";

// ** Third Party Imports
import * as yup from "yup";
import { useForm, Controller } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";

// ** Configs
import themeConfig from "src/configs/themeConfig";

// ** Demo Imports
import FooterIllustrationsV2 from "src/views/pages/auth/FooterIllustrationsV2";
import {
  Alert,
  AlertTitle,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  CircularProgress,
  TextField,
} from "@mui/material";

// Styled Components
const ForgotPasswordIllustration = styled("img")(({ theme }) => ({
  zIndex: 2,
  maxHeight: 550,
  margin: theme.spacing(5),
  [theme.breakpoints.down(1540)]: {
    maxHeight: 500,
  },
  [theme.breakpoints.down("lg")]: {
    maxHeight: 450,
  },
}));

const RightWrapper = styled(Box)(({ theme }) => ({
  [theme.breakpoints.up("lg")]: {
    minWidth: "40%",
  },
  [theme.breakpoints.down("lg")]: {
    minWidth: "100%",
  },
}));

const LinkStyled = styled(Link)(({ theme }) => ({
  display: "flex",
  fontSize: "1rem",
  alignItems: "center",
  textDecoration: "none",
  justifyContent: "center",
  color: theme.palette.primary.main,
}));

const defaultValues = {
  newPassword: "",
  confirmNewPassword: "",
};

const schema = yup.object().shape({
  newPassword: yup
    .string()
    .required("Password is Mandatory")
    .min(8, "Password must be at least 8 characters")
    .matches(
      /^(?=.*[0-9])(?=.*[a-z])(?=.*[A-Z])(?=.*[@#$%^&-+=()!])(?=\S+$).{8,20}$/,
      "Must contain 8 characters, 1 uppercase, 1 lowercase, 1 number and 1 special case character"
    ),
  confirmNewPassword: yup
    .string()
    .required("Re-enter new password is mandatory")
    .oneOf([yup.ref("newPassword")], "Passwords must match"),
});

const ResetPassword = () => {
  const baseUrl = authConfig.baseURL;
  const forgotPasswordUrl = "forgot-password/?email=";
  const loginUrl = baseUrl + "login";
  const router = useRouter();
  const [isFailed, setIsFailed] = useState(false);

  const { resetCode, verificationCode, emailId, userId } = router.query;
  console.log("userId on rest page", userId);
  const successMessage = verificationCode
    ? "Email verified and new password has been set."
    : "New Password has been set.";
  const errorMessage = verificationCode
    ? "Email verification failed. Try again later."
    : "Password reset Failed. Try again later.";

  const {
    setValue,
    reset,
    control,
    handleSubmit,
    getValues,
    formState: { errors },
    trigger,
  } = useForm({
    defaultValues,
    resolver: yupResolver(schema),
    mode: "onSubmit", // Adding the onChange mode
  });

  // Set the form data on component mount
  useEffect(() => {
    if (verificationCode) {
      setValue("verificationCode", verificationCode);
    } else {
      setValue("resetCode", resetCode);
    }
    if (emailId) {
      setValue("emailId", emailId);
    }
  }, [router.query]);

  const [values, setValues] = useState({
    showNewPassword: false,
    showConfirmNewPassword: false,
  });

  // ** Hooks
  const theme = useTheme();
  const auth = useAuth();

  const handleClickShowNewPassword = () => {
    setValues({ ...values, showNewPassword: !values.showNewPassword });
  };

  const handleClickShowConfirmNewPassword = () => {
    setValues({
      ...values,
      showConfirmNewPassword: !values.showConfirmNewPassword,
    });
  };

  // ** Vars
  const hidden = useMediaQuery(theme.breakpoints.down("md"));

  const [openDialog, setOpenDialog] = useState(false);
  const [pageLoading, setPageLoading] = useState(false);
  const [alertConfig, setAlertConfig] = useState(null);
  const [formData, setFormData] = useState({});
  const [dialogMessage, setDialogMessage] = useState("");
  const [loading, setLoading] = useState(true);

  const handleCloseDialog = () => {
    if (
      !dialogMessage.includes(
        "New password cannot be the same as the old password"
      )
    ) {
      handleLoginData(formData);
    }
    setOpenDialog(false);
  };

  const handleSuccess = () => {
    setIsFailed(false);
    let message = "";
    if (verificationCode) {
      message = `
        <div>
          <h3>
            Password Reset Successful!
          </h3>
        </div>
      `;
    } else {
      if (userId) {
        message = `
        <div>
          <h3>
            Password Created Successfully.
          </h3>
        </div>
      `;
      } else {
        message = `
        <div>
          <h3>
            Password Reset Successful.
          </h3>
        </div>
      `;
      }
    }
    setDialogMessage(message);
    setPageLoading(true);
    setOpenDialog(true);
  };

  const handleFailure = (errorObj) => {
    const errorMessage =
      errorObj && errorObj.message
        ? errorObj.message
        : "Failed to Reset Password, Please try again later.";
    setIsFailed(true);
    const messageContent = `
      <div>
        <h3>
          ${errorMessage}
        </h3>
      </div>
    `;
    setOpenDialog(true);
    setPageLoading(false);
    setDialogMessage(messageContent);
  };

  const fetchIpAddress = async () => {
    try {
      const response = await fetch("https://api.ipify.org?format=json");
      const data = await response.json();

      return data.ip;
    } catch (error) {
      console.error("Error fetching IP address:", error);

      return null;
    }
  };

  const handleLoginData = async (formData) => {
    try {
      const ipAddress = await fetchIpAddress();
      auth.login(
        {
          email: formData?.emailId,
          password: formData?.newPassword,
          overrideExistingLogins: true,
          ipAddress,
        },
        (errorCallback) => {
          console.log(errorCallback);
          setPageLoading(false);
        },
        (successCallback) => {
          setLoading(true);
          setPageLoading(true);
          console.log("Login successful!");
        }
      );
    } catch (error) {
      console.log("Login failed:", error);
      setPageLoading(false);
    }
  };

  const onSubmit = async (data) => {
    try {
      const { verificationCode, newPassword, resetCode, emailId } = data;
      await schema.validate(data);
      setFormData(data);
      console.log("Data: ", setFormData(data));
      console.log("Submitted Data: ", data);

      if (verificationCode) {
        auth.emailVerify(
          { verificationCode, emailId, password: newPassword },
          (errMessage) => {
            handleFailure(errMessage);
          }
        );
      } else {
        auth.resetPassword(
          { resetCode, emailId, password: newPassword },
          (errMessage) => {
            handleFailure(errMessage);
          },
          handleSuccess
        );
      }
    } catch (error) {
      console.error("Validation error:", error);
    }
  };

  useEffect(() => {
    console.log("Data:USE EFFECT ", formData);
  }, [formData]);

  useEffect(() => {
    if (openDialog) {
      const timeoutId = setTimeout(() => {
        {
          !isFailed && handleCloseDialog();
        }
        // Close the dialog after 3 seconds
      }, 0);

      return () => clearTimeout(timeoutId); // Clear the timeout if the component unmounts or openDialog changes
    }
  }, [openDialog]);

  return (
    <>
      <Dialog
        open={openDialog}
        
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
        PaperProps={{
          sx: {
            p: (theme) => `${theme.spacing(2.5)} !important`,
            backgroundColor: (theme) => theme.palette.primary.background,
          },
        }}
        onClose={reason => {
          const isInvalidLinkMessage = dialogMessage.includes("The password reset link you've used is invalid or has expired. Please request a new reset link.");
          if (isFailed || reason === "backdropClick" || isInvalidLinkMessage) {
            return;
          }
          handleCloseDialog();
        }}      
      >
        <Box
          sx={{
            width: "100%",
            borderRadius: 1,
            textAlign: "center",
            border: (theme) => `1px solid ${theme.palette.divider}`,
            borderColor: "primary.main",
          }}
        >
          <>
            <DialogContent>
              <DialogContentText
                id="alert-dialog-description"
                color="primary.main"
              >
                <div dangerouslySetInnerHTML={{ __html: dialogMessage }} />
              </DialogContentText>
              {alertConfig && (
                <Alert
                  severity={alertConfig.severity}
                  style={{ margin: "20px" }}
                >
                  <AlertTitle>{alertConfig.severity}</AlertTitle>
                  {alertConfig.message}
                </Alert>
              )}
            </DialogContent>
            {!isFailed && (
              <>
                <Button style={{ margin: "10px auto", width: 100 }}>
                  {loading ? (
                    <CircularProgress color="inherit" size={24} />
                  ) : (
                    "Okay"
                  )}
                </Button>
              </>
            )}
            {isFailed && (
              <>
                <DialogActions
                  style={{ display: "flex", justifyContent: "flex-end" }}
                >
                  <Button
                    href="/forgot-password/?email="
                    style={{ width: "calc(50%)" }}
                  >
                    Forgot Password Page
                  </Button>
                </DialogActions>
              </>
            )}
          </>
        </Box>
      </Dialog>

      {pageLoading ? (
        <div
          style={{
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            height: "100vh",
          }}
        >
          <CircularProgress></CircularProgress>
        </div>
      ) : (
        <>
          <Box
            className="content-right"
            sx={{ backgroundColor: "background.paper" }}
          >
            <RightWrapper>
              <Box
                sx={{
                  p: [8, 14],
                  height: "100%",
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                }}
              >
                <Box sx={{ width: "100%", maxWidth: 400 }}>
                  <a href="/login">
                    <img
                      width={30}
                      height={28}
                      alt=""
                      src="/images/logo.webp"
                    ></img>
                  </a>

                  <Box sx={{ my: 6 }}>
                    <Typography
                      sx={{
                        mb: 1.5,
                        fontWeight: 500,
                        fontSize: "1.625rem",
                        lineHeight: 1.385,
                      }}
                    >
                      {/* {verificationCode
                    ? "Verify email and Set the password"
                    : "Reset Password? 🔒"} */}
                      {userId
                        ? "Create New Password? 🔒"
                        : "Reset Password? 🔒"}
                    </Typography>
                    <Typography sx={{ color: "text.secondary" }}>
                      Please set New Password for your{" "}
                      {`${themeConfig.templateName}`} account
                    </Typography>
                  </Box>
                  <form autoComplete="off" onSubmit={handleSubmit(onSubmit)}>
                    <FormControl fullWidth sx={{ mb: 3 }}>
                      <Controller
                        name="newPassword"
                        control={control}
                        render={({ field }) => (
                          <TextField
                            {...field}
                            label="New Password"
                            size="small"
                            type={values.showNewPassword ? "text" : "password"}
                            error={Boolean(errors.newPassword)}
                            // onChange={(e) => {
                            //   field.onChange(e);
                            //   trigger("confirmNewPassword"); // Trigger validation for confirmNewPassword field
                            // }}
                            helperText={errors.newPassword?.message}
                            onCopy={(e) => {
                              e.preventDefault();
                              e.stopPropagation();
                            }}
                            onCut={(e) => {
                              e.preventDefault();
                              e.stopPropagation();
                            }}
                            InputProps={{
                              endAdornment: (
                                <InputAdornment position="end">
                                  <IconButton
                                    aria-label="toggle password visibility"
                                    onClick={handleClickShowNewPassword}
                                    onMouseDown={(e) => e.preventDefault()}
                                    edge="end"
                                    disabled={!field.value}
                                  >
                                    <Icon
                                      icon={
                                        values.showNewPassword
                                          ? "tabler:eye"
                                          : "tabler:eye-off"
                                      }
                                    />
                                  </IconButton>
                                </InputAdornment>
                              ),
                            }}
                          />
                        )}
                      />
                    </FormControl>
                    <FormControl fullWidth sx={{ mb: 3 }}>
                      <Controller
                        name="confirmNewPassword"
                        control={control}
                        render={({ field }) => (
                          <TextField
                            {...field}
                            size="small"
                            label="Re-enter New Password"
                            type={
                              values.showConfirmNewPassword
                                ? "text"
                                : "password"
                            }
                            error={Boolean(errors.confirmNewPassword)}
                            helperText={errors.confirmNewPassword?.message}
                            onCopy={(e) => {
                              e.preventDefault();
                              e.stopPropagation();
                            }}
                            onCut={(e) => {
                              e.preventDefault();
                              e.stopPropagation();
                            }}
                            onPaste={(e) => {
                              e.preventDefault();
                              e.stopPropagation();
                            }}
                            InputProps={{
                              endAdornment: (
                                <InputAdornment position="end">
                                  <IconButton
                                    aria-label="toggle confirm password visibility"
                                    onClick={handleClickShowConfirmNewPassword}
                                    onMouseDown={(e) => e.preventDefault()}
                                    edge="end"
                                    disabled={!field.value}
                                  >
                                    <Icon
                                      icon={
                                        values.showConfirmNewPassword
                                          ? "tabler:eye"
                                          : "tabler:eye-off"
                                      }
                                    />
                                  </IconButton>
                                </InputAdornment>
                              ),
                            }}
                          />
                        )}
                      />
                    </FormControl>

                    <Box sx={{ my: 6, textAlign: "center" }}>
                      <Button
                        size="medium"
                        type="submit"
                        variant="contained"
                        //disabled={Object.keys(errors).length > 0}
                        sx={{ mr: 3 }}
                      >
                        {userId ? "Save" : "Save Changes"}
                      </Button>
                    </Box>
                    <Typography sx={{ fontWeight: 500 }}>
                      Password Requirements:
                    </Typography>
                    <Box
                      component="ul"
                      sx={{
                        pl: 5,
                        mb: 3,
                        "& li": { mb: 1.25, color: "text.secondary" },
                      }}
                    >
                      <li>Minimum 8 characters long - the more, the better</li>
                      <li>At least one lowercase & one uppercase character</li>
                      <li>
                        At least one number, symbol, or whitespace character
                      </li>
                    </Box>
                    <LinkStyled href="/login">
                      <Icon fontSize="1.25rem" icon="tabler:chevron-left" />
                      <span>Back to login</span>
                    </LinkStyled>
                  </form>
                </Box>
              </Box>
            </RightWrapper>
            {!hidden ? (
              <Box
                sx={{
                  flex: 1,
                  display: "flex",
                  position: "relative",
                  alignItems: "center",
                  borderRadius: "20px",
                  justifyContent: "center",
                  backgroundColor: "customColors.bodyBg",
                  margin: (theme) => theme.spacing(10),
                }}
              >
                <ForgotPasswordIllustration
                  alt="forgot-password-illustration"
                  src={`/images/pages/reset.webp`}
                />
                <FooterIllustrationsV2 />
              </Box>
            ) : null}
          </Box>
        </>
      )}
    </>
  );
};

ResetPassword.getLayout = (page) => <BlankLayout>{page}</BlankLayout>;
ResetPassword.guestGuard = true;

export default ResetPassword;
