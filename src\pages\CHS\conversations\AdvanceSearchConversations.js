import { useContext, useEffect, useState } from "react";
import Drawer from "@mui/material/Drawer";
import Button from "@mui/material/Button";
import MenuItem from "@mui/material/MenuItem";
import { styled } from "@mui/material/styles";
import IconButton from "@mui/material/IconButton";
import Typography from "@mui/material/Typography";
import Box from "@mui/material/Box";
import Tooltip from "@mui/material/Tooltip";
import CustomAvatar from "src/@core/components/mui/avatar";
import SearchIcon from "@mui/icons-material/Search";
import {
  Grid,
  FormControl,
  InputLabel,
  Select,
  FormHelperText,
  FormControlLabel,
  Checkbox,
  TextField,
  InputAdornment,
} from "@mui/material";
import { Controller, useForm } from "react-hook-form";
import PerfectScrollbar from "react-perfect-scrollbar";
import authConfig from "src/configs/auth";
import axios from "axios";
import { getAuthorizationHeaders, getUrl } from "src/helpers/utils";
import Icon from "src/@core/components/icon";
import { AuthContext } from "src/context/AuthContext";
import MultiSelectAutoComplete from "src/@core/components/custom-components/MultiSelectAutoComplete";

const Header = styled(Box)(({ theme }) => ({
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
  position: "relative",
  padding: theme.spacing(3),
  borderBottom: `1px solid ${theme.palette.divider}`,
}));

const AdvancedSearchConversations = (props) => {
  const {
    open,
    toggle,
    searchKeyword,
    searchData,
    setSearchKeyword,
    setSearchData,
    fetchUsers,
    page,
    pageSize,
    searchingState,
    setSearchingState
  } = props;
  const { getAllListValuesByListNameId } = useContext(AuthContext);

  const {
    control,
    setValue,
    reset,
    getValues,
    formState: { errors },
  } = useForm({
    defaultValues: {
      serviceTypeUUIDs: [],
      locationUUIDs: [],
      leadStatusUUIDs: [],
      leadPriorityUUIDs: [],
      assignedToUUIDs: [],
      portalsRegisteredUUIDs : [],
      searchKeywordByEmail: "",
      searchKeywordBySocietyName: "",
      searchKeywordByMobile: "",
      searchKeywordByComments:"",
      searchKeyword: "",// for search by name 
      isMicroSiteEmpanelled: null,
      isListingEmpanelled: null,
      searchingState: true,
      outcomeUUIDs: [], 
      targetUUIDs: [],
      typeUUIDs: [],
      shallRemindUUIDs:[],
    },
  });

  useEffect(() => {
    reset(searchData);
  }, [searchData, reset]);

  const handleCancel = () => {
    setSearchKeyword("");
    setSearchingState(false);
    reset({
      serviceTypeUUIDs: [],
      locationUUIDs: [],
      leadStatusUUIDs: [],
      leadPriorityUUIDs: [],
      assignedToUUIDs: [],
      portalsRegisteredUUIDs : [],
      isMicroSiteEmpanelled: null,
      isListingEmpanelled: null,
      searchKeywordByEmail: "",
      searchKeywordBySocietyName: "",
      searchKeywordByMobile: "",
      searchKeywordByComments:"",
      searchKeyword: "",

      outcomeUUIDs: [],
      targetUUIDs: [],
      typeUUIDs: [],
      shallRemindUUIDs:[],
    });
    setSearchData({});
    fetchUsers(page, pageSize, searchKeyword);
  };

  const handleApply =() =>{
    setSearchingState(true);    
      toggle();
  }
  const handleClose = () => {
    toggle();
  };

  const [listOfEmployees, setListOfEmployees] = useState([]);
  const [employeesOptions, setEmployeesOptions] = useState([]);

  useEffect(() => {
    axios({
      method: "get",
      url: getUrl(authConfig.selectDropdownNew) + "?selectionType=EMPLOYEES",
      headers: getAuthorizationHeaders(),
    })
      .then((res) => {
        setListOfEmployees(res.data.data);
      })
      .catch((err) => console.log("Employees error", err));
  }, []);

  useEffect(() => {
    if (listOfEmployees.length) {
      const data = listOfEmployees.map((entry) => ({
        value: entry.id,
        key: entry.name,
      }));
      setEmployeesOptions(data);
    }
  }, [listOfEmployees]);

  const updateFilters = (data) => {
    setSearchData(data);
    fetchUsers(page, pageSize, searchKeyword, data);
  };

  const [leadStatusData, setLeadStatusData] = useState(null);
  const [leadPriorityData, setLeadPriorityData] = useState(null);
  const [locationsData, setLocationsData] = useState(null);
  const [services, setServices] = useState(null);
  const [assignedTo, setDesignation] = useState(null);
  const [portalsRegistered, setPortalsRegisteredData] = useState(null);

  const [outcomeData, setOutcomeData] = useState(null);
  const [targetData, setTargetData] = useState(null);
  const [typeData, setTypeData] = useState(null);
  const[shallRemindData , setShallRemindData]= useState(null);

  const [matchedLocations, setMatchedLocations] = useState([]);
  const [matchedServices, setMatchedServices] = useState([]);
  const [matchedLeadStatus, setMatchedLeadStatus] = useState([]);
  const [matchedLeadPriority, setMatchedLeadPriority] = useState([]);
  const [matchedAssignedTo, setMatchedAssignedTo] = useState([]);
  const [matchedPortalsRegistered, setPortalsRegistered] = useState([]);
  useEffect(() => {
    if (!!authConfig) {
      getAllListValuesByListNameId(
        authConfig.allServicesListNameId,
        (data) =>
          setServices(
            data?.listValues.map((item) => ({
              value: item.id,
              key: item.listValue,
            }))
          ),
        handleError
      );
    }
    if (!!authConfig) {
      getAllListValuesByListNameId(
        authConfig.locationlistNameId,
        (data) =>
          setLocationsData(
            data?.listValues.map((item) => ({
              value: item.id,
              key: item.listValue,
            }))
          ),
        handleError
      );
    }
    if (!!authConfig) {
      getAllListValuesByListNameId(
        authConfig.leadStatusListNamesId,
        (data) =>
          setLeadStatusData(
            data?.listValues.map((item) => ({
              value: item.id,
              key: item.listValue,
            }))
          ),
        handleError
      );
    }
    if (!!authConfig) {
      getAllListValuesByListNameId(
        authConfig.leadPriorityListNamesId,
        (data) =>
          setLeadPriorityData(
            data?.listValues.map((item) => ({
              value: item.id,
              key: item.listValue,
            }))
          ),
        handleError
      );
    }

    if (!!authConfig) {
      getAllListValuesByListNameId(
        authConfig.designationId,
        (data) =>
          setDesignation(
            data?.listValues.map((item) => ({
              value: item.id,
              key: item.listValue,
            }))
          ),
        handleError
      );
    }
    if (!!authConfig) {
        getAllListValuesByListNameId(
          authConfig.outcomeConversation,
          (data) =>
            setOutcomeData(
              data?.listValues.map((item) => ({
                value: item.id,
                key: item.listValue,
              }))
            ),
          handleError
        );
      }
      if (!!authConfig) {
        getAllListValuesByListNameId(
          authConfig.target,
          (data) =>
            setTargetData(
              data?.listValues.map((item) => ({
                value: item.id,
                key: item.listValue,
              }))
            ),
          handleError
        );
      }
      // Fetch type data
      if (!!authConfig) {
        getAllListValuesByListNameId(
          authConfig.conversationType,
          (data) =>
            setTypeData(
              data?.listValues.map((item) => ({
                value: item.id,
                key: item.listValue,
              }))
            ),
          handleError
        );
      }
      if (!!authConfig) {
        getAllListValuesByListNameId(
          authConfig.shallRemind,
          (data) =>
            setShallRemindData(
              data?.listValues.map((item) => ({
                value: item.id,
                key: item.listValue,
              }))
            ),
          handleError
        );
      }
  }, [authConfig]);


  const [listValues, setListValues] = useState(null);

  useEffect(() => {
    const locationUUIDsKeys =
      searchData?.locationUUIDs?.map((item) => item.key) || [];
    setMatchedLocations(locationUUIDsKeys);
    const serviceTypeUUIDsKeys =
      searchData?.serviceTypeUUIDs?.map((item) => item.key) || [];
    setMatchedServices(serviceTypeUUIDsKeys);

    const leadStatusUUIDsKeys =
      searchData?.leadStatusUUIDs?.map((item) => item.key) || [];
    setMatchedLeadStatus(leadStatusUUIDsKeys);

    const leadPriorityUUIDsKeys =
      searchData?.leadPriorityUUIDs?.map((item) => item.key) || [];
    setMatchedLeadPriority(leadPriorityUUIDsKeys);

    const assignedToUUIDsKeys =
      searchData?.assignedToUUIDs?.map((item) => item.key) || [];
      setMatchedAssignedTo(assignedToUUIDsKeys);

      const portalsRegisteredUUIDsKeys =
      searchData?.portalsRegisteredUUIDs?.map((item) => item.key) || [];
      setPortalsRegistered(portalsRegisteredUUIDsKeys);

  }, [searchData, listValues]);
  const handleLeadStatusSuccess = (data) => setLeadStatusData(data?.listValues);
  const handleLeadPrioritySuccess = (data) =>setLeadPriorityData(data?.listValues);
  const handleLocationsSuccess = (data) => setLocationsData(data?.listValues);
  const handleServices = (data) => setServices(data?.listValues);
  const handleError = (error) => console.error("Error:", error);
  const handleDesignations = (data) => setDesignation(data?.listValues)
  const handlePortalRegistered = (dat) => setPortalsRegisteredData(dat?.listValues)

  return (
    <>
      <Tooltip title="Advanced Search">
        <CustomAvatar
          variant="rounded"
          sx={{ width: 36, height: 36, cursor: "pointer" }}
          onClick={toggle}
        >
          <Icon icon="tabler:filter" fontSize={27} />
        </CustomAvatar>
      </Tooltip>

     

      <Drawer
        open={open}
        anchor="right"
        variant="temporary"
        onClose={handleClose}
        ModalProps={{ keepMounted: true }}
        sx={{ "& .MuiDrawer-paper": { width: { xs: "90%", sm: 500 } } }} 
      >
        <Header
          sx={{
            position: "relative", 
            display: "flex",
            alignItems: "center", 
            justifyContent: "space-between", 
          }}
        >
          <Typography variant="h5" sx={{
            marginLeft: "12px",
          }}>Advanced Search</Typography>
          <Box sx={{ position: "absolute", top: "8px", right: "26px" }}>
            <IconButton
              size="small"
              onClick={handleClose}
              sx={{
                borderRadius: 1,
                color: "common.white",
                backgroundColor: "primary.main",
                "&:hover": {
                  backgroundColor: "#66BB6A",
                  transition: "background 0.5s ease, transform 0.5s ease",
                },
                mt: 2,
              }}
            >
              <Icon icon="tabler:x" fontSize="1rem" />
            </IconButton>
          </Box>
        </Header>
        <PerfectScrollbar options={{ wheelPropagation: false }}>
          <Box sx={{ p: (theme) => theme.spacing(4, 6) }}>
          <Grid container spacing={2} direction="column">

           <Grid item xs={6} sm={4}>
           <FormControl fullWidth>
           <Controller
                    name="mainSearch"
                    control={control}
                    render={({ field: { onChange } }) => (
                      <TextField
                        id="mainSearch"
                        placeholder="Search by Name"
                        label="Name"
                        variant="outlined"
                        InputLabelProps={{
                          shrink: true,
                        }} 
                        value={searchKeyword}
                        onChange={(e) => {
                          onChange(e.target.value);
                          setSearchKeyword(e.target.value);
                        }}
                        onKeyDown={(e) => {
                          if (e.key === "Enter") {
                            setSearchKeyword(searchKeyword);
                            fetchUsers(
                              page,
                              pageSize,
                              searchKeyword,
                              searchData
                            );
                          }
                        }}
                        sx={{
                          "& .MuiInputBase-root": {
                            height: "40px",
                          },
                          "& .MuiInputBase-input::placeholder": {
                            fontSize: "0.9rem",
                          },
                        }}
                        InputProps={{
                          endAdornment: (
                            <InputAdornment position="start">
                              <SearchIcon
                                sx={{
                                  cursor: "pointer",
                                  marginRight: "-15px",
                                }}
                                onClick={() => {
                                  setSearchKeyword(searchKeyword);
                                  fetchUsers(
                                    page,
                                    pageSize,
                                    searchKeyword,
                                    searchData
                                  );
                                }}
                              />{" "}
                            </InputAdornment>
                          ),
                        }}
                      />
                    )}
                  />
                </FormControl>
              </Grid>
              <Grid item xs={12} md={6}>
              <FormControl fullWidth>
                  <Controller
                    name="searchKeywordBySocietyName"
                    control={control}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        id="searchKeywordBySocietyName"
                        placeholder="Search by Society Name"
                        label="Society Name"
                        variant="outlined"
                        InputLabelProps={{
                          shrink: true,
                        }} 
                        value={field.value || ""}
                        onChange={(event) => {
                          updateFilters({
                            ...getValues(),
                            searchKeywordBySocietyName: event.target.value,
                          });
                        }}
                        onKeyDown={(e) => {
                          if (e.key === "Enter") {
                            // fetchServiceProviders(
                            //   page,
                            //   pageSize,
                            //   searchKeyword,
                            //   searchData
                            // );
                          }
                        }}
                        sx={{
                          "& .MuiInputBase-root": {
                            height: "40px",
                          },
                          "& .MuiInputBase-input::placeholder": {
                            fontSize: "0.9rem",
                          },
                        }}
                        InputProps={{
                          endAdornment: (
                            <InputAdornment position="start">
                              <SearchIcon
                                sx={{
                                  cursor: "pointer",
                                  marginRight: "-15px",
                                }}
                                onClick={(event) => {
                                  updateFilters({
                                    ...getValues(),
                                    searchKeywordBySocietyName: event.target.value,
                                  });
                                }}
                              />{" "}
                            </InputAdornment>
                          ),
                        }}
                      />
                    )}
                  />
                </FormControl>
              </Grid>
              <Grid item xs={12} md={6}>
              <FormControl fullWidth>
                  <Controller
                    name="searchKeywordByEmail"
                    control={control}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        id="searchKeywordByEmail"
                        placeholder="Search by Email"
                        label="Email"
                        variant="outlined"
                        InputLabelProps={{
                          shrink: true,
                        }} 
                        value={field.value || ""}
                        onChange={(event) => {
                          updateFilters({
                            ...getValues(),
                            searchKeywordByEmail: event.target.value,
                          });
                        }}
                        onKeyDown={(e) => {
                          if (e.key === "Enter") {
                            // fetchServiceProviders(
                            //   page,
                            //   pageSize,
                            //   searchKeyword,
                            //   searchData
                            // );
                          }
                        }}
                        sx={{
                          "& .MuiInputBase-root": {
                            height: "40px",
                          },
                          "& .MuiInputBase-input::placeholder": {
                            fontSize: "0.9rem",
                          },
                        }}
                        InputProps={{
                          endAdornment: (
                            <InputAdornment position="start">
                              <SearchIcon
                                sx={{
                                  cursor: "pointer",
                                  marginRight: "-15px",
                                }}
                                onClick={(event) => {
                                  updateFilters({
                                    ...getValues(),
                                    searchKeywordByEmail: event.target.value,
                                  });
                                }}
                              />{" "}
                            </InputAdornment>
                          ),
                        }}
                      />
                    )}
                  />
                </FormControl>
              </Grid>

              <Grid item xs={12} md={6}>
              <FormControl fullWidth>
                  <Controller
                    name="searchKeywordByComments"
                    control={control}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        id="searchKeywordByComments"
                        placeholder="Search by Comments"
                        label="Comments"
                        variant="outlined"
                        InputLabelProps={{
                          shrink: true,
                        }} 
                        value={field.value || ""}
                        onChange={(event) => {
                          updateFilters({
                            ...getValues(),
                            searchKeywordByComments: event.target.value,
                          });
                        }}
                        onKeyDown={(e) => {
                          if (e.key === "Enter") {
                            // fetchServiceProviders(
                            //   page,
                            //   pageSize,
                            //   searchKeyword,
                            //   searchData
                            // );
                          }
                        }}
                        sx={{
                          "& .MuiInputBase-root": {
                            height: "40px",
                          },
                          "& .MuiInputBase-input::placeholder": {
                            fontSize: "0.9rem",
                          },
                        }}
                        InputProps={{
                          endAdornment: (
                            <InputAdornment position="start">
                              <SearchIcon
                                sx={{
                                  cursor: "pointer",
                                  marginRight: "-15px",
                                }}
                                onClick={(event) => {
                                  updateFilters({
                                    ...getValues(),
                                    searchKeywordByComments: event.target.value,
                                  });
                                }}
                              />{" "}
                            </InputAdornment>
                          ),
                        }}
                      />
                    )}
                  />
                </FormControl>
              </Grid>
              <Grid item xs={12} md={6}>
              <FormControl fullWidth>
                  <Controller
                    name="searchKeywordByMobile"
                    control={control}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        id="searchKeywordByMobile"
                        placeholder="Search by Mobile No."
                        label="Mobile No."
                        variant="outlined"
                        InputLabelProps={{
                          shrink: true,
                        }} 
                        value={field.value || ""}
                        onChange={(event) => {
                          updateFilters({
                            ...getValues(),
                            searchKeywordByMobile: event.target.value,
                          });
                        }}
                        onKeyDown={(e) => {
                          if (e.key === "Enter") {
                            // fetchServiceProviders(
                            //   page,
                            //   pageSize,
                            //   searchKeyword,
                            //   searchData
                            // );
                          }
                        }}
                        sx={{
                          "& .MuiInputBase-root": {
                            height: "40px",
                          },
                          "& .MuiInputBase-input::placeholder": {
                            fontSize: "0.9rem",
                          },
                        }}
                        InputProps={{
                          endAdornment: (
                            <InputAdornment position="start">
                              <SearchIcon
                                sx={{
                                  cursor: "pointer",
                                  marginRight: "-15px",
                                }}
                                onClick={(event) => {
                                  updateFilters({
                                    ...getValues(),
                                    searchKeywordByMobile: event.target.value,
                                  });
                                }}
                              />{" "}
                            </InputAdornment>
                          ),
                        }}
                      />
                    )}
                  />
                </FormControl>
              </Grid>
              <Grid item xs={12} md={6}>
              <FormControl fullWidth>
                  <Controller
                    name="serviceTypeUUIDs"
                    control={control}
                    rules={{ required: false }}
                    render={({ field }) => (
                      <MultiSelectAutoComplete
                        id="serviceTypeUUIDs"
                        size="small"
                        label="Select Services"
                        nameArray={services || []}
                        value={field.value || []}
                        onChange={(event) => {
                          field.onChange(event.target.value);
                          updateFilters({
                            ...getValues(),
                            serviceTypeUUIDs: event.target.value,
                          });
                        }}
                        error={Boolean(errors.service)}
                      />
                    )}
                  />
                </FormControl>
                {errors.service && (
                  <FormHelperText sx={{ color: "error.main" }} id="validation-service">
                    {errors.service?.message}
                  </FormHelperText>
                )}
              </Grid>

              <Grid item xs={12}>
              <FormControl fullWidth>
                  <Controller
                    name="leadStatusUUIDs"
                    control={control}
                    rules={{ required: false }}
                    render={({ field }) => (
                      <MultiSelectAutoComplete
                        size="small"
                        id="leadStatus"
                        label="Lead Status"
                        nameArray={leadStatusData || []}
                        value={field.value || []}
                        onChange={(e) => {
                          field.onChange(e);
                          updateFilters({
                            ...getValues(),
                            leadStatusUUIDs: e.target.value,
                          });
                        }}
                        error={Boolean(errors.serivcesProvided)}
                      />
                    )}
                  />
                </FormControl>
                {errors.leadStatus && (
                  <FormHelperText
                    sx={{ color: "error.main" }}
                    id="validation-leadStatus"
                  >
                    {errors.leadStatus?.message}
                  </FormHelperText>
                )}
              </Grid>

              <Grid item xs={6} md={4}>
              <FormControl fullWidth>
                  <Controller
                    name="leadPriorityUUIDs"
                    control={control}
                    rules={{ required: false }}
                    render={({ field }) => (
                      <MultiSelectAutoComplete
                        id="lead-priority-select"
                        size="small"
                        label="Lead Priority"
                        nameArray={leadPriorityData || []}
                        value={field.value || []}
                        onChange={(e) => {
                          field.onChange(e);
                          updateFilters({
                            ...getValues(),
                            leadPriorityUUIDs: e.target.value,
                          });
                        }}
                        error={Boolean(errors.service)}
                      />
                    )}
                  />
                </FormControl>
                {errors.leadPriority && (
                  <FormHelperText
                    sx={{ color: "error.main" }}
                    id="validation-leadPriority"
                  >
                    {errors.leadPriority?.message}
                  </FormHelperText>
                )}
              </Grid>

              <Grid item xs={12} md={6}>
              <FormControl fullWidth>
                  <Controller
                    name="locationUUIDs"
                    control={control}
                    rules={{ required: false }}
                    render={({ field }) => (
                      <MultiSelectAutoComplete
                        id="locations-select"
                        size="small"
                        label="Location"
                        nameArray={locationsData || []}
                        value={field.value || []}
                        onChange={(event) => {
                          field.onChange(event.target.value);
                          updateFilters({
                            ...getValues(),
                            locationUUIDs: event.target.value,
                          });
                        }}
                        error={Boolean(errors.location)}
                      />
                    )}
                  />
                </FormControl>
                {errors.location && (
                  <FormHelperText
                    sx={{ color: "error.main" }}
                    id="validation-location"
                  >
                    {errors.location?.message}
                  </FormHelperText>
                )}
              </Grid>

             

              <Grid item xs={12} md={6}>
              <FormControl fullWidth>
                  <Controller
                    name="assignedToUUIDs"
                    control={control}
                    rules={{ required: false }}
                    render={({ field }) => (
                      <MultiSelectAutoComplete
                        id="assignedTo-select"
                        size="small"
                        label="Assigned To"
                        nameArray={assignedTo || []}
                        value={field.value || []}
                        onChange={(e) => {
                          field.onChange(e);
                          updateFilters({
                            ...getValues(),
                            assignedToUUIDs: e.target.value,
                          });
                        }}
                        error={Boolean(errors.service)}
                      />
                    )}
                  />
                </FormControl>
                {errors.leadPriority && (
                  <FormHelperText
                    sx={{ color: "error.main" }}
                    id="validation-leadPriority"
                  >
                    {errors.leadPriority?.message}
                  </FormHelperText>
                )}
              </Grid>

              <Grid item xs={12} md={6}>
              <FormControl fullWidth>
                  <Controller
                    name="outcomeUUIDs"
                    control={control}
                    rules={{ required: false }}
                    render={({ field }) => (
                      <MultiSelectAutoComplete
                        id="outcome-select"
                        size="small"
                        label="Outcome"
                        nameArray={outcomeData || []}
                        value={field.value || []}
                        onChange={(e) => {
                          field.onChange(e);
                          updateFilters({
                            ...getValues(),
                            outcomeUUIDs: e.target.value,
                          });
                        }}
                        error={Boolean(errors.outcomeUUIDs)}
                      />
                    )}
                  />
                </FormControl>
                {errors.outcomeUUIDs && (
                  <FormHelperText sx={{ color: "error.main" }} id="validation-outcome">
                    {errors.outcomeUUIDs?.message}
                  </FormHelperText>
                )}
              </Grid>

              {/* Target Multiselect */}
              <Grid item xs={12} md={6}>
              <FormControl fullWidth>
                  <Controller
                    name="targetUUIDs"
                    control={control}
                    rules={{ required: false }}
                    render={({ field }) => (
                      <MultiSelectAutoComplete
                        id="target-select"
                        size="small"
                        label="Target"
                        nameArray={targetData || []}
                        value={field.value || []}
                        onChange={(e) => {
                          field.onChange(e);
                          updateFilters({
                            ...getValues(),
                            targetUUIDs: e.target.value,
                          });
                        }}
                        error={Boolean(errors.targetUUIDs)}
                      />
                    )}
                  />
                </FormControl>
                {errors.targetUUIDs && (
                  <FormHelperText sx={{ color: "error.main" }} id="validation-target">
                    {errors.targetUUIDs?.message}
                  </FormHelperText>
                )}
              </Grid>

              {/* Type Multiselect */}
              <Grid item xs={12} md={6}>
              <FormControl fullWidth>
                  <Controller
                    name="typeUUIDs"
                    control={control}
                    rules={{ required: false }}
                    render={({ field }) => (
                      <MultiSelectAutoComplete
                        id="type-select"
                        size="small"
                        label="Type"
                        nameArray={typeData || []}
                        value={field.value || []}
                        onChange={(e) => {
                          field.onChange(e);
                          updateFilters({
                            ...getValues(),
                            typeUUIDs: e.target.value,
                          });
                        }}
                        error={Boolean(errors.typeUUIDs)}
                      />
                    )}
                  />
                </FormControl>
                {errors.typeUUIDs && (
                  <FormHelperText sx={{ color: "error.main" }} id="validation-type">
                    {errors.typeUUIDs?.message}
                  </FormHelperText>
                )}
              </Grid>

              <Grid item xs={12} md={6}>
              <FormControl fullWidth>
                  <Controller
                    name="shallRemindUUIDs"
                    control={control}
                    rules={{ required: false }}
                    render={({ field }) => (
                      <MultiSelectAutoComplete
                        id="shallRemind-select"
                        size="small"
                        label="Shall Remind"
                        nameArray={shallRemindData || []}
                        value={field.value || []}
                        onChange={(e) => {
                          field.onChange(e);
                          updateFilters({
                            ...getValues(),
                            shallRemindUUIDs: e.target.value,
                          });
                        }}
                        error={Boolean(errors.shallRemindUUIDs)}
                      />
                    )}
                  />
                </FormControl>
                {errors.shallRemindUUIDs && (
                  <FormHelperText sx={{ color: "error.main" }} id="validation-shallRemind">
                    {errors.shallRemindUUIDs?.message}
                  </FormHelperText>
                )}
              </Grid>
            </Grid>
          </Box>
        </PerfectScrollbar>
        <Box
          sx={{
            borderTop: (theme) => `1px solid ${theme.palette.divider}`,
            p: (theme) => theme.spacing(2),
            justifyContent: "flex-end",
            display: "flex",
            alignItems: "center",
          }}
        >
           <Button variant="tonal" sx={{ mr: 2 }} onClick={handleCancel}>
            Clear All
          </Button>
          <Button
          sx={{ mr: {
            xs: 4, 
            sm: 4, 
            md: 4, 
            lg: 4, 
            xl: 4,
          },} }
            variant="contained"
            //  onClick={handleSubmit(handleApply)}
            onClick={toggle}
          >
            Apply
          </Button>
        </Box>
      </Drawer>



    </>
  );
};

export default AdvancedSearchConversations;
