import {
  Autocomplete,
  Box,
  FormControl,
  Grid,
  InputAdornment,
  InputLabel,
  MenuItem,
  Select,
  TextField,
  Typography,
} from "@mui/material";
import axios from "axios";
import { useContext, useEffect, useRef, useState } from "react";
import { Controller, useForm } from "react-hook-form";
import GoogleMapsIconButton from "src/@core/components/custom-components/toastDisplay.js";
import authConfig from "src/configs/auth";
import { AuthContext } from "src/context/AuthContext";
import { getAuthorizationHeaders, getUrl } from "src/helpers/utils";

const SocietyDetails = ({ formData, onUpdate }) => {
  const {
    control,
    watch,
    reset,
    formState: { errors },
  } = useForm({
    defaultValues: formData?.societyDetails || {}, // Initialize form fields with existing data
  });

  const { listValues, user } = useContext(AuthContext);

  const [listOfLocations, setListOfLocations] = useState([]);
  const [locationsOptions, setLocationsOptions] = useState([]);
  const [locationId, setLocationId] = useState(
    formData?.societyDetails?.locationId || ""
  );
  const [zone, setZone] = useState(formData?.societyDetails?.zone || "");
  // Ref to track if the initial reset has been called
  const isInitialized = useRef(false);

  useEffect(() => {
    axios({
      method: "get",
      url: getUrl(authConfig.selectDropdownNew) + "?selectionType=LOCATIONS",
      headers: getAuthorizationHeaders(),
    })
      .then((res) => {
        setListOfLocations(res.data.data);
      })
      .catch((err) => console.log("Locations error", err));
  }, []);

  useEffect(() => {
    if (!!listOfLocations) {
      let data = [];
      listOfLocations?.map((entry) => {
        data.push({ value: entry.id, key: entry.name });
      });
      setLocationsOptions(data);
    }
  }, [listOfLocations]);

  // Reinitialize form when formData.societyDetails changes
  useEffect(() => {
    if (formData?.societyDetails && !isInitialized.current) {
      reset(formData.societyDetails); // Reset the form with initial values
      isInitialized.current = true; // Mark as initialized
    }
  }, [formData?.societyDetails, reset]);

  const handleLocationChange = (newValue) => {
    setLocationId(newValue || "");
    const selectedLocation = listOfLocations.find(
      (location) => location.id === newValue
    );
    if (selectedLocation) {
      const zone = selectedLocation.zoneId
        ? listValues.find((item) => item.id === selectedLocation.zoneId)?.name
        : "";
      setZone(zone || "");
    }
  };

  // Watch all fields for changes
  const watchedFields = watch();
  // Update formData on any change
  const previousWatchedFields = useRef();

  useEffect(() => {
    // Compare previous watched fields with current watched fields
    const hasWatchedFieldsChanged = JSON.stringify(previousWatchedFields.current) !== JSON.stringify(watchedFields);

    if (hasWatchedFieldsChanged || zone !== previousWatchedFields.current?.zone || locationId !== previousWatchedFields.current?.locationId) {
      onUpdate({
        ...watchedFields,
        zone: zone,
        locationId: locationId,
      });
      previousWatchedFields.current = watchedFields; // Update the ref with the latest value
    }
  }, [watchedFields, zone, locationId, onUpdate]);


  const validateLocationUrl = (value) => {
    const urlPattern =
      /^https:\/\/(www\.)?(maps\.app\.goo\.gl|google\.com\/maps)\/.+$/;
    if (!urlPattern.test(value)) {
      return "Please enter a valid Google Maps URL";
    }
    try {
      const url = new URL(value);
      if (url.hostname !== "maps.app.goo.gl" && url.hostname !== "google.com") {
        return "Please enter a valid Google Maps URL";
      }
    } catch (error) {
      return "Invalid URL format";
    }
    return true;
  };

  const renderTextField = (
    name,
    label,
    placeholder,
    rules,
    type = "text",
    multiline = false
  ) => (
    <FormControl fullWidth>
      <Controller
        name={name}
        control={control}
        rules={rules}
        render={({ field }) => (
          <TextField
            {...field}
            value={field.value || ""}
            label={label}
            placeholder={placeholder}
            InputLabelProps={{ shrink: true }}
            size="small"
            type={type}
            multiline={multiline}
            rows={multiline ? 2 : 1}
            error={Boolean(errors[name])}
            helperText={errors[name]?.message || ""}
          />
        )}
      />
    </FormControl>
  );

  return (
    <Box sx={{ pt: 3 }}>
      <Grid
        container
        spacing={3}
        sx={{
          width: "100%",
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          marginTop: "2rem",
        }}
      >
        {/* Society Name */}
        <Grid item xs={12} sm={6} md={4} lg={4} xl={4} sx={{ width: "100%" }}>
          {renderTextField("name", "Society Name", "Enter your Society name", {
            required: "Society name is required",
          })}
        </Grid>

        {/* Google Map Location */}
        <Grid item xs={12} sm={6} md={4} lg={4} xl={4} sx={{ width: "100%" }}>
          <FormControl fullWidth>
            <Controller
              name="googleMapLocation"
              control={control}
              rules={{
                required: "Google Maps URL is required",
                validate: validateLocationUrl,
              }}
              render={({ field }) => (
                <TextField
                  {...field}
                  value={field.value || ""}
                  label="Google location"
                  InputLabelProps={{ shrink: true }}
                  size="small"
                  placeholder="Click on icon to navigate & paste URL here"
                  error={Boolean(errors.googleMapLocation)}
                  helperText={errors.googleMapLocation?.message}
                  aria-describedby="validation-basic-googleMapLocation"
                  InputProps={{
                    endAdornment: (
                      <InputAdornment position="end">
                        <span
                          style={{ position: "absolute", right: 8, top: 0 }}
                        >
                          <GoogleMapsIconButton />
                        </span>
                      </InputAdornment>
                    ),
                  }}
                />
              )}
            />
          </FormControl>
        </Grid>

        {/* Plot CTS No */}
        <Grid item xs={12} sm={6} md={4} lg={4} xl={4} sx={{ width: "100%" }}>
          {renderTextField(
            "plotCTSNo",
            "Plot CTS No",
            "Enter your Plot CTS No"
          )}
        </Grid>

        {/* Team Member */}
        <Grid item xs={12} sm={6} md={4} lg={4} xl={4} sx={{ width: "100%" }}>
          {renderTextField("teamMember", "Team Member", "Enter team member")}
        </Grid>

        {/* Authority */}
        <Grid item xs={12} sm={6} md={4} lg={4} xl={4} sx={{ width: "100%" }}>
          {renderTextField("authority", "Authority", "Enter authority")}
        </Grid>

        <Grid item xs={12} sm={6} md={4} lg={4} xl={4} sx={{ width: "100%" }}>
          <Autocomplete
            id="locationId"
            options={locationsOptions} // Array of { value, key }
            value={
              locationsOptions.find((option) => option.value === locationId) ||
              null
            }
            onChange={(event, newValue) => {
              handleLocationChange(newValue?.value || null);
            }}
            getOptionLabel={(option) => option?.key || ""}
            isOptionEqualToValue={(option, value) =>
              option.value === value?.value
            }
            renderInput={(params) => (
              <TextField
                {...params}
                value={params.inputProps.value || ""}
                label="Select Society Location"
                variant="outlined"
                size="small"
              />
            )}
          />
        </Grid>

        <Grid
          container
          item
          xs={12}
          sm={6}
          md={4}
          lg={4}
          xl={4}
          sx={{ width: "100%", mt: 1, mb: 2, ml: 3 }}
          spacing={2}
        >
          <Grid item>
            <Typography className="data-field"> Society Zone:</Typography>
          </Grid>
          <Grid item>
            <Typography style={{ fontWeight: "bold" }}>{zone}</Typography>
          </Grid>
        </Grid>

        <Grid item xs={12} sm={6} md={4} lg={4} xl={4} sx={{ width: "100%" }}>
          <FormControl fullWidth>
            <Controller
              name="pinCode"
              control={control}
              rules={{
                required: "This field is required",
                minLength: {
                  value: 6,
                  message: "Enter a 6 digit pincode",
                },
                maxLength: {
                  value: 6,
                  message: "Pin Code cannot exceed 6 digits",
                },
                pattern: {
                  value: /^[0-9]+$/,
                  message: "Only numeric values are allowed",
                },
              }}
              render={({ field }) => (
                <TextField
                  {...field}
                  type="text"
                  inputMode="numeric"
                  size="small"
                  label="Pin Code"
                  InputLabelProps={{ shrink: true }}
                  onKeyDown={(event) => {
                    // Prevent entering more than 6 digits
                    if (
                      field.value?.length >= 6 &&
                      event.key !== "Backspace" &&
                      event.key !== "Delete" &&
                      event.key !== "ArrowLeft" &&
                      event.key !== "ArrowRight"
                    ) {
                      event.preventDefault();
                    }
                    // Allow only numeric keys, backspace, and delete
                    if (
                      !/^\d$/.test(event.key) &&
                      event.key !== "Backspace" &&
                      event.key !== "Delete" &&
                      event.key !== "ArrowLeft" &&
                      event.key !== "ArrowRight"
                    ) {
                      event.preventDefault();
                    }
                  }}
                  inputProps={{
                    maxLength: 6, // Prevents input beyond 6 characters at a native level
                  }}
                  placeholder="Enter Pin Code"
                  error={Boolean(errors.pinCode)}
                  helperText={errors.pinCode?.message}
                  aria-describedby="validation-basic-pin-code"
                />
              )}
            />
          </FormControl>
        </Grid>

        {/* Road Width */}
        <Grid item xs={12} sm={6} md={4} lg={4} xl={4} sx={{ width: "100%" }}>
          {renderTextField(
            "roadWidth",
            "Road Width",
            "Enter Road Width",
            undefined,
            "number"
          )}
        </Grid>
        {/* Gross Plot Area  */}
        <Grid item xs={12} sm={6} md={4} lg={4} xl={4} sx={{ width: "100%" }}>
          {renderTextField(
            "grossPlotArea",
            "Gross Plot Area",
            "Enter Gross Plot Area",
            undefined,
            "number"
          )}
        </Grid>

        {/* Enrolled Date */}
        <Grid item xs={12} sm={6} md={4} lg={4} xl={4} sx={{ width: "100%" }}>
          {renderTextField(
            "enrolledDate",
            "Enrolled Date",
            undefined,
            undefined,
            "date"
          )}
        </Grid>

        {/* Registered For */}
        <Grid item xs={12} sm={6} md={4} lg={4} xl={4} sx={{ width: "100%" }}>
          <FormControl fullWidth>
            <InputLabel id="registered-label">
              Are you interested in?
            </InputLabel>
            <Controller
              name="registeredFor"
              control={control}
              defaultValue={[]}
              render={({ field }) => (
                <Select
                  {...field}
                  size="small"
                  label="Are you interested in?"
                  multiple
                  renderValue={(selected) =>
                    selected
                      ?.map((value) => {
                        switch (value) {
                          case "RED":
                            return "Readiness";
                          case "PRO":
                            return "Professional Services";
                          default:
                            return value;
                        }
                      })
                      .join(", ")
                  }
                >
                  <MenuItem value="RED">Readiness</MenuItem>
                  <MenuItem value="PRO">Professional Services </MenuItem>
                </Select>
              )}
            />
          </FormControl>
        </Grid>

        {user.organisationCategory === "EMPLOYEE" && (
          <>
            {/* Requisition */}
            <Grid
              item
              xs={12}
              sm={6}
              md={4}
              lg={4}
              xl={4}
              sx={{ width: "100%" }}
            >
              <FormControl fullWidth>
                <InputLabel id="requisition-label">Requisition</InputLabel>
                <Controller
                  name="requisition"
                  control={control}
                  defaultValue=""
                  render={({ field }) => (
                    <Select
                      {...field}
                      value={field.value || ""}
                      size="small"
                      labelId="requisition-label"
                      InputLabelProps={{ shrink: true }}
                      label="Requisition"
                    >
                      <MenuItem value="yes">Yes</MenuItem>
                      <MenuItem value="no">No</MenuItem>
                    </Select>
                  )}
                />
              </FormControl>
            </Grid>

            {/* Readiness */}
            <Grid
              item
              xs={12}
              sm={6}
              md={4}
              lg={4}
              xl={4}
              sx={{ width: "100%" }}
            >
              <FormControl fullWidth>
                <InputLabel id="readiness-label">Readiness</InputLabel>
                <Controller
                  name="readiness"
                  control={control}
                  defaultValue="" // Default value to avoid out-of-range warning
                  render={({ field }) => (
                    <Select
                      {...field}
                      value={field.value || ""} // Fallback to default if `field.value` is null
                      size="small"
                      labelId="readiness-label"
                      InputLabelProps={{ shrink: true }}
                      label="Readiness"
                    >
                      <MenuItem value="yes">Yes</MenuItem>
                      <MenuItem value="no">No</MenuItem>
                    </Select>
                  )}
                />
              </FormControl>
            </Grid>
          </>
        )}

        {/* Address */}
        <Grid item xs={12} sm={6} md={4} lg={4} xl={4} sx={{ width: "100%" }}>
          {renderTextField(
            "societyAddress",
            "Address",
            "Enter address",
            {},
            "text",
            true
          )}
        </Grid>
      </Grid>
    </Box>
  );
};

export default SocietyDetails;
