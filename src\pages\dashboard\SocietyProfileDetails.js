import { Card } from "@mui/material";
import { useRBAC } from "src/pages/permission/RBACContext";
import { MENUS, PAGES, PERMISSIONS } from "src/constants";
import { useRouter } from "next/router";
import { useEffect, useState } from "react";
import NavTabsProfiles from "src/@core/components/custom-components/NavTabsProfiles";
import authConfig from "src/configs/auth";
import LeadSnapshots from "./LeadSnapshots";
import SocietyCHS from "./SocietyCHS";
import Email from "../storedEmails";

const SocietyProfileDetails = () => {
  const [activeTab, setActiveTab] = useState("");
  useEffect(() => {
    setActiveTab("onboarded");
  }, []);
  return (
    <>
      <NavTabsProfiles
        activeTab={activeTab}
        onTabChange={(newTab) => setActiveTab(newTab)}
        tabContent1={
          <>
            <SocietyCHS />
          </>
        }
        tabContent2={
          <>
            <LeadSnapshots category={"SOCIETY"} type={"CHS"} />
          </>
        }
        tabContent3 = {
          <>
            <Email role={authConfig.societyRoleId} />
          </>
        }
      />
    </>
  );
};

export default SocietyProfileDetails;
