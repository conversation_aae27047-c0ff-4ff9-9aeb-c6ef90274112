// ** MUI Imports
import Box from "@mui/material/Box";
import Switch from "@mui/material/Switch";
import Typography from "@mui/material/Typography";
import InputLabel from "@mui/material/InputLabel";
import useMediaQuery from "@mui/material/useMediaQuery";

// ** Icon Import
import Icon from "src/@core/components/icon";

// ** Custom Component Import
import CustomChip from "src/@core/components/mui/chip";

const PricingHeader = (props) => {
  // ** Props
  const {handleChange } = props;

  // ** Hook
  const hidden = useMediaQuery((theme) => theme.breakpoints.down("sm"));

  return (
    <Box sx={{ mb: [5, 10], textAlign: "center" }}>
      <Typography
        variant="h4"
        sx={{ fontSize: { sm: "1.7rem !important", xs: "1.5rem !important" } }}
      >
        Pricing Plans
      </Typography>

   
    </Box>
  );
};

export default PricingHeader;
