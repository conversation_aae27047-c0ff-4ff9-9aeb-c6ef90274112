// ** MUI Imports
import { Card } from '@mui/material'
import Grid from '@mui/material/Grid'
import { useTheme } from '@mui/material/styles'

// ** Custom Components Imports
import PlanDetails from 'src/@core/components/plan-details'

const PricingPlans = props => {
  // ** Props
  const { data } = props
  const theme = useTheme()


  return (
      <Card
        sx={{border: `1.2px solid ${theme.palette.divider}`}}
      >
    <Grid container spacing={6}>
  {data?.map(item => (
    <Grid item xs={12} sm={2} md={4} key={item.title ? item.title.toLowerCase() : ''}>
      <PlanDetails data={item} />
    </Grid>
  ))}
</Grid>
  </Card>
  )
}

export default PricingPlans
