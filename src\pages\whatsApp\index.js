import React, { useEffect } from "react";
import { <PERSON>, <PERSON>po<PERSON>, But<PERSON> } from "@mui/material";
import WhatsAppIcon from "@mui/icons-material/WhatsApp";
import { useRBAC } from "src/pages/permission/RBACContext";
import { MENUS, PAGES, PERMISSIONS } from "src/constants";
import { useRouter } from "next/router";

const OpenWhatsApp = () => {
  const openWhatsApp = () => {
    const whatsappURL = "https://wa.me/918928497829";
    const newWindow = window.open(whatsappURL, "noopener,noreferrer");

    if (!newWindow) {
      console.error(
        "Failed to open WhatsApp. It might be blocked by the browser."
      );
    }
  };
  const { canMenuPage, rbacRoles } = useRBAC();

  const router = useRouter();

  const canAccessWhatsApp = (requiredPermission) =>
    canMenuPage(MENUS.LEFT, PAGES.WHATS_APP, requiredPermission);

  useEffect(() => {
    if (rbacRoles != null && rbacRoles.length > 0) {
      if (!canAccessWhatsApp(PERMISSIONS.READ)) {
        router.push("/401");
      }
    }
  }, [rbacRoles]);

  if (canAccessWhatsApp(PERMISSIONS.READ)) {
    return (
      <Box
        sx={{
          height: "50vh",
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          padding: 2,
        }}
      >
        <Box
          sx={{
            p: 4,
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
            justifyContent: "center",
            textAlign: "center",
            borderRadius: "12px",
            mt: 3,
          }}
        >
          <Typography
            sx={{
              mb: 2,
              fontWeight: "bold",
              fontSize: "1rem",
            }}
          >
            Redirecting to WhatsApp...
          </Typography>
          <Typography
            sx={{
              mb: 3,
              fontSize: "1rem",
            }}
          >
            If you're not redirected automatically, click the button below.
          </Typography>
          <Button
            variant="contained"
            startIcon={<WhatsAppIcon />}
            onClick={openWhatsApp}
            size="small"
            sx={{
              backgroundColor: "primary.main",
              "&:hover": {
                backgroundColor: "primary.main",
              },
              textTransform: "none",
              fontSize: "1rem",
              padding: "10px 20px",
              borderRadius: "8px",
            }}
          >
            Open WhatsApp
          </Button>
        </Box>
      </Box>
    );
  } else {
    return null;
  }
};

export default OpenWhatsApp;
