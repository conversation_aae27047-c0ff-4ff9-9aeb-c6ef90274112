import {
  <PERSON>,
  Card,
  CardContent,
  DialogContentText,
  Divider,
  <PERSON>u,
  <PERSON>uItem,
  Tooltip,
} from "@mui/material";
import Button from "@mui/material/Button";
import Dialog from "@mui/material/Dialog";
import DialogActions from "@mui/material/DialogActions";
import DialogContent from "@mui/material/DialogContent";
import DialogTitle from "@mui/material/DialogTitle";
import FormControl from "@mui/material/FormControl";
import Grid from "@mui/material/Grid";
import IconButton from "@mui/material/IconButton";
import TextField from "@mui/material/TextField";
import Typography from "@mui/material/Typography";
import { DataGrid } from "@mui/x-data-grid";
import axios from "axios";
import { useContext, useEffect, useState } from "react";
import { Controller, useForm } from "react-hook-form";
import Icon from "src/@core/components/icon";
import CustomAvatar from "src/@core/components/mui/avatar";
import authConfig from "src/configs/auth";
import { AuthContext } from "src/context/AuthContext";
import { getAuthorizationHeaders, getUrl } from "src/helpers/utils";
import { useAuth } from "src/hooks/useAuth";
import CustomChip from "src/@core/components/mui/chip";
import NameTextField from "src/@core/components/custom-components/NameTextField";
import { format } from "date-fns";
import DeleteDialog from "./DeleteDialog";
import LookupNamesView from "./LookupNamesView";
import ActivateDialog from "./ActivateDialog";
import FallbackSpinner from "src/@core/components/spinner";

const userStatusObj = {
  true: "Active",
  false: "InActive",
};

const LookupNames = () => {
  const [namesList, setNamesList] = useState([]);
  const [openDeleteDialog, setOpenDeleteDialog] = useState(false);
  const [openActivateDialog, setOpenActivateDialog] = useState(false);
  const [openDialogContent, setOpenDialogContent] = useState(false);

  const [openDialog, setOpenDialog] = useState(false);
  const auth = useAuth();

  const { listNamesData, setListNamesData, listNamesDataDetails } =
    useContext(AuthContext);

  const [menu, setMenu] = useState(null);

  const handleCloseMenuItems = () => {
    setMenu(null);
  };

  const [loading, setLoading] = useState(true);

  const [searchKeyword, setSearchKeyword] = useState("");
  const [expanded, setExpanded] = useState(true);
  const rowsPerPageOptions = [10, 15, 20, 25, 50, 100];
  const [pageSize, setPageSize] = useState(rowsPerPageOptions[0]);
  const [page, setPage] = useState(1);
  const [currentRow, setCurrentRow] = useState();
  const [rowCount, setRowCount] = useState(0);
  const [dialogMessage, setDialogMessage] = useState("");
  const handleClose = () => setOpenDialogContent(false);

  const {
    register,
    handleSubmit,
    setError,
    clearErrors,
    control,
    reset,
    formState: { errors },
  } = useForm({
    defaultValues: {
      name: "",
    },
  });

  async function submit(data) {
    const fields = {
      name: data?.name.trim(),
    };
    try {
      const response = await auth.postListName(
        fields,
        handleFailure,
        handleSuccess
      );
      reset();
    } catch (error) {
      console.error("Service Data Creation failed:", error);
      handleFailure();
    }

    setOpenDialog(false);
    reset();
    fetchNames(page, pageSize, searchKeyword);
  }

  const handleSuccess = () => {
    const message = `
      <div> 
        <h3> LookUp Name added Successfully.</h3>
      </div>
    `;
    setDialogMessage(message);
    setOpenDialogContent(true);
  };

  const handleFailure = (err) => {
    let message;
     if(err.response.data.message.includes("existing entry in List Names")){
      message = `
      <div> 
        <h3> LookUp name already Exists.</h3>
      </div>
    `;
     }else{
      message = `
      <div> 
        <h3> Failed to Add LookUp name. Please try again later.</h3>
      </div>
    `;
     }
     
    setDialogMessage(message);
    setOpenDialogContent(true);
  };

  const handleButtonClick = () => {
    setOpenDialogContent(false);
  };

  const handleCloseDialog = () => {
    reset();
    setOpenDialog(false);
    fetchNames(page, pageSize, searchKeyword);
  };

  const fetchNames = async (currentPage, currentPageSize, searchKeyword) => {
    const url = getUrl(authConfig.listNamesGetAll);

    const headers = getAuthorizationHeaders();

    const data = {
      page: currentPage,
      pageSize: currentPageSize,
      searchKeyword: searchKeyword,
    };

    try {
      const response = await axios({
        method: "post",
        url: url,
        headers: headers,
        data: data,
      });

      if (response.data) {
        setNamesList(response.data?.listNamesResponse);
        setRowCount(response.data?.rowCount || 0);
      } else {
        console.error("Unexpected API response format:", response);
      }
    } catch (error) {
      console.error("Error fetching users:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleCloseDeleteDialog = () => {
    setOpenDeleteDialog(false);
    fetchNames(page, pageSize, searchKeyword);
  };

  const handleCloseActivateDialog = () => {
    setOpenActivateDialog(false);
    fetchNames(page, pageSize, searchKeyword);
  };

  useEffect(() => {
    fetchNames(page, pageSize, searchKeyword);
  }, [page, pageSize, searchKeyword]);

  const handlePageChange = (direction) => {
    if (direction === page) {
      setPage(page + 1);
    } else {
      setPage(page - 1);
    }
  };

  const handlePageSizeChange = (params) => {
    if (params) {
      setPageSize(params);
      setPage(1);
    }
  };

  const mapIsActiveToLabel = (isActive) => {
    return userStatusObj[isActive] || "Unknown";
  };

  const columns = [
    { field: "name", headerName: "Name", flex: 0.11, minWidth: 120 },
    {
      field: "isActive",
      headerName: "Status",
      flex: 0.11,
      minWidth: 100,
      renderCell: ({ row }) => {
        return (
          <CustomChip
            rounded={true}
            skin="light"
            size="small"
            label={mapIsActiveToLabel(row.isActive)}
            color={row.isActive === true ? "success" : "error"}
            sx={{ textTransform: "capitalize" }}
          />
        );
      },
    },
    {
      field: "createdOn",
      headerName: "Created on",
      flex: 0.11,
      minWidth: 100,
      renderCell: ({ row }) => {
        return format(new Date(row.createdOn), "dd-MM-yyyy");
      },
    },
    {
      field: "updatedOn",
      headerName: "Updated on",
      flex: 0.11,
      minWidth: 100,
      renderCell: ({ row }) => {
        return format(new Date(row.updatedOn), "dd-MM-yyyy");
      },
    },
    {
      flex: 0.05,
      field: "actions",
      headerName: "Actions",
      sortable: false,
      minWidth: 95,
      disableClickEventBubbling: true,
      renderCell: (params) => {
        const handleClickMenu = (event) => {
          event.stopPropagation();
          setMenu(event.currentTarget);
          const row = params.row;
          setCurrentRow(row);
          setListNamesData({
            ...listNamesData,
            id: row.id,
          });
        };

        const onClickViewProfile = () => {
          setOpenDialog(true);
          handleCloseMenuItems();
        };
        const onClickDeleteProfile = () => {
          setOpenDeleteDialog(true);
          handleCloseMenuItems();
        };

        const onClickActivateProfile = () => {
          setOpenActivateDialog(true);
          handleCloseMenuItems();
        };

        return (
          <div
            style={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
            }}
          >
              <CustomAvatar
                skin="light"
                variant="rounded"
                sx={{
                  mr: { xs: 2, lg: 4 },
                  width: 34,
                  height: 34,
                  cursor: "pointer",
                }}
                onClick={handleClickMenu}
              >
                <Icon icon="bi:three-dots-vertical" />
              </CustomAvatar>
            <Menu
              id="actions-menu"
              anchorEl={menu}
              keepMounted
              open={Boolean(menu)}
              onClose={handleCloseMenuItems}
            >
              <MenuItem onClick={onClickViewProfile}>Edit</MenuItem>
              {currentRow?.isActive ? (
                <MenuItem onClick={onClickDeleteProfile}>DeActivate</MenuItem>
              ) : (
                <MenuItem onClick={onClickActivateProfile}>Activate</MenuItem>
              )}
            </Menu>
          </div>
        );
      },
    },
  ];

  return (
    <>
      <Grid>
        <Box
          sx={{
            py: 3,
            px: 6,
            rowGap: 2,
            columnGap: 4,
            display: "flex",
            flexWrap: "wrap",
            alignItems: "center",
            justifyContent: "space-between",
          }}
        >
          <Grid container spacing={3} alignItems="center">
            <Grid item xs={12} sm={4} sx={{ textAlign: "flex-start" }}>
              <Typography variant="h6">{"List of LookUp Names"}</Typography>
            </Grid>

            <Grid item xs={12} sm={8}>
              <Grid
                container
                spacing={2}
                alignItems="center"
                justifyContent="flex-end"
              >
                <Grid item xs={12} sm={5}>
                  <FormControl fullWidth>
                    <Controller
                      name="name"
                      control={control}
                      rules={{ required: "lookup name is required" }}
                      render={({ field }) => (
                        <NameTextField
                          {...field}
                          size="small"
                          InputLabelProps={{ shrink: true }}
                          placeholder={"Enter lookup name to add"}
                          error={Boolean(errors.name)}
                          helperText={errors.name?.message}
                          aria-describedby="Section1-name"
                        />
                      )}
                    />
                  </FormControl>
                </Grid>
                <Grid item xs="auto" sm="auto">
                  <Button variant="contained" onClick={handleSubmit(submit)}>
                    Add LookUp Name
                  </Button>
                </Grid>
              </Grid>
            </Grid>
          </Grid>
        </Box>
        <Divider />
        <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth={"md"}>
          <DialogTitle
            sx={{
              position: "relative",
              borderBottom: (theme) => `1px solid ${theme.palette.divider}`,
              p: (theme) => `${theme.spacing(1.75, 4)} !important`,
              display: "flex",
              alignItems: "center",
              justifyContent: "start",
              fontSize: { xs: 12, md: 18 },
              height: "50px", // height
            }}
            textAlign={"center"}
          >
            Edit LookUp Name
            <Box sx={{ position: "absolute", top: "9px", right: "10px" }}>
              <IconButton
                size="small"
                onClick={handleCloseDialog}
                sx={{
                  // p: "0.438rem",
                  borderRadius: 1,
                  color: "common.white",
                  backgroundColor: "primary.main",
                  "&:hover": {
                    backgroundColor: "#66BB6A",
                    transition: "background 0.5s ease, transform 0.5s ease",
                  },
                }}
              >
                <Icon icon="tabler:x" fontSize="1rem" />
              </IconButton>
            </Box>
          </DialogTitle>
          <DialogContent
            sx={{
              position: "relative",
              pt: (theme) => `${theme.spacing(8)} !important`,
              pb: (theme) => `${theme.spacing(5)} !important`,
              px: (theme) => [`${theme.spacing(8)} !important`],
            }}
          >
            <LookupNamesView
              data={listNamesDataDetails}
              expanded={expanded}
              onCancel={handleCloseDialog}
              fetchUsers={fetchNames}
            />
          </DialogContent>
          <DialogActions
            sx={{
              justifyContent: "end",
              borderTop: (theme) => `1px solid ${theme.palette.divider}`,
              p: (theme) => `${theme.spacing(2.5)} !important`,
              height: "50px", // height
            }}
          >
            <Button
              display="flex"
              justifyContent="center"
              variant="outlined"
              color="primary"
              onClick={handleCloseDialog}
            >
              Close
            </Button>
          </DialogActions>
        </Dialog>

        <Divider />
        <CardContent>
          <div style={{ height: 430, width: "100%" }}>
            {loading ? (
              <Box
                display="flex"
                justifyContent="center"
                alignItems="center"
                height="60vh"
              >
                <FallbackSpinner />
              </Box>
            ) : (
              <DataGrid
                rows={namesList}
                columns={columns}
                pagination
                pageSize={pageSize}
                page={page - 1}
                rowsPerPageOptions={rowsPerPageOptions}
                rowCount={rowCount}
                paginationMode="server"
                onPageChange={handlePageChange}
                onPageSizeChange={handlePageSizeChange}
                rowHeight={38}
                headerHeight={38}
              />
            )}
          </div>
        </CardContent>

        <DeleteDialog
          open={openDeleteDialog}
          onClose={handleCloseDeleteDialog}
          data={currentRow}
        />
        <ActivateDialog
          open={openActivateDialog}
          onClose={handleCloseActivateDialog}
          data={currentRow}
        />
      </Grid>

      <Dialog
        open={openDialogContent}
        onClose={handleButtonClick}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
        PaperProps={{
          sx: {
            p: (theme) => `${theme.spacing(2.5)} !important`,
            backgroundColor: (theme) => theme.palette.primary.background,
          },
        }}
      >
        <Box
          sx={{
            width: "100%",
            borderRadius: 1,
            textAlign: "center",
            border: (theme) => `1px solid ${theme.palette.divider}`,
            borderColor: "primary.main",
          }}
        >
          <DialogContent>
            <DialogContentText
              id="alert-dialog-description"
              color="primary.main"
            >
              <div dangerouslySetInnerHTML={{ __html: dialogMessage }} />
            </DialogContentText>
          </DialogContent>
          <DialogActions>
            <Button
              variant="contained"
              onClick={handleButtonClick}
              sx={{ margin: "auto", width: 100 }}
            >
              Okay
            </Button>
          </DialogActions>
        </Box>
      </Dialog>
    </>
  );
};

export default LookupNames;
