// ** React Imports
import { useContext, useEffect, useRef, useState } from "react";

// ** Next Imports
import Link from "next/link";

// ** MUI Components
import {
  Card,
  CircularProgress,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  Divider,
  Grid,
} from "@mui/material";
import Box from "@mui/material/Box";
import Button from "@mui/material/Button";
import FormControl from "@mui/material/FormControl";
import MuiFormControlLabel from "@mui/material/FormControlLabel";
import IconButton from "@mui/material/IconButton";
import InputAdornment from "@mui/material/InputAdornment";
import { styled, useTheme } from "@mui/material/styles";
import TextField from "@mui/material/TextField";
import Typography from "@mui/material/Typography";
import useMediaQuery from "@mui/material/useMediaQuery";
import CustomChip from "src/@core/components/mui/chip";
import { AuthContext } from "src/context/AuthContext";
// ** Icon Imports
import Icon from "src/@core/components/icon";

// ** Third Party Imports
import { yupResolver } from "@hookform/resolvers/yup";
import { Controller, useForm } from "react-hook-form";
import * as yup from "yup";

// ** Hooks
import { Tab, Tabs } from "@mui/material";
import useBgColor from "src/@core/hooks/useBgColor";
import { useSettings } from "src/@core/hooks/useSettings";
import { useAuth } from "src/hooks/useAuth";

import axios from "axios";

import { getUrl } from "src/helpers/utils";

// ** Configs

// ** Config
import { useRouter } from "next/router";
import authConfig from "src/configs/auth";

// ** Layout Import
import BlankLayout from "src/@core/layouts/BlankLayout";

// ** Demo Imports
import SelectAutoComplete from "src/@core/components/custom-components/SelectAutoComplete";
import FooterIllustrationsV2 from "src/views/pages/auth/FooterIllustrationsV2";
import GoogleLoginDialog from "./GoogleLoginDialog";
import GoogleMapsIconButton from "src/@core/components/custom-components/toastDisplay";

const CustomTab = styled(Tab)(({ theme }) => ({
  [theme.breakpoints.up("lg")]: {
    fontSize: "0.8rem",
  },
  [theme.breakpoints.down("md")]: {
    fontSize: "0.7rem",
  },
  [theme.breakpoints.up("md")]: {
    fontSize: "0.7rem",
  },
  [theme.breakpoints.down("sm")]: {
    fontSize: "0.56rem",
  },
}));

const companyTypeOptions = [
  { value: "INDIVIDUAL", key: "Individual" },
  { value: "PROPRIETORSHIP", key: "Proprietorship" },
  { value: "PARTNERSHIP", key: "Partnership" },
  { value: "HUF", key: "HUF" },
  { value: "PRIVATE_LIMITED", key: "Private Limited" },
  { value: "PUBLIC_LIMITED", key: "Public Limited" },
  { value: "AOP", key: "AOP" },
];

function TabPanel(props) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ width: "100%" }}>
          {" "}
          {/* Ensure Box takes full width */}
          <Typography component="div">{children}</Typography>
        </Box>
      )}
    </div>
  );
}

function a11yProps(index) {
  return {
    id: `simple-tab-${index}`,
    "aria-controls": `simple-tabpanel-${index}`,
  };
}

// ** Styled Components
const LoginIllustration = styled("img")(({ theme }) => ({
  zIndex: 2,
  maxHeight: 250,
  margin: theme.spacing(10),
  [theme.breakpoints.down(1540)]: {
    maxHeight: 250,
  },
  [theme.breakpoints.down("lg")]: {
    maxHeight: 250,
  },
}));

const RightWrapper = styled(Box)(({ theme }) => ({
  [theme.breakpoints.up("lg")]: {
    minWidth: "40%",
  },
  [theme.breakpoints.down("lg")]: {
    minWidth: "40%",
  },
}));

const LinkStyled = styled(Link)(({ theme }) => ({
  fontSize: { xs: "0.7rem", lg: "0.9rem" },
  textDecoration: "none",
  color: theme.palette.primary.main,
}));

const FormControlLabel = styled(MuiFormControlLabel)(({ theme }) => ({
  "& .MuiFormControlLabel-label": {
    fontSize: "0.875rem",
    color: theme.palette.text.secondary,
  },
}));

const passwordValidationSchema = yup.object().shape({
  password: yup.string().required("Password is required").min(8).max(64),
});

const defaultValues = {
  firstName: "",
  lastName: "",
  email: "",
  mobileNumber: "",
  password: "",
};

const CustomTabDesktop = styled(Tabs)(({ theme }) => ({
  width: "100%",
  flex: 1,
  "& .MuiTabs-flexContainer": {
    justifyContent: "space-between",
    "& button": {
      minWidth: "unset",
      // width: "40%",
      padding: "0.5em 1em",
      [theme.breakpoints.down("xs")]: {
        width: "auto",
      },
    },
    "& button:nth-child(2)": {
      // width: "60%",
      padding: "0.5em 1em",
      [theme.breakpoints.down("xs")]: {
        width: "auto",
      },
    },
  },
}));

const CustomizedTabStyle = styled(Tabs)(({ theme }) => ({
  width: "100%",
  flex: 1,
  "& .MuiTabs-flexContainer": {
    justifyContent: "space-between",
    "& button": {
      minWidth: "unset",
      width: "50%",
      padding: "0.5em 1em",
      [theme.breakpoints.down("xs")]: {
        width: "auto",
      },
    },
  },
}));

//SignUpPage

const SignUpPage = () => {
  const [value, setValue] = useState(0);
  const router = useRouter();
  const isLargeScreen = useMediaQuery("(min-width:600px)");
  const isMobileView = useMediaQuery((theme) => theme.breakpoints.down("xs"));
  const [rememberMe, setRememberMe] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [email, setEmail] = useState("");
  const [openDialog, setOpenDialog] = useState(false);
  const [dialogMessage, setDialogMessage] = useState("");
  const [isFailed, setIsFailed] = useState(false);
  const [loading, setLoading] = useState(false);
  const [showPopup, setShowPopup] = useState(true);
  const [emailVerified, setEmailVerified] = useState(false);
  const [mobileVerified, setMobileVerified] = useState(false);
  const [otpOptions, setOtpOptions] = useState(false);
  const [otp, setOtp] = useState("");
  const [countdown, setCountdown] = useState(0);
  const [loadingValidate, setLoadingValidate] = useState(false);
  const [okayButtonClose, setOkayButtonClose] = useState(false);
  const { role } = router.query; // Extract role from query parameter
  const [selectedRole, setSelectedRole] = useState("");
  const [circular, setCircular] = useState(false);
  const [tempEmail, setTempEmail] = useState("");
  const [organisationName, setOrganisationName] = useState("");

  const [googleLogin, setGoogleLogin] = useState(false);

  const sortedCompanyTypeOptions = companyTypeOptions.sort((a, b) =>
    a.key.localeCompare(b.key)
  );

  const emailValidationSchema = yup.object().shape({
    email: yup.string().email("Invalid email").required("Email is required"),
    firstName: yup
      .string()
      .required("First Name is required")
      .max(50, "First Name cannot exceed 50 characters"),
    lastName: yup.string().max(50, "Last Name cannot exceed 50 characters"),
    organisationName: yup
      .string()
      .required(
        selectedRole === "SOCIETY"
          ? "Society Name is required"
          : "Company Name is required"
      ),
    password: yup
      .string()
      .nullable()
      .notRequired()
      .required("Password is required")
      .test(
        "password-validation",
        "Password must be at least 8 characters long, contain at least 1 uppercase letter, 1 special character, and 1 number.",
        (value) => {
          if (!value) return true;
          return /^(?=.*[A-Z])(?=.*[!@#$%^&*])(?=.*[0-9])(?=.*[a-zA-Z0-9]).{8,}$/.test(
            value
          );
        }
      ),
  });

  const currentValidationSchema =
    value === 0 ? emailValidationSchema : mobileValidationSchema;

  const {
    control,
    setError,
    handleSubmit,
    getValues,
    register,
    formState: { errors },
  } = useForm({
    defaultValues,
    resolver: yupResolver(currentValidationSchema),
  });

  // ** Hooks
  const auth = useAuth();
  const theme = useTheme();
  const bgColors = useBgColor();
  const { settings } = useSettings();
  const hidden = useMediaQuery(theme.breakpoints.down("md"));

  const [companyType, setCompanyType] = useState("");

  const url = authConfig.googleAuthUrl;

  const googleUrl = `${url}&role=${selectedRole}&organisationName=${organisationName}&companyType=${companyType}`;

  const facebookUrl = authConfig.facebookAuthUrl;

  const { fetchProfile } = useContext(AuthContext);

  // ** Vars
  const { skin } = settings;

  const CustomTabsComponent = isMobileView ? CustomTabMobile : CustomTabDesktop;

  useEffect(() => {
    Promise.resolve().then(() => {
      if (
        window.localStorage?.getItem("userData") &&
        window.localStorage?.getItem("accessToken")
      ) {
        window.location.href = "/dashboard";
      }
    });
  }, [router.route]);

  useEffect(() => {
    localStorage.removeItem("refreshToken");
  }, []);

  const handleTabChange = (event, newValue) => {
    setValue(newValue);
  };

  useEffect(() => {
    // If the role exists in the query parameter, set it in the state
    if (role) {
      setSelectedRole(role);
    }
  }, [role]);

  const handleCloseGoogleLogin = () => {
    if (!organisationName) {
      setError("name", {
        type: "manual",
        message: "This field is required",
      });
      return; // Do not proceed further if validation fails
    }
    router.push(googleUrl);
    setGoogleLogin(false);
  };

  async function verifyOtp() {
    setLoadingValidate(true);

    await axios({
      method: "POST",
      url: getUrl(authConfig.otpVerifyEndpoint3) + "?isMember=true",
      data: {
        otpCode: otp,
        email: email,
      },
    })
      .then((response) => {
        const returnedResponse = response.data?.passwordResetLink;
        const params = new URLSearchParams(returnedResponse.split("?")[1]);

        const extractedResetCode = params.get("resetCode");
        localStorage.setItem("resetCode", extractedResetCode);
        const message = `
          <div>
            <h3>
              Email Verified. </br>Please set your password now. 
            </h3>
            <h3>${email}</h3>
          </div>
        `;

        setDialogMessage(message);
        setOtpOptions(false);
        setEmailVerified(true);
        setOkayButtonClose(false);
        setLoadingValidate(false);
        setLoadingValidate(false);
      })
      .catch((error) => {
        console.error("Error verifying OTP:", error);

        const message = `
          <div>
          <h3>
          Failed to verify OTP. Please try again.
          </h3>
          </div>
        `;
        setDialogMessage(message);
        setLoadingValidate(false);
      });
  }

  const [loadingVerify, setLoadingVerify] = useState(false);

  const isapicalling = useRef(false);
  async function handleEmailVerification() {
    setLoadingVerify(true);
    if (isapicalling.current) {
      // API call is already in progress, return early
      return;
    }

    isapicalling.current = true;
    const data = getValues();
    let fields = {};
    if (selectedRole !== "SOCIETY") {
      data.companyType = companyType;
    }
    if (selectedRole === "SOCIETY") {
      data.googleLocation = data?.googleLocation;
    }

    // If the email tab is active, populate fields with email and password
    if (value === 0) {
      fields = {
        email: data?.email,
        firstName: data?.firstName,
        lastName: data?.lastName,
        organisationName: data?.organisationName,
        password: data?.password,
      };
      if (selectedRole === "SOCIETY") {
        fields.googleLocation = data?.googleLocation;
      }
      await emailValidationSchema.validate(fields);
    }
    // If the mobile number tab is active, populate fields with mobileNumber and password
    else if (value === 1) {
      fields = {
        mobileNumber: data?.mobileNumber,
        password: data?.password,
      };
    }

    setLoading(true);
    const ipAddress = await fetchIpAddress();

    data.ipAddress = ipAddress;
    data.role = selectedRole;
    // Implement the OTP verification logic here
    const response = await auth.signupV3(data, handleFailure, handleSuccess);

    setEmail(data.email);
    isapicalling.current = false;
  }

  const handleLoginData = async (email, password) => {
    try {
      const ipAddress = await fetchIpAddress();
      auth.login(
        {
          email: email,
          password: password,
          overrideExistingLogins: true,
          ipAddress,
        },
        (errorCallback) => {
          console.log(errorCallback);
        },
        (successCallback) => {
          setCircular(true);
          const message = `
          <div>
          <h3>
          Registration Successful.Redirecting to dashboard.
          </h3>
          </div>
        `;
          setDialogMessage(message);
          setOpenDialog(true);
        }
      );
    } catch (error) {
      console.log("Login failed:", error);
    }
  };

  async function handleSignUp() {
    const data = getValues();
    let fields = {}; // Initialize fields as an empty object

    // If the email tab is active, populate fields with email and password

    fields = {
      password: data?.password,
    };
    await passwordValidationSchema.validate(fields);

    let resetCode = localStorage.getItem("resetCode");
    auth.resetPassword(
      { resetCode: resetCode, emailId: data?.email, password: data?.password },
      (errMessage) => {
        handleSignUpFailure(errMessage);
      },
      () => {
        handleLoginData(data?.email, data?.password);
      }
    );
  }

  const handleMobileVerification = () => {
    // Implement the OTP verification logic here
    setMobileVerified(true);
  };

  const handleFailure = () => {
    setLoading(false);
    setShowPopup(false);
    setIsFailed(true);
    const message = ` 
    <div>
      <h3>Failed to register. Please try again later</h3>
    </div>
    `;
    setDialogMessage(message);
    setOpenDialog(true);
    setOkayButtonClose(true);
  };

  const handleSuccess = (data) => {
    setLoading(false);
    setLoadingVerify(false);
    if (data?.isVerified) {
      const message = ` 
    <div>
      <h3>User already exists with this email .Please try to login.Redirecting to login page in 5 seconds</h3>
    </div>
    `;
      setDialogMessage(message);
      setOpenDialog(true);
      setTimeout(() => {
        router.push("/login");
      }, 5000);
    } else {
      const email = getValues().email;
      const message = ` 
    <div>
      <h3>OTP sent to '${email}' email. Please check and verify.</h3>
    </div>
    `;
      setLoading(false);
      setDialogMessage(message);
      setOtpOptions(true);
      setCountdown(30);
      setOpenDialog(true);
    }
  };

  const handleSignUpFailure = () => {
    setLoading(false);
    setLoadingVerify(false);
    setShowPopup(false);
    setIsFailed(true);
    const message = ` 
    <div>
      <h3>Failed to register. Please try again later</h3>
    </div>
    `;
    setDialogMessage(message);
    setOpenDialog(true);
    // setOkayButtonClose(true)
  };

  const handleSignUpSuccess = () => {
    setLoading(false);
    setShowPopup(false);
    setIsFailed(true);
    const message = ` 
    <div>
      <h3>Sign Up Successful</h3>
    </div>
    `;
    setDialogMessage(message);
    setOpenDialog(true);
    setOkayButtonClose(true);
  };

  const handleClose = () => {
    setOpenDialog(false);
  };

  useEffect(() => {
    const emailPattern = /^[a-zA-Z0-9.]+@[a-zA-Z0-9.]+\.[a-zA-Z]{2,}$/;

    const handleEmail = async () => {
      axios({
        method: "POST",
        url: getUrl(authConfig.emailSaving) + "?flowTypeEnums=SIGN_UP",
        headers: {},
        data: {
          email: tempEmail,
        },
      })
        .then((res) => {
          console.log("Saved individual in DB");
        })
        .catch((err) => console.log("error", err));
    };

    if (tempEmail) {
      if (emailPattern.test(tempEmail)) {
        handleEmail();
      }
    }
  }, [tempEmail]);

  const StyledCard = styled(Card)(({ theme }) => ({
    marginTop: "3.5%",
    marginBottom: "3.5%",
    marginRight: "5%",
    marginLeft: "5%",
    width: "100%",

    [theme.breakpoints.up("md")]: {
      marginRight: "5%",
      marginLeft: "0.5%",
      borderRadius: "40px",
    },
    [theme.breakpoints.down("lg")]: {
      marginRight: "5%",
      marginLeft: "5%",
    },
    [theme.breakpoints.up("lg")]: {
      marginRight: "10%",
      marginLeft: "10%",
    },
    overflow: "hidden",
  }));
  const fetchIpAddress = async () => {
    try {
      const response = await fetch("https://api.ipify.org?format=json");
      const data = await response.json();

      return data.ip;
    } catch (error) {
      console.error("Error fetching IP address:", error);

      return null;
    }
  };

  const validateLocationUrl = (value) => {
    const urlPattern =
      /^https:\/\/(www\.)?(maps\.app\.goo\.gl|google\.com\/maps)\/.+$/;
    if (!urlPattern.test(value)) {
      return "Please enter a valid Google Maps URL";
    }
    try {
      const url = new URL(value);
      if (url.hostname !== "maps.app.goo.gl" && url.hostname !== "google.com") {
        return "Please enter a valid Google Maps URL";
      }
    } catch (error) {
      return "Invalid URL format";
    }
    return true;
  };
  const SocialLoginBlock = ({ imageUrl, text, url }) => {
    const [isHovered, setIsHovered] = useState(false);

    const handleMouseEnter = () => {
      setIsHovered(true);
    };

    const handleMouseLeave = () => {
      setIsHovered(false);
    };

    // Append action to URL

    return (
      <Grid item>
        <div
          onMouseEnter={handleMouseEnter}
          onMouseLeave={handleMouseLeave}
          style={{
            border: isHovered ? "1px solid #aaa" : "1px solid #ccc",
            padding: "5px",
            borderRadius: "5px",
            textAlign: "center",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            gap: "10px",
            cursor: "pointer",
            backgroundColor: isHovered ? "#f2f7f2" : "transparent",
          }}
        >
          <Box
            component="img"
            sx={{
              height: {
                xs: "1.3rem",
                lg: "1.5rem",
              },
            }}
            src={imageUrl}
            alt={text}
          />
          <LinkStyled
            href={url}
            sx={{ color: "black", fontSize: { xs: "0.75rem", lg: "0.8rem" } }}
          >
            {text}
          </LinkStyled>
        </div>
      </Grid>
    );
  };

  const SocialLogin = () => {
    const handleSocial = () => {
      setGoogleLogin(true);
    };

    return (
      <Grid item xs={12}>
        <div
          style={{
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            gap: "5px",
            padding: "8px 0",
            border: "1px solid rgb(204, 204, 204)",
            borderRadius: "5px",
            cursor: "pointer",
          }}
        >
          <img
            src="/images/google-logo.png"
            alt="Google Logo"
            style={{
              maxWidth: "100%",
              height: "auto",
              width: "20px",
              cursor: "pointer",
            }}
          />
          <Typography
            variant="body1"
            sx={{
              textDecoration: "none",
              color: "inherit",
              backgroundColor: "transparent",
              border: "none",
              fontSize: { xs: "12px", lg: "16px", sm: "14px" },
              cursor: "pointer",
            }}
            onClick={handleSocial}
          >
            Continue with Google
          </Typography>
        </div>
      </Grid>
    );
  };

  useEffect(() => {
    if (countdown > 0) {
      const timerId = setTimeout(() => setCountdown(countdown - 1), 1000);
      return () => clearTimeout(timerId);
    }
  }, [countdown]);

  const OTP_INFO = "OTP sent to";

  const onSubmit = async (data) => {
    let processedData = { ...data };

    let fields = {}; // Initialize fields as an empty object

    // If the email tab is active, populate fields with email and password
    if (value === 0) {
      fields = {
        email: data?.email,
        password: data?.password,
      };
    }
    // If the mobile number tab is active, populate fields with mobileNumber and password
    else if (value === 1) {
      fields = {
        mobileNumber: data?.mobileNumber,
        password: data?.password,
      };
    }

    const { email, password } = data;
    //await schema.validate(data);

    const ipAddress = await fetchIpAddress();
  };

  const imageSource =
    skin === "bordered"
      ? "auth-v2-login-illustration-bordered"
      : "auth-v2-login-illustration";
  if (
    !(
      window.localStorage?.getItem("userData") &&
      window.localStorage?.getItem("accessToken")
    )
  ) {
    return (
      <Box className="content-right" sx={{ backgroundColor: "#f2f7f2" }}>
        <Dialog
          fullWidth={dialogMessage.includes(OTP_INFO)}
          open={openDialog}
          aria-labelledby="alert-dialog-title"
          aria-describedby="alert-dialog-description"
          PaperProps={{
            sx: {
              backgroundColor: (theme) => theme.palette.primary.background,
              p: !dialogMessage.includes(OTP_INFO)
                ? (theme) => `${theme.spacing(2.5)} !important`
                : null,
            },
          }}
        >
          {dialogMessage.includes(OTP_INFO) && (
            <DialogTitle
              sx={{
                position: "relative",
                borderBottom: (theme) => `1px solid ${theme.palette.divider}`,
                p: (theme) => `${theme.spacing(1.75, 4.5)} !important`,
                display: "flex",
                alignItems: "center",
                justifyContent: { xs: "start" },
                height: "40px",
              }}
              textAlign="center"
            >
              <Box
                sx={{
                  fontSize: {
                    xs: 14,
                    sm: 15,
                    md: 17,
                    lg: 16,
                  },
                  fontWeight: 500,
                }}
              >
                Validate OTP
              </Box>
              <Box sx={{ position: "absolute", top: "1px", right: "10px" }}>
                <IconButton
                  size="small"
                  onClick={handleClose}
                  sx={{
                    position: "absolute",
                    top: "5px",
                    right: "6.5px",
                    // p: "0.438rem",
                    borderRadius: 1,
                    color: "common.white",
                    backgroundColor: "primary.main",
                    "&:hover": {
                      backgroundColor: "#66BB6A",
                      transition: "background 0.5s ease, transform 0.5s ease",
                    },
                  }}
                >
                  <Icon icon="tabler:x" fontSize="1rem" />
                </IconButton>
              </Box>
            </DialogTitle>
          )}
          {/* Dialog Content */}
          <Box
            sx={{
              width: "100%",
              borderRadius: 1,
              textAlign: "center",
              ...(!dialogMessage.includes(OTP_INFO) && {
                border: (theme) => `1px solid ${theme.palette.divider}`,
                borderColor: "primary.main",
              }),
            }}
          >
            <DialogContent
              sx={{
                position: "relative",
                p: (theme) => `${theme.spacing(4, 4)} !important`,
              }}
            >
              <DialogContentText
                id="alert-dialog-description"
                color="primary.main"
              >
                {/* Render Dialog Message */}
                <div dangerouslySetInnerHTML={{ __html: dialogMessage }} />
              </DialogContentText>
            </DialogContent>

            {/* Dialog Actions */}
            <DialogActions>
              {otpOptions && (
                <Grid container spacing={5}>
                  <Grid container justifyContent="center">
                    <Grid item xs={12} sm={12}>
                      <Controller
                        name="otp"
                        control={control}
                        rules={{
                          required: "OTP is required",
                          pattern: {
                            value: /^[0-9]{6}$/,
                            message: "OTP must be a 6-digit number",
                          },
                        }}
                        render={({ field }) => (
                          <TextField
                            {...field}
                            type="text"
                            inputProps={{
                              inputMode: "numeric",
                              pattern: "[0-9]*",
                            }}
                            placeholder="OTP"
                            value={otp}
                            onChange={(e) => {
                              const value = e.target.value;
                              if (/^\d*$/.test(value) && value.length <= 6) {
                                setOtp(value);
                              }
                            }}
                            InputLabelProps={{ shrink: true }}
                            error={Boolean(errors?.otp)}
                            helperText={errors?.otp?.message}
                            sx={{
                              borderRadius: "5px",
                              background: "white",
                            }}
                          />
                        )}
                      />
                    </Grid>
                  </Grid>
                  <Grid item xs={12} sm={12}>
                    <Grid container justifyContent="center">
                      <Button
                        variant="contained"
                        disabled={!otp || Boolean(errors?.otp)}
                        onClick={verifyOtp}
                        sx={{
                          marginBottom: "16px",
                          "&:disabled": { color: "primary.main" },
                        }}
                      >
                        {loadingValidate ? (
                          <CircularProgress color="inherit" size={24} />
                        ) : (
                          "VALIDATE OTP"
                        )}
                      </Button>
                      <Button
                        variant={countdown > 0 ? "outlined" : "contained"}
                        disabled={countdown > 0}
                        onClick={handleEmailVerification}
                        sx={{
                          marginLeft: "7px",
                          marginBottom: "16px",
                          "&.Mui-disabled": {
                            backgroundColor: "grey.100", // Set the background color for disabled state
                            color: "grey.500", // Set the text color for disabled state
                            borderColor: "grey.400", // Optional: Adjust border color if outlined
                          },
                        }}
                      >
                        {loading ? (
                          <CircularProgress color="inherit" size={24} />
                        ) : (
                          "RESEND OTP"
                        )}
                      </Button>
                    </Grid>
                    {countdown > 0 && (
                      <Typography
                        variant="body1"
                        sx={{
                          marginTop: "2px",
                          marginBottom: "10px",
                          color: "primary.main",
                        }}
                      >
                        Resend OTP in: {countdown}s
                      </Typography>
                    )}
                  </Grid>
                </Grid>
              )}
              {okayButtonClose && (
                <Button
                  onClick={handleClose}
                  style={{ margin: "10px auto", width: 100 }}
                >
                  Okay
                </Button>
              )}
              {emailVerified && (
                <Button
                  onClick={handleClose}
                  style={{ margin: "10px auto", width: 100 }}
                >
                  {circular ? (
                    <CircularProgress color="inherit" size={24} />
                  ) : (
                    "Okay"
                  )}
                </Button>
              )}
            </DialogActions>
          </Box>
        </Dialog>

        {!hidden ? (
          <Box
            sx={{
              flex: 1,
              display: "flex",
              position: "relative",
              alignItems: "center",
              borderRadius: "20px",
              justifyContent: "center",
              backgroundColor: "customColors.bodyBg",
              margin: (theme) => theme.spacing(10), // Default margin for other views
              [theme.breakpoints.up("md")]: {
                margin: (theme) => theme.spacing(-12), // Adjusted margin for medium screens
              },
              [theme.breakpoints.up("lg")]: {
                margin: (theme) => theme.spacing(10), // Adjusted margin for large screens
              },
              transform: "scale(1.0)",
              transition: "transform 0.2s ease-in-out",
            }}
          >
            <LoginIllustration
              alt="login-illustration"
              src={`/images/pages/Login-page-image.webp`}
              sx={{
                transform: {
                  xs: "none", // No transform for mobile
                  sm: "none", // No transform for tablet
                  md: "scale(0.8) translateX(25px)", // Transform for medium screens and up
                  lg: "scale(0.9)",
                  xl: "scale(1.0)",
                },
                transition: "transform 0.2s ease-in-out",
              }}
            />
            {/* <FooterIllustrationsV2 /> */}
          </Box>
        ) : null}

        <StyledCard
          sx={{
            transform: {
              xs: "none", // No transform for mobile
              sm: "none", // No transform for tablet
              md: "scale(0.7)", // Transform for medium screens
              lg: "scale(0.9)", // Transform for large screens
              xl: "scale(1.0)", // Transform for extra-large screens
            },
            transition: "transform 0.2s ease-in-out",
            borderRadius: "10px",
            "@media (max-width: 1024px) and (max-height: 600px)": {
              transform: "scale(0.8)", // Adjust transform for 1024x600 resolution
            },
            "@media (max-width: 1024px) and (max-height: 544px)": {
              transform: "scale(0.8)", // Adjust transform for 1024x544 resolution
            },
          }}
        >
          <RightWrapper
            sx={{
              transform: {
                xs: "scale(0.9)",
                sm: "none",
                md: "scale(0.8)",
                lg: "scale(0.9)",
                xl: "none",
                "@media (max-width: 1024px) and (max-height: 544px)": {
                  transform: "scale(0.95)", // Adjust transform for 1024x544 resolution
                },

                "@media (max-width: 1024px) and (max-height: 600px)": {
                  transform: "scale(0.9)", // Adjust transform for 1024x544 resolution
                },
              },
            }}
          >
            <Box
              sx={{
                p: [6, 10],
                height: "80%",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                transform: "scale(1.1)", // Zoom effect
                transition: "transform 0.3s ease-in-out",
              }}
            >
              <Box sx={{ width: "100%", maxWidth: 350 }}>
                {" "}
                {/* Increased width */}
                <Typography
                  variant="h6"
                  fontWeight="600"
                  sx={{
                    fontSize: { xs: "15px", lg: "20px", sm: "18px" },
                    mt: 2,
                    mb: 2,
                    display: "flex",
                    alignItems: "center",
                    // gap: "8px",
                  }}
                >
                  <a href={authConfig.guestURL + "home"}>
                    <img
                      src="/images/houzer-logo.webp"
                      alt="Houzer Logo"
                      style={{ width: "50px", height: "45px" }} // Increased size
                    />
                  </a>
                  Welcome to Houzer! 👋🏻
                </Typography>
                <Typography
                  sx={{
                    color: "text.secondary",
                    ml: { xs: 9, lg: 11 },
                    mb: { xs: 4, lg: 4 },
                    mt: { xs: -3, lg: -4 },
                    fontSize: { xs: "0.6rem", lg: "0.8rem" }, // Larger font size
                  }}
                >
                  {selectedRole !== "SOCIETY"
                    ? "Sign up as Service Provider"
                    : "Sign up as Society member"}
                </Typography>
                {/* Remaining Form Content */}
                <form
                  noValidate
                  autoComplete="off"
                  onSubmit={handleSubmit(onSubmit)}
                >
                  <Box sx={{ width: "100%", maxWidth: 450 }}>
                    {" "}
                    {/* Increased width */}
                    <SocialLogin />
                  </Box>

                  <Divider
                    sx={{
                      mt: 4,
                      fontSize: { xs: "0.8rem", lg: "1rem" }, // Larger font size
                    }}
                  >
                    or
                  </Divider>

                  {/* Remaining form layout */}
                  <Box
                    sx={{
                      display: "flex",
                      flexDirection: { xs: "column" }, // Stack on mobile, row on larger screens
                      alignItems: "center",
                      justifyContent: "space-between",
                      mb: 2,
                      mt: 2,
                    }}
                  ></Box>

                  <TabPanel value={value} index={0}>
                    <Grid
                      container
                      spacing={2}
                      direction="column"
                      sx={{ minHeight: "27vh" }}
                    >
                      {/* First Row */}
                      <Grid item xs={12}>
                        <FormControl fullWidth sx={{ width: "100%" }}>
                          <Grid container spacing={2}>
                            <Grid item xs={6}>
                              <Controller
                                name="firstName"
                                control={control}
                                render={({ field }) => (
                                  <TextField
                                    {...field}
                                    label="First Name*"
                                    placeholder="Enter First Name"
                                    fullWidth
                                    sx={{
                                      "& .MuiInputBase-input::placeholder": {
                                        fontSize: "1rem",
                                        "@media (max-width:600px)": {
                                          fontSize: "0.75rem",
                                        },
                                      },
                                      "& .MuiInputBase-input": {
                                        padding: "8px",
                                        fontSize: "1rem",
                                        "@media (max-width:600px)": {
                                          padding: "6px",
                                          fontSize: "0.75rem",
                                        },
                                      },
                                      "& .MuiInputLabel-root": {
                                        fontSize: "1.0rem",
                                        "@media (max-width:600px)": {
                                          fontSize: "0.75rem",
                                        },
                                      },
                                    }}
                                    size="small"
                                    onChange={(e) => {
                                      const newValue = e.target.value.replace(
                                        /\s/g,
                                        " "
                                      );
                                      field.onChange(newValue);
                                    }}
                                    id="auth-login-v2-first-name"
                                    error={Boolean(errors.firstName)}
                                    helperText={errors.firstName?.message}
                                    InputLabelProps={{ shrink: true }}
                                  />
                                )}
                              />
                            </Grid>
                            <Grid item xs={6}>
                              <Controller
                                name="lastName"
                                control={control}
                                render={({ field }) => (
                                  <TextField
                                    {...field}
                                    label="Last Name"
                                    placeholder="Enter Last Name"
                                    fullWidth
                                    sx={{
                                      "& .MuiInputBase-input::placeholder": {
                                        fontSize: "1rem",
                                        "@media (max-width:600px)": {
                                          fontSize: "0.75rem",
                                        },
                                      },
                                      "& .MuiInputBase-input": {
                                        padding: "8px",
                                        fontSize: "1rem",
                                        "@media (max-width:600px)": {
                                          padding: "6px",
                                          fontSize: "0.75rem",
                                        },
                                      },
                                      "& .MuiInputLabel-root": {
                                        fontSize: "1.0rem",
                                        "@media (max-width:600px)": {
                                          fontSize: "0.75rem",
                                        },
                                      },
                                    }}
                                    size="small"
                                    onChange={(e) => {
                                      const newValue = e.target.value.replace(
                                        /\s/g,
                                        " "
                                      );
                                      field.onChange(newValue);
                                    }}
                                    id="auth-login-v2-last-name"
                                    error={Boolean(errors.lastName)}
                                    helperText={errors.lastName?.message}
                                    InputLabelProps={{ shrink: true }}
                                  />
                                )}
                              />
                            </Grid>
                          </Grid>
                        </FormControl>
                      </Grid>

                      <Grid item xs={12}>
                        <Controller
                          name="organisationName"
                          control={control}
                          render={({ field }) => (
                            <TextField
                              {...field}
                              label={
                                selectedRole !== "SOCIETY"
                                  ? "Company Name*"
                                  : "Society Name*"
                              }
                              placeholder={
                                selectedRole !== "SOCIETY"
                                  ? "Enter Company Name"
                                  : "Enter Society Name"
                              }
                              fullWidth
                              sx={{
                                "& .MuiInputBase-input::placeholder": {
                                  fontSize: "1rem",
                                  "@media (max-width:600px)": {
                                    fontSize: "0.75rem",
                                  },
                                },
                                "& .MuiInputBase-input": {
                                  padding: "8px",
                                  fontSize: "1rem",
                                  "@media (max-width:600px)": {
                                    padding: "6px",
                                    fontSize: "0.75rem",
                                  },
                                },
                                "& .MuiInputLabel-root": {
                                  fontSize: "1.0rem",
                                  "@media (max-width:600px)": {
                                    fontSize: "0.75rem",
                                  },
                                },
                              }}
                              size="small"
                              onChange={(e) => {
                                const newValue = e.target.value.replace(
                                  /\s/g,
                                  " "
                                );
                                field.onChange(newValue);
                                // setFormData((prevData) => ({ ...prevData, firstName: newValue }));
                              }}
                              id="auth-login-v2-organisation-name"
                              error={Boolean(errors.organisationName)}
                              helperText={errors.organisationName?.message}
                              InputLabelProps={{ shrink: true }}
                            />
                          )}
                        />
                      </Grid>

                      {selectedRole === "SERVICE_PROVIDER" && (
                        <Grid item xs={12}>
                          <FormControl
                            fullWidth
                            error={Boolean(errors.companyType)}
                            sx={{
                              "& .MuiInputLabel-root": {
                                fontSize: "1rem", // Adjust font size
                                "@media (max-width:600px)": {
                                  fontSize: "0.9rem", // Optional: smaller font size for mobile
                                },
                              },
                            }}
                          >
                            <SelectAutoComplete
                              register={() =>
                                register("companyType", {
                                  required: "This field is required",
                                })
                              }
                              id={"companyType"}
                              label={"Registration Type*"}
                              name="companyType"
                              // nameArray={companyTypeOptions.sort((a, b) =>
                              //   a.localeCompare(b) // Sorting alphabetically
                              // )}
                              nameArray={sortedCompanyTypeOptions}
                              value={companyType}
                              onChange={(e) => setCompanyType(e.target.value)}
                              error={Boolean(errors.companyType)}
                              aria-describedby="validation-companyType"
                              sx={{
                                "& .MuiInputLabel-root": {
                                  fontSize: "1.2rem", // Larger font size for the label
                                  fontWeight: "bold", // Optional: Make the label bold
                                },
                              }}
                            />
                            {errors.companyType && (
                              <FormHelperText
                                sx={{ color: "error.main" }}
                                id="validation-companyType"
                              >
                                {errors.companyType.message}
                              </FormHelperText>
                            )}
                          </FormControl>
                        </Grid>
                      )}
                      {selectedRole === "SOCIETY" && (
                        <Grid item xs={12}>
                          <FormControl fullWidth>
                            <Controller
                              name="googleLocation"
                              control={control}
                              rules={{
                                validate: validateLocationUrl,
                              }}
                              render={({ field }) => (
                                <TextField
                                  {...field}
                                  label="Google location"
                                  InputLabelProps={{ shrink: true }}
                                  size="small"
                                  placeholder="Click on icon to navigate & paste URL here"
                                  error={Boolean(errors.googleLocation)}
                                  helperText={errors.googleLocation?.message}
                                  aria-describedby="validation-basic-googleLocation"
                                  InputProps={{
                                    endAdornment: (
                                      <InputAdornment position="end">
                                        <span
                                          style={{
                                            position: "absolute",
                                            right: 8,
                                            top: 0,
                                          }}
                                        >
                                          <GoogleMapsIconButton />
                                        </span>
                                      </InputAdornment>
                                    ),
                                  }}
                                />
                              )}
                            />
                          </FormControl>
                        </Grid>
                      )}
                      <Grid item xs={12}>
                        <FormControl sx={{ width: "100%" }}>
                          <Box display="flex" alignItems="center" width="100%">
                            <Controller
                              name="email"
                              control={control}
                              render={({ field }) => (
                                <TextField
                                  {...field}
                                  label="Email*"
                                  size="small"
                                  InputLabelProps={{ shrink: true }}
                                  helperText={errors.email?.message}
                                  error={Boolean(errors.email)}
                                  placeholder="<EMAIL>"
                                  fullWidth
                                  disabled={emailVerified}
                                  onBlur={(event) =>
                                    setTempEmail(event.target.value)
                                  }
                                  sx={{
                                    "& .MuiInputBase-input::placeholder": {
                                      fontSize: "1rem",
                                      "@media (max-width:600px)": {
                                        fontSize: "0.75rem",
                                      },
                                    },
                                    "& .MuiInputBase-input": {
                                      padding: "8px",
                                      fontSize: "1rem",
                                      "@media (max-width:600px)": {
                                        padding: "6px",
                                        fontSize: "0.75rem",
                                      },
                                    },
                                    "& .MuiInputLabel-root": {
                                      fontSize: "1.0rem",
                                      "@media (max-width:600px)": {
                                        fontSize: "0.75rem",
                                      },
                                    },
                                  }}
                                  InputProps={{
                                    endAdornment: (
                                      <InputAdornment position="end">
                                        {emailVerified ? (
                                          <CustomChip
                                            rounded={true}
                                            skin="light"
                                            size="small"
                                            label={
                                              <span
                                                style={{ fontSize: "inherit" }}
                                              >
                                                Verified
                                              </span>
                                            }
                                            color="success"
                                            sx={{
                                              textTransform: "capitalize",
                                              mr: { xs: 0, lg: 0 },
                                              fontSize: {
                                                xs: "0.7rem",
                                                lg: "0.8rem",
                                              },
                                            }}
                                          />
                                        ) : (
                                          <Box
                                            onClick={
                                              !emailVerified && !loadingVerify
                                                ? handleSubmit(
                                                    handleEmailVerification
                                                  )
                                                : undefined
                                            }
                                            sx={{
                                              border: "1px solid #f2f7f2",
                                              borderRadius: "4px",
                                              padding: "0 5px",
                                              cursor: "pointer",
                                              color: "blue",
                                            }}
                                          >
                                            <Typography
                                              variant="caption"
                                              sx={{
                                                fontSize: {
                                                  xs: "0.7rem",
                                                  lg: "0.8rem",
                                                },
                                                color: "blue",
                                              }}
                                            >
                                              {loadingVerify ? (
                                                <CircularProgress
                                                  color="inherit"
                                                  size={22}
                                                />
                                              ) : (
                                                "Verify"
                                              )}
                                            </Typography>
                                          </Box>
                                        )}
                                      </InputAdornment>
                                    ),
                                  }}
                                />
                              )}
                            />
                          </Box>
                        </FormControl>
                      </Grid>

                      <Grid item xs={12}>
                        <FormControl fullWidth sx={{ width: "100%" }}>
                          <Controller
                            name="password"
                            control={control}
                            render={({ field }) => (
                              <TextField
                                {...field}
                                label="Password*"
                                placeholder="Enter Password"
                                sx={{
                                  "& .MuiInputBase-input::placeholder": {
                                    fontSize: "1rem",
                                    "@media (max-width:600px)": {
                                      fontSize: "0.75rem",
                                    },
                                  },
                                  "& .MuiInputBase-input": {
                                    padding: "8px",
                                    fontSize: "1rem",
                                    "@media (max-width:600px)": {
                                      padding: "6px",
                                      fontSize: "0.75rem",
                                    },
                                  },
                                  "& .MuiInputLabel-root": {
                                    fontSize: "1.0rem",
                                    "@media (max-width:600px)": {
                                      fontSize: "0.75rem",
                                    },
                                  },
                                }}
                                size="small"
                                onChange={(e) => {
                                  const newValue = e.target.value.replace(
                                    /\s/g,
                                    ""
                                  );
                                  field.onChange(newValue);
                                }}
                                id="auth-login-v2-password"
                                error={Boolean(errors.password)}
                                fullWidth
                                helperText={errors.password?.message}
                                InputLabelProps={{ shrink: true }}
                                type={showPassword ? "text" : "password"}
                                InputProps={{
                                  endAdornment: (
                                    <InputAdornment position="end">
                                      <IconButton
                                        edge="end"
                                        onMouseDown={(e) => e.preventDefault()}
                                        onClick={() =>
                                          setShowPassword(!showPassword)
                                        }
                                        disabled={!field.value}
                                      >
                                        <Icon
                                          icon={
                                            showPassword
                                              ? "tabler:eye"
                                              : "tabler:eye-off"
                                          }
                                          fontSize={{ xs: 5, lg: 20 }}
                                        />
                                      </IconButton>
                                    </InputAdornment>
                                  ),
                                }}
                              />
                            )}
                          />
                        </FormControl>
                      </Grid>
                    </Grid>
                  </TabPanel>

                  <TabPanel value={value}>
                    <Grid
                      container
                      spacing={2}
                      direction="column"
                      sx={{ minHeight: "27vh" }}
                    >
                      {/* First Row */}
                      <Grid item xs={12}>
                        <FormControl sx={{ width: "100%" }}>
                          <Box display="flex" alignItems="center" width="100%">
                            <TextField
                              label=""
                              size="small"
                              disabled
                              value="+91"
                              sx={{
                                marginRight: 1,
                                maxWidth: "40px",
                                "& .MuiInputBase-input": {
                                  padding: "8px",
                                  fontSize: "1rem",
                                  "@media (max-width:600px)": {
                                    padding: "6px",
                                    fontSize: "0.75rem",
                                    maxWidth: "30px",
                                  },
                                },
                              }}
                            />
                            <TextField
                              label="Mobile Number*"
                              size="small"
                              InputLabelProps={{ shrink: true }}
                              helperText={errors.mobileNumber?.message}
                              error={Boolean(errors.mobileNumber)}
                              placeholder="999 999 99 99"
                              fullWidth
                              disabled={mobileVerified} // Disable field based on emailVerified state
                              sx={{
                                "& .MuiInputBase-input::placeholder": {
                                  fontSize: "1rem",
                                  "@media (max-width:780px)": {
                                    fontSize: "0.75rem",
                                  },
                                },
                                "& .MuiInputBase-input": {
                                  padding: "8px",
                                  fontSize: "1.0rem",
                                  "@media (max-width:600px)": {
                                    padding: "6px",
                                    fontSize: "0.75rem",
                                  },
                                },
                                "& .MuiInputLabel-root": {
                                  fontSize: "1.0rem",
                                  "@media (max-width:600px)": {
                                    fontSize: "0.75rem",
                                  },
                                },
                              }}
                              InputProps={{
                                endAdornment: (
                                  <InputAdornment position="end">
                                    {mobileVerified ? (
                                      <CustomChip
                                        rounded={true}
                                        skin="light"
                                        size="small"
                                        label={
                                          <span style={{ fontSize: "inherit" }}>
                                            Verified
                                          </span>
                                        }
                                        color="success"
                                        sx={{
                                          textTransform: "capitalize",
                                          fontSize: {
                                            xs: "0.7rem",
                                            lg: "0.8rem",
                                          }, // Responsive font size
                                        }}
                                      />
                                    ) : (
                                      <Box
                                        onClick={
                                          !mobileVerified
                                            ? handleMobileVerification
                                            : undefined
                                        }
                                        sx={{
                                          border: "1px solid #f2f7f2", // Grey border color
                                          borderRadius: "4px",
                                          padding: "0 5px",
                                          cursor: "pointer",
                                          color: "blue",
                                        }}
                                      >
                                        <Typography
                                          variant="caption"
                                          sx={{
                                            fontSize: {
                                              xs: "0.7rem",
                                              lg: "0.8rem",
                                            },
                                            color: "blue",
                                          }}
                                        >
                                          Verify
                                        </Typography>
                                      </Box>
                                    )}
                                  </InputAdornment>
                                ),
                              }}
                            />
                          </Box>
                        </FormControl>
                      </Grid>

                      {/* Second Row */}
                      <Grid item xs={12}>
                        {/* Content for the second row */}
                      </Grid>

                      {/* Third Row */}
                      <Grid item xs={12}>
                        {/* Content for the third row */}
                      </Grid>
                    </Grid>
                  </TabPanel>

                  <Box
                    sx={{
                      mb: 1.75,
                      display: "flex",
                      flexWrap: "wrap",
                      alignItems: "start",
                      justifyContent: "flex-start",
                    }}
                  >
                    {/* Ensure no leading spaces or new lines before 'By signing up' */}
                    <Typography
                      sx={{
                        fontSize: { xs: "0.6rem", lg: "0.7rem" },
                        margin: 0,
                        padding: 0,
                      }}
                    >
                      By signing up you agree to the{" "}
                      <Link
                        href="https://houzer.co.in/terms-conditions/"
                        passHref
                      >
                        <Typography
                          component="a"
                          sx={{
                            textDecoration: "none",
                            fontSize: { xs: "0.6rem", lg: "0.7rem" },
                            cursor: "pointer",
                            fontWeight: 600,
                            color: "primary.main", // Apply primary color
                            margin: 0, // Ensure no default margin
                            padding: 0, // Ensure no default padding
                          }}
                        >
                          T & C
                        </Typography>
                      </Link>{" "}
                      and{" "}
                      <Link
                        href="https://houzer.co.in/privacy-policy/"
                        passHref
                      >
                        <Typography
                          component="a"
                          sx={{
                            textDecoration: "none !important",
                            fontSize: { xs: "0.6rem", lg: "0.7rem" },
                            cursor: "pointer",
                            fontWeight: 600,
                            color: "primary.main", // Apply primary color
                            margin: 0, // Ensure no default margin
                            padding: 0, // Ensure no default padding
                          }}
                        >
                          Privacy Policy
                        </Typography>
                      </Link>
                    </Typography>
                  </Box>

                  <Button
                    fullWidth
                    size={isLargeScreen ? "medium" : "small"}
                    onClick={handleSubmit(handleSignUp)}
                    disabled={!emailVerified || !getValues().password}
                    variant="contained"
                    sx={{
                      width: "100%",
                      mb: { xs: 2, lg: 2 },
                      fontSize: { xs: "0.7rem", lg: "0.8rem" },
                    }}
                  >
                    Sign Up
                  </Button>

                  <Box
                    sx={{
                      display: "flex",
                      flexWrap: "wrap",
                      justifyContent: "center", // Align items to the start of the box
                      alignItems: "center", // Vertically center the items in the box
                    }}
                  >
                    <Typography
                      sx={{
                        color: "text.secondary",
                        mr: 2,
                        fontSize: { xs: "0.7rem", lg: "0.9rem" },
                      }}
                    >
                      Already registered?
                    </Typography>
                    <Typography
                      sx={{
                        color: "text.secondary",

                        fontSize: { xs: "0.7rem", lg: "0.9rem" },
                      }}
                    >
                      <LinkStyled
                        href="/login"
                        sx={{
                          fontWeight: 500,
                          fontSize: { xs: "0.7rem", lg: "0.9rem" },
                        }}
                      >
                        Login
                      </LinkStyled>
                    </Typography>
                  </Box>
                </form>
              </Box>
            </Box>
          </RightWrapper>
        </StyledCard>

        {/* <Dialog
          open={googleLogin}
          aria-labelledby="alert-dialog-title"
          aria-describedby="alert-dialog-description"
          PaperProps={{
            sx: {
              p: (theme) => `${theme.spacing(2.5)} !important`,
              backgroundColor: (theme) => theme.palette.primary.background,
            },
          }}
        >
          <Box
            sx={{
              width: "100%",
              borderRadius: 1,
              textAlign: "center",
              border: (theme) => `1px solid ${theme.palette.divider}`,
              borderColor: "primary.main",
            }}
          >
            <DialogContent>
              <Grid container spacing={5}>
               
                <Grid item xs={12}>
                  <Controller
                    name="name"
                    control={control}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        label={
                          selectedRole !== "SOCIETY"
                            ? "Company Name*"
                            : "Society Name*"
                        }
                        fullWidth
                        sx={{
                          borderRadius: "5px",
                          background: "white",
                          "& .MuiInputBase-input::placeholder": {
                            fontSize: "1rem",
                            "@media (max-width:600px)": {
                              fontSize: "0.75rem",
                            },
                          },
                          "& .MuiInputBase-input": {
                            padding: "8px",
                            fontSize: "1rem",
                            "@media (max-width:600px)": {
                              padding: "6px",
                              fontSize: "0.75rem",
                            },
                          },
                          "& .MuiInputLabel-root": {
                            fontSize: "0.9rem",
                            "@media (max-width:600px)": {
                              fontSize: "0.75rem",
                            },
                          },
                        }}
                        value={organisationName}
                        size="small"
                        onChange={(e) => {
                          const newValue = e.target.value.replace(/\s/g, " ");
                          field.onChange(newValue);
                          setOrganisationName(newValue)
                          // setFormData((prevData) => ({ ...prevData, firstName: newValue }));
                        }}
                        id="auth-login-v2-organisation-name"
                        error={Boolean(errors.name)}
                        helperText={errors.name?.message}
                        InputLabelProps={{ shrink: true }}
                      />
                    )}
                  />
                </Grid>

                {selectedRole === "SERVICE_PROVIDER" && (
                  <Grid item xs={12}>
                    <FormControl fullWidth error={Boolean(errors.companyType)}>
                      <SelectAutoComplete
                        register={() =>
                          register("companyType", {
                            required: "This field is required",
                          })
                        }
                        id={"companyType"}
                        label={"Registration Type"}
                        name="companyType"
                        nameArray={companyTypeOptions}
                        value={companyType}
                        onChange={(e) => setCompanyType(e.target.value)}
                        error={Boolean(errors.companyType)}
                        aria-describedby="validation-companyType"
                        sx={{
                          borderRadius: "5px",
                          background: "white",
                        }}
                      />
                      {errors.companyType && (
                        <FormHelperText
                          sx={{ color: "error.main" }}
                          id="validation-companyType"
                        >
                          {errors.companyType.message}
                        </FormHelperText>
                      )}
                    </FormControl>
                  </Grid>
                )}
              </Grid>
            </DialogContent>
            <DialogActions>
              <Button
                variant="contained"
                onClick={handleCloseGoogleLogin}
                style={{ margin: "10px auto", width: 100 }}
              >
                Continue
              </Button>
            </DialogActions>
          </Box>
        </Dialog> */}
        <GoogleLoginDialog
          open={googleLogin}
          close={() => setGoogleLogin(false)}
          handleClose={handleCloseGoogleLogin}
          control={control}
          selectedRole={selectedRole}
          organisationName={organisationName}
          setOrganisationName={setOrganisationName}
          errors={errors}
          register={register}
          companyTypeOptions={sortedCompanyTypeOptions}
          companyType={companyType}
          setCompanyType={setCompanyType}
        />
      </Box>
    );
  }
};
SignUpPage.getLayout = (page) => <BlankLayout>{page}</BlankLayout>;
SignUpPage.guestGuard = true;

export default SignUpPage;
