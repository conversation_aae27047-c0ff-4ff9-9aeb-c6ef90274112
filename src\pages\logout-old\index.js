// ** React Imports
import { useState } from "react";
import { useEffect } from "react";

// ** Next Imports
import Link from "next/link";

// ** MUI Components
import Alert from "@mui/material/Alert";
import Button from "@mui/material/Button";
import Divider from "@mui/material/Divider";
import Checkbox from "@mui/material/Checkbox";
import TextField from "@mui/material/TextField";
import Typography from "@mui/material/Typography";
import InputLabel from "@mui/material/InputLabel";
import IconButton from "@mui/material/IconButton";
import Box from "@mui/material/Box";
import {
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  CircularProgress,
  Menu,
  MenuItem,
  Card,
} from "@mui/material";
import FormControl from "@mui/material/FormControl";
import useMediaQuery from "@mui/material/useMediaQuery";
import OutlinedInput from "@mui/material/OutlinedInput";
import { styled, useTheme } from "@mui/material/styles";
import FormHelperText from "@mui/material/FormHelperText";
import InputAdornment from "@mui/material/InputAdornment";
import MuiFormControlLabel from "@mui/material/FormControlLabel";

// ** Icon Imports
import Icon from "src/@core/components/icon";

// ** Third Party Imports
import * as yup from "yup";
import { useForm, Controller } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";

// ** Hooks
import { useAuth } from "src/hooks/useAuth";
import useBgColor from "src/@core/hooks/useBgColor";
import { useSettings } from "src/@core/hooks/useSettings";

// ** Configs
import themeConfig from "src/configs/themeConfig";

// ** Config
import authConfig from "src/configs/auth";

// ** Layout Import
import BlankLayout from "src/@core/layouts/BlankLayout";

// ** Demo Imports
import FooterIllustrationsV2 from "src/views/pages/auth/FooterIllustrationsV2";

// ** Styled Components
const LoginIllustration = styled("img")(({ theme }) => ({
  zIndex: 2,
  maxHeight: 250,
  margin: theme.spacing(4),
  [theme.breakpoints.down(1540)]: {
    maxHeight: 380,
  },
  [theme.breakpoints.between("md", "lg")]: {
    maxHeight: 300, // Adjusted for small laptops
  },
  [theme.breakpoints.down("md")]: {
    maxHeight: 200, // Even smaller for smaller screens
    width: "100%", // Full width for small screens
  },
}));

const RightWrapper = styled(Box)(({ theme }) => ({
  [theme.breakpoints.up("lg")]: {
    minWidth: "40%", // Default for large screens
    display: "flex",
    flexDirection: "column",
  },
  [theme.breakpoints.between("md", "lg")]: {
    minWidth: "41%", // Adjust for small laptops
    display: "flex",
    flexDirection: "column",
  },
}));

const schema = yup.object().shape({
  email: yup.string().email().required("Email is Mandatory"),
  password: yup.string().required("Password is Mandatory").min(8),
});

const defaultValues = {
  password: "",
  email: "",
};

const LogoutPage = () => {
  useEffect(() => {
    localStorage.removeItem(authConfig.storageUserKeyName); //userData
    localStorage.removeItem(authConfig.storageTokenKeyName); //accessToken
    localStorage.removeItem("refreshToken");
  }, []);

  // ** Hooks
  const auth = useAuth();
  const theme = useTheme();
  const bgColors = useBgColor();
  const { settings } = useSettings();
  const hidden = useMediaQuery(theme.breakpoints.down("md"));

  // ** Vars
  const { skin } = settings;

  const {
    control,
    setError,
    handleSubmit,
    formState: { errors },
  } = useForm({
    defaultValues,
    resolver: yupResolver(schema),
  });

  const [anchorEl, setAnchorEl] = useState(null);

  const handleOpenMenu = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleCloseMenu = () => {
    setAnchorEl(null);
  };

  const handleMenuItemClick = (role) => {
    localStorage.setItem("role", role);
    setAnchorEl(null);
    window.location.href = "/register?role=" + role;
    handleCloseMenu();
  };

  return (
    <Box
      className="content-right"
      sx={{
        display: "flex",
        flexDirection: { xs: "column", md: "row" },
        justifyContent: {
          xs: "center", // Center content for extra-small screens
          sm: "center", // Center content for small screens
        },
        height: "100vh", // Set the height of the parent container to 100vh
        position: "relative",
        // backgroundColor: "background.paper",
      }}
    >
      <Card
        sx={{
          bottom: 0, // Move the card to the bottom of the parent container
          left: {
            xs: "0%", // For extra-small screens (mobile)
            sm: "0%", // For small screens (tablet)
            md: "3%", // For medium screens (default for larger tablets/desktops)
            lg: "6%", // For large screens (desktops)
            xl: "8%", // For extra-large screens
          }, // Move the card to the left of the parent container
          right: 0, // Move the card to the right of the parent container
          margin: "auto", // Center the card horizontally
          minWidth: "20%",
          display: "flex",
          flexDirection: "row",
          justifyContent: "center",
          alignItems: "center",
          height: "65vh", // Set the height of the card to auto
          position: "relative", // Position the card absolutely
          borderRadius: "20px", // Rounded corners
          transform: {
            md: "scale(1.0)", // Medium screens
            lg: "scale(1.2)", // Large screens
            xl: "scale(1.2)", // Extra-large screens
          },
          transition: "transform 0.3s ease-in-out", // Smooth transition
        }}
      >
        <RightWrapper>
          <Box
            sx={{
              p: [8, 14],
              height: "100%",
              display: "flex",
              flexDirection: "column",
              alignItems: "center",
              justifyContent: "center",
            }}
          >
            <Box sx={{ width: "100%", maxWidth: 400 }}>
              <a href={authConfig.guestURL + "home"}>
                <img
                  width={50}
                  height={46}
                  alt=""
                  src="/images/houzer-logo.webp"
                  style={{ marginLeft: "-10px" }}
                  className=""
                ></img>
              </a>
              <Box sx={{ mt: 1, mb: 8 }}>
                <Typography
                  sx={{
                    mb: 10,
                    fontWeight: 500,
                    fontSize: "1.625rem",
                    lineHeight: 1.385,
                  }}
                >
                  {` Successfully logged out 👋🏻`}
                </Typography>
                <Box sx={{ mb: 6 }}>
                  <Button
                    href="/login"
                    fullWidth
                    variant="contained"
                    color="primary"
                  >
                    Login
                  </Button>
                </Box>
                <Box>
                  <Button
                    onClick={handleOpenMenu}
                    variant="outlined"
                    color="primary"
                    fullWidth
                  >
                    Create New Account
                  </Button>
                  <Menu
                    anchorEl={anchorEl}
                    open={Boolean(anchorEl)}
                    onClose={handleCloseMenu}
                    anchorReference="anchorPosition"
                    anchorPosition={{
                      top: anchorEl
                        ? anchorEl.getBoundingClientRect().bottom
                        : 0,
                      left: anchorEl
                        ? anchorEl.getBoundingClientRect().left
                        : 0,
                    }}
                  >
                    <MenuItem
                      onClick={() => handleMenuItemClick("SERVICE_PROVIDER")}
                      style={{ padding: "3px 6px" }}
                    >
                      Service Provider
                    </MenuItem>
                    <MenuItem
                      onClick={() => handleMenuItemClick("SOCIETY")}
                      style={{ padding: "3px 6px" }}
                    >
                      Society Member
                    </MenuItem>
                  </Menu>
                </Box>
              </Box>
            </Box>
          </Box>
        </RightWrapper>
      </Card>
      {!hidden ? (
        <Box
          sx={{
            flex: 1,
            display: "flex",
            position: "relative",
            alignItems: "center",
            borderRadius: "20px",
            justifyContent: "center",
            margin: (theme) => theme.spacing(10),
          }}
        >
          <LoginIllustration
            alt="login-illustration"
            src={`/images/pages/Login-page-image.webp`}
            sx={{
              transform: {
                md: "scale(0.7) translateX(8%)",
                lg: "scale(0.7) translateX(18%)", 
                xl: "scale(0.8) translateX(18%)",
              },
              transition: "transform 0.3s ease-in-out", // Smooth transition
            }}
          />
          {/* <FooterIllustrationsV2 /> */}
        </Box>
      ) : null}
    </Box>
  );
};
LogoutPage.getLayout = (page) => <BlankLayout>{page}</BlankLayout>;
LogoutPage.guestGuard = true;

export default LogoutPage;
