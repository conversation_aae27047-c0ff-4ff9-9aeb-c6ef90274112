import { useContext, useEffect, useState } from "react";

// ** MUI Imports
import Box from "@mui/material/Box";
import Button from "@mui/material/Button";
import Card from "@mui/material/Card";
import CardContent from "@mui/material/CardContent";
import Collapse from "@mui/material/Collapse";
import IconButton from "@mui/material/IconButton";
import MuiStep from "@mui/material/Step";
import StepLabel from "@mui/material/StepLabel";
import Stepper from "@mui/material/Stepper";
import { styled } from "@mui/material/styles";
import Typography from "@mui/material/Typography";
import authConfig from "src/configs/auth";

// ** Icon Imports
import Icon from "src/@core/components/icon";

// ** Custom Components Imports

// ** Styled Components
import StepperWrapper from "src/@core/styles/mui/stepper";
// ** Util Import
import {
  Divider,
  FormControl,
  FormControlLabel,
  InputLabel,
  MenuItem,
  Radio,
  RadioGroup,
  Select,
  Switch,
  TextField,
} from "@mui/material";
import useMediaQuery from "@mui/material/useMediaQuery";
import PerfectScrollbar from "react-perfect-scrollbar";
import { hexToRGBA } from "src/@core/utils/hex-to-rgba";

import { Controller, useForm } from "react-hook-form";
import { useSettings } from "src/@core/hooks/useSettings";
import { AuthContext } from "src/context/AuthContext";

const StepperHeaderContainer = styled(CardContent)(({ theme }) => ({
  borderRight: `1px solid ${theme.palette.divider}`,
  overflowY: "auto",
  maxHeight: "75vh",
  [theme.breakpoints.down("lg")]: {
    borderRight: 0,
    borderBottom: `1px solid ${theme.palette.divider}`,
  },
}));

const Step = styled(MuiStep)(({ theme }) => ({
  "& .MuiStepLabel-root": {
    paddingTop: 0,
  },
  "&:not(:last-of-type) .MuiStepLabel-root": {
    paddingBottom: theme.spacing(2),
  },
  "&:last-of-type .MuiStepLabel-root": {
    paddingBottom: 0,
  },
  "& .MuiStepLabel-iconContainer": {
    display: "none",
  },
  "& .step-subtitle": {
    color: `${theme.palette.text.disabled} !important`,
  },
  "& + svg": {
    color: theme.palette.text.disabled,
  },
  "&.Mui-completed .step-title": {
    color: theme.palette.text.disabled,
  },
  "& .MuiStepLabel-label": {
    cursor: "pointer",
    fontWeight: "bold",
  },
}));

const Subsection = styled(Box)(({ theme, active }) => ({
  display: "flex",
  alignItems: "center",
  cursor: "pointer",
  padding: theme.spacing(1.5, 2),
  marginLeft: theme.spacing(4),
  borderRadius: theme.shape.borderRadius,
  transition: "background-color 0.3s ease, color 0.3s ease",
  fontSize: theme.typography.pxToRem(14),
  minWidth: "80%",
  ...(active
    ? {
        backgroundColor: theme.palette.primary.main,
        color: theme.palette.primary.dark,
        fontWeight: theme.typography.fontWeightBold,
        boxShadow: `0px 4px 8px ${hexToRGBA(theme.palette.primary.main, 0.2)}`,
        border: `0.5px solid ${theme.palette.primary.main}`,
      }
    : {
        "&:hover": {
          backgroundColor: theme.palette.action.hover,
          color: theme.palette.primary.dark,
          boxShadow: `0px 3px 6px ${hexToRGBA(
            theme.palette.primary.main,
            0.1
          )}`,
        },
      }),
}));

const BOQWizard = ({ data }) => {
  // ** States
  const [activeStep, setActiveStep] = useState(0);
  const [expandedSections, setExpandedSections] = useState({});
  const [activeSubSection, setActiveSubSection] = useState(0);
  const hidden = useMediaQuery((theme) => theme.breakpoints.down("lg"));

  const { settings } = useSettings();
  const { navCollapsed } = settings;

  const { listNames, listValues } = useContext(AuthContext);

  // Initial Expand of First Section and Subsection
  useEffect(() => {
    setExpandedSections({ 0: true }); // Expand the first section on initial render
  }, []);

  const {
    control,
    formState: { errors },
  } = useForm();

  const [steps, setSteps] = useState([]);

  useEffect(() => {
    const transformData = (data) => {
      return data?.map((section) => {
        return {
          title: section.id,
          subsections: section?.subSections?.map((subSection) => subSection.id),
          fields: section.fields?.map((field) => field.labelId),
        };
      });
    };

    const result = transformData(data);

    setSteps(result);
  }, [data]);

  // Handle Section Expand/Collapse
  const handleSectionToggle = (index) => {
    const step = steps[index];
    if (step.subsections && step.subsections.length > 0) {
      setExpandedSections({
        ...expandedSections,
        [index]: !expandedSections[index],
      });
    } else {
      setActiveStep(index);
      setActiveSubSection(null);
    }
  };

  // Handle Subsection Click
  const handleSubSectionClick = (sectionIndex, subSectionIndex) => {
    setActiveStep(sectionIndex);
    setActiveSubSection(subSectionIndex);
  };

  // Handle Stepper
  const handleNext = () => {
    const currentSubsections = steps[activeStep].subsections || [];
    if (
      activeSubSection !== null &&
      activeSubSection < currentSubsections.length - 1
    ) {
      // Move to next subsection in the current section
      setActiveSubSection(activeSubSection + 1);
    } else if (activeStep < steps.length - 1) {
      // Move to the first subsection of the next section
      setActiveStep(activeStep + 1);
      const nextStep = steps[activeStep + 1];
      if (nextStep.subsections && nextStep.subsections.length > 0) {
        setActiveSubSection(0);
      } else {
        setActiveSubSection(null);
      }
    }
  };

  const handlePrev = () => {
    if (activeSubSection !== null && activeSubSection > 0) {
      // Move to previous subsection in the current section
      setActiveSubSection(activeSubSection - 1);
    } else if (activeStep > 0) {
      // Move to last subsection of the previous section
      const prevStep = steps[activeStep - 1];
      setActiveStep(activeStep - 1);
      if (prevStep.subsections && prevStep.subsections.length > 0) {
        setActiveSubSection(prevStep.subsections.length - 1);
      } else {
        setActiveSubSection(null);
      }
    }
  };

  const getSubSectionContent = (sectionIndex, subSectionIndex) => {
    if (sectionIndex === null || subSectionIndex === null) return null;

    const section = data[sectionIndex];
    const subSection = section?.subSections?.[subSectionIndex];

    // Extract fields from both section and subsection
    const sectionFields = section?.fields || [];
    const subSectionFields = subSection?.fields || [];
    const combinedFields = [...sectionFields, ...subSectionFields];

    if (combinedFields?.length > 0) {
      // Sort fields by displayOrder
      const sortedFields = [...combinedFields].sort(
        (a, b) => a.displayOrder - b.displayOrder
      );

      return (
        <div>
          {sortedFields?.map((field) => {
            const fieldName =
              listNames?.find((item) => item.id === field.labelId)?.name || "-";
            return (
              <div key={field.labelId} style={{ margin: "16px" }}>
                {(() => {
                  switch (field.componentId) {
                    case authConfig.radioButtonComponentId:
                      return (
                        <FormControl component="fieldset">
                          <Controller
                            name={`selectedOptions.${field.labelId}`}
                            control={control}
                            render={({ field: controllerField }) => (
                              <RadioGroup
                                {...controllerField}
                                aria-label="option"
                                size="small"
                                value={controllerField.value}
                                onChange={(event) => {
                                  controllerField.onChange(event);
                                }}
                                row
                              >
                                {field.values?.map((value) => (
                                  <FormControlLabel
                                    key={value.key}
                                    value={value.value}
                                    control={<Radio disabled/>}
                                    label={value.key}
                                  />
                                ))}
                              </RadioGroup>
                            )}
                          />
                        </FormControl>
                      );
                    case authConfig.multiSelectComponentId:
                      return (
                        <FormControl style={{ width: "500px" }}>
                          <InputLabel id={field.labelId} style={{ zIndex: 0 }}>
                            {fieldName}
                          </InputLabel>
                          <Controller
                            name={`selectedOptions.${field.labelId}`}
                            control={control}
                            render={({ field: controllerField }) => (
                              <Select
                                multiple
                                labelId={field.labelId}
                                size="small"
                                label={fieldName}
                                value={controllerField.value || []}
                                onChange={(event) => {
                                  controllerField.onChange(event.target.value);
                                }}
                                 disabled
                                renderValue={(selected) => (
                                  <span>
                                    {selected
                                      ?.map(
                                        (selectedValue) =>
                                          field.values.find(
                                            (value) =>
                                              value.value === selectedValue
                                          )?.key
                                      )
                                      .join(", ")}
                                  </span>
                                )}
                              >
                                {field.values?.map((value) => (
                                  <MenuItem
                                    key={value.value}
                                    value={value.value}
                                  >
                                    {value.key}
                                  </MenuItem>
                                ))}
                              </Select>
                            )}
                          />
                        </FormControl>
                      );
                    case authConfig.singleSelectComponentId:
                      return (
                        <FormControl style={{ width: "500px" }}>
                          <InputLabel id={field.labelId} style={{ zIndex: 0 }}>
                            {fieldName}
                          </InputLabel>
                          <Controller
                            name={`selectedOptions.${field.labelId}`}
                            control={control}
                            render={({ field: controllerField }) => (
                              <Select
                                labelId={field.labelId}
                                size="small"
                                label={fieldName}
                                value={controllerField.value || ""}
                                onChange={(event) => {
                                  controllerField.onChange(event.target.value);
                                }}
                                disabled
                              >
                                {field.values?.map((value) => (
                                  <MenuItem
                                    key={value.value}
                                    value={value.value}
                                  >
                                    {value.key}
                                  </MenuItem>
                                ))}
                              </Select>
                            )}
                          />
                        </FormControl>
                      );
                    case authConfig.switchComponentId:
                      return (
                        <Controller
                          name={`selectedOptions.${field.labelId}`}
                          control={control}
                          render={({ field: controllerField }) => (
                            <FormControlLabel
                              control={
                                <Switch
                                  {...controllerField}
                                  checked={controllerField.value}
                                  size="small"
                                  onChange={(event) => {
                                    controllerField.onChange(event);
                                  }}
                                  disabled
                                  name={fieldName}
                                  inputProps={{
                                    "aria-label": field.labelId,
                                  }}
                                />
                              }
                              label="Yes"
                            />
                          )}
                        />
                      );
                    case authConfig.numberTextFieldComponentId:
                      return (
                        <FormControl style={{ width: "500px" }}>
                          <Controller
                            name={`selectedOptions.${field.labelId}`}
                            control={control}
                            render={({ field: controllerField }) => (
                              <TextField
                                {...controllerField}
                                label={fieldName}
                                size="small"
                                type="number"
                                variant="outlined"
                                onChange={(event) => {
                                  controllerField.onChange(event);
                                }}
                                disabled
                                fullWidth
                                inputProps={{
                                  "aria-label": field.labelId,
                                }}
                              />
                            )}
                          />
                        </FormControl>
                      );
                    case authConfig.textAreaComponentId:
                      return (
                        <FormControl style={{ width: "500px" }}>
                          <Controller
                            name={`selectedOptions.${field.labelId}`}
                            control={control}
                            render={({ field: controllerField }) => (
                              <TextField
                                {...controllerField}
                                label={fieldName}
                                size="small"
                                rows={3}
                                multiline
                                variant="outlined"
                                onChange={(event) => {
                                  controllerField.onChange(event);
                                }}
                                disabled
                                fullWidth
                                inputProps={{
                                  "aria-label": field.labelId,
                                }}
                              />
                            )}
                          />
                        </FormControl>
                      );
                    case authConfig.textFieldComponentId:
                      return (
                        <FormControl style={{ width: "500px" }}>
                          <Controller
                            name={`selectedOptions.${field.labelId}`}
                            control={control}
                            render={({ field: controllerField }) => (
                              <TextField
                                {...controllerField}
                                label={fieldName}
                                size="small"
                                variant="outlined"
                                onChange={(event) => {
                                  controllerField.onChange(event);
                                }} 
                                disabled
                                fullWidth
                                inputProps={{
                                  "aria-label": field.labelId,
                                }}
                              />
                            )}
                          />
                        </FormControl>
                      );
                    default:
                      return null;
                  }
                })()}
              </div>
            );
          })}
        </div>
      );
    } else {
      return (
        <Typography sx={{ textAlign: "center", mt: 4 }}>
          No fields available for this subsection.
        </Typography>
      );
    }
  };

  const renderContent = () => {
    const step = steps[activeStep];
    if (step?.subsections && activeSubSection !== null) {
      return getSubSectionContent(activeStep, activeSubSection);
    } else if (step?.fields) {
      return getSubSectionContent(activeStep);
    } else {
      return (
        <Typography sx={{ textAlign: "center", mt: 4 }}>
          Please select a subsection to view its details.
        </Typography>
      );
    }
  };

  const renderFooter = () => {
    const isLastStep = activeStep === steps.length - 1;
    const isFirstStep =
      activeStep === 0 && (activeSubSection === 0 || activeSubSection === null);

    return (
      <Box
        sx={{
          mt: 6,
          display: "flex",
          alignItems: "center",
          borderTop: (theme) => `1px solid ${theme.palette.divider}`,
          p: (theme) => `${theme.spacing(2.5)} !important`,
        }}
      >
        {!isFirstStep && (
          <Button
            color="secondary"
            variant="outlined"
            onClick={handlePrev}
            startIcon={<Icon icon="tabler:chevron-left" />}
          >
            Previous
          </Button>
        )}
        <Box sx={{ flex: 1 }} /> {/* Spacer */}
        {!isLastStep && (
          <Button
            variant="contained"
            //color={isLastStep ? "success" : "primary"}
            {...(!isLastStep
              ? { endIcon: <Icon icon="tabler:chevron-right" /> }
              : {})}
            onClick={() => (isLastStep ? null : handleNext())}
          >
            Next
          </Button>
        )}
      </Box>
    );
  };

  const renderStepLabel = (step, index, isExpanded) => {
    const isActiveSection = activeStep === index;

    return (
      <Box
        sx={{
          display: "flex",
          alignItems: "center",
          cursor: "pointer",
          padding: (theme) => theme.spacing(0.5),
          backgroundColor: isActiveSection ? "rgba(0, 0, 0, 0.04)" : "inherit",
          "&:hover": {
            backgroundColor: "rgba(0, 0, 0, 0.05)",
          },
          minHeight: "40px",
        }}
        onClick={() => handleSectionToggle(index)}
      >
        <Box sx={{ flex: 1 }}>
          <Typography
            className="step-title"
            sx={{
              fontWeight: "bold",
              ml: 2,
              color: isActiveSection ? "primary.main" : "text.primary",
            }}
          >
            {listValues.find((item) => item.id === step.title)?.name || "-"}
          </Typography>
        </Box>
        {step.subsections && step.subsections.length > 0 && (
          <IconButton onClick={() => handleSectionToggle(index)}>
            <Icon
              icon={isExpanded ? "tabler:chevron-up" : "tabler:chevron-down"}
            />
          </IconButton>
        )}
      </Box>
    );
  };

  const ScrollWrapper = ({ children, hidden }) => {
    if (hidden) {
      return (
        <Box sx={{ maxHeight: 330, overflowY: "auto", overflowX: "hidden" }}>
          {children}
        </Box>
      );
    } else {
      return (
        <PerfectScrollbar options={{ wheelPropagation: true }}>
          {children}
        </PerfectScrollbar>
      );
    }
  };

  return (
    <Card
      sx={{
        display: "flex",
        flexDirection: { xs: "column", md: "row" },
        height: "78vh",
      }}
    >
      <StepperHeaderContainer>
        <ScrollWrapper hidden={hidden}>
          <StepperWrapper sx={{ height: "100%" }}>
            <Stepper
              connector={<></>}
              orientation="vertical"
              activeStep={activeStep}
              sx={{ height: "100%", minWidth: "14rem" }}
            >
              {steps?.map((step, index) => {
                const isExpanded = !!expandedSections[index];

                return (
                  <Step key={index}>
                    <StepLabel>
                      {renderStepLabel(step, index, isExpanded)}
                    </StepLabel>

                    {step.subsections && step.subsections.length > 0 && (
                      <Collapse in={isExpanded}>
                        {step.subsections?.map((subsection, subIndex) => (
                          <Subsection
                            key={subIndex}
                            active={
                              activeStep === index &&
                              activeSubSection === subIndex
                            }
                            onClick={() =>
                              handleSubSectionClick(index, subIndex)
                            }
                          >
                            <Typography
                              sx={{
                                color:
                                  activeStep === index &&
                                  activeSubSection === subIndex
                                    ? "white"
                                    : "black",
                              }}
                            >
                              {listValues.find((item) => item.id === subsection)
                                ?.name || "-"}
                              {/* {subsection} */}
                            </Typography>
                          </Subsection>
                        ))}
                        <Divider sx={{ my: 2 }} />
                      </Collapse>
                    )}
                  </Step>
                );
              })}
            </Stepper>
          </StepperWrapper>
        </ScrollWrapper>
      </StepperHeaderContainer>
      <CardContent
        sx={{
          pt: (theme) => `${theme.spacing(6)} !important`,
          flex: "1 1 auto",
          display: "flex",
          flexDirection: "column",
          maxHeight: "80vh",
        }}
      >
        <Box sx={{ flexGrow: 1, overflowY: "auto" }}>{renderContent()}</Box>
        <Box sx={{ flexShrink: 0 }}>{renderFooter()}</Box>
      </CardContent>
    </Card>
  );
};

export default BOQWizard;
