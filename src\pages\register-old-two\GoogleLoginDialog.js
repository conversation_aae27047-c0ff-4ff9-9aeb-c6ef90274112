import React from "react";
import {
  <PERSON>alog,
  DialogContent,
  DialogActions,
  Button,
  Box,
  Grid,
  FormControl,
  TextField,
  FormHelperText,
  DialogTitle,
  IconButton,
} from "@mui/material";
import { Controller } from "react-hook-form";
import SelectAutoComplete from "src/@core/components/custom-components/SelectAutoComplete";
import { Icon } from "@iconify/react";

const GoogleLoginDialog = ({
  open,
  handleClose,
  control,
  selectedRole,
  organisationName,
  setOrganisationName,
  errors,
  register,
  companyTypeOptions,
  companyType,
  setCompanyType,
  close,
}) => {
  return (
    <Dialog
      open={open}
      width="md"
    >
      <DialogTitle
        sx={{
          position: "relative",
          borderBottom: (theme) => `1px solid ${theme.palette.divider}`,
          p: (theme) => `${theme.spacing(1.75, 4)} !important`,
          display: "flex",
          alignItems: "center",
          justifyContent: { xs: "start" },
          fontSize: { xs: 19, md: 20 },
          height: "50px",
        }}
        textAlign={"center"}
      >
        Google Sign-In
        <Box sx={{ position: "absolute", top: "9px", right: "12px" }}>
          <IconButton
            size="small"
            onClick={close}
            sx={{
              // p: "0.438rem",
              borderRadius: 1,
              color: "common.white",
              backgroundColor: "primary.main",
              "&:hover": {
                backgroundColor: "#66BB6A",
                transition: "background 0.5s ease, transform 0.5s ease",
              },
            }}
          >
            <Icon icon="tabler:x" fontSize="1rem" />
          </IconButton>
        </Box>
      </DialogTitle>
      <DialogContent
        sx={{
          position: "relative",
          pt: (theme) => `${theme.spacing(8)} !important`,
          pb: (theme) => `${theme.spacing(5)} !important`,
          px: (theme) => [`${theme.spacing(8)} !important`],
        }}
      >
        <Grid container spacing={5}>
          <Grid item xs={12}>
            <Controller
              name="name"
              control={control}
              render={({ field }) => (
                <TextField
                  {...field}
                  label={
                    selectedRole !== "SOCIETY"
                      ? "Company Name*"
                      : "Society Name*"
                  }
                  fullWidth
                  sx={{
                    borderRadius: "5px",
                    background: "white",
                    "& .MuiInputBase-input::placeholder": {
                      fontSize: "1rem",
                      "@media (max-width:600px)": {
                        fontSize: "0.75rem",
                      },
                    },
                    "& .MuiInputBase-input": {
                      padding: "8px",
                      fontSize: "1rem",
                      "@media (max-width:600px)": {
                        padding: "6px",
                        fontSize: "0.75rem",
                      },
                    },
                    "& .MuiInputLabel-root": {
                      fontSize: "0.9rem",
                      "@media (max-width:600px)": {
                        fontSize: "0.75rem",
                      },
                    },
                  }}
                  value={organisationName}
                  size="small"
                  onChange={(e) => {
                    const newValue = e.target.value.replace(/\s/g, " ");
                    field.onChange(newValue);
                    setOrganisationName(newValue);
                    // setFormData((prevData) => ({ ...prevData, firstName: newValue }));
                  }}
                  id="auth-login-v2-organisation-name"
                  error={Boolean(errors.name)}
                  helperText={errors.name?.message}
                  InputLabelProps={{ shrink: true }}
                />
              )}
            />
          </Grid>

          {selectedRole === "SERVICE_PROVIDER" && (
            <Grid item xs={12}>
              <FormControl fullWidth error={Boolean(errors.companyType)}>
                <SelectAutoComplete
                  register={() =>
                    register("companyType", {
                      required: "This field is required",
                    })
                  }
                  id={"companyType"}
                  label={"Registration Type"}
                  name="companyType"
                  nameArray={companyTypeOptions}
                  value={companyType}
                  onChange={(e) => setCompanyType(e.target.value)}
                  error={Boolean(errors.companyType)}
                  aria-describedby="validation-companyType"
                  sx={{
                    borderRadius: "5px",
                    background: "white",
                  }}
                />
                {errors.companyType && (
                  <FormHelperText
                    sx={{ color: "error.main" }}
                    id="validation-companyType"
                  >
                    {errors.companyType.message}
                  </FormHelperText>
                )}
              </FormControl>
            </Grid>
          )}
        </Grid>
      </DialogContent>
      <DialogActions
        sx={{
          justifyContent: "end",
          borderTop: (theme) => `1px solid ${theme.palette.divider}`,
          p: (theme) => `${theme.spacing(2.5)} !important`,
          height: "50px", // Set fixed height for footer
        }}
      >
        <Button variant="contained" onClick={handleClose}>
          Continue
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default GoogleLoginDialog;
