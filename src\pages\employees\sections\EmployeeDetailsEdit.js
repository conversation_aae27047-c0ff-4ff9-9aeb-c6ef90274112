// ** React Imports

// ** MUI Imports
import Button from "@mui/material/Button";
import FormControl from "@mui/material/FormControl";
import Grid from "@mui/material/Grid";
import TextField from "@mui/material/TextField";
import axios from "axios";
// ** Third Party Imports
import { getAuthorizationHeaders, getUrl } from "src/helpers/utils";

import { Controller, useForm } from "react-hook-form";

// ** Hooks
// ** Icon Imports
import { yupResolver } from "@hookform/resolvers/yup";
import {
  Card,
  Divider,
  FormControlLabel,
  InputLabel,
  MenuItem,
  Select,
  Switch,
  Typography,
} from "@mui/material";
import { Box } from "@mui/system";
import MobileNumberValidation from "src/@core/components/custom-components/MobileNumberValidation";
import NameTextField from "src/@core/components/custom-components/NameTextField";
import { useAuth } from "src/hooks/useAuth";
import EmployeeValidations from "./EmployeeValidations";
import { useContext, useEffect, useState } from "react";
import SelectCategory from "src/@core/components/custom-components/SelectCategory";

import authConfig from "src/configs/auth";
import { AuthContext } from "src/context/AuthContext";
import SelectAutoComplete from "src/@core/components/custom-components/SelectAutoComplete";
import SelectAutoCompleteRole from "src/@core/components/custom-components/SelectAutoCompleteRole";

const EmployeeDetailsEdit = ({
  onCancel,
  formData,
  fetchUsers,
  departmentOptions,
  rolesArray
}) => {
  const { getAllListValuesByListNameId, setEmployeeProfile, employeeProfile } =
    useContext(AuthContext);

  const [employeeId, setEmployeeId] = useState(formData?.reportingTo);
  const [departmentId, setDepartmentId] = useState(formData?.department);
  const [roleId, setRoleId] = useState(formData?.rolesId);
  const [selectedLocationId, setSelectedLocationId] = useState(
    formData?.employeeMetaData?.employeeData?.workLocation
  );
  const [locationsData, setLocationsData] = useState(null);
  const [selectedDesignationId, setSelectedDesignationId] = useState(
    formData?.designation
  );
  const [designationsData, setDesignationsData] = useState(null);
  const [roles, setRoles] = useState([]);
  const [fromDate, setFromDate] = useState(formData?.employeeMetaData?.employeeData?.fromDate);

  //Hooks
  const auth = useAuth();
  const fields = ["firstName", "lastName", "mobileNumber"];

  const {
    register,
    handleSubmit,
    setError,
    clearErrors,
    control,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(EmployeeValidations(fields)),
    mode: "onChange",
  });

  async function submit(data) {
    const fields = {
      id: formData.id,
      firstName: data.firstName,
      lastName: data.lastName,
      mobileNumber: data.mobileNumber,
      email: data.email,
      designation: selectedDesignationId,
      reportingTo: employeeId,
      department: departmentId,
      rolesId: roleId,
      employeeMetaData: {
        employeeData: {
          workLocation: selectedLocationId,
          fromDate: fromDate,
          toDate: data?.toDate,
        },
      },
    };

    const response = await auth.patchEmployee(fields, () => {
      console.error(" Employee Details failed");
    });

    if (response) {
      setEmployeeProfile({
        ...employeeProfile,
        id: formData.id,
      });
    }

    const currentPage = 1;
    const currentPageSize = 10;

    fetchUsers(currentPage, currentPageSize);
    onCancel();
  }

  const [listOfEmployees, setListOfEmployees] = useState([]);
  const [employeesOptions, setEmployeesOptions] = useState([]);

  useEffect(() => {
    // Fetch employees //to do as we fetching same data in edit. define in auth context to use in both files
    axios({
      method: "get",
      url: getUrl(authConfig.selectDropdownNew) + "?selectionType=EMPLOYEES",
      headers: getAuthorizationHeaders(),
    })
      .then((res) => {
        setListOfEmployees(res.data.data);
      })
      .catch((err) => console.log("Employees error", err));
  }, []);

  useEffect(() => {
    if (!!authConfig) {
      getAllListValuesByListNameId(
        authConfig.workLocation,
        (data) =>
          setLocationsData(
            data?.listValues?.map((item) => ({
              value: item.id,
              key: item.listValue,
            }))
          ),
        handleError
      );

      getAllListValuesByListNameId(
        authConfig.designationId,
        (data) =>
          setDesignationsData(
            data?.listValues?.map((item) => ({
              value: item.id,
              key: item.listValue,
            }))
          ),
        handleError
      );
    }
  }, [authConfig]);

  useEffect(() => {
    if (!!listOfEmployees) {
      let data = [];
      listOfEmployees?.map((entry) => {
        data.push({ value: entry.id, key: entry.name });
      });
      setEmployeesOptions(data);
    }
  }, [listOfEmployees]);

  const handleEmployeeChange = (newValue) => {
    setEmployeeId(newValue);
  };

  const handleDepartmentChange = (newValue) => {
    setDepartmentId(newValue);
  };

  const handleError = (error) => {
    console.error("Employees page:", error);
  };

  const handleSelectChange = (event) => {
    const selectedId = event.target.value;
    setSelectedLocationId(selectedId);
  };

  const handleSelectDesignationChange = (event) => {
    const selectedId = event.target.value;
    setSelectedDesignationId(selectedId);
  };


  return (
    <>
      <Grid
        sx={{
          backgroundColor: "#f2f7f2",
          mt: 2,
          paddingTop: 0,
          height: "36px",
          display: "flex",
          alignItems: "center",
          ml: -1,
          justifyContent: "space-between",
        }}
      >
        <Typography variant="body1" fontWeight={"bold"} sx={{ mt: 0, ml: 2 }}>
          Employee Profile
        </Typography>
      </Grid>

      <Box sx={{ pt: 3 }}>
        <Grid container spacing={5}>
          <Grid item xs={12} sm={4}>
            <FormControl fullWidth>
              <Typography className="data-field">Employee Id:</Typography>
              <Typography style={{ fontWeight: "bold" }}>
                {formData?.systemCode}
              </Typography>
            </FormControl>
          </Grid>
          <Grid item xs={12} sm={4}>
            <FormControl fullWidth>
              <Typography className="data-field">Email:</Typography>
              <Typography style={{ fontWeight: "bold" }}>
                {formData?.email}
              </Typography>
            </FormControl>
          </Grid>
          <Grid item xs={12} sm={4}>
            <FormControl fullWidth>
              <Controller
                name="firstName"
                control={control}
                defaultValue={formData?.firstName}
                render={({ field }) => (
                  <NameTextField
                    {...field}
                    size="small"
                    label=" First Name"
                    InputLabelProps={{ shrink: true }}
                    placeholder="Enter your firstName"
                    error={Boolean(errors.firstName)}
                    helperText={errors.firstName?.message}
                    aria-describedby="Section1-firstName"
                  />
                )}
              />
            </FormControl>
          </Grid>

          <Grid item xs={12} sm={4}>
            <FormControl fullWidth>
              <Controller
                name="lastName"
                control={control}
                defaultValue={formData?.lastName}
                render={({ field }) => (
                  <NameTextField
                    {...field}
                    size="small"
                    label="Last Name"
                    InputLabelProps={{ shrink: true }}
                    placeholder="Enter your last name"
                    error={Boolean(errors.lastName)}
                    helperText={errors.lastName?.message}
                    aria-describedby="Section1-lastName"
                  />
                )}
              />
            </FormControl>
          </Grid>

          <Grid item xs={12} sm={4}>
            <FormControl
              fullWidth
              error={Boolean(errors.selectedDesignationId)}
            >
              <Controller
                name="selectedDesignationId"
                control={control}
                rules={{ required: "Designation is required" }}
                defaultValue={selectedDesignationId}
                render={({ field }) => (
                  <SelectAutoComplete
                    id="designation"
                    label="Designation"
                    nameArray={designationsData}
                    value={field.value}
                    onChange={(e) => {
                      field.onChange(e.target.value);
                      handleSelectDesignationChange(e);
                    }}
                  />
                )}
              />
              {errors.selectedDesignationId && (
                <FormHelperText
                  sx={{ color: "error.main" }}
                  id="validation-selectedDesignationId"
                >
                  {errors.selectedDesignationId.message}
                </FormHelperText>
              )}
            </FormControl>
          </Grid>

          <Grid item xs={12} sm={4}>
          <SelectAutoCompleteRole
              register={register}
              clearErrors={clearErrors}
              id={"roleId"}
              label={"Role"}
              name="roleId"
              nameArray={rolesArray}
              defaultValue={roleId}
              value={roleId}
              onChange={(event) => setRoleId(event.target.value)}
              aria-describedby="roleId"
            />
          </Grid>

          <Grid item xs={12} sm={4}>
            <SelectAutoComplete
              register={register}
              clearErrors={clearErrors}
              id={"employeeId-select"}
              label={"Reporting to "}
              name="employeeId-select"
              nameArray={employeesOptions}
              defaultValue={employeeId}
              value={employeeId}
              onChange={(event) => handleEmployeeChange(event.target.value)}
              aria-describedby="employeeId-select"
            />
          </Grid>

          <Grid item xs={12} sm={4}>
            <SelectAutoComplete
              register={register}
              clearErrors={clearErrors}
              id={"departmentId-select"}
              label={"Department"}
              name="departmentId-select"
              nameArray={departmentOptions}
              defaultValue={departmentId}
              value={departmentId}
              onChange={(event) => handleDepartmentChange(event.target.value)}
              aria-describedby="departmentId-select"
            />
          </Grid>

          <Grid item xs={12} sm={4}>
            <FormControl fullWidth>
              <Controller
                name="workLocation"
                control={control}
                defaultValue={formData?.workLocation || ""}
                render={({ field }) => (
                  <SelectAutoComplete
                    {...field}
                    labelId="location-select-label"
                    id="location-select"
                    size="small"
                    value={selectedLocationId}
                    label="Work Location"
                    nameArray={locationsData}
                    onChange={(e) => {
                      field.onChange(e);
                      handleSelectChange(e);
                    }}
                  />
                )}
              />
            </FormControl>
            {errors.selectedLocationId && (
              <FormHelperText
                sx={{ color: "error.main" }}
                id="validation-selectedLocationId"
              >
                {errors.selectedLocationId?.message}
              </FormHelperText>
            )}
          </Grid>

          <Grid item xs={12} sm={4}>
            <FormControl fullWidth>
              <Controller
                name="mobileNumber"
                control={control}
                defaultValue={formData?.mobileNumber}
                render={({ field }) => (
                  <MobileNumberValidation
                    {...field}
                    size="small"
                    type="tel"
                    label="Mobile Number"
                    InputLabelProps={{ shrink: true }}
                    error={Boolean(errors.mobileNumber)}
                    placeholder="Enter 10 digit Mobile Number"
                    helperText={errors.mobileNumber?.message}
                    aria-describedby="Section1-contactNumber"
                  />
                )}
              />
            </FormControl>
          </Grid>
          <Grid item xs={12} sm={6} md={4} lg={4} xl={4} sx={{ width: "100%" }}>
            <FormControl fullWidth>
              <Controller
                name="fromDate"
                control={control}
                rules={{ required: false }}
                render={({ field }) => (
                  <TextField
                    {...field}
                    value={fromDate || ""}
                    onChange={(event) => setFromDate(event.target.value)}
                    size="small"
                    label="Date of joining"
                    type="date"
                    InputLabelProps={{ shrink: true }}
                    error={Boolean(errors.fromDate)}
                    helperText={errors.fromDate?.message}
                    aria-describedby="fromDate"
                  />
                )}
              />
            </FormControl>
          </Grid>
          {formData?.status === "INACTIVE" && (
            <Grid
              item
              xs={12}
              sm={6}
              md={4}
              lg={4}
              xl={4}
              sx={{ width: "100%" }}
            >
              <FormControl fullWidth>
                <Controller
                  name="toDate"
                  control={control}
                  rules={{ required: false }}
                  defaultValue={formData?.employeeMetaData?.employeeData?.toDate}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      value={field.value || ""}
                      size="small"
                      label="Resigned Date"
                      type="date"
                      InputLabelProps={{ shrink: true }}
                      error={Boolean(errors.toDate)}
                      helperText={errors.toDate?.message}
                      aria-describedby="toDate"
                      inputProps={{
                        min: fromDate || "", // Restrict selecting past dates
                      }}
                      disabled={!fromDate}
                    />
                  )}
                />
              </FormControl>
            </Grid>
          )}

          <Grid item xs={12}>
            <center>
              <Button
                size="medium"
                sx={{ mr: 3 }}
                variant="outlined"
                color="primary"
                onClick={() => onCancel()}
              >
                Cancel
              </Button>
              <Button
                size="medium"
                type="submit"
                variant="contained"
                onClick={() => {
                  handleSubmit(submit)();
                }}
              >
                Save
              </Button>
            </center>
          </Grid>
        </Grid>
      </Box>
    </>
  );
};

export default EmployeeDetailsEdit;
