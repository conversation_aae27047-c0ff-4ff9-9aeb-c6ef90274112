import {
  Box,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
} from "@mui/material";
import axios from "axios";
import { useContext, useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import FallbackSpinner from "src/@core/components/spinner";
import authConfig from "src/configs/auth";
import { AuthContext } from "src/context/AuthContext";
import { getAuthorizationHeaders, getUrl } from "src/helpers/utils";
import { useAuth } from "src/hooks/useAuth";
import ServicesTabs from "./ServiceTabs";
import { useRBAC } from "src/pages/permission/RBACContext";
import { MENUS, PAGES, PERMISSIONS } from "src/constants";
import { useRouter } from "next/router";

const ParentComponent = (userDataAllProfile) => {

  const [data, setData] = useState([]);
  const [serviceId, setServiceId] = useState([]);
  const [selectedOptions, setSelectedOptions] = useState({});
  const [isEditMode, setIsEditMode] = useState(false);
  const [loading, setLoading] = useState(true); // Loading state

  const [dialogMessage, setDialogMessage] = useState("");
  const [openDialogContent, setOpenDialogContent] = useState(false);

  const [userList, setUserList] = useState([]);

  const { user, getBasicProfileData, basicProfileDataAllProfiles, fetchOne, userData } =
    useContext(AuthContext);

  const [listValues, setListValues] = useState(null);

  const auth = useAuth();

  const { canMenuPageSection, rbacRoles } = useRBAC();

  const router = useRouter();

  const canAccessServiceProfile = (requiredPermission) =>
    canMenuPageSection(MENUS.LEFT, PAGES.SP_PROFILE, PAGES.SERVICE_PROFILE , requiredPermission);

  const canAccessServiceProvider = (requiredPermission) =>
    canMenuPageSection(MENUS.LEFT, PAGES.GROWTH, PAGES.SERVICE_PROVIDERS, requiredPermission);

  useEffect(() => {
    if (rbacRoles != null && rbacRoles.length > 0) {
      if (!(canAccessServiceProfile(PERMISSIONS.READ) || canAccessServiceProvider(PERMISSIONS.READ))) {
        router.push("/401");
      }
    }
  }, [rbacRoles]);

  function replaceUnderscoresWithSpace(text) {
    text = text?.replace(/_/g, " ");
    text = text?.replace(/-/g, " ");
    return text;
  }
  const userId = userDataAllProfile?.userDataAllProfile?.id;

  const { handleSubmit } = useForm();

  const handleChange = (category, event) => {
    setSelectedOptions({
      ...selectedOptions,
      [category]: event.target.value,
    });
  };

  const handleChangeSelect = (category, event) => {
    setSelectedOptions({
      ...selectedOptions,
      [category]: [event.target.value],
    });
  };

  useEffect(() => {
    axios({
      method: "post",
      url: getUrl(authConfig.allListNamesValues) + "?selectType=LIST_VALUES",
    })
      .then((res) => {
        setListValues(res.data.data);
        window.localStorage.setItem(
          authConfig.listValues,
          JSON.stringify(res.data.data)
        );
        setLoading(false); // Stop loading when data is fetched
      })
      .catch((err) => {
        setLoading(false); // Stop loading on error
      });
  }, []);

  const [serviceNames, setServiceNames] = useState([]);
  useEffect(() => {
    if (listValues && basicProfileDataAllProfiles?.servicesProvided) {
      const namesWithIds = basicProfileDataAllProfiles?.servicesProvided
        ?.map((serviceId) => {
          const service = listValues?.find((item) => item.id === serviceId);
          return service
            ? {
                id: service.id,
                name: service.name,
                otherValue: service.otherValue,
              }
            : null;
        })
        .filter(Boolean);

      const filteredServiceNames = namesWithIds.filter(
        (service) => !service?.otherValue
      );

      setServiceNames(filteredServiceNames);
    }
  }, [basicProfileDataAllProfiles?.servicesProvided, listValues]);


  const userUniqueId = userId !== undefined ? userId : user.id;

  useEffect(() => {
    // to fetch the basic profile get data
    getBasicProfileData(userDataAllProfile?.userDataAllProfile?.organisationId || user?.orgId);
    fetchOne(userUniqueId);
  }, [userUniqueId]);

  // to fetch the service profile data (list of labels with values based on service type id) from service profile table 
  const fetchAll = async (serviceId, data) => {
    const url = `${getUrl(
      authConfig.getAllServiceProfiles
    )}/${serviceId}/datafields`;
    const headers = getAuthorizationHeaders();

    try {
      const response = await axios({
        method: "get",
        url: url,
        headers: headers,
        data: data,
      });

      if (response.data) {
        setUserList(response.data);
      } else {
        console.error("Unexpected API response format:", response);
      }
    } catch (error) {
      console.error("Error fetching users:", error);
    }
  };

  function reverseTransformData(transformedData) {
    const originalData = {};

    transformedData?.forEach((item) => {
      if (item.listValues.length > 0) {
        originalData[item.listNameId] = item.listValues.map(
          (valueItem) => valueItem.listValueId
        );
      } else {
        originalData[item.listNameId] = item.otherValue;
      }
    });

    return originalData;
  }

  const [finalUser, setFinalUser] = useState({});
  useEffect(() => {
    if (userData && userData.length > 0) {
      const userWithMatchingService = userData.find(
        (user) => user.serviceNameId === serviceId
      );
      if (userWithMatchingService) {
        setFinalUser(userWithMatchingService);
        const reverse = reverseTransformData(
          userWithMatchingService.metadata.listNames
        );
        setSelectedOptions(reverse);
      }
    }
  }, [userData, serviceId]);

  useEffect(() => {
    if (finalUser && finalUser.metadata) {
      const listValueMap = userList.reduce((map, item) => {
        item.values.forEach((value) => {
          map[value.id] = { id: value.id, name: value.name };
        });
        return map;
      }, {});

      const listNames = userList.map((item) => {
        const metadataItem = finalUser.metadata.listNames?.find(
          (list) => list.listNameId === item.id
        );
        const otherValue = metadataItem ? metadataItem.otherValue : null;

        return {
          id: item.id,
          name: item.name,
          otherValue: otherValue,
          values:
            metadataItem && metadataItem.listValues.length
              ? metadataItem.listValues.map(
                  (value) => listValueMap[value.listValueId]
                )
              : [],
        };
      });
      setData(listNames);
    }
  }, [finalUser, userList]);

  if (loading) {
    return <FallbackSpinner />; // Show spinner while loading
  }

  const handleSuccess = () => {
    const message = `
      <div> 
        <h3> Services added Successfully.</h3>
      </div>
    `;
    setDialogMessage(message);
    setOpenDialogContent(true);
  };

  const handleFailure = () => {
    const message = `
      <div> 
        <h3> Failed to Add services. Please try again later.</h3>
      </div>
    `;
    setDialogMessage(message);
    setOpenDialogContent(true);
  };

  const handleButtonClick = () => {
    setOpenDialogContent(false);
  };

  function transformData(originalData) {
    const transformedData = [];
    for (const listNameId in originalData) {
      let listValues = [];
      let otherValue = null;

      if (Array.isArray(originalData[listNameId])) {
        listValues = originalData[listNameId].map((listValueId) => ({
          listValueId,
        }));
      } else {
        otherValue = originalData[listNameId];
      }

      transformedData.push({
        listNameId,
        otherValue,
        listValues,
      });
    }

    return transformedData;
  }

  // to save the service profile data in user service profile table 
  async function submit(data) {
    const transformedData = transformData(selectedOptions);

    const payload = {
      id: finalUser.id,
      metadata: {
        listNames: transformedData,
      },
      isActive: true,
      serviceNameId: finalUser.serviceNameId,
      userServicesDataGroup: finalUser.userServicesDataGroup,
    };

    const userUniqueId = userId !== undefined ? userId : user.id;
    const response = await auth.patchServices(payload, userUniqueId, () => {
      console.error("Service Profile Details failed");
    });

    if (response) {
      handleSuccess();
    } else {
      handleFailure();
    }
    setIsEditMode(false);
    fetchAll(serviceId);
    fetchOne(userUniqueId);
  }

  const handleTabChange = (serviceId) => {
    fetchAll(serviceId);
    setServiceId(serviceId);
    setSelectedOptions({});
  };

  

  if(canAccessServiceProfile(PERMISSIONS.READ) || canAccessServiceProvider(PERMISSIONS.READ)){
    return (
      <>
        <ServicesTabs
          tabContents={serviceNames}
          data={data}
          userList={userList}
          formData={data}
          selectedOptions={selectedOptions}
          handleChange={handleChange}
          handleChangeSelect={handleChangeSelect}
          handleSubmit={handleSubmit(submit)}
          isEditMode={isEditMode}
          setIsEditMode={setIsEditMode}
          onTabChange={handleTabChange}
        />
        <Dialog
          open={openDialogContent}
          onClose={handleButtonClick}
          aria-labelledby="alert-dialog-title"
          aria-describedby="alert-dialog-description"
          PaperProps={{
            sx: {
              p: (theme) => `${theme.spacing(2.5)} !important`,
              backgroundColor: (theme) => theme.palette.primary.background,
            },
          }}
        >
          <Box
            sx={{
              width: "100%",
              borderRadius: 1,
              textAlign: "center",
              border: (theme) => `1px solid ${theme.palette.divider}`,
              borderColor: "primary.main",
            }}
          >
            <DialogContent>
              <DialogContentText
                id="alert-dialog-description"
                color="primary.main"
              >
                <div dangerouslySetInnerHTML={{ __html: dialogMessage }} />
              </DialogContentText>
            </DialogContent>
            <DialogActions>
              <Button
                variant="contained"
                onClick={handleButtonClick}
                sx={{ margin: "auto", width: 100 }}
              >
                Okay
              </Button>
            </DialogActions>
          </Box>
        </Dialog>
      </>
    );
  } else {
    return null;
  }
};

export default ParentComponent;