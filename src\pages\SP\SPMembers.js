import { useTheme } from "@emotion/react";
import {
  Autocomplete,
  Box,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  FormControl,
  Grid,
  IconButton,
  TextField,
} from "@mui/material";
import axios from "axios";
import { useContext, useEffect, useState } from "react";
import { Controller, useForm } from "react-hook-form";
import Icon from "src/@core/components/icon";
import { AuthContext } from "src/context/AuthContext";
import { getUrl } from "src/helpers/utils";
import authConfig from "src/configs/auth";

const SPMembers = ({
  rowData,
  spMembers,
  setSpMembers,
  open,
  onClose,
  designationList,
}) => {
  const theme = useTheme();

  const [designation, setDesignation] = useState(null);
  const [error, setError] = useState(false);

  const { user } = useContext(AuthContext);

  const {
    control,
    handleSubmit,
    setValue,
    reset,
    formState: { errors },
  } = useForm({
    mode: "onChange",
  });

  const handleCloseDialog = () => {
    onClose();
    reset();
    setDesignation(null);
    reset();
    setValue("name", "");
    setValue("mobileNumber", "");
    setValue("alternateMobileNumber", "");
    setValue("email", "");
  };

  useEffect(() => {
    setValue("name", rowData?.name);
    setValue("mobileNumber", rowData?.mobileNumber);
    setValue("alternateMobileNumber", rowData?.alternateMobileNumber);
    setValue("email", rowData?.email);
    setDesignation(rowData?.designation);
  }, [rowData]);

  const submit = (data) => {
    if (!designation) {
      setError(true);
      return;
    } else {
      setError(false);
    }
    const fields = {
      id: crypto.randomUUID(),
      name: data?.name,
      mobileNumber: data?.mobileNumber,
      alternateMobileNumber: data?.alternateMobileNumber,
      email: data?.email,
      designation: designation,
      isActive:true
    };
    setSpMembers((prevSpMembers) => [...(prevSpMembers || []), fields]);
    handleCloseDialog();
  };

  const update = (data) => {
    if (!designation) {
      setError(true);
      return;
    } else {
      setError(false);
    }
    const updatedMembers = spMembers?.map((member) =>
      member.id === rowData?.id
        ? {
            ...member,
            name: data?.name,
            mobileNumber: data?.mobileNumber,
            alternateMobileNumber: data?.alternateMobileNumber,
            email: data?.email,
            designation: designation,
            isActive:rowData?.isActive
          }
        : member
    );

    setSpMembers(updatedMembers);
    handleCloseDialog();
  };

  return (
    <>
      <Dialog open={open} onClose={handleCloseDialog} maxWidth="sm" fullWidth>
        <DialogTitle
          sx={{
            position: "relative",
            borderBottom: (theme) => `1px solid ${theme.palette.divider}`,
            p: (theme) => `${theme.spacing(1.75, 4)} !important`,
            display: "flex",
            alignItems: "center",
            justifyContent: { xs: "start" },
            fontSize: { xs: 19, md: 20 },
            margin: "2px 2px 2px 2px",
          }}
          textAlign={"center"}
        >
          <Box
            sx={{
              marginLeft: 1.5,
            }}
          >
            SP Company Members
          </Box>
          <Box
            sx={{
              position: "absolute",
              top: "4px",
              right: "14px",
              marginRight: 2,
            }}
          >
            <IconButton
              size="small"
              onClick={handleCloseDialog}
              sx={{
                p: "0.2rem",
                borderRadius: 1,
                color: "common.white",
                backgroundColor: "primary.main",
                "&:hover": {
                  backgroundColor: "#66BB6A",
                  transition: "background 0.5s ease, transform 0.5s ease",
                },
              }}
            >
              <Icon icon="tabler:x" fontSize="1rem" />
            </IconButton>
          </Box>
        </DialogTitle>
        <DialogContent>
          <Grid
            container
            alignItems="center"
            justifyContent="center"
            spacing={3}
            sx={{ marginBottom: 2 }}
          >
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <Controller
                  name="name"
                  control={control}
                  rules={{ required: "SP member Name is required" }}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      value={field.value || ""}
                      label="SP Member Name"
                      InputLabelProps={{ shrink: true }}
                      size="small"
                      placeholder="Enter SP Member Name"
                      error={Boolean(errors.name)}
                      helperText={errors.name?.message}
                      aria-describedby="validation-basic-name"
                      inputProps={{ maxLength: 50 }}
                    />
                  )}
                />
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6}>
              <Autocomplete
                id="designation"
                options={designationList} // Array of { value, key }
                value={
                  designationList?.find(
                    (option) => option.value === designation
                  ) || null
                } // The currently selected value (e.g. an object with 'value' and 'key')
                onChange={(event, newValue) => {
                  // Ensure newValue is an object with 'value' and 'key' properties
                  setDesignation(newValue?.value || null);
                  setError(false);
                }}
                getOptionLabel={(option) => option.key || ""} // Access 'key' for displaying the label
                isOptionEqualToValue={(option, value) => option.value === value} // Compare based on 'value'
                renderInput={(params) => (
                  <TextField
                    {...params}
                    label="Designation"
                    variant="outlined"
                    size="small"
                    error={error}
                    helperText={error ? "Designation is required" : ""}
                  />
                )}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <Controller
                  name="mobileNumber"
                  control={control}
                  rules={{ required: "Contact Number is required" }}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      value={field.value || ""}
                      type="tel"
                      label="Contact Number"
                      size="small"
                      InputLabelProps={{ shrink: true }}
                      placeholder="1234567890"
                      inputProps={{
                        maxLength: 10,
                      }}
                      error={Boolean(errors.mobileNumber)}
                      helperText={errors.mobileNumber?.message}
                      aria-describedby="validation-basic-mobileNumber"
                      onKeyDown={(e) => {
                        if (
                          !/[0-9]/.test(e.key) &&
                          e.key !== "Backspace" &&
                          e.key !== "ArrowLeft" &&
                          e.key !== "ArrowRight" &&
                          e.key !== "Delete" &&
                          e.key !== "Tab"
                        ) {
                          e.preventDefault();
                        }
                      }}
                    />
                  )}
                />
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <Controller
                  name="alternateMobileNumber"
                  control={control}
                  rules={{ required: false }}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      value={field.value || ""}
                      type="text"
                      label="Alternate Contact Number"
                      size="small"
                      InputLabelProps={{ shrink: true }}
                      placeholder="1234567890"
                      error={Boolean(errors.alternateMobileNumber)}
                      helperText={errors.alternateMobileNumber?.message}
                      aria-describedby="validation-basic-alternateMobileNumber"
                    />
                  )}
                />
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={12}>
              <FormControl fullWidth>
                <Controller
                  name="email"
                  control={control}
                  rules={{
                    required: "Email is required",
                    pattern: {
                      value: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
                      message: "Enter a valid email address",
                    },
                    minLength: {
                      value: 8,
                      message: "Email must be at least 8 characters long",
                    },
                    maxLength: {
                      value: 100,
                      message: "Email cannot be longer than 100 characters",
                    },
                    validate: async (value) => {
                      if (rowData?.email === value) {
                        return true; // Skip validation if email hasn't changed
                      }
                      // Check if email exists in spMembers
                      const emailExistsInSP = spMembers.some(
                        (member) =>
                          member.email.toLowerCase() === value.toLowerCase()
                      );
                      if (emailExistsInSP) {
                        return "Email Already Exist";
                      }
                      try {
                        const res = await axios.post(
                          getUrl(authConfig.individualVerificationAudit) +
                            "/check-email",
                          { email: value }
                        );
                        if (res?.data?.message === "Email Already Exist") {
                          return "Email Already Exist";
                        }
                        return true;
                      } catch (err) {
                        return "Failed to validate email";
                      }
                    },
                  }}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      value={field.value || ""}
                      label="Email"
                      InputLabelProps={{ shrink: true }}
                      size="small"
                      placeholder="Enter Email"
                      error={Boolean(errors.email)}
                      helperText={errors.email?.message}
                      aria-describedby="validation-basic-email"
                      inputProps={{ maxLength: 50 }}
                    />
                  )}
                />
              </FormControl>
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions
          sx={{
            justifyContent: "end",
            borderTop: (theme) => `1px solid ${theme.palette.divider}`,
            p: (theme) => `${theme.spacing(2.5)} !important`,
          }}
        >
          <Button onClick={handleCloseDialog}>Cancel</Button>

          {rowData ? (
            <Button
              onClick={handleSubmit(update)}
              variant="contained"
              sx={{
                marginRight: 3.5,
              }}
            >
              Update
            </Button>
          ) : (
            <Button
              onClick={handleSubmit(submit)}
              variant="contained"
              sx={{
                marginRight: 3.5,
              }}
            >
              Add
            </Button>
          )}
        </DialogActions>
      </Dialog>
    </>
  );
};

export default SPMembers;
