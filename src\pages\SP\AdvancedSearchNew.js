import { FormControl, Grid, TextField } from "@mui/material";
import Box from "@mui/material/Box";
import Button from "@mui/material/Button";
import Drawer from "@mui/material/Drawer";
import IconButton from "@mui/material/IconButton";
import { styled } from "@mui/material/styles";
import Tooltip from "@mui/material/Tooltip";
import Typography from "@mui/material/Typography";
import { useEffect, useMemo, useState } from "react";
import { Controller, useForm } from "react-hook-form";
import PerfectScrollbar from "react-perfect-scrollbar";
import MultiSelectAutoComplete from "src/@core/components/custom-components/MultiSelectAutoComplete";
import SelectClearAutoComplete from "src/@core/components/custom-components/SelectClearAutoComplete";
import Icon from "src/@core/components/icon";
import CustomAvatar from "src/@core/components/mui/avatar";

const Header = styled(Box)(({ theme }) => ({
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
  position: "relative",
  padding: theme.spacing(3),
  borderBottom: `1px solid ${theme.palette.divider}`,
}));

const AdvancedSearchNew = (props) => {
  const {
    open,
    toggle,
    selectedFilters,
    setSearchingState,
    clearAllFilters,
    onApplyFilters,
    priorityData,
    employeesData
  } = props;

  const {
    setValue,
    control,
    reset,
    handleSubmit,
    formState: { errors },
  } = useForm();

  const [selectedPriorities, setSelectedPriorities] = useState("");
  const [assignedTo, setAssignedTo] = useState("");

    const transformedEmployeesData = useMemo(
      () => employeesData?.map((emp) => ({ value: emp.id, key: emp.name })),
      [employeesData]
    );

  useEffect(() => {
    // Create a map of filter keys for easy lookup
    const filterMap = new Map(selectedFilters?.map((filter) => [filter.key, filter.value]));

    // Set values based on filters or clear them if the filter is not present
    setValue("name", filterMap.get("nameFilter") || "");
    setValue("companyName", filterMap.get("searchByCompanyName") || "");
    setValue("email", filterMap.get("emailFilter") || "");
    setValue("mobileNumber", filterMap.get("mobileFilter") || "");
    setValue("societyAddress", filterMap.get("searchBySocietyAddress") || "");

    const priorities = filterMap.get("searchByLeadPriority");
    if (priorities) {
      const matchingPriorities = priorityData.filter((priority) =>
        priorities.includes(priority.value)
      );
      setSelectedPriorities(matchingPriorities);
    } else {
      setSelectedPriorities(null);
    }

    const assignments = filterMap.get("assignedToGivenEmployeeId");
    if (assignments) {
      const matchingAssignments = transformedEmployeesData.filter((employee) =>
        assignments.includes(employee.value)
      );
      setAssignedTo(matchingAssignments);
    } else {
      setAssignedTo(null);
    }

  }, [selectedFilters, setValue]);

  const handleCancel = () => {
    reset();
    setSearchingState(false);
    clearAllFilters();
    setSelectedPriorities("");
    setAssignedTo("");
  };

  const handleClose = () => {
    toggle();
  };

  const handleApply = (data) => {
    // Prepare selected filters for chips
    const filters = [];
    if (data?.name) {
      filters.push({ key: "nameFilter", label: "Name", value: data?.name });
    }
    if (data?.companyName) {
      filters.push({ key: "searchByCompanyName", label: "Company Name", value: data?.companyName });
    }
    if (data?.email) {
        filters.push({ key: "emailFilter", label: "Email", value: data?.email });
      }
      if (data?.mobileNumber) {
        filters.push({ key: "mobileFilter", label: "Mobile Number", value: data?.mobileNumber });
      }
      if (data?.societyAddress) {
        filters.push({ key: "searchBySocietyAddress", label: "Society Address", value: data?.societyAddress });
      }

    if (selectedPriorities) {
        const priorityValues = selectedPriorities?.map(
          (priority) => priority?.value
        );
        filters.push({
          key: "searchByLeadPriority",
          label: "Priority",
          value: priorityValues,
        });
      }

      if (assignedTo) {
        const assignedValues = assignedTo.map((assigned) => assigned?.value);
        filters.push({
          key: "assignedToGivenEmployeeId",
          label: "Assigned To",
          value: assignedValues,
        });
      }
    // Call the parent callback with selected filters
    onApplyFilters(filters);
    setSearchingState(true); // Update searching state in parent if needed
    toggle(); // Close the drawer
  };

  const handlePriorityChange = (event) => {
    const value = event.target?.value;
    setSelectedPriorities(value);
  };

  const handleAssignmentChange = (event) => {
    const value = event.target?.value;
    setAssignedTo(value);
  };

  return (
    <>
      <Tooltip title="Advanced Search">
        <CustomAvatar
          variant="rounded"
          sx={{ width: 36, height: 36, cursor: "pointer" }}
          onClick={toggle}
        >
          <Icon icon="tabler:filter" fontSize={27} />
        </CustomAvatar>
      </Tooltip>

      <Drawer
        open={open}
        anchor="right"
        variant="temporary"
        onClose={handleClose}
        ModalProps={{ keepMounted: true }}
        sx={{ "& .MuiDrawer-paper": { width: { xs: "85%", sm: 500 } } }}
      >
        <Header 
          sx={{position: "relative", 
            display: "flex",
            alignItems: "center", 
            justifyContent: "space-between"
           }}
           >
          <Typography variant="h5" sx={{
            ml:{
              xs :3,
              sm:3,

              xl :3
            }}}
            
            
            > Advanced Search&nbsp;   </Typography>
          <Box sx={{ position: "absolute", top: "8px", right: "14px", 
              mt:{
                xs:1,
              xl:1.5,
              lg:1.5,
              md:1.5,
              sm:1
            },
              mr:{
                xs:2.5,
                xl:2.5,
                lg:2.5,
                md:2.5,
                sm:2.5
              },
            

           }}>
            <IconButton
              size="small"
              onClick={handleClose}
              sx={{
                borderRadius: 1,
                color: "common.white",
                backgroundColor: "primary.main",
                "&:hover": {
                  backgroundColor: "#66BB6A",
                  transition: "background 0.5s ease, transform 0.5s ease",
                },
              }}
            >
              <Icon icon="tabler:x" fontSize="1rem" />
            </IconButton>
          </Box>
        </Header>

        <PerfectScrollbar options={{ wheelPropagation: false }}>
          <Box sx={{ p: (theme) => theme.spacing(4, 6) }}>
            <Grid container spacing={3} alignItems={"center"}>
              <Grid item xs={12} md={12}>
                <FormControl fullWidth>
                  <Controller
                    name="companyName"
                    control={control}
                    defaultValue=""
                    render={({ field }) => (
                      <TextField
                        {...field}
                        size="small"
                        label=" Search by Company Name"
                        placeholder="Search By Company Name"
                        InputLabelProps={{ shrink: true }}
                        aria-describedby="companyName"
                        value={field.value}
                      />
                    )}
                  />
                </FormControl>
              </Grid>
              <Grid item xs={12} md={12}>
                <FormControl fullWidth>
                  <Controller
                    name="name"
                    control={control}
                    defaultValue=""
                    render={({ field }) => (
                      <TextField
                        {...field}
                        size="small"
                        label=" Search by Name"
                        placeholder="Search By Name"
                        InputLabelProps={{ shrink: true }}
                        aria-describedby="name"
                        value={field.value}
                      />
                    )}
                  />
                </FormControl>
              </Grid>
              <Grid item xs={12} md={12}>
                <FormControl fullWidth>
                  <Controller
                    name="email"
                    control={control}
                    defaultValue=""
                    render={({ field }) => (
                      <TextField
                        {...field}
                        size="small"
                        label=" Search by Email"
                        placeholder="Search By Email"
                        InputLabelProps={{ shrink: true }}
                        aria-describedby="email"
                        value={field.value}
                      />
                    )}
                  />
                </FormControl>
              </Grid>
              <Grid item xs={12} md={12}>
                <FormControl fullWidth>
                  <Controller
                    name="mobileNumber"
                    control={control}
                    defaultValue=""
                    render={({ field }) => (
                      <TextField
                        {...field}
                        size="small"
                        type="number"
                        label=" Search by Mobile Number"
                        placeholder="Search By Mobile Number"
                        InputLabelProps={{ shrink: true }}
                        aria-describedby="mobileNumber"
                        value={field.value}
                      />
                    )}
                  />
                </FormControl>
              </Grid>
              <Grid item xs={12} md={12}>
                <FormControl fullWidth error={Boolean(errors.priority)}>
                  <Controller
                    name="priority"
                    control={control}
                    render={({ field }) => (
                      <MultiSelectAutoComplete
                        id="priority"
                        label="Select Priority"
                        nameArray={priorityData}
                        value={selectedPriorities || []}
                        onChange={(e) => {
                          handlePriorityChange(e);
                          field.onChange(e.target.value);
                        }}
                        error={Boolean(errors.priority)}
                      />
                    )}
                  />
                </FormControl>
              </Grid>
              {/* <Grid item xs={12} md={12}>
                <FormControl fullWidth error={Boolean(errors.leadStatus)}>
                  <Controller
                    name="leadStatus"
                    control={control}
                    render={({ field }) => (
                      <MultiSelectAutoComplete
                        id="leadStatus"
                        label="Select Lead Status"
                        nameArray={priorityData}
                        value={selectedPriorities || []}
                        onChange={(e) => {
                          handlePriorityChange(e);
                          field.onChange(e.target.value);
                        }}
                        error={Boolean(errors.priority)}
                      />
                    )}
                  />
                </FormControl>
              </Grid> */}
              <Grid item xs={12} md={12}>
                  <FormControl fullWidth error={Boolean(errors.assignedTo)}>
                    <Controller
                      name="assignedTo"
                      control={control}
                      render={({ field }) => (
                        <MultiSelectAutoComplete
                          id="assignedTo"
                          label="Select Assigned To"
                          nameArray={transformedEmployeesData}
                          value={assignedTo || []}
                          onChange={(e) => {
                            handleAssignmentChange(e);
                            field.onChange(e.target.value);
                          }}
                          error={Boolean(errors.assignedTo)}
                        />
                      )}
                    />
                  </FormControl>
                </Grid>
            </Grid>
          </Box>
        </PerfectScrollbar>

        <Box
          sx={{
            borderTop: (theme) => `1px solid ${theme.palette.divider}`,
            p: (theme) => theme.spacing(2),
            justifyContent: "flex-end",
            display: "flex",
            alignItems: "center",
          }}
        >
          <Button variant="tonal" sx={{ mr: 3 }} onClick={handleCancel}>
            Clear All
          </Button>
          <Button variant="contained" onClick={handleSubmit(handleApply)} sx={{ mr: {
                xs: 4, 
                sm: 4, 
                md: 4, 
                lg: 4, 
                xl: 4,
              },} }>
            Apply
          </Button>
        </Box>
      </Drawer>
    </>
  );
};

export default AdvancedSearchNew;