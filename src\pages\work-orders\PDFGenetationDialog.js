// PDFGenerationDialog.js
import React from 'react';
import { Dialog, DialogContent, DialogContentText, DialogActions, Button, Box } from '@mui/material';

const PDFGenerationDialog = ({ generatingPDF, handleClosePDFDialog, handlePDFGeneration, dialogMessage }) => {
  return (
    <Dialog
        open={generatingPDF}
        onClose={handleClosePDFDialog}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
        PaperProps={{
          sx: {
            p: (theme) => `${theme.spacing(2.5)} !important`,
            backgroundColor: (theme) => theme.palette.primary.background,
          },
        }}
      >
        <Box
          sx={{
            width: "100%",
            borderRadius: 1,
            textAlign: "center",
            border: (theme) => `1px solid ${theme.palette.divider}`,
            borderColor: "primary.main",
          }}
        >
          <DialogContent>
            <DialogContentText
              id="alert-dialog-description"
              color="primary.main"
            >
              <div dangerouslySetInnerHTML={{ __html: dialogMessage }} />
            </DialogContentText>
          </DialogContent>
          <DialogActions>
            <Button
              variant="contained"
              onClick={handlePDFGeneration}
              sx={{ margin: "auto", width: 100 }}
            >
              Yes
            </Button>
            <Button
              variant="contained"
              onClick={handleClosePDFDialog}
              sx={{ margin: "auto", width: 100 }}
            >
              No
            </Button>
          </DialogActions>
        </Box>
      </Dialog>
  );
};

export default PDFGenerationDialog;