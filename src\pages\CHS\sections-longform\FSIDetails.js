// ** React Imports
import { useEffect, useRef, useState } from "react";

// ** MUI Imports
import FormControl from "@mui/material/FormControl";
import FormHelperText from "@mui/material/FormHelperText";
import Grid from "@mui/material/Grid";
import TextField from "@mui/material/TextField";

// ** Third Party Imports
import { Controller, useForm } from "react-hook-form";
import SelectCategory from "src/@core/components/custom-components/SelectCategory";

const FSIDetails = ({ formData, onUpdate }) => {
  const {
    control,
    clearErrors,
    register,
    watch,
    formState: { errors },
  } = useForm({
    defaultValues: formData?.fsi || {}, // Initialize form fields with existing data
  });

  const [scheme, setScheme] = useState(formData?.fsi?.scheme);

  const schemeOptions = [
    {
      value: "_33_7B",
      key: "33(7B)",
    },
  ];

  // Watch all fields for changes
  const watchedFields = watch();

  // Track previous watched fields using useRef
  const previousWatchedFields = useRef();

  // Update formData.societyDetails when any field changes
  useEffect(() => {
    // Compare previous watched fields with current watched fields
    if (
      JSON.stringify(previousWatchedFields.current) !==
      JSON.stringify(watchedFields)
    ) {
      onUpdate(watchedFields); // Send only the updated fields to the parent
      previousWatchedFields.current = watchedFields; // Update the ref with the latest value
    }
  }, [watchedFields, onUpdate]);

  const fields = [
    {
      name: "dpRestrictions",
      label: "Dp Restrictions",
      placeholder: "Dp Restrictions",
      maxLength: 200,
      type: "text",
    },
    {
      name: "litigationsOrEncroachment",
      label: "Litigations / Encroachment",
      placeholder: "Litigations / Encroachment",
      maxLength: 200,
      type: "text",
    },
    {
      name: "buildingAge",
      label: "Building Age",
      placeholder: "Enter Building Age",
      maxLength: 20,
      type: "text",
    },
    {
      name: "heightRestriction",
      label: "Height Restriction",
      placeholder: "Height restriction in feet",
      maxLength: 100,
      type: "number",
    },
  ];

  return (
    <>
      <Grid
        container
        spacing={3}
        sx={{
          width: "100%",
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          marginTop: "2rem",
        }}
      >
        {[
          {
            name: "fsiConsumedFsi",
            label: "Consumed FSI",
            placeholder: "Consumed FSI in sq. ft.",
          },
          {
            name: "fsi_AvailableFsi",
            label: "Available FSI",
            placeholder: "Available FSI in sq. ft.",
          },
          {
            name: "fsi_PermissibleFsi",
            label: "Permissible FSI",
            placeholder: "Permissible FSI in sq. ft.",
          },
        ].map((field) => (
          <Grid
            item
            xs={12}
            sm={6}
            md={4}
            lg={4}
            xl={4}
            sx={{ width: "100%" }}
            key={field.name}
          >
            <FormControl fullWidth>
              <Controller
                name={field.name}
                control={control}
                rules={{
                  required: false,
                  maxLength: {
                    value: 100,
                    message: "Must not exceed 100 characters",
                  },
                }}
                defaultValue={formData?.[field.name]}
                render={({ field: fieldProps }) => (
                  <TextField
                    {...fieldProps}
                    type="text"
                    label={field.label}
                    placeholder={field.placeholder}
                    inputProps={{
                      pattern: "[0-9]*",
                      maxLength: 6, // allow up to 6 digits
                    }}
                    size="small"
                    error={Boolean(errors[field.name])}
                    helperText={errors[field.name]?.message}
                    aria-describedby={`validation-basic-${field.name}`}
                    onKeyPress={(event) => {
                      if (!/\d/.test(event.key)) {
                        event.preventDefault();
                      }
                    }}
                  />
                )}
              />
            </FormControl>
          </Grid>
        ))}

        <Grid item xs={12} sm={6} md={4} lg={4} xl={4} sx={{ width: "100%" }}>
          <SelectCategory
            clearErrors={clearErrors}
            register={register}
            id={"scheme"}
            label={"*Scheme"}
            name="scheme"
            nameArray={schemeOptions}
            value={scheme}
            onChange={(e) => {
              setScheme(e.target.value);
              onUpdate({ ...watchedFields, scheme: e.target.value }); // Update parent on scheme change
            }}
            error={Boolean(errors.scheme)}
            aria-describedby="validation-scheme"
          />
          {errors.scheme && (
            <FormHelperText sx={{ color: "error.main" }} id="validation-scheme">
              Please select Scheme
            </FormHelperText>
          )}
        </Grid>

        {fields.map((field) => (
          <Grid
            item
            xs={12}
            sm={6}
            md={4}
            lg={4}
            xl={4}
            sx={{ width: "100%" }}
            key={field.name}
          >
            <FormControl fullWidth>
              <Controller
                name={field.name}
                control={control}
                rules={{
                  required: false,
                  maxLength: {
                    value: field.maxLength,
                    message: `Must not exceed ${field.maxLength} characters`,
                  },
                }}
                defaultValue={formData?.[field.name]}
                render={({ field: fieldProps }) => (
                  <TextField
                    {...fieldProps}
                    type={field.type}
                    label={field.label}
                    placeholder={field.placeholder}
                    size="small"
                    error={Boolean(errors[field.name])}
                    helperText={errors[field.name]?.message}
                    aria-describedby={`validation-basic-${field.name}`}
                  />
                )}
              />
            </FormControl>
          </Grid>
        ))}
      </Grid>
    </>
  );
};

export default FSIDetails;
