import {
  Dialog,
  DialogActions,
  DialogContent,
  Divider,
  IconButton,
  Table,
  TableBody,
  TableCell,
  Grid,
  Button,
  TableHead,
  TableRow,
  Typography,
  DialogTitle,
  Box,
  TableContainer,
} from "@mui/material";
import Icon from "src/@core/components/icon";
import { useContext, useEffect, useState } from "react";
import axios from "axios";
import { getAuthorizationHeaders, getUrl } from "src/helpers/utils";
import authConfig from "src/configs/auth";
import { AuthContext } from "src/context/AuthContext";
import SRInformation from "../SRInformation";
import ViewScopeOfWork from "./ViewScopeWork";
import ViewPaymentSchedule from "./ViewPayments";
import ViewTermsAndConditions from "./ViewTermsCondition";
import ViewSnapshotByLocation from "src/@core/components/custom-components/ViewSnapshotByLocation";
import NewSectionView from "./NewSectionView";
import { Download as DownloadIcon } from "@mui/icons-material";

const WorkOrdersViewDialog = ({ open, onClose , handleOpenDialog}) => {

  const { workOrderDetails, requisitionDataDetails } = useContext(AuthContext);

  const [scopeData, setScopeData] = useState([]);
  const [document, setDocument] = useState({});
  const [selectedDocument, setSelectedDocument] = useState(null);
  const [editablePaymentData, setEditablePaymentData] = useState([]);
  const [paymentData, setPaymentData] = useState([]);
  const [newSectionData, setNewSectionData] = useState([]);
  const [terms, setTerms] = useState([]);
  const [societySelected, setSocietySelected] = useState("");
  const [serviceRequirement, setServiceRequirement] = useState("");
  const [societyOptions, setSocietyOptions] = useState([]);
  const [requirements, setRequirements] = useState([]);
  const [contractValue, setContractValue] = useState("");
  const [gstPercentage, setGstPercentage] = useState();

  const handleDialogClose = () => {
    setSelectedDocument(null);
  };
  useEffect(() => {
    axios({
      method: "get",
      url:
        getUrl(authConfig.serviceRequisitionsEndpoint) + "/get-society-names",
      headers: getAuthorizationHeaders(),
    })
      .then((res) => {
        setSocietyOptions(res.data);
      })
      .catch((err) => console.log("Categories error", err));
  }, []);
  useEffect(() => {
    if (societySelected) {
      axios({
        method: "get",
        url:
          getUrl(authConfig.serviceRequisitionsEndpoint) +
          "/get-service-type-names/" +
          societySelected,
        headers: getAuthorizationHeaders(),
      })
        .then((res) => {
          setRequirements(res.data);
        })
        .catch((err) => console.log("Categories error", err));
    }
  }, [societySelected]);
  useEffect(() => {
    if (requisitionDataDetails?.serviceTypeId) {
      axios({
        method: "get",
        url:
          getUrl(authConfig.serviceRequisitionsEndpoint) +
          "/get-template-names/" +
          requisitionDataDetails?.serviceTypeId,
        headers: getAuthorizationHeaders(
          authConfig.WORK_ORDERS_GET_TEMPLATE_NAMES_RES_V1
        ),
      })
        .then((res) => {
          const transformedTemplatesList = res?.data?.map((template) => ({
            value: template?.id,
            key: template?.name,
          }));
          // setTemplatesList(transformedTemplatesList);
        })
        .catch((err) => console.log("Templates error", err));
    }
  }, [serviceRequirement]);

  useEffect(() => {
    if (workOrderDetails) {
      setSocietySelected(workOrderDetails?.societyId);
      setServiceRequirement(workOrderDetails?.srId);
      setScopeData(workOrderDetails?.scopeOfWork);
      setEditablePaymentData(workOrderDetails?.paymentSchedule?.payment);
      setGstPercentage(workOrderDetails?.paymentSchedule?.gstPercentage);
      setContractValue(workOrderDetails?.paymentSchedule?.totalContractValue);
      setTerms(workOrderDetails?.termsAndConditions?.descriptions);
      setDocument({
        id: workOrderDetails?.fileLocation?.split("/")?.slice(-2, -1)?.[0],
        location: workOrderDetails?.fileLocation,
      });
      setPaymentData({
        recurring: workOrderDetails?.paymentSchedule?.recurring,
        frequency: workOrderDetails?.paymentSchedule?.frequency,
        followUpFrequency: workOrderDetails?.paymentSchedule?.followUpFrequency,
        paymentTerms: workOrderDetails?.paymentSchedule?.paymentTerms,
        remarks: workOrderDetails?.paymentSchedule?.remarks,
        houzerPayment: workOrderDetails?.paymentSchedule?.houzerPayment,
        renewalDate: workOrderDetails?.paymentSchedule?.renewalDate,
        dateOfStart: workOrderDetails?.paymentSchedule?.dateOfStart,
        dateOfFinish: workOrderDetails?.paymentSchedule?.dateOfFinish,
      });
      setNewSectionData({
        workOrderStatus: workOrderDetails?.workOrderMetaData?.workOrderStatus,
        workStatus: workOrderDetails?.workOrderMetaData?.workStatus,
        lastContactDate: workOrderDetails?.workOrderMetaData?.lastContactDate,
        nextFollowUpDate: workOrderDetails?.workOrderMetaData?.nextFollowUpDate,
      });
    } else {
      setSocietySelected("");
      setServiceRequirement("");
      setScopeData([]);
      setEditablePaymentData([]);
      setGstPercentage("");
      setContractValue("");
      setTerms([]);
      setDocument({});
      setPaymentData({});
    }
  }, [workOrderDetails]);

  return (
    <>
      <Dialog
        open={open}
        onClose={onClose}
        fullScreen
        maxWidth="sm"
        scroll="body"
      >
        <DialogTitle
          sx={{
            position: "sticky",
            top: 0,
            zIndex: 10,
            backgroundColor: "background.paper",
            borderBottom: (theme) => `1px solid ${theme.palette.divider}`,
            p: (theme) => `${theme.spacing(1.75, 7)} !important`,
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between",
            fontSize: { xs: 19, md: 20 },
            height: "50px", // height
          }}
          textAlign={"center"}
        >
          View Work Orders&nbsp;
          <Box sx={{ top: "9px", right: "10px" }}>
            <IconButton
              size="small"
              onClick={onClose}
              sx={{
                // p: "0.438rem",
                borderRadius: 1,
                color: "common.white",
                backgroundColor: "primary.main",
                "&:hover": {
                  backgroundColor: "#66BB6A",
                  transition: "background 0.5s ease, transform 0.5s ease",
                },
              }}
            >
              <Icon icon="tabler:x" fontSize="1rem" />
            </IconButton>
          </Box>
        </DialogTitle>
        <DialogContent
          sx={{
            position: "relative",
            p: (theme) => `${theme.spacing(3, 8)} !important`,
          }}
        >
          <Grid
            sx={{
              backgroundColor: "#f2f7f2",
              mt: 4,
              paddingTop: 0,
              height: "36px",
              display: "flex",
              alignItems: "center",
            }}
          >
            <Typography
              variant="body1"
              fontWeight={"bold"}
              sx={{ mt: 0, ml: 2 }}
            >
              Service Requisition Information
            </Typography>
            <Divider />
          </Grid>
          <Divider />

          <SRInformation
            societyOptions={societyOptions}
            serviceRequirement={serviceRequirement}
            setServiceRequirement={setServiceRequirement}
            societySelected={societySelected}
            setSocietySelected={setSocietySelected}
            serviceRequirements={requirements}
            data={workOrderDetails}
          />

          {/* Main Points List */}
          <Grid
            sx={{
              backgroundColor: "#f2f7f2",
              mt: 4,
              paddingTop: 0,
              height: "36px",
              display: "flex",
              alignItems: "center",
            }}
          >
            <Typography
              variant="body1"
              fontWeight={"bold"}
              sx={{ mt: 0, ml: 2 }}
            >
              Scope of Work
            </Typography>
            <Divider />
          </Grid>
          <Divider />
          <ViewScopeOfWork scopeData={scopeData} />
          <Divider sx={{ mt: 2 }} />
          <Grid
            sx={{
              backgroundColor: "#f2f7f2",
              mt: 4,
              paddingTop: 0,
              height: "36px",
              display: "flex",
              alignItems: "center",
            }}
          >
            <Typography
              variant="body1"
              fontWeight={"bold"}
              sx={{ mt: 0, ml: 1 }}
            >
              Payment Schedule
            </Typography>
            <Divider />
          </Grid>

          <ViewPaymentSchedule
            PaymentData={editablePaymentData}
            contractValue={contractValue}
            gstPercentage={gstPercentage}
            paymentMetaData={paymentData}
          />
          <Divider sx={{ mt: 2 }} />
          <Grid
            sx={{
              backgroundColor: "#f2f7f2",
              mt: 4,
              paddingTop: 0,
              height: "36px",
              display: "flex",
              alignItems: "center",
            }}
          >
            <Typography
              variant="body1"
              fontWeight={"bold"}
              sx={{ mt: 0, ml: 1 }}
            >
              Work Engagement Details
            </Typography>
            <Divider />
          </Grid>
          <NewSectionView data={newSectionData} />
          <Grid
            sx={{
              backgroundColor: "#f2f7f2",
              mt: 4,
              paddingTop: 0,
              height: "36px",
              display: "flex",
              alignItems: "center",
            }}
          >
            <Typography
              variant="body1"
              fontWeight={"bold"}
              sx={{ mt: 0, ml: 2 }}
            >
              Terms & Conditions
            </Typography>
          </Grid>
          <Divider />
          <ViewTermsAndConditions terms={terms} />
          <Grid
            sx={{
              backgroundColor: "#f2f7f2",
              mt: 4,
              paddingTop: 0,
              height: "36px",
              display: "flex",
              alignItems: "center",
            }}
          >
            <Typography
              variant="body1"
              fontWeight={"bold"}
              sx={{ mt: 0, ml: 2 }}
            >
              Download
            </Typography>
            <Divider />
          </Grid>
          <Grid container spacing={4} sx={{ mt: 4, mb: 2 }}>
              {/* Step 1 */}
              <Grid
                item
                xs={12}
                sm={4}
                sx={{
                  display: "flex",
                  flexDirection: "column",
                  alignItems: "center",
                }}
              >
                <Button
                  variant="contained"
                  sx={{ mt: 2 }}
                  disabled={
                    !societySelected ||
                    !serviceRequirement ||
                    scopeData?.length === 0 ||
                    editablePaymentData?.length === 0 ||
                    terms?.length === 0
                  }
                  onClick={handleOpenDialog}
                  startIcon={<DownloadIcon />}

                >
                  Download
                </Button>
                {/* Show contextual error messages if disabled */}
                {(!societySelected || !serviceRequirement || scopeData?.length === 0 || editablePaymentData?.length === 0 || terms?.length === 0) && (
               <Typography variant="body2" color="error" sx={{ mt: 1 }}>
              {!societySelected && 'Please select a society. '}
              {!serviceRequirement && 'Please specify the service requirement. '}
              {scopeData?.length === 0 && 'Scope data is missing. '}
              {editablePaymentData?.length === 0 && 'Payment details are incomplete. '}
              {terms?.length === 0 && 'Terms and conditions are required. '}
              {newSectionData?.length === 0 && 'Work engagement details are required. '}
              </Typography>
             )}
              </Grid>
             </Grid>
          <Grid container spacing={2}>
            <Grid item xs={12} sx={{ mt: 2 }}>
              {document?.location && (
                <TableContainer
                  style={{ overflowX: "auto", maxHeight: "90px" }}
                >
                  <Table sx={{ width: "100%" }}>
                    <TableHead>
                      <TableRow style={{ backgroundColor: "#f2f7f2" }}>
                        <TableCell
                          style={{
                            fontWeight: "bold",
                            fontSize: "10px",
                            height: "10px",
                            paddingTop: "3px",
                            paddingBottom: "3px",
                          }}
                        >
                          File Name
                        </TableCell>
                        <TableCell
                          style={{
                            fontWeight: "bold",
                            fontSize: "10px",
                            height: "10px",
                            paddingTop: "1px",
                            paddingBottom: "1px",
                          }}
                        >
                          Actions
                        </TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      <TableRow>
                        <TableCell>
                          <Typography className="data-field">
                            {document?.location &&
                              document?.location?.split("/").pop()}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <IconButton
                            onClick={() => setSelectedDocument(document)}
                            color="error"
                          >
                            <Icon icon="iconamoon:eye" />
                          </IconButton>
                        </TableCell>
                      </TableRow>
                    </TableBody>
                  </Table>
                </TableContainer>
              )}
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions
          sx={{
            position: "sticky",
            bottom: 0,
            zIndex: 10,
            backgroundColor: "background.paper",
            justifyContent: "end",
            borderTop: (theme) => `1px solid ${theme.palette.divider}`,
            p: (theme) => `${theme.spacing(1.75, 7)} !important`,
          }}
        >
          <Button onClick={onClose} variant="outlined">
            Close
          </Button>
        </DialogActions>
      </Dialog>

      <ViewSnapshotByLocation
        location={selectedDocument}
        setSelectedLocation={setSelectedDocument}
        onClose={handleDialogClose}
      />
    </>
  );
};

export default WorkOrdersViewDialog;
