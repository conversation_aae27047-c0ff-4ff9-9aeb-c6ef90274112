import { fetchIpAddress } from "./FetchIpAddress";

export function extractNonNullFields(formData, profileData) {
    const result = {};
    const member = {
      id: profileData?.chsProfileDTO?.userId,
      name: formData?.societyMemberInformation?.societyMemberName,
      contactNumber:
        formData?.societyMemberInformation?.societyMemberContactNumber,
      alternateNumber: formData?.societyMemberInformation?.alternateNumber,
      email: formData?.societyMemberInformation?.loginEmail,
      designation: formData?.societyMemberInformation?.societyMemberDesignation,
      fromDate: formData?.societyMemberInformation?.fromDate,
      toDate: formData?.societyMemberInformation?.toDate,
    };
  
    const mappings = {
      societyAddress: formData?.societyDetails?.societyAddress,
      societyCommitteeMemberInformationList: Array.isArray(
        formData?.societyMemberInformation?.societyCommitteeMemberInformationList
      )
        ? [
            ...formData.societyMemberInformation
              .societyCommitteeMemberInformationList,
          ]
        : [],
      userId: profileData?.chsProfileDTO?.userId, 
      name: formData?.societyDetails?.name,
      googleMapLocation: formData?.societyDetails?.googleMapLocation,
      enrolledDate: formData?.societyDetails?.enrolledDate,
      registeredFor: formData?.societyDetails?.registeredFor,
      roadWidth: formData?.societyDetails?.roadWidth,
      grossPlotArea: formData?.societyDetails?.grossPlotArea,
      authority: formData?.societyDetails?.authority,
      teamMember: formData?.societyDetails?.teamMember,
      plotCTSNo: formData?.societyDetails?.plotCTSNo,
      locationId: formData?.societyDetails?.locationId,
      zone: formData?.societyDetails?.zone,
      pinCode: formData?.societyDetails?.pinCode,
      societyMemberName: formData?.societyMemberInformation?.societyMemberName,
      societyMemberDesignation:
        formData?.societyMemberInformation?.societyMemberDesignation,
      societyMemberContactNumber:
        formData?.societyMemberInformation?.societyMemberContactNumber,
      loginEmail: formData?.societyMemberInformation?.loginEmail,
      alternateNumber: formData?.societyMemberInformation?.alternateNumber,
      fromDate: formData?.societyMemberInformation?.fromDate,
      toDate: formData?.societyMemberInformation?.toDate,
      bankName: formData?.businessInformation?.bankName,
      branch: formData?.businessInformation?.branch,
      accountNumber: formData?.businessInformation?.accountNumber,
      ifscCode: formData?.businessInformation?.ifscCode,
      gstNo: formData?.businessInformation?.gstNo,
      panNo: formData?.businessInformation?.panNo,
      stateName: formData?.businessInformation?.stateName,
      doYouHaveGstNo:
        formData?.businessInformation?.doYouHaveGstNo?.toUpperCase(),
      builtUpAreaResidential: formData?.landDetails?.builtUpAreaResidential,
      builtUpAreaCommercial: formData?.landDetails?.builtUpAreaCommercial,
      noOfResidence: formData?.landDetails?.noOfResidence,
      noOfCommercial: formData?.landDetails?.noOfCommercial,
      buildingAge: formData?.fsi?.buildingAge,
      fsiConsumedFsi: formData?.fsi?.fsiConsumedFsi,
      fsi_AvailableFsi: formData?.fsi?.fsi_AvailableFsi,
      fsi_PermissibleFsi: formData?.fsi?.fsi_PermissibleFsi,
      heightRestriction: formData?.fsi?.heightRestriction,
      scheme: formData?.fsi?.scheme,
      dpRestrictions: formData?.fsi?.dpRestrictions,
      litigationsOrEncroachment: formData?.fsi?.litigationsOrEncroachment,
      requirements_ExtraArea: formData?.requirements?.requirements_ExtraArea,
      requirements_Rent: formData?.requirements?.requirements_Rent,
      requirements_Corpus: formData?.requirements?.requirements_Corpus,
      notes: formData?.requirements?.notes,
      leadGivenTo: formData?.requirements?.leadGivenTo,
    };
  
    mappings.societyCommitteeMemberInformationList.push(member);
  
    for (const [key, value] of Object.entries(mappings)) {
      if (value) {
        result[key] = value;
      }
    }
  
    return result;
  }
  