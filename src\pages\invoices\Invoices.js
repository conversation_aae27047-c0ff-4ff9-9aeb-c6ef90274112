import {
  <PERSON><PERSON>,
  <PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>u<PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Tab,
  Chip,
} from "@mui/material";
import { DataGrid } from "@mui/x-data-grid";
import axios from "axios";
import { useRouter } from "next/router";
import { useContext, useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import Icon from "src/@core/components/icon";
import CustomAvatar from "src/@core/components/mui/avatar";
import authConfig from "src/configs/auth";
import { MENUS, PAGES, PERMISSIONS } from "src/constants";
import {
  getAuthorizationHeaders,
  getFileUploadPDFHeaders,
  getUrl,
} from "src/helpers/utils";
import { useAuth } from "src/hooks/useAuth";
import { useRBAC } from "src/pages/permission/RBACContext";
import InvoiceDialog from "./invoiceDialog";

import { Box } from "@mui/system";

import FallbackSpinner from "src/@core/components/spinner";
import { AuthContext } from "src/context/AuthContext";
import DeActivateDialog from "./DeActivateDialog";
import useColumns from "./InvoicesColumns";
import ViewDocumentByte from "src/@core/components/custom-components/ViewDocumentByte";
import ViewInvoiceDialog from "./view-invoice-page";
import AnchorInvoice from "./AnchorInvoice";
import ConfirmationDialog from "./ConfirmationDialog";

const Invoices = () => {
  const {
    user,
    listValues,
    invoiceId,
    setInvoiceId,
    invoiceDetails,
    setInvoiceDetails,
    getAllListValuesByListNameId,
  } = useContext(AuthContext);

  // Use States
  const {
    reset,
    formState: { errors },
  } = useForm();

  const { canMenuPage, rbacRoles } = useRBAC();
  const router = useRouter();
  const { id } = router.query;
  const { overdueInvoices } = router.query;
  const { currentWeekInvoices } = router.query;
  const { fromDate,toDate,employeeId} = router.query;

  const auth = useAuth();
  const [invoicesList, setInvoicesList] = useState([]);
  // Constants
  const rowsPerPageOptions = [10, 15, 20, 25, 50, 100];
  const [pageSize, setPageSize] = useState(rowsPerPageOptions[0]);
  const [loading, setLoading] = useState(false);
  const [page, setPage] = useState(1);
  const [rowCount, setRowCount] = useState(0);
  const [searchFilters, setSearchFilters] = useState([]);
  const [currentRow, setCurrentRow] = useState("");
  const [openDialog, setOpenDialog] = useState(false);
  const [statusList, setStatusList] = useState([]);
  const [status, setStatus] = useState(statusList[0]?.value || "");
  const [senderType, setSenderType] = useState("");
  const [recipientType, setRecipientType] = useState("");
  const [orgName, setOrgName] = useState("");
  const [recipientOrgName, setRecipientOrgName] = useState("");
  const [openDeleteDialog, setOpenDeleteDialog] = useState(false);

  const [viewPDf, setViewPDF] = useState(false);

  const [anchorEl, setAnchorEl] = useState(null);
  const [menuPosition, setMenuPosition] = useState(null);
  const [OpenViewDialog, setOpenViewDialog] = useState(false);

  const [employeesData, setEmployeesData] = useState([]);

  useEffect(() => {
    axios({
      method: "get",
      url: getUrl(authConfig.individualEndpoint) + "/employees",
      headers: getAuthorizationHeaders(authConfig.individualEmployeesMIMEType),
    })
      .then((res) => {
        setEmployeesData(res.data);
      })
      .catch((err) => console.log("error", err));
  }, []);

  const handleMenuClick = (event, params) => {
    setViewPDF(false);
    event.stopPropagation(); // Prevent the click from bubbling up
    //  event.preventDefault();  // Prevent default behavior
    setAnchorEl(event.currentTarget); // Set the anchor element to the clicked icon
    setMenuPosition({ mouseX: event.clientX, mouseY: event.clientY });
    setInvoiceId({
      ...invoiceId,
      id: params.row.id,
    });
    setCurrentRow(params.row); // Save the row details for later actions
  };

  const handleMenuClose = () => {
    setAnchorEl(null); // Close menu
    setMenuPosition(null); // Reset menu position
  };

  const onClickToggleStatus = () => {
    setOpenDeleteDialog(true);
    handleMenuClose();
  };

  const handleEditDialog = () => {
    setOpenDialog(true);
    handleMenuClose();
  };

  const handleViewPDF = () => {
    setViewPDF(true);
    handleMenuClose();
  };

  const handleViewDialog = () => {
    setOpenViewDialog(true);
    handleMenuClose();
  };
  const ClosehandleViewDialog = () => {
    setInvoiceDetails(null);
    setOpenViewDialog(false);
  };
  const [loadingConfirm, setLoadingConfirm] = useState(false);
  const handlePDFGeneration = async () => {
    setLoadingConfirm(true);
    const url =
      getUrl(authConfig.invoicesEndpoint) + "/generate/" + invoiceDetails?.id;

    const headers = getFileUploadPDFHeaders();

    try {
      const response = await axios({
        method: "post",
        url: url,
        headers: headers,
        responseType: "arraybuffer",
      });
      setOpenConfirmationDialog(false);

      if (response.status === 200) {
        const pdfBlob = new Blob([response?.data], { type: "application/pdf" });
        saveAs(pdfBlob, "Invoice.pdf");
      } else {
        console.error("Document object is not available.");
      }
    } catch (error) {
      console.error("Error fetching users:", error);
    } finally {
      setLoadingConfirm(false);
    }
  };

  const handleCloseDeleteDialog = () => {
    setOpenDeleteDialog(false);
    fetchInvoices(page, pageSize, searchFilters, status);
  };

  const [invoiceCode, setInvoiceCode] = useState();
  const [dialogOpen, setDialogOpen] = useState(false);

  const handleClick = (event, row) => {
    event.preventDefault(); // Prevent the default link behavior
    setDialogOpen(true);
    setCurrentRow(row);
  };

  const handleClose = () => {
    setDialogOpen(false);
    setCurrentRow(null);
  };
  const [openConfirmationDialog, setOpenConfirmationDialog] = useState(false);

  const handleConfirmationDialog = () => {
    setOpenConfirmationDialog(true);
    handleMenuClose();
  };

  const columns = useColumns({ handleClick }).concat([
    {
      field: "actions",
      headerName: "Actions",
      width: 100,
      sortable: false,
      renderCell: (params) => (
        <>
          <CustomAvatar
            skin="light"
            variant="rounded"
            sx={{
              mr: { xs: 2, lg: 4 },
              width: 34,
              height: 34,
              cursor: "pointer",
            }}
            onClick={(event) => handleMenuClick(event, params)}
          >
            <Icon icon="bi:three-dots-vertical" />
          </CustomAvatar>

          <Menu
            anchorEl={anchorEl}
            open={Boolean(anchorEl)}
            onClose={handleMenuClose}
            anchorReference="anchorPosition"
            anchorPosition={
              menuPosition
                ? { top: menuPosition.mouseY, left: menuPosition.mouseX }
                : undefined
            }
          >
            <MenuItem onClick={handleViewDialog}>View</MenuItem>
            <MenuItem onClick={handleConfirmationDialog}>Download</MenuItem>
            <MenuItem onClick={handleViewPDF}>View PDF</MenuItem>
            {user.organisationCategory === "EMPLOYEE" && (
              <>
                <MenuItem onClick={handleEditDialog}>Edit</MenuItem>
                <MenuItem onClick={onClickToggleStatus}>
                  {currentRow?.isActive ? "Deactivate" : "Activate"}
                </MenuItem>
              </>
            )}
          </Menu>
        </>
      ),
    },
  ]);

  useEffect(() => {
    fetchInvoices(page, pageSize, searchFilters, status);
  }, [page, pageSize, searchFilters, status]);

  useEffect(() => {
    // Set the default status to the first item in the statusList
    if (statusList?.length > 0) {
      setStatus(statusList[0].value);
      if (overdueInvoices || currentWeekInvoices) {
        setStatus(authConfig.invoicesOverdueId);
      }
    }
  }, [statusList,overdueInvoices,currentWeekInvoices]);

  const fetchInvoices = async (
    currentPage,
    currentPageSize,
    searchFilters,
    status
  ) => {
    setLoading(true);

    let url;
    if (user?.roleId === authConfig?.superAdminRoleId) {
      url = getUrl(authConfig.invoicesEndpoint) + "/admin/all";
    } else {
      url = getUrl(authConfig.invoicesEndpoint) + "/all";
    }

    let headers;

    if (user?.roleId === authConfig.superAdminRoleId) {
      headers = getAuthorizationHeaders({
        contentType: authConfig.INVOICES_GET_ALL_BY_ADMIN_REQ_V1,
        accept: authConfig.INVOICES_GET_ALL_BY_ADMIN_RES_V1,
      });
    } else {
      headers = getAuthorizationHeaders({
        contentType: authConfig.INVOICES_GET_ALL_BY_SP_CHS_EMPLOYEE_REQ_V1,
        accept: authConfig.INVOICES_GET_ALL_BY_SP_CHS_EMPLOYEE_RES_V1,
      });
    }

    const data = {
      page: currentPage,
      pageSize: currentPageSize,
      statusId: status,
    };

    searchFilters?.forEach((filter) => {
      const key = filter.key;
      data[key] = filter.value;
    });

    try {
      const response = await axios({
        method: "post",
        url: url,
        headers: headers,
        data: data,
      });

      if (response.data) {
        setRowCount(response.data?.count || 0);
        setInvoicesList(response.data?.invoicesResponseDTOList || []);
        setInvoiceCode(response.data.invoicesResponseDTOList[0]?.systemCode);
      } else {
        console.error("Unexpected API response format:", response);
      }
    } catch (error) {
      console.error("Error fetching users:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleError = (error) => console.error("Error:", error);

  useEffect(() => {
    if (!!authConfig) {
      getAllListValuesByListNameId(
        authConfig.invoiceStatus,
        (data) => {
          const order = ["Open", "Paid", "Over Due", "Cancelled"];

          const listValues = data?.listValues?.map((item) => ({
            value: item.id,
            key: item.listValue,
          }));

          const sortedListValues = listValues.sort((a, b) => {
            const indexA = order.findIndex(
              (status) => status.toLowerCase() === a.key.toLowerCase()
            );
            const indexB = order.findIndex(
              (status) => status.toLowerCase() === b.key.toLowerCase()
            );

            // If status is not found in the order array, place it after "Cancelled"
            const adjustedIndexA = indexA === -1 ? order.length : indexA;
            const adjustedIndexB = indexB === -1 ? order.length : indexB;

            return adjustedIndexA - adjustedIndexB;
          });

          setStatusList(sortedListValues);
        },
        handleError
      );
    }
  }, [authConfig]);

  const handleOpenDialog = () => {
    setInvoiceDetails(null);
    setStatus("");
    setSenderType("");
    setRecipientType("");
    setOrgName("");
    setRecipientOrgName("");
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    reset();
    setStatus(statusList[0].value);
    setOpenDialog(false);
  };

  const handlePageChange = (direction) => {
    if (direction === page) {
      setPage(page + 1);
    } else {
      setPage(page - 1);
    }
  };

  const handlePageSizeChange = (params) => {
    if (params) {
      setPageSize(params);
      setPage(1);
    }
  };
  const handleTabChange = (event, newValue) => {
    if (status !== newValue) {
      setStatus(newValue);
      setPage(1); // Reset to the first page only when the tab actually changes
    }
  };


  const canAccessInvoices = (requiredPermission) =>
    canMenuPage(MENUS.LEFT, PAGES.INVOICES, requiredPermission);

  useEffect(() => {
    if (rbacRoles != null && rbacRoles.length > 0) {
      if (!canAccessInvoices(PERMISSIONS.READ)) {
        router.push("/401");
      }
    }
  }, [rbacRoles]);

  useEffect(() => {
    if (id) {
      setSearchFilters([{ key: "idFilter", value: id }]); // Wrap status in an array
    }
  }, [id]);

  useEffect(() => {
    if (currentWeekInvoices) {
      setSearchFilters([
        { key: "currentWeekOverDueInvoice", value: currentWeekInvoices },
      ]);
    }
  }, [currentWeekInvoices]);

  useEffect(() => {
        if (fromDate && toDate) {
    
          setSearchFilters((prevFilters) => {
            const newFilters = [...prevFilters];
      
            newFilters.push({ key: "fromDate", value: fromDate });
            newFilters.push({ key: "toDate", value: toDate });
           
            if(employeeId){
              newFilters.push({ key: "assignedToEmployeeId", value: employeeId });
            }
      
            return newFilters;
          });
        }
      }, [fromDate, toDate,employeeId]);
  
      const clearFilterSR = () => {
        setSearchFilters((prevFilters) =>
          prevFilters.filter((filter) => filter.key !== "fromDate")
        );
    
        setSearchFilters((prevFilters) =>
          prevFilters.filter((filter) => filter.key !== "toDate")
        );
    
        setSearchFilters((prevFilters) =>
          prevFilters.filter((filter) => filter.key !== "assignedToEmployeeId")
        );
    
        // Update the URL to remove the status query parameter
        router.replace(
          {
            // pathname: router.pathname,
            query: {}, // Clear all query parameters
          },
          undefined,
          { shallow: false } // Allow page reload if necessary
        );
      };


  const handleIdClearFilter = () => {
    // Clear the status filter from selected filters
    setSearchFilters((prevFilters) =>
      prevFilters.filter((filter) => filter.key !== "idFilter")
    );

    // Update the URL to remove the status query parameter
    router.replace(
      {
        // pathname: router.pathname,
        query: {}, // Clear all query parameters
      },
      undefined,
      { shallow: false } // Allow page reload if necessary
    );
  };

  const handleCurrentWeekClearFilter = () => {
    setSearchFilters((prevFilters) =>
      prevFilters.filter((filter) => filter.key !== "currentWeekOverDueInvoice")
    );

    // Update the URL to remove the zone query parameter
    router.replace(
      {
        // pathname: router.pathname,
        query: {}, // Clear all query parameters
      },
      undefined,
      { shallow: false } // Allow page reload if necessary
    );
  };

  if (canAccessInvoices(PERMISSIONS.READ)) {
    return (
      <>
        <div>
          {/* {user.organisationCategory === "EMPLOYEE" && ( */}
            <>
              <Box
                sx={{
                  py: 3,
                  px: 6,
                  rowGap: 2,
                  columnGap: 4,
                  display: "flex",
                  flexWrap: "wrap",
                  alignItems: "center",
                  justifyContent: "space-between",
                }}
              >
                <Grid container spacing={3} alignItems="center">
                  <Grid item xs={12} sm={4} sx={{ textAlign: "flex-start" }}>
                    <Typography variant="h6">Invoices</Typography>
                  </Grid>

                  <Grid item xs={12} sm={8}>
                    <Grid
                      container
                      spacing={2}
                      alignItems="center"
                      justifyContent="flex-end"
                    >
                      {user?.organisationCategory === "EMPLOYEE" && (
                      <Grid item xs="auto" sm="auto">
                        <Button
                          variant="contained"
                          sx={{ textTransform: "none" }}
                          onClick={handleOpenDialog}
                        >
                          + Create Invoice
                        </Button>
                      </Grid>
                      )} 
                    </Grid>
                  </Grid>
                </Grid>
              </Box>
              <Divider />
            </>
          {/* )} */}

          <Tabs
            value={status}
            onChange={handleTabChange}
            indicatorColor="primary"
            textColor="primary"
            variant="scrollable"
            scrollButtons="auto"
          >
            {/* Dynamically Rendered Tabs, including "All" */}
            {statusList?.map((item) => (
              <Tab key={item.value} label={item.key} value={item.value} />
            ))}
          </Tabs>
          <CardContent>
            <div style={{ fontSize: "13px", marginBottom: "2px" }}></div>
            {id && (
              <Chip
                label={<>Recently generated Invoice</>}
                color="primary"
                variant="outlined"
                sx={{ mb: 3 }}
                onDelete={handleIdClearFilter}
              />
            )}

            {currentWeekInvoices && (
              <Chip
                label={<> Invoices generated this week </>}
                color="primary"
                variant="outlined"
                sx={{ mb: 3 }}
                onDelete={handleCurrentWeekClearFilter}
              />
            )}

{(fromDate && toDate) && (
                         <Chip
                         label={
                           <>
                             <Typography
                               component="span"
                               sx={{ fontWeight: 'bold', color: 'primary.main' }}
                             >
        
                              
                               {(() => {
                              
                                if (employeeId) return `Consolidated Invoices by ${
                                  employeesData?.find((item) => item.id === employeeId)?.name ||
                                  ""
                                }`;
                              else return `Total consolidated Invoices from ${fromDate} to ${toDate}`
                              
                            })()}
                              
                             </Typography>{' '}
                           </>
                         }
                         color="primary"
                         variant="outlined"
                         sx={{ mb: 3 }}
                         onDelete={clearFilterSR}
                       />      
                        )}

            <div style={{ height: 380, width: "100%" }}>
              {loading ? (
                <Box
                  display="flex"
                  justifyContent="center"
                  alignItems="center"
                  height="60vh"
                >
                  <FallbackSpinner />
                </Box>
              ) : (
                <DataGrid
                  rows={invoicesList || []}
                  columns={columns}
                  pagination
                  pageSize={pageSize}
                  page={page - 1}
                  rowsPerPageOptions={
                    invoicesList.length > 0 ? rowsPerPageOptions : []
                  }
                  rowCount={rowCount}
                  paginationMode="server"
                  onPageChange={handlePageChange}
                  onPageSizeChange={handlePageSizeChange}
                  rowHeight={38}
                  headerHeight={38}
                  components={{
                    NoRowsOverlay: () => (
                      <Typography
                        variant="body1"
                        align="center"
                        sx={{ marginTop: "120px" }}
                      >
                        {invoicesList?.length === 0 ? "No Data" : "No Rows"}
                      </Typography>
                    ),
                  }}
                />
              )}
            </div>
          </CardContent>

          <InvoiceDialog
            open={openDialog}
            onClose={handleCloseDialog}
            data={invoiceDetails}
            statusList={statusList}
            fetchInvoices={fetchInvoices}
            page={page}
            pageSize={pageSize}
            searchFilters={searchFilters}
            status={status}
            setStatus={setStatus}
            senderType={senderType}
            setSenderType={setSenderType}
            recipientType={recipientType}
            setRecipientType={setRecipientType}
            orgName={orgName}
            setOrgName={setOrgName}
            recipientOrgName={recipientOrgName}
            setRecipientOrgName={setRecipientOrgName}
            invoiceCode={invoiceCode}
          />

          <DeActivateDialog
            open={openDeleteDialog}
            onClose={handleCloseDeleteDialog}
            data={currentRow}
          />
          {viewPDf && <ViewDocumentByte invoiceDetails={invoiceDetails} />}
        </div>

        <ViewInvoiceDialog
          open={OpenViewDialog}
          onClose={ClosehandleViewDialog}
        />

        <AnchorInvoice
          open={dialogOpen}
          onClose={handleClose}
          data={currentRow}
        />

        <ConfirmationDialog
          open={openConfirmationDialog}
          onClose={() => setOpenConfirmationDialog(false)}
          onConfirm={handlePDFGeneration}
          loading={loadingConfirm}
        />
      </>
    );
  } else {
    return null;
  }
};

export default Invoices;
