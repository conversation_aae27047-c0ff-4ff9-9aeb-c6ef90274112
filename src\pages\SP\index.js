import { Card } from "@mui/material";
import { useRBAC } from "src/pages/permission/RBACContext";
import { MENUS, PAGES, PERMISSIONS } from "src/constants";
import { useRouter } from "next/router";
import { useEffect, useState } from "react";
import NavTabsProfiles from "src/@core/components/custom-components/NavTabsProfiles";
import authConfig from "src/configs/auth";
import LeadSnapshots from "../leads/snapshots";
import UsersOverView from "./UsersOverview";
import Email from "../storedEmails";

const AllUsers = () => {
  const { canMenuPage, rbacRoles, canMenuPageSection } = useRBAC();
  const router = useRouter();

  // Track the active tab using state
  const [activeTab, setActiveTab] = useState(""); // Default to "All"

  useEffect(()=>{
    setActiveTab("onboarded")
  },[])

  const canAccessServiceProvider = (requiredPermission) =>
    canMenuPageSection(MENUS.LEFT, PAGES.GROWTH, PAGES.SERVICE_PROVIDERS, requiredPermission);

  // Update the active tab based on the query parameter
  useEffect(() => {
    if (router?.query?.tab) {
      setActiveTab(router?.query?.tab);
    }
  }, [router.query.tab]);

  useEffect(() => {
    if (rbacRoles != null && rbacRoles.length > 0) {
      if (!canAccessServiceProvider(PERMISSIONS.READ)) {
        router.push("/401");
      }
    }
  }, [rbacRoles]);

  if (canAccessServiceProvider(PERMISSIONS.READ)) {
    return (
      <>
        <NavTabsProfiles
          activeTab={activeTab}
          onTabChange={(newTab) => setActiveTab(newTab)} 
          tabContent1={
            <>
              <UsersOverView />
            </>
          }
          tabContent2={
            <>
              <LeadSnapshots category={"SERVICE_PROVIDER"} type={"SP"} setActiveTab={setActiveTab}/>
            </>
          }
          tabContent3 = {
            <>
              <Email role={authConfig.serviceProviderRoleId}/>
            </>
          }
        />
      </>
    );
  } else {
    return null;
  }
};

export default AllUsers;