import React, { useEffect, useState, useContext } from "react";
import { useForm, Controller } from "react-hook-form";
import { useMediaQuery, useTheme } from "@mui/material";
import {
  Box,
  Button,
  Grid,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TextField,
  Typography,
  IconButton,
} from "@mui/material";
import Icon from "src/@core/components/icon";
import { AuthContext } from "src/context/AuthContext";

const TestimonialForm = ({ data, onCancel, userData }) => {
  const { patchMicrosite, user } = useContext(AuthContext);
  const { control, handleSubmit, reset, setValue, unregister, getValues } =
    useForm();
  const [entries, setEntries] = useState([]);
  const [fieldChanged, setFieldChanged] = useState(false);

  useEffect(() => {
    (entries ?? []).forEach((entry, index) => {
      setValue(`testimonials[${index}].name`, entry.name);
      setValue(`testimonials[${index}].designation`, entry.designation);
      setValue(`testimonials[${index}].description`, entry.description);
    });
  }, [entries, setValue]);

  useEffect(() => {
    if (data && data?.testimonialsList) {
      setEntries(data?.testimonialsList);
    }
  }, [data]);

  const addEntry = () => {
    const currentValues = getValues();
    const currentEntries = currentValues.testimonials || [];
    setEntries([...currentEntries, { name: "", description: "" }]);
  };

  const removeEntry = (index) => {
    const newEntries = entries.filter((_, i) => i !== index);
    setEntries(newEntries);
    unregister(`testimonials[${index}].name`);
    unregister(`testimonials[${index}].designation`);
    unregister(`testimonials[${index}].description`);
    reset({
      ...getValues(),
      testimonials: newEntries,
    });
    setFieldChanged(true);
  };

  const onSubmit = async (data) => {
    const testimonialsData = data.testimonials.filter(
      (entry) =>
        entry.name !== "" ||
        entry.designation !== "" ||
        entry.description !== ""
    );

    const userUniqueId =
      userData && userData.id !== undefined ? userData.organisationId : user.orgId;

    await patchMicrosite(
      { testimonialsList: testimonialsData },
      userUniqueId,
      () => {
        console.log("Success testimonials.");
      },
      () => {
        console.error("testimonials failed");
      }
    );

    onCancel();
    reset();
  };

  const theme = useTheme(); // Access current theme
  const isSmallScreen = useMediaQuery(theme.breakpoints.down("md"));
  const isExtraSmallScreen = useMediaQuery("(max-width:360px)");

  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <Grid
        container
        justifyContent={
          entries.length === 0 ? { lg: "space-between", md: "space-between", sm: "space-between" } : "flex-end"
        }
        flexDirection={
          entries.length === 0 ? { xs: "column", lg: "row", md: "row", sm: "row" } : ""
        }        alignItems="center"
        sx={{
          mt: { xs: 2, lg: 2 },
          mb: { xs: 2, lg: 4 },
        }}
      >
        {entries.length === 0 && (
          <Typography
            style={{
              textAlign: "center",
              flex: 1,
              marginLeft: isSmallScreen ? "" : "4rem",
            }}
          >
            Click on ADD to add Testimonials
          </Typography>
        )}
        <Button
        id="addTestimonial"
          onClick={addEntry}
          color="primary"
          variant="contained"
          sx={{
            mb: { xs: 2, lg: 0 },
            mt: { xs: 2, lg: 2 },
            alignSelf: isExtraSmallScreen || isSmallScreen ? "flex-end" : "auto",
          }}
        >
          Add
        </Button>
      </Grid>

      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            {entries.length > 0 && (
              <TableRow style={{ backgroundColor: "#f2f7f2" }}>
                <TableCell sx={{ padding: "5px" }}>Name</TableCell>
                <TableCell sx={{ padding: "5px" }}>Designation</TableCell>
                <TableCell sx={{ padding: "5px" }}>Testimonials</TableCell>
                <TableCell sx={{ padding: "5px" }}>Delete</TableCell>
              </TableRow>
            )}
          </TableHead>
          <TableBody>
            {entries?.map((entry, index) => (
              <TableRow key={index}>
                <TableCell>
                  <Controller
                    name={`testimonials[${index}].name`}
                    control={control}
                    defaultValue={entry.name}
                    rules={{ required: "Name is required" }}
                    render={({ field, fieldState }) => (
                      <TextField
                      id="name"
                        {...field}
                        label="Name"
                        variant="outlined"
                        size="small"
                        fullWidth
                        error={Boolean(fieldState?.error?.message)}
                        helperText={fieldState?.error?.message || " "}
                        onChange={(e) => {
                          field.onChange(e);
                          setFieldChanged(true);
                        }}
                        sx={{ width: isSmallScreen ? "100px" : "100%" }}
                      />
                    )}
                  />
                </TableCell>
                <TableCell>
                  <Controller
                    name={`testimonials[${index}].designation`}
                    control={control}
                    defaultValue={entry.designation}
                    rules={{ required: "Designation is required" }}
                    render={({ field, fieldState }) => (
                      <TextField
                      id="designation"
                        {...field}
                        label="Designation"
                        variant="outlined"
                        size="small"
                        fullWidth
                        error={Boolean(fieldState?.error?.message)}
                        helperText={fieldState?.error?.message || " "}
                        onChange={(e) => {
                          field.onChange(e);
                          setFieldChanged(true);
                        }}
                        sx={{ width: isSmallScreen ? "200px" : "100%" }}
                      />
                    )}
                  />
                </TableCell>
                <TableCell>
                  <Controller
                    name={`testimonials[${index}].description`}
                    control={control}
                    defaultValue={entry.description}
                    rules={{ required: "Testimonial is required" }}
                    render={({ field, fieldState }) => (
                      <TextField
                      id="description"
                        {...field}
                        label="Testimonial"
                        size="small"
                        multiline
                        rows={4}
                        variant="outlined"
                        fullWidth
                        error={Boolean(fieldState?.error?.message)}
                        helperText={fieldState?.error?.message || " "}
                        onChange={(e) => {
                          field.onChange(e);
                          setFieldChanged(true);
                        }}
                        sx={{ width: isSmallScreen ? "200px" : "100%" }}
                      />
                    )}
                  />
                </TableCell>
                <TableCell>
                  <IconButton onClick={() => removeEntry(index)} color="error">
                    <Icon icon="iconamoon:trash" />
                  </IconButton>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
      <Grid
        container
        justifyContent="center"
        sx={{
          mt: { xs: 2, lg: 4 },
          mb: { xs: 2, lg: 1 },
        }}
      >
        <Box
          sx={{
            display: "flex",
            gap: isExtraSmallScreen ? "1rem" : 0,
            marginTop: '5px'
          }}
        >
          <Button
            size="medium"
            sx={{ mr: isExtraSmallScreen ? 0 : 3 }}
            variant="outlined"
            color="primary"
            onClick={() => onCancel()}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            color="primary"
            variant="contained"
            disabled={!fieldChanged}
          >
            Submit
          </Button>
        </Box>
      </Grid>
    </form>
  );
};

export default TestimonialForm;
