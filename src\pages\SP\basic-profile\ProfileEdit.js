// ** React Imports
import { useContext, useEffect, useState } from "react";

// ** MUI Imports
import Button from "@mui/material/Button";
import FormControl from "@mui/material/FormControl";
import Grid from "@mui/material/Grid";
import TextField from "@mui/material/TextField";

// ** Third Party Imports

import { Controller, useForm, useWatch } from "react-hook-form";
import toast from "react-hot-toast";
// ** Hooks
// ** Icon Imports
import CheckCircleOutlineIcon from "@mui/icons-material/CheckCircleOutline";
import {
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  Divider,
  FormHelperText,
  IconButton,
  InputLabel,
  MenuItem,
  Select,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  Typography,
} from "@mui/material";
import { Box } from "@mui/system";
import DeleteConfirmationDialog from "src/@core/components/custom-components/DeleteConfirmationDialog";
import DocumentUploadDialog from "src/@core/components/custom-components/DocumentDialogUpload";
import MobileNumberValidation from "src/@core/components/custom-components/MobileNumberValidation";
import NameTextField from "src/@core/components/custom-components/NameTextField";
import ViewDialogByLocation from "src/@core/components/custom-components/ViewDialogByLocation";
import Icon from "src/@core/components/icon";
import authConfig from "src/configs/auth";
import { AuthContext } from "src/context/AuthContext";
import { useAuth } from "src/hooks/useAuth";

import MultiSelectAutoComplete from "src/@core/components/custom-components/MultiSelectAutoComplete";
import SelectAutoComplete from "src/@core/components/custom-components/SelectAutoComplete";
import SelectAutoCompleteLocation from "src/@core/components/custom-components/SelectAutoCompleteLocation";
import CompanyAddressInformation from "../CompanyAddressInformation";
import SPMemberInformation from "../SPMemberInformation";
import { useRouter } from "next/router";
import { fetchIpAddress } from "src/@core/components/custom-components/FetchIpAddress";


const ProfileEdit = ({
  onCancel,
  formData,
  userData,
  employeesData,
  handleAssignedToChange,
  assignedTo,
  assignedToName,
  createdBy,
  areaOfOperationList,
  typeOfClientServedOptions
}) => {
  const {
    uploadDocuments,
    getAllDocuments,
    documentMicrositeDelete,
    allCategories,
    allSubCategories,
    user,
    shortFormData,
    getAllListValuesByListNameId,
    listValues,
  } = useContext(AuthContext);
  
  const [entityType, setEntityType] = useState(formData?.entityType);
    const router = useRouter();


  useEffect(() => {
    const handleRouteChange = () => {
      onCancel(); // Call onCancel when route is changing
    };

    router.events.on("routeChangeStart", handleRouteChange);

    return () => {
      router.events.off("routeChangeStart", handleRouteChange);
    };
  }, [router, onCancel]);
  
  const [noOfSalesTeamMembers, setNoOfSalesTeamMembers] = useState(
    formData?.noOfSalesTeamMembers
  );

  const [salesTeamMembers, setSalesTeamMembers] = useState([]);

  const [companyTypeOptions, setCompanyTypeOptions] = useState([]);
  const [referenceOptions, setReferenceOptions] = useState([]);
  const [referralNameOptions, setReferralNameOptions] = useState([]);
  const [addresses, setAddresses] = useState([]);
  const [spMembers, setSpMembers] = useState([]);
  const [companyType, setCompanyType] = useState(formData?.companyType);

  const [modalPopup, setModalPopup] = useState(false);
  const [selectedFiles, setSelectedFiles] = useState([]);
  const [loading, setLoading] = useState(false);
  const [disableButton, setDisableButton] = useState(false);

  const [selectedCompany, setSelectedCompany] = useState(false);
  const [confirmDeleteDialogOpen, setConfirmDeleteDialogOpen] = useState(false);
  const [companyToDelete, setCompanyToDelete] = useState(false);
  const [dailogSuccess, setDialogSuccess] = useState(false);

  const [documents, setDocuments] = useState([]);

  //Hooks
  const auth = useAuth();

  const {
    register,
    handleSubmit,
    setError,
    control,
    clearErrors,
    setValue,
    formState: { errors },
  } = useForm();

  const [allServicesList, setAllServicesList] = useState([]);

  useEffect(() => {
    setValue("selectedLocationId", formData?.locationId);
    setValue("selectedDesignationId", formData?.designation);
    setValue("gstNo",formData?.gstNo)
    setGstOption(formData?.doYouHaveGst)
    if (formData?.servicesProvided && allServicesList.length > 0) {
      const mappedServices = formData.servicesProvided?.map((id) =>
        allServicesList.find((service) => service.value === id)
      );
      setValue("servicesProvided", mappedServices);
    }
    if (formData?.areaOfOperation && areaOfOperationList.length > 0) {
      const mappedAreaOfOperation = formData.areaOfOperation?.map((id) =>
        areaOfOperationList.find((service) => service.value === id)
      );
      setValue("areaOfOperation", mappedAreaOfOperation);
    }
    if (formData?.typeOfClientServed && typeOfClientServedOptions.length > 0) {
      const mappedClients = formData.typeOfClientServed?.map((id) =>
        typeOfClientServedOptions.find((service) => service.value === id)
      );
      setValue("typeOfClientServed", mappedClients);
    }
    setSelectedYearsOfExperienceNumber(formData.yearsOfExperience || "");
    setSelectedYearsOfExperienceId(formData.yearsOfExperienceId || "");
    setSelectedPortalsRegisteredId(formData.portalsRegistered || "");
    const portalRegister = portalsRegisteredData?.find(
      (portalsRegistered) => portalsRegistered?.value === formData?.portalsRegistered
    );
    setSelectedPortalsRegisteredName(portalRegister?.key)
    setValue("anyOtherPortalRegistered", formData?.anyOtherPortalRegistered || "");
  }, [formData, allServicesList]);

  const [selectedLocationId, setSelectedLocationId] = useState(
    formData?.locationId
  );

  const [selectedYearsOfExperienceId, setSelectedYearsOfExperienceId] =
    useState(formData?.yearsOfExperienceId || "");

  const [selectedPortalsRegisteredId, setSelectedPortalsRegisteredId] =
    useState("");

  const [selectedPortalsRegisteredName, setSelectedPortalsRegisteredName] =
    useState("");

  const [leadStatus, setLeadStatus] = useState(formData?.leadStatus);

  const [leadPriority, setLeadPriority] = useState(formData?.leadPriority);

  const handleLeadStatusChange = (event) => {
    const selectedId = event.target.value;
    setLeadStatus(selectedId);
  };

  const handleLeadPriorityChange = (event) => {
    const selectedId = event.target.value;
    setLeadPriority(selectedId);
  };

  const [sourceGroup, setSourceGroup] = useState(formData?.sourceGroup);

  const [subSourceGroup, setSubSourceGroup] = useState(
    formData?.subSourceGroup
  );

  const [locationsData, setLocationsData] = useState(null);

  const [selectedLocationName, setSelectedLocationName] = useState(null);

  const [yearsOfExperienceData, setYearsOfExperienceData] = useState(null);
  const [selectedYearsOfExperienceNumber, setSelectedYearsOfExperienceNumber] =
    useState(null);

  const [portalsRegisteredData, setPortalsRegisteredData] = useState(null);

  const [selectedDesignationId, setSelectedDesignationId] = useState(
    formData?.designation
  );
  const [designationsData, setDesignationsData] = useState(null);
  const [addressTypeList, setAddressTypeList] = useState(null);


  const [reference, setReference] = useState(formData?.referenceType);
  const [referralName, setReferralName] = useState(formData?.referralName);
  const [curatedBy, setCuratedBy] = useState(formData?.curatedBy);

  const handleReferenceTypeChange = (e) => {
    const selectedValue = e.target.value;
    setReference(selectedValue);
  };

  const handleSelectDesignationChange = (event) => {
    const selectedId = event.target.value;
    setSelectedDesignationId(selectedId);
  };

  const handleSelectChange = (event) => {
    const selectedId = event.target.value;
    setSelectedLocationId(selectedId);
    const selectedLocation = locationsData.find(
      (location) => location.id === selectedId
    );
    const locationName = selectedLocation ? selectedLocation.listValue : "";
    setSelectedLocationName(locationName);
  };

  const handleYearsOfExperienceChange = (event) => {
    const selectedId = event.target.value;
    setSelectedYearsOfExperienceId(selectedId);
    const selectedYearsOfExperience = yearsOfExperienceData.find(
      (yearsOfExperience) => yearsOfExperience.id === selectedId
    );
    const yearsOfExperienceNumbers = selectedYearsOfExperience
      ? selectedYearsOfExperience.listValue
      : "";
    setSelectedYearsOfExperienceNumber(yearsOfExperienceNumbers);
  };

  const handlePortalsRegisteredChange = (event) => {
    const selectedId = event.target.value;
    setSelectedPortalsRegisteredId(selectedId);
    const selectedPortalsRegistered = portalsRegisteredData.find(
      (portalsRegistered) => portalsRegistered?.value === selectedId
    );
    
    setSelectedPortalsRegisteredName(selectedPortalsRegistered?.key);
 

  if (selectedPortalsRegistered?.key === "any other") {
    setValue("anyOtherPortalRegistered", ""); // update form value
  } else {selectedPortalsRegisteredName
    setValue("anyOtherPortalRegistered", null); // reset form value
  }
  };

  const handleYearsOfExperienceSuccess = (data) => {
    setYearsOfExperienceData(data?.listValues);
  };

  const handleError = (error) => {
    console.error("Basic profile: All Services:", error);
  };

  const handleClose = () => {
    setDialogSuccess(false);
  };

  const handleDialogClose = () => {
    setSelectedCompany(null);
  };

  const handleViewIconClick = (company) => {
    setSelectedCompany(company);
  };

  const uniqueId =  userData && userData.organisationId !== undefined ? userData.organisationId : user.orgId;
  async function handleDelete() {
    const parts = companyToDelete.split("/");
    const documentId = parts[parts.length - 2];
    await documentMicrositeDelete(uniqueId,documentId,"COMPANY");
    setSelectedCompany(null);
    setConfirmDeleteDialogOpen(false);
    fetchAllDocuments();
  }

  const fetchAllDocuments = async () => {
    if (allCategories.length > 0 && allSubCategories.length > 0) {
      let allDocs;
      if(user?.organisationCategory === "EMPLOYEE"){
        allDocs = await getAllDocuments(documentJson,false);
      }else{
        allDocs = await getAllDocuments(documentJson,true);
      }
       

      setDocuments(allDocs);
    }
  };

  useEffect(() => {
    fetchAllDocuments();
  }, []);

  useEffect(() => {
    if (companyType === "INDIVIDUAL") {
      setValue("companyName", ""); // Clear the Company Name field
    }
  }, [companyType, setValue]);

  useEffect(() => {
    if (!!authConfig) {
      getAllListValuesByListNameId(
        authConfig.designationSPId,
        (data) =>
          setDesignationsData(
            data?.listValues?.map((item) => ({
              value: item.id,
              key: item.listValue,
            }))
          ),
        handleError
      );
      
      getAllListValuesByListNameId(
        authConfig.addressTypeId,
        (data) =>
          setAddressTypeList(
            data?.listValues?.map((item) => ({
              value: item.id,
              key: item.listValue,
            }))
          ),
        handleError
      );
      getAllListValuesByListNameId(
        authConfig.allServicesListNameId,
        (data) =>
          setAllServicesList(
            data?.listValues?.map((item) => ({
              value: item.id,
              key: item.listValue,
            }))
          ),
        handleError
      );
      getAllListValuesByListNameId(
        authConfig.locationlistNameId,
        (data) =>
          setLocationsData(
            data?.listValues?.map((item) => ({
              value: item.id,
              key: item.listValue,
            }))
          ),
        handleError
      );
      getAllListValuesByListNameId(
        authConfig.yearsOfExperienceListNameId,
        handleYearsOfExperienceSuccess,
        handleError
      );
      getAllListValuesByListNameId(
        authConfig.portalsRegisteredListNameId,
        (data) =>
          setPortalsRegisteredData(
            data?.listValues?.map((item) => ({
              value: item.id,
              key: item.listValue,
            }))
          ),
        handleError
      );
      getAllListValuesByListNameId(
        authConfig.salesTeamMembers,
        (data) =>
          setSalesTeamMembers(
            data?.listValues?.map((item) => ({
              value: item.id,
              key: item.listValue,
            }))
          ),
        handleError
      );
      getAllListValuesByListNameId(
        authConfig.companyType,
        (data) =>
          setCompanyTypeOptions(
            data?.listValues?.map((item) => ({
              value: item.id,
              key: item.listValue,
            }))
          ),
        handleError
      );
      getAllListValuesByListNameId(
        authConfig.referralType,
        (data) =>
          setReferenceOptions(
            data?.listValues?.map((item) => ({
              value: item.id,
              key: item.listValue,
            }))
          ),
        handleError
      );
      getAllListValuesByListNameId(
        authConfig.referralName,
        (data) =>
          setReferralNameOptions(
            data?.listValues?.map((item) => ({
              value: item.id,
              key: item.listValue,
            }))
          ),
        handleError
      );
    }
  }, [authConfig, setValue]);
  const onFormInvalid = (toastError) => {
    Object.keys(toastError).forEach((error) => {
      if (toastError[error].message.length > 0) {
        toast.error(toastError[error].message);
      }
    });
  };


  const handleGroupDataSuccess = (data) => {
    setGroupData(data?.listValues);
  };


  const [leadStatusData, setLeadStatusData] = useState(null);
  const [leadPriorityData, setLeadPriorityData] = useState(null);
  const [groupData, setGroupData] = useState(null);
  const [subGroupData, setSubGroupData] = useState(null);

  // const gstOption = useWatch({ control, name: "gstOption" }); // Add useWatch to monitor gstOption
  const [gstOption,setGstOption] = useState("");
  const watchServicesProvided = useWatch({ control, name: "servicesProvided" });


  const servicesProvidedValues = watchServicesProvided?.map(
    (id) => allServicesList.find((service) => service.value === id?.value)?.key
  );


  useEffect(() => {
    if (!!authConfig) {
      getAllListValuesByListNameId(
        authConfig.leadPriorityListNamesId,
        (data) =>
          setLeadPriorityData(
            data?.listValues?.map((item) => ({
              value: item.id,
              key: item.listValue,
            }))
          ),
        handleError
      );

      getAllListValuesByListNameId(
        authConfig.groupDataListNameId,
        handleGroupDataSuccess,
        handleError
      );

      getAllListValuesByListNameId(
        authConfig.leadStatusListNamesId,
        (data) =>
          setLeadStatusData(
            data?.listValues
              .map((item) => ({
                value: item.id,
                key: item.listValue,
              }))
          ),
        handleError
      );
    }
  }, [authConfig]);
  const [isSubmitting, setIsSubmitting] = useState(false);
   
  async function submit(data) {
    setIsSubmitting(true); // Disable button

     const ipAddress = await fetchIpAddress();
    try { 
      data.userId = formData?.userId;
      data.ipAddress = ipAddress;
      data.location = selectedLocationName;
      data.locationId = selectedLocationId;
      data.designation = selectedDesignationId;
      data.companyType = companyType;
      data.referenceType = reference;
      data.assignedTo = assignedTo;
      data.sourceGroup = sourceGroup;
      data.subSourceGroup = subSourceGroup;
      data.leadStatus = leadStatus;
      data.leadPriority = leadPriority;
      data.yearsOfExperience = selectedYearsOfExperienceNumber;
      data.yearsOfExperienceId = selectedYearsOfExperienceId;
      data.portalsRegistered = selectedPortalsRegisteredId;
      data.spAddresses = addresses;
      (data.serviceProviderIndividuals = spMembers),
        (data.referralName = referralName),
        (data.doYouHaveGst = gstOption),
        (data.curatedBy = curatedBy),
        (data.servicesProvided = data?.servicesProvided?.map(
          (service) => service?.value
        ));
      data.typeOfClientServed = data?.typeOfClientServed?.map(
        (service) => service.value
      );
      data.areaOfOperation = data?.areaOfOperation?.map(
        (service) => service.value
      );

      const userUniqueId =
        userData && userData.id !== undefined ? userData?.organisationId : user?.orgId;
      await auth.basicProfile(data, userUniqueId);
      toast.success("Profile updated successfully");
      onCancel(); // Call cancel after successful submission
    } catch (error) {
      console.error("Submission failed:", error);
      toast.error("An error occurred while submitting the form.");
      toast.error("Failed to update profile");
    } finally {
      setIsSubmitting(false); // Enable button after process
    }
  }

  const getDocumentCategoryId = (categoryName) => {
    const matchingCategory = allCategories.find(
      (category) => category.documentCategory === categoryName
    );
    return matchingCategory ? matchingCategory.id : null;
  };

  const getDocumentSubCategoryId = (subCategoryName) => {
    const matchingCategory = allSubCategories.find(
      (category) => category.documentSubCategory === subCategoryName
    );
    return matchingCategory ? matchingCategory.id : null;
  };

  const userUniqueId =
    userData && userData.id !== undefined ? userData.id : user?.id;

  const documentDetails = {
    userId: userUniqueId,
    documentCategory: "d1ab1eea-7643-4445-9232-10032bd60d44",
    documentSubCategory: "d1ab1eea-7643-4445-9232-10032bd60d54",
    documentFrom: "SERVICE_PROVIDER",
    documentTo: "SERVICE_PROVIDER",
  };

  const documentJson = {
    orgId: userData?.organisationId || user?.orgId,
    documentCategory: "d1ab1eea-7643-4445-9232-10032bd60d44",
    documentSubCategory: "d1ab1eea-7643-4445-9232-10032bd60d54",
  };

  const handleSave = async () => {
    setDisableButton(true);
    setLoading(true);

    const formData = new FormData();

    selectedFiles.forEach((file) => {
      formData.append("files", file);
    });

    formData.append("documentDetails", JSON.stringify(documentDetails));
    formData.append("documentSectionsEnum", "COMPANY");

    // API call
    await uploadDocuments(
      formData,
      uniqueId,
      () => {
        setModalPopup(false);
        setDialogSuccess(true);
        setSelectedFiles([]);
      },

      () => {
        console.log("Failure");
      }
    );

    fetchAllDocuments();
    setLoading(false);
    setDisableButton(false);
  };

  function formatDateTime(createdOn) {
    const dateObj = new Date(createdOn);

    // Extract date components
    const day = String(dateObj.getDate()).padStart(2, '0');
    const month = String(dateObj.getMonth() + 1).padStart(2, '0'); // Months are zero-based
    const year = dateObj.getFullYear();

    // Extract time components
    let hours = dateObj.getHours();
    const minutes = String(dateObj.getMinutes()).padStart(2, '0');
    const ampm = hours >= 12 ? 'PM' : 'AM';

    // Convert hours to 12-hour format
    hours = hours % 12 || 12; // Converts 0 to 12

    return ` ${day}-${month}-${year} at ${String(hours).padStart(2, '0')}:${minutes} ${ampm}`;
}

  return (
    <Box sx={{ pt: 3 }}>
      <Grid container spacing={5}>
      
        <Grid
          item
          xs={12}
          sx={{
            backgroundColor: "#f2f7f2",
            mt: 4,
            paddingTop: 0,
            height: "36px",
            display: "flex",
            alignItems: "center",
            ml: 5,
            justifyContent: "space-between",
          }}
        >
          <Typography
            variant="body1"
            fontWeight={"bold"}
            sx={{ mt: 0, ml: -3, mb: 5, textAlign: "center" }}
          >
            Company Information
          </Typography>
          <Divider />
        </Grid>
      
        <Grid item xs={12} sm={4}>
          <FormControl fullWidth>
            <Controller
              name="companyName"
              control={control}
              defaultValue={formData?.companyName || ""}
              rules={{ required: false }}
              render={({ field }) => (
                <TextField
                  {...field}
                  label="Name of the Company"
                  InputLabelProps={{ shrink: true }}
                  size="small"
                  placeholder="Enter your company name"
                  error={Boolean(errors.companyName)}
                  aria-describedby="Section1-companyName"
                />
              )}
            />
          </FormControl>
        </Grid>

        <Grid item xs={12} sm={4}>
          <SelectAutoComplete
            register={() =>
              register("companyType", { required: "this field is required" })
            }
            id={"companyType"}
            label={"Company Type"}
            name="companyType"
            nameArray={companyTypeOptions}
            value={companyType}
            defaultValue={formData?.companyType}
            onChange={(e) => setCompanyType(e.target.value)}
            error={Boolean(errors.companyType)}
            aria-describedby="validation-companyType"
          />
          {errors.companyType && (
            <FormHelperText
              sx={{ color: "error.main" }}
              id="validation-companyType"
            >
              Please select company type
            </FormHelperText>
          )}
        </Grid>

        {/* Multi-Select Servcies Providing*/}
        <Grid item xs={12} sm={4}>
          <FormControl fullWidth error={Boolean(errors.servicesProvided)}>
            <Controller
              name="servicesProvided"
              control={control}
              rules={{ required: "Type of Profession is required" }}
              render={({ field }) => (
                <MultiSelectAutoComplete
                  id="servicesProvided"
                  label="Type of Vendor"
                  getOptionLabel={(option) => option.key}
                  nameArray={allServicesList}
                  value={field.value}
                  onChange={(e) => field.onChange(e.target.value)}
                />
              )}
            />
            {errors.servicesProvided && (
              <FormHelperText sx={{ color: "error.main" }}>
                {errors.servicesProvided.message}
              </FormHelperText>
            )}
          </FormControl>
        </Grid>
        {servicesProvidedValues &&
          servicesProvidedValues.includes("Any other") && (
            <Grid item xs={12} sm={4}>
              <FormControl fullWidth>
                <Controller
                  name="anyOtherServiceProvided"
                  control={control}
                  defaultValue={formData?.anyOtherServiceProvided || ""}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      label="Specify Profession Type"
                      InputLabelProps={{ shrink: true }}
                      size="small"
                      placeholder="Enter your profession type"
                      error={Boolean(errors.anyOtherServiceProvided)}
                      helperText={errors.anyOtherServiceProvided?.message}
                      aria-describedby="validation-basic-other-service"
                      value={field.value}
                    />
                  )}
                />
                {/* {errors.anyOtherServiceProvided && (
        <FormHelperText sx={{ color: "error.main" }}>
          {errors.anyOtherServiceProvided.message}
        </FormHelperText>
      )} */}
              </FormControl>
            </Grid>
          )}

        <Grid item xs={12} sm={4}>
          <FormControl fullWidth>
            <Controller
              name="individualName"
              control={control}
              rules={{ required: false }}
              defaultValue={formData?.individualName}
              render={({ field }) => (
                <NameTextField
                  {...field}
                  label="Name of the person"
                  InputLabelProps={{ shrink: true }}
                  size="small"
                  placeholder="Enter your name"
                  // error={Boolean(errors.name)}
                  // helperText={errors.name?.message}
                  aria-describedby="validation-basic-name"
                />
              )}
            />
          </FormControl>
        </Grid>

        {/* Designation */}
        <Grid item xs={12} sm={4}>
          <FormControl fullWidth error={Boolean(errors.selectedDesignationId)}>
            <Controller
              name="selectedDesignationId"
              control={control}
              rules={{ required: "Designation is required" }}
              render={({ field }) => (
                <SelectAutoComplete
                  id="designation"
                  label="Designation"
                  nameArray={designationsData}
                  defaultValue={formData?.designation}
                  value={selectedDesignationId}
                  onChange={(e) => {
                    field.onChange(e);
                    handleSelectDesignationChange(e);
                  }}
                />
              )}
            />
            {/* {errors.selectedDesignationId && (
              <FormHelperText sx={{ color: "error.main" }}>
                {errors.selectedDesignationId.message}
              </FormHelperText>
            )} */}
          </FormControl>
          {errors.selectedDesignationId && (
            <FormHelperText
              sx={{ color: "error.main" }}
              id="validation-selectedDesignationId"
            >
              {errors.selectedDesignationId?.message}
            </FormHelperText>
          )}
        </Grid>

        {selectedDesignationId === authConfig.anyOtherDesignationId && (
          <Grid item xs={12} sm={4}>
            <FormControl fullWidth>
              <Controller
                name="otherDesignation"
                control={control}
                defaultValue={formData?.otherDesignation || ""}
                render={({ field }) => (
                  <TextField
                    {...field}
                    label="Specify Designation"
                    InputLabelProps={{ shrink: true }}
                    size="small"
                    placeholder="Enter your designation"
                    error={Boolean(errors.otherDesignation)}
                    helperText={errors.otherDesignation?.message}
                    aria-describedby="validation-basic-other-designation"
                  />
                )}
              />
              {errors.otherDesignation && (
                <FormHelperText sx={{ color: "error.main" }}>
                  {errors.otherDesignation.message}
                </FormHelperText>
              )}
            </FormControl>
          </Grid>
        )}

        {/* Mobile No. */}
        <Grid item xs={12} sm={4}>
          <FormControl fullWidth>
            <Controller
              name="mobileNumber"
              control={control}
              rules={{ required: false }}
              defaultValue={formData?.mobileNumber}
              render={({ field }) => (
                <MobileNumberValidation
                  {...field}
                  type="tel"
                  label="Mobile Number"
                  size="small"
                  error={Boolean(errors.mobileNumber)}
                  helperText={errors.mobileNumber?.message}
                  InputLabelProps={{ shrink: true }}
                  placeholder="+91 1234567890"
                  inputProps={{
                    maxLength: field?.value?.startsWith("+91") ? 13 : 10,
                  }}
                />
              )}
            />
            {/* {errors.mobileNumber?.type === "required" && (
              <FormHelperText sx={{ color: "error.main" }}>
                This field is required
              </FormHelperText>
            )} */}
          </FormControl>
        </Grid>
        {/* Alt Mob. No */}
        <Grid item xs={12} sm={4}>
          <FormControl fullWidth>
            <Controller
              name="alternateMobileNumber"
              control={control}
              defaultValue={formData?.alternateMobileNumber}
              render={({ field }) => (
                <TextField
                  {...field}
                  label="Alternate Number"
                  size="small"
                  error={Boolean(errors.alternateMobileNumber)}
                  helperText={errors.alternateNumber?.message}
                  InputLabelProps={{ shrink: true }}
                />
              )}
            />
          </FormControl>
        </Grid>

        {/* Email */}
        <Grid item xs={12} sm={4}>
          <FormControl fullWidth>
            <Grid container alignItems="center" wrap="nowrap">
              <Grid item>
                <Typography
                  fontSize="body1"
                  sx={{ fontWeight: "bold", mr: 1, ml: 1 }}
                >
                  Email:
                </Typography>
              </Grid>
              <Grid
                item
                xs
                container
                alignItems="center"
                justifyContent="flex-start"
                sx={{ overflow: "hidden" }}
              >
                <Typography
                  variant="body1"
                  sx={{
                    textOverflow: "ellipsis",
                    overflow: "hidden",
                    whiteSpace: "nowrap",
                  }}
                >
                  {formData?.email}
                </Typography>
              </Grid>
              <Grid item>
                <CheckCircleOutlineIcon
                  sx={{ color: "green", marginLeft: 1 }}
                />
              </Grid>
            </Grid>
          </FormControl>
        </Grid>

        {/* Website URl */}
        <Grid item xs={12} sm={4}>
          <FormControl fullWidth>
            <Controller
              name="websiteUrl"
              control={control}
              rules={{
                required: false,
                pattern: {
                  value: /^(https?:\/\/|ftp:\/\/|www\.)[^\s/$.?#].[^\s]*$/i,
                  message: 'Enter a valid website URL',
                },
              }}
        
            
              defaultValue={formData?.websiteUrl}
              render={({ field: { value, onChange } }) => (
                <TextField
                  type="url"
                  value={value}
                  size="small"
                  label="Website URL"
                  onChange={onChange}
                  InputLabelProps={{ shrink: true }}
                  error={Boolean(errors.websiteUrl)}
                  helperText={errors.websiteUrl?.message}
                  placeholder="https://www.example.com or www.example.com"
                  aria-describedby="validation-websiteUrl"
                />
              )}
            />
            {/* {errors.websiteUrl?.type === "required" && (
              <FormHelperText
                sx={{ color: "error.main" }}
                id="validation-websiteUrl"
              >
                Website URL is required
              </FormHelperText>
            )}
            {errors.websiteUrl?.type === "pattern" && (
              <FormHelperText
                sx={{ color: "error.main" }}
                id="validation-websiteUrl"
              >
                Please enter a valid Website URL
              </FormHelperText>
            )} */}
          </FormControl>
        </Grid>
        {/* Social Media Presence Field */}
        <Grid item xs={12} sm={4}>
          <FormControl fullWidth>
            <Controller
              name="socialMediaPresence"
              control={control}
              rules={{ required: false }} // Add any validation rules here
              defaultValue={formData?.socialMediaPresence} // Set default value if available
              render={({ field }) => (
                <NameTextField
                  {...field}
                  label="Social Media Presence"
                  InputLabelProps={{ shrink: true }}
                  size="small"
                  placeholder="Enter your social media presence"
                  error={Boolean(errors.socialMediaPresence)}
                  helperText={errors.socialMediaPresence?.message}
                  aria-describedby="validation-basic-social-media"
                />
              )}
            />
            {/* {errors.socialMediaPresence?.type === "required" && (
              <FormHelperText sx={{ color: "error.main" }}>
                This field is required
              </FormHelperText>
            )} */}
          </FormControl>
        </Grid>
        <Grid item xs={12} sm={4}>
          <FormControl fullWidth error={Boolean(errors.areaOfOperation)}>
            <Controller
              name="areaOfOperation"
              control={control}
              rules={{ required: false }}
              render={({ field }) => (
                <MultiSelectAutoComplete
                  id="areaOfOperation"
                  label="Area Of Operation"
                  getOptionLabel={(option) => option.key}
                  nameArray={areaOfOperationList}
                  value={field.value}
                  onChange={(e) => field.onChange(e.target.value)}
                />
              )}
            />
            {errors.areaOfOperation && (
              <FormHelperText sx={{ color: "error.main" }}>
                {errors.areaOfOperation.message}
              </FormHelperText>
            )}
          </FormControl>
        </Grid>

        <Grid
          item
          xs={12}
          sx={{
            backgroundColor: "#f2f7f2",
            ml: 5,
            mt: 4,
            paddingTop: 0,
            height: "36px",
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between",
          }}
        >
          <Typography
            variant="body1"
            fontWeight={"bold"}
            sx={{ mt: 0, ml: -3, mb: 5, textAlign: "center" }}
          >
            Business Information
          </Typography>
          <Divider />
        </Grid>

        {/* Portals Registered Field */}
        <Grid item xs={12} sm={4}>
          <FormControl fullWidth>
            <Controller
              name="portalsRegistered"
              control={control}
              size="small"
              render={({ field }) => (
                <SelectAutoComplete
                  size="small"
                  labelId="portals-registered-label"
                  label="Portals Registered"
                  nameArray={portalsRegisteredData}
                  value={selectedPortalsRegisteredId}
                  onChange={(e) => {
                    field.onChange(e);
                    handlePortalsRegisteredChange(e);
                  }}
                />
              )}
            />
          </FormControl>
        </Grid>

        {selectedPortalsRegisteredName  === "any other" && (
          <Grid item xs={12} sm={4}>
            <FormControl fullWidth>
              <Controller
                name="anyOtherPortalRegistered"
                control={control}
                rules={{ required: false }}
                render={({ field }) => (
                  <TextField
                    {...field}
                    label="Specify Portals Registered"
                    InputLabelProps={{ shrink: true }}
                    size="small"
                    placeholder="Enter more details"
                    error={Boolean(errors.anyOtherPortalRegistered)}
                    helperText={errors.anyOtherPortalRegistered?.message}
                    aria-describedby="validation-basic-other-portals-registered-details"
                  />
                )}
              />
              {/* {errors.anyOtherPortalsRegistered && (
        <FormHelperText sx={{ color: "error.main" }}>
          {errors.anyOtherPortalsRegistered.message}
        </FormHelperText>
        )} */}
            </FormControl>
          </Grid>
        )}

        {/* PAN NO. */}
        <Grid item xs={12} sm={4}>
          <FormControl fullWidth>
            <Controller
              name="panNo"
              control={control}
              defaultValue={formData?.panNo}
              rules={{ required: false }}
              render={({ field }) => (
                <TextField
                  {...field}
                  label="PAN No."
                  size="small"
                  InputLabelProps={{ shrink: true }}
                  placeholder="Enter your PAN No."
                  error={Boolean(errors.panNo)}
                  helperText={errors.panNo?.message}
                  aria-describedby="validation-basic-panNo"
                  inputProps={{
                    maxLength: 10, // Limits the input to 10 characters
                  }}
                />
              )}
            />
            {/* {errors.panNo?.type === "required" && (
              <FormHelperText sx={{ color: "error.main" }}>
                Pan No is required
              </FormHelperText>
            )} */}
          </FormControl>
        </Grid>

        {/* GST No. */}

        <Grid item xs={12} sm={4}>
          <FormControl fullWidth>
            <InputLabel id="gst-label">Do you have GST?</InputLabel>
            <Controller
              name="gstOption"
              control={control}
              defaultValue={formData?.doYouHaveGst || ""}
              render={({ field }) => (
                <Select
                  {...field}
                  value={gstOption}
                  onChange={(e) => {
                    const selectedValue = e.target.value;
                    setGstOption(selectedValue);  // update local component state
                  }}
                  size="small"
                  labelId="gst-label"
                  label="Do you have GST?"
                >
                  <MenuItem value="yes">Yes</MenuItem>
                  <MenuItem value="no">No</MenuItem>
                </Select>
              )}
            />
          </FormControl>
        </Grid>

        {(gstOption === "yes") && (
          <Grid item xs={12} sm={4}>
            <FormControl fullWidth>
              <Controller
                name="gstNo"
                control={control}
                rules={{ required: false }}
                defaultValue={formData?.gstNo}
                render={({ field }) => (
                  <TextField
                    {...field}
                    label="GST No."
                    size="small"
                    InputLabelProps={{ shrink: true }}
                    placeholder="Enter your GST No."
                    error={Boolean(errors.gstNo)}
                    helperText={errors.gstNo ? "GST No. is required" : ""}
                  />
                )}
              />
            </FormControl>
          </Grid>
        )}

        {/* TAN No */}
        <Grid item xs={12} sm={4}>
          <FormControl fullWidth>
            <Controller
              name="tanNo"
              control={control}
              defaultValue={formData?.tanNo}
              rules={{ required: false }}
              render={({ field }) => (
                <TextField
                  {...field}
                  label="TAN No."
                  size="small"
                  InputLabelProps={{ shrink: true }}
                  placeholder="Enter your TAN No."
                />
              )}
            />
          </FormControl>
        </Grid>
        {/* CIN No */}
        <Grid item xs={12} sm={4}>
          <FormControl fullWidth>
            <Controller
              name="cinNo"
              control={control}
              defaultValue={formData?.cinNo}
              rules={{ required: false }}
              render={({ field }) => (
                <TextField
                  {...field}
                  label="CIN No."
                  size="small"
                  InputLabelProps={{ shrink: true }}
                  placeholder="Enter your CIN No."
                />
              )}
            />
          </FormControl>
        </Grid>
        {/* years of experience */}

        <Grid item xs={12} sm={4}>
          <FormControl fullWidth>
            <Controller
              name="yearsOfExperience"
              control={control}
              defaultValue={selectedYearsOfExperienceId}
              render={({ field }) => (
                <SelectAutoComplete
                  id="years-of-experience"
                  label="Years of Experience"
                  nameArray={yearsOfExperienceData?.map(
                    (yearsOfExperience) => ({
                      key: yearsOfExperience.listValue,
                      value: yearsOfExperience.id,
                    })
                  )}
                  value={selectedYearsOfExperienceId}
                  onChange={(e) => {
                    handleYearsOfExperienceChange(e);
                    field.onChange(e); // Sync with react-hook-form field change
                  }}
                />
              )}
            />
          </FormControl>
        </Grid>

        {/* No of Team  Members */}
        <Grid item xs={12} sm={4}>
          <FormControl fullWidth>
            <Controller
              name="teamSize"
              control={control}
              rules={{
                required: false,
                pattern: /^(?:[1-9]|[1-9][0-9]|100)$/,
              }}
              defaultValue={formData?.teamSize}
              render={({ field: { value, onChange } }) => (
                <TextField
                  type="number"
                  value={value}
                  size="small"
                  label="Number of Team Members"
                  InputLabelProps={{ shrink: true }}
                  onChange={onChange}
                  error={Boolean(errors.teamSize)}
                  placeholder="Enter number of team members (1-100)"
                  aria-describedby="validation-noOfTeamMembers"
                  inputProps={{ min: 1, max: 100 }}
                />
              )}
            />
            {/* {errors.teamSize?.type === "required" && (
              <FormHelperText
                sx={{ color: "error.main" }}
                id="validation-teamSize"
              >
                This field is required
              </FormHelperText>
            )} */}
            {errors.noOfTeamMembers?.type === "pattern" && (
              <FormHelperText
                sx={{ color: "error.main" }}
                id="validation-noOfTeamMembers"
              >
                Please enter a valid number of team members (1-100)
              </FormHelperText>
            )}
          </FormControl>
        </Grid>

        {/* no of sales team members */}
        <Grid item xs={12} sm={4}>
          <Controller
            name="noOfSalesTeamMembers"
            control={control}
            defaultValue={formData?.noOfSalesTeamMembers} // Prepopulate selected values
            rules={{ required: false }}
            render={({ field }) => (
              <SelectAutoComplete
                id="noOfSalesTeamMembers"
                label="Number of Sales Team Members"
                nameArray={salesTeamMembers}
                value={field.value || []}
                onChange={(event) => {
                  const newValue = event.target.value;
                  setNoOfSalesTeamMembers(newValue);
                  field.onChange(newValue); // Update React Hook Form state
                }}
                error={Boolean(errors.noOfSalesTeamMembers)}
                aria-describedby="validation-noOfSalesTeamMembers"
              />
            )}
          />
          {/* {errors.noOfSalesTeamMembers && (
            <FormHelperText
              sx={{ color: "error.main" }}
              id="validation-noOfSalesTeamMembers"
            >
              {errors.noOfSalesTeamMembers?.message}
            </FormHelperText>
          )} */}
        </Grid>

        {/* type of client Served */}
        <Grid item xs={12} sm={4}>
          <FormControl fullWidth error={Boolean(errors.typeOfClientServed)}>
            <Controller
              name="typeOfClientServed"
              control={control}
              rules={{ required: false }}
              render={({ field }) => (
                <MultiSelectAutoComplete
                  id="typeOfClientServed"
                  label="Type of Client Served"
                  getOptionLabel={(option) => option.key}
                  nameArray={typeOfClientServedOptions}
                  value={field.value}
                  onChange={(e) => field.onChange(e.target.value)}
                />
              )}
            />
            {errors.typeOfClientServed && (
              <FormHelperText sx={{ color: "error.main" }}>
                {errors.typeOfClientServed.message}
              </FormHelperText>
            )}
          </FormControl>
        </Grid>

        {/* Field for lastThreeYearsTurnOver */}
        <Grid item xs={12} sm={4}>
          <FormControl fullWidth>
            <Controller
              name="lastThreeYearsTurnOver"
              control={control}
              rules={{
                required: false,
                pattern: /^[0-9]+$/,
              }}
              defaultValue={formData?.lastThreeYearsTurnOver}
              render={({ field: { value, onChange } }) => (
                <TextField
                  type="number"
                  value={value}
                  size="small"
                  label="Last 3 Years Turnover (in Lakhs)"
                  InputLabelProps={{ shrink: true }}
                  onChange={onChange}
                  error={Boolean(errors.lastThreeYearsTurnOver)}
                  placeholder="Enter turnover (in Lakhs)"
                  aria-describedby="validation-lastThreeYearsTurnOver"
                  inputProps={{ min: 0 }}
                />
              )}
            />
            {errors.lastThreeYearsTurnOver?.type === "required" && (
              <FormHelperText
                sx={{ color: "error.main" }}
                id="validation-lastThreeYearsTurnOver"
              >
                This field is required
              </FormHelperText>
            )}
            {/* {errors.lastThreeYearsTurnOver?.type === "pattern" && (
              <FormHelperText
                sx={{ color: "error.main" }}
                id="validation-lastThreeYearsTurnOver"
              >
                Please enter a valid value for Turnover (in Lakhs)
              </FormHelperText>
            )} */}
          </FormControl>
        </Grid>
        {/* Field for completedProjectsOrCases */}
        <Grid item xs={12} sm={4}>
          <FormControl fullWidth>
            <Controller
              name="completedProjectsOrCases"
              control={control}
              rules={{
                required: false,
                min: 0,
              }}
              defaultValue={formData?.completedProjectsOrCases}
              render={({ field: { value, onChange } }) => (
                <TextField
                  type="number"
                  value={value}
                  size="small"
                  label="Completed Projects or Cases"
                  InputLabelProps={{ shrink: true }}
                  onChange={onChange}
                  error={Boolean(errors.completedProjectsOrCases)}
                  placeholder="Enter completed projects or cases"
                  aria-describedby="validation-completedProjectsOrCases"
                  inputProps={{ min: 0 }}
                />
              )}
            />
            {/* {errors.completedProjectsOrCases?.type === "required" && (
              <FormHelperText
                sx={{ color: "error.main" }}
                id="validation-completedProjectsOrCases"
              >
                This field is required
              </FormHelperText>
            )} */}
          </FormControl>
        </Grid>
        {/* Field for onGoingProjectsOrCases */}
        <Grid item xs={12} sm={4}>
          <FormControl fullWidth>
            <Controller
              name="onGoingProjectsOrCases"
              control={control}
              rules={{
                required: false,
                min: 0,
              }}
              defaultValue={formData?.onGoingProjectsOrCases}
              render={({ field: { value, onChange } }) => (
                <TextField
                  type="number"
                  value={value}
                  size="small"
                  label="Ongoing Projects or Cases"
                  InputLabelProps={{ shrink: true }}
                  onChange={onChange}
                  error={Boolean(errors.onGoingProjectsOrCases)}
                  placeholder="Enter ongoing projects or cases"
                  aria-describedby="validation-onGoingProjectsOrCases"
                  inputProps={{ min: 0 }}
                />
              )}
            />
            {/* {errors.onGoingProjectsOrCases?.type === "required" && (
              <FormHelperText
                sx={{ color: "error.main" }}
                id="validation-onGoingProjectsOrCases"
              >
                This field is required
              </FormHelperText>
            )} */}
          </FormControl>
        </Grid>

        <Grid
          item
          xs={12}
          sx={{
            backgroundColor: "#f2f7f2",
            mt: 4,
            paddingTop: 0,
            height: "36px",
            display: "flex",
            alignItems: "center",
            ml: 5,
            justifyContent: "space-between",
          }}
        >
          <Typography
            variant="body1"
            fontWeight={"bold"}
            sx={{ mt: 0, ml: -3, mb: 5, textAlign: "center" }}
          >
            Bank Details
          </Typography>
          <Divider />
        </Grid>

        {/* Bank Name */}
        <Grid item xs={12} sm={4}>
          <FormControl fullWidth>
            <Controller
              name="bankName"
              control={control}
              rules={{ required: false }}
              defaultValue={formData?.bankName}
              render={({ field }) => (
                <TextField
                  {...field}
                  label="Bank Name"
                  size="small"
                  InputLabelProps={{ shrink: true }}
                  placeholder="Enter Bank Name"
                  error={Boolean(errors.bankName)}
                  helperText={errors.bankName?.message}
                  aria-describedby="validation-basic-bankName"
                />
              )}
            />
            {errors.bankName?.type === "required" && (
              <FormHelperText sx={{ color: "error.main" }}>
                This field is required
              </FormHelperText>
            )}
          </FormControl>
        </Grid>
        {/* branch */}
        <Grid item xs={12} sm={4}>
          <FormControl fullWidth>
            <Controller
              name="branch"
              control={control}
              rules={{ required: false }}
              defaultValue={formData?.branch}
              render={({ field }) => (
                <TextField
                  {...field}
                  label="Branch"
                  size="small"
                  InputLabelProps={{ shrink: true }}
                  placeholder="Enter branch name"
                  error={Boolean(errors.branch)}
                  helperText={errors.branch?.message}
                  aria-describedby="validation-basic-branch"
                />
              )}
            />
            {/* {errors.branch?.type === "required" && (
              <FormHelperText sx={{ color: "error.main" }}>
                This field is required
              </FormHelperText>
            )} */}
          </FormControl>
        </Grid>

        {/* Account nUmber */}
        <Grid item xs={12} sm={4}>
          <FormControl fullWidth>
            <Controller
              name="accountNumber"
              control={control}
              rules={{
                required: false,
                maxLength: 18,
                validate: {
                  isAlphanumeric: (value) =>
                    /^[a-zA-Z0-9]*$/.test(value) ||
                    "Please enter a valid alphanumeric account number",
                },
              }}
              defaultValue={formData?.accountNumber}
              render={({ field: { value, onChange } }) => (
                <TextField
                  type="text"
                  value={value}
                  onChange={onChange}
                  size="small"
                  label="Account Number"
                  InputLabelProps={{ shrink: true }}
                  inputProps={{
                    maxLength: 18,
                  }}
                  error={Boolean(errors.accountNumber)}
                  placeholder="Enter account number"
                />
              )}
            />
            {/* {errors.accountNumber?.type === "required" && (
              <FormHelperText sx={{ color: "error.main" }}>
                This field is required
              </FormHelperText>
            )} */}
            {errors.accountNumber?.type === "maxLength" && (
              <FormHelperText sx={{ color: "error.main" }}>
                Account number cannot exceed 18 characters
              </FormHelperText>
            )}
            {errors.accountNumber?.type === "pattern" && (
              <FormHelperText sx={{ color: "error.main" }}>
                Please enter a valid alphanumeric account number
              </FormHelperText>
            )}
          </FormControl>
        </Grid>

        {/* ifsc code*/}
        <Grid item xs={12} sm={4}>
          <FormControl fullWidth>
            <Controller
              name="ifscCode"
              control={control}
              rules={{ required: false }}
              defaultValue={formData?.ifscCode}
              render={({ field }) => (
                <TextField
                  {...field}
                  onChange={(e) => field.onChange(e.target.value.toUpperCase())}
                  label="IFSC Code"
                  size="small"
                  InputLabelProps={{ shrink: true }}
                  placeholder="Enter IFSC Code"
                  error={Boolean(errors.ifscCode)}
                  helperText={errors.ifscCode?.message}
                  aria-describedby="validation-basic-ifscCode"
                  inputProps={{
                    maxLength: 11,
                  }}
                />
              )}
            />
            {/* {errors.ifscCode?.type === "required" && (
              <FormHelperText sx={{ color: "error.main" }}>
                This field is required
              </FormHelperText>
            )} */}
          </FormControl>
        </Grid>

        {userData && userData.id !== undefined && (
          <>
            <Grid
              item
              xs={12}
              sx={{
                backgroundColor: "#f2f7f2",
                mt: 4,
                ml: 5,
                paddingTop: 0,
                height: "36px",
                display: "flex",
                alignItems: "center",
                justifyContent: "space-between",
              }}
            >
              <Typography
                variant="body1"
                fontWeight={"bold"}
                sx={{ mt: 0, ml: -3, mb: 5, textAlign: "center" }}
              >
                Status & Assignment Details
              </Typography>
              <Divider />
            </Grid>
           
            <Grid item xs={12} sm={4}>
              <FormControl fullWidth>
                <Controller
                  name="assignedTo"
                  control={control}
                  defaultValue={formData?.assignedTo}
                  render={({ field }) => (
                    <SelectAutoComplete
                      {...field}
                      id="assigned-to"
                      label="Assigned To"
                      nameArray={employeesData?.map((data) => ({
                        value: data.id,
                        key: data.name,
                      }))}
                      value={assignedTo}
                      onChange={(e) => {
                        field.onChange(e);
                        handleAssignedToChange(e);
                      }}
                    />
                  )}
                />
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={4}>
              <FormControl fullWidth>
                <Controller
                  name="status"
                  control={control}
                  size="small"
                  defaultValue={formData?.leadStatus}
                  InputLabelProps={{ shrink: true }}
                  render={({ field }) => (
                    <SelectAutoComplete
                      id="status"
                      label="Lead Status"
                      nameArray={leadStatusData}
                      value={leadStatus}
                      onChange={(e) => {
                        field.onChange(e);
                        handleLeadStatusChange(e);
                      }}
                    />
                  )}
                />
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={4}>
              <FormControl fullWidth>
                <Controller
                  name="lead-priority"
                  control={control}
                  size="small"
                  defaultValue={formData?.leadPriority}
                  // InputLabelProps={{ shrink: true }}
                  render={({ field }) => (
                    <SelectAutoComplete
                      id="lead-priority"
                      label="Lead Priority"
                      nameArray={leadPriorityData}
                      value={leadPriority}
                      onChange={(e) => {
                        field.onChange(e);
                        handleLeadPriorityChange(e);
                      }}
                    />
                  )}
                />
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={4}>
              <FormControl fullWidth>
                <Controller
                  name="reference"
                  control={control}
                  defaultValue={formData?.reference || ""}
                  render={({ field }) => (
                    <SelectAutoComplete
                      {...field}
                      id="reference"
                      label="Reference Type"
                      name="reference"
                      nameArray={referenceOptions}
                      value={reference}
                      onChange={(e) => {
                        field.onChange(e);
                        handleReferenceTypeChange(e);
                      }}
                      error={Boolean(errors.reference)}
                      aria-describedby="validation-reference"
                    />
                  )}
                />
                {errors.reference && (
                  <FormHelperText
                    sx={{ color: "error.main" }}
                    id="validation-reference"
                  >
                    Please select a reference type
                  </FormHelperText>
                )}
              </FormControl>
            </Grid>
            {/* other reference */}
            {listValues?.find((item) => item.id === reference)?.name ===
              "Any Other" && (
              <Grid item xs={12} sm={4}>
                <FormControl fullWidth>
                  <Controller
                    name="otherReference"
                    control={control}
                    defaultValue={formData?.otherReference}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        label="Other Reference"
                        size="small"
                        InputLabelProps={{ shrink: true }}
                        placeholder="Enter Other Reference"
                        error={Boolean(errors.otherReference)}
                        helperText={errors.otherReference?.message}
                        aria-describedby="validation-basic-otherReference"
                      />
                    )}
                  />
                </FormControl>
              </Grid>
            )}
            <Grid item xs={12} sm={4}>
              <FormControl fullWidth>
                <Controller
                  name="referralName"
                  control={control}
                  defaultValue={formData?.referralName || ""}
                  render={({ field }) => (
                    <SelectAutoComplete
                      {...field}
                      id="referralName"
                      label="Referral Name"
                      name="referralName"
                      nameArray={referralNameOptions}
                      value={referralName}
                      onChange={(e) => {
                        field.onChange(e);
                        setReferralName(e.target.value);
                      }}
                      error={Boolean(errors.referralName)}
                      aria-describedby="validation-referralName"
                    />
                  )}
                />
                {errors.referralName && (
                  <FormHelperText
                    sx={{ color: "error.main" }}
                    id="validation-referralName"
                  >
                    Please select a referralName type
                  </FormHelperText>
                )}
              </FormControl>
            </Grid>
            {listValues?.find((item) => item.id === reference)?.name ===
              "Any Other" ? (
                <></>
            ):(
              <Grid item xs={12} sm={4}>
              </Grid>
            )}
            <Grid item xs={12} sm={4}>
              <FormControl fullWidth>
                <Controller
                  name="curatedBy"
                  control={control}
                  defaultValue={formData?.curatedBy}
                  render={({ field }) => (
                    <SelectAutoComplete
                      {...field}
                      id="curatedBy"
                      label="Curated By"
                      nameArray={employeesData?.map((data) => ({
                        value: data.id,
                        key: data.name,
                      }))}
                      value={curatedBy}
                      onChange={(e) => {
                        field.onChange(e);
                        setCuratedBy(e.target.value);
                      }}
                    />
                  )}
                />
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={4}>
              <FormControl fullWidth>
                <Controller
                  name="curatedOn"
                  control={control}
                  rules={{ required: false }}
                  defaultValue={formData?.curatedOn || ""}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      value={field.value || ""}
                      size="small"
                      label="Curated Date"
                      type="date"
                      InputLabelProps={{ shrink: true }}
                      error={Boolean(errors.curatedOn)}
                      helperText={errors.curatedOn?.message}
                      aria-describedby="curatedOn"
                    />
                  )}
                />
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={8}>
              <FormControl fullWidth>
                <Grid container spacing={1} alignItems="center">
                  <Grid item xs={12} sm={2}>
                    <Typography>Created On: </Typography>
                  </Grid>
                  <Grid item xs={12} sm={4}>
                    <Typography
                      variant="body1"
                      style={{ fontSize: "14px", fontWeight: "500" }}
                    >
                      {formData?.createdOn?.split("T")[0]}
                    </Typography>
                  </Grid>
                  <Grid item xs={12} sm={3}>
                    <Typography>Created By:</Typography>
                  </Grid>
                  <Grid item xs={12} sm={3}>
                    <Typography variant="h6" style={{ fontSize: "14px" }}>
                      {createdBy}
                    </Typography>
                  </Grid>
                </Grid>
              </FormControl>
            </Grid>

            <Grid item sm={8} xs={12}>
              <FormControl fullWidth>
                <Controller
                  name="remarks"
                  control={control}
                  defaultValue={formData?.remarks}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      rows={4}
                      multiline
                      label="Smart Summary"
                      InputLabelProps={{ shrink: true }}
                      error={Boolean(errors.remarks)}
                      helperText={errors.remarks?.message}
                      aria-describedby="statusAssignmentDetails_remarks"
                    />
                  )}
                />
              </FormControl>
            </Grid>
          </>
        )}

       
<Grid
          item
          xs={12}
          sx={{
            backgroundColor: "#f2f7f2",
            ml: 5,
            mt: 4,
            paddingTop: 0,
            height: "36px",
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between",
          }}
        >
          <Typography
            variant="body1"
            fontWeight={"bold"}
            sx={{ mt: 0, ml: -3, mb: 5, textAlign: "center" }}
          >
            Company Members Information
          </Typography>
          <Divider />
        </Grid>
        <Grid item xs={12}>
          <SPMemberInformation
            formData={formData}
            designationList={designationsData}
            spMembers={spMembers}
            setSpMembers={setSpMembers}
          />
        </Grid>

        <Grid
          item
          xs={12}
          sx={{
            backgroundColor: "#f2f7f2",
            ml: 5,
            mt: 4,
            paddingTop: 0,
            height: "36px",
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between",
          }}
        >
          <Typography
            variant="body1"
            fontWeight={"bold"}
            sx={{ mt: 0, ml: -3, mb: 5, textAlign: "center" }}
          >
            Company Addresses
          </Typography>
          <Divider />
        </Grid>
        <Grid item xs={12}>
          <CompanyAddressInformation
            formData={formData}
            locationsData={locationsData}
            addressTypeList={addressTypeList}
            addresses={addresses}
            setAddresses={setAddresses}
          />
        </Grid>

        <Grid
          item
          xs={12}
          sx={{
            backgroundColor: "#f2f7f2",
            ml: 5,
            mt: 4,
            paddingTop: 0,
            height: "36px",
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between",
          }}
        >
          <Typography
            variant="body1"
            fontWeight={"bold"}
            sx={{ mt: 0, ml: -3, mb: 5, textAlign: "center" }}
          >
            Uploads
          </Typography>
          <Divider />
        </Grid>

        <Grid item xs={12}>
          <Button
            aria-controls="simple-menu"
            aria-haspopup="true"
            onClick={() => {
              setModalPopup(true);
            }}
            variant="contained"
            sx={{ px: 4, ml: 0 }}
          >
            Upload Profile
          </Button>
        </Grid>

        {documents && documents?.data && documents?.data?.length > 0 && (
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Upload Profile</TableCell>
                <TableCell>Action</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {documents.data?.map((company, index) => (
                <TableRow key={index}>
                  <TableCell>
                    <Typography className="data-field">
                      {company && company.split("/").pop()}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <IconButton
                      onClick={() => {
                        setConfirmDeleteDialogOpen(true);
                        setCompanyToDelete(company);
                      }}
                      color="error"
                    >
                      <Icon icon="iconamoon:trash" />
                    </IconButton>
                    <IconButton
                      onClick={() => handleViewIconClick(company)}
                      color="error"
                      disabled={selectedCompany}
                    >
                      <Icon icon="iconamoon:eye" />
                    </IconButton>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        )}


        <DocumentUploadDialog
          open={modalPopup}
          onClose={() => setModalPopup(false)}
          onSave={() => handleSave()}
          selectedFiles={selectedFiles}
          setSelectedFiles={setSelectedFiles}
          loading={loading}
          disableButton={disableButton}
        />

<Grid item xs={12}>
          <DialogActions
            sx={{
              justifyContent: "end",
              // borderTop: (theme) => `1px solid ${theme.palette.divider}`,
              p: (theme) => `${theme.spacing(1.5)} !important`,
            }}
          >
            <Button
              display="flex"
              justifyContent="center"
              variant="outlined"
              color="primary"
              onClick={() => onCancel()}
            >
              Cancel
            </Button>
            <Button
              display="flex"
              justifyContent="center"
              variant="contained"
              color="primary"
              onClick={handleSubmit(submit, onFormInvalid)}
              disabled={isSubmitting}
              sx={{
                mr: {
                  xs: -2,
                  sm: -2,
                  md: -2,
                  lg: -2,
                  xl: -2,
                },
              }}
            >
              Save
            </Button>
          </DialogActions>
        </Grid>

        <Grid>
          <Dialog
            open={dailogSuccess}
            onClose={handleClose}
            aria-labelledby="alert-dialog-title"
            aria-describedby="alert-dialog-description"
            PaperProps={{
              sx: {
                p: (theme) => `${theme.spacing(2.5)} !important`,
                backgroundColor: (theme) => theme.palette.primary.background,
              },
            }}
          >
            <Box
              sx={{
                width: "100%",
                borderRadius: 1,
                textAlign: "center",
                border: (theme) => `1px solid ${theme.palette.divider}`,
                borderColor: "primary.main",
              }}
            >
              <DialogContent>
                <DialogContentText
                  id="alert-dialog-description"
                  color="primary.main"
                >
                  Successfully uploaded
                </DialogContentText>
              </DialogContent>
              <DialogActions>
                <Button
                  variant="contained"
                  onClick={handleClose}
                  sx={{ margin: "auto", width: 100 }}
                >
                  Ok
                </Button>
              </DialogActions>
            </Box>
          </Dialog>
          <ViewDialogByLocation
            location={selectedCompany}
            setSelectedLocation={setSelectedCompany}
            onClose={handleDialogClose}
          />
          <DeleteConfirmationDialog
            open={confirmDeleteDialogOpen}
            onClose={() => setConfirmDeleteDialogOpen(false)}
            onConfirm={handleDelete}
          />
        </Grid>

      </Grid>
    </Box>
  );
};

export default ProfileEdit;