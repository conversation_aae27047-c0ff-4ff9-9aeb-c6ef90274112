// ** Next Import
import Link from "next/link";

// ** <PERSON><PERSON> Components
import TextField from "@mui/material/TextField";
import Typography from "@mui/material/Typography";
import Box from "@mui/material/Box";
import useMediaQuery from "@mui/material/useMediaQuery";
import { styled, useTheme } from "@mui/material/styles";
import { useAuth } from "src/hooks/useAuth";
import { useForm, Controller } from "react-hook-form";
import FormControl from "@mui/material/FormControl";
import DialogContentText from "@mui/material/DialogContentText";
import DialogContent from "@mui/material/DialogContent";

// ** Icon Imports
import Icon from "src/@core/components/icon";

// ** Layout Import
import BlankLayout from "src/@core/layouts/BlankLayout";

// ** Demo Imports
import FooterIllustrationsV2 from "src/views/pages/auth/FooterIllustrationsV2";
import { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON>ert<PERSON><PERSON>le,
  <PERSON>ton,
  CircularProgress,
  Dialog,
  FormHelperText,
  Grid,
} from "@mui/material";

// Styled Components
const ForgotPasswordIllustration = styled("img")(({ theme }) => ({
  zIndex: 2,
  maxHeight: 650,
  margin: theme.spacing(5),
  [theme.breakpoints.down(1540)]: {
    maxHeight: 500,
  },
  [theme.breakpoints.down("lg")]: {
    maxHeight: 450,
  },
}));

const RightWrapper = styled(Box)(({ theme }) => ({
  [theme.breakpoints.up("lg")]: {
    minWidth: "40%",
  },
  [theme.breakpoints.down("lg")]: {
    minWidth: "100%",
  },
}));

const LinkStyled = styled(Link)(({ theme }) => ({
  display: "flex",
  fontSize: "1rem",
  alignItems: "center",
  textDecoration: "none",
  justifyContent: "center",
  color: theme.palette.primary.main,
}));

const ForgotPassword = () => {
  // ** Hooks
  const theme = useTheme();
  const {
    register,
    handleSubmit,
    setError,
    setValue,
    control,
    formState: { errors },
  } = useForm();

  const auth = useAuth();

  useEffect(() => {
    const queryString = window.location.search;
    const urlParams = new URLSearchParams(queryString);
    const email = urlParams.get("email");
    if (email) {
      setValue("email", email);
    }
  }, []);

  const [openDialog, setOpenDialog] = useState(false);
  const [alertConfig, setAlertConfig] = useState(null);
  const [dialogMessage, setDialogMessage] = useState("");
  const [loading, setLoading] = useState(true);
  const [loadingReset, setLoadingReset] = useState(false);

  const handleCloseDialog = () => {
    setOpenDialog(false);
  };

  const handleButtonClick = () => {
    window.location.href = "/login";
  };
const [isDisable, setIsDisable] = useState(false);
  async function submit(data) {
    setLoadingReset(true);
    setIsDisable(true);
    try {
      await auth.triggerPasswordReset(data.email);
      handleSuccess();
      console.log("Submitted Data: ", data);
    } catch (error) {
      handleSuccess();
      console.error(error);
    }
    setLoadingReset(false);
    setIsDisable(false);
  }

  const handleSuccess = () => {
    const message = `
  <div>
  <h3>
  If the email exists in our database, a verification mail has been sent to your email address.
  </h3>
  </div>`;
    setDialogMessage(message);
    setOpenDialog(true);
  };

  useEffect(() => {
    if (openDialog) {
      const timeoutId = setTimeout(() => {
        {
          handleButtonClick();
        }
        // Close the dialog after 3 seconds
      }, 3000);
      return () => clearTimeout(timeoutId); // Clear the timeout if the component unmounts or openDialog changes
    }
  }, [openDialog]);

  // ** Vars
  const hidden = useMediaQuery(theme.breakpoints.down("md"));

  return (
    <>
      <Dialog
        open={openDialog}
        onClose={handleCloseDialog}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
        PaperProps={{
          sx: {
            p: (theme) => `${theme.spacing(2.5)} !important`,
            backgroundColor: (theme) => theme.palette.primary.background,
          },
        }}
      >
        <Box
          sx={{
            width: "100%",
            borderRadius: 1,
            textAlign: "center",
            border: (theme) => `1px solid ${theme.palette.divider}`,
            borderColor: "primary.main",
          }}
        >
          <>
            <DialogContent>
              <DialogContentText
                id="alert-dialog-description"
                color="primary.main"
              >
                <div dangerouslySetInnerHTML={{ __html: dialogMessage }} />
              </DialogContentText>
              {alertConfig && (
                <Alert
                  severity={alertConfig.severity}
                  style={{ margin: "20px" }}
                >
                  <AlertTitle>{alertConfig.severity}</AlertTitle>
                  {alertConfig.message}
                </Alert>
              )}
            </DialogContent>
            <Button style={{ margin: "10px auto", width: 100 }}>
              {loading ? (
                <CircularProgress color="inherit" size={24} />
              ) : (
                "Okay"
              )}
            </Button>
          </>
        </Box>
      </Dialog>
      <Box
        className="content-right"
        sx={{ backgroundColor: "background.paper" }}
      >
        <RightWrapper>
          <Box
            sx={{
              p: [8, 14],
              height: "100%",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
            }}
          >
            <Box sx={{ width: "100%", maxWidth: 400 }}>
              <Box sx={{ mt: 0 }}>
                <img
                  width={50}
                  height={48}
                  alt=""
                  src="/images/houzer-logo.webp"
                  className=""
                  style={{ marginLeft: "-10px" }}
                ></img>
              </Box>
              <Box sx={{ my: 0 }}>
                <Typography
                  sx={{
                    mb: 4,
                    fontWeight: 500,
                    fontSize: "1.625rem",
                    lineHeight: 1.385,
                  }}
                >
                  Forgot Password? 🔓
                </Typography>
                <Typography sx={{ color: "text.secondary", mb: 4 }}>
                  Enter your email and we&prime;ll send you instructions to
                  reset your password
                </Typography>
              </Box>
              <form
                noValidate
                autoComplete="off"
                onSubmit={(e) => e.preventDefault()}
              >
                <FormControl fullWidth>
                  <TextField
                    autoFocus
                    type="email"
                    size="small"
                    label="Email*"
                    id="email"
                    name="email"
                    inputProps={{
                      ...register("email", {
                        required: "Email address is required",
                        pattern: {
                          value:
                            /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
                          message: "Please enter a valid email address",
                        },
                      }),
                    }}
                    InputLabelProps={{ shrink: true }}
                    placeholder="<EMAIL>"
                    sx={{ display: "flex", mb: 6 }}
                    error={Boolean(errors.email)}
                    helperText={errors.email?.message}
                  />
                </FormControl>

                <Button
                  fullWidth
                  size="medium"
                  type="submit"
                  variant="contained"
                  sx={{ mb: 6 }}
                  disabled={isDisable}
                  onClick={handleSubmit(submit)}
                >
                  {loadingReset ? (
                    <CircularProgress color="inherit" size={24} />
                  ) : (
                    "Send reset link"
                  )}
                </Button>
                <Typography
                  sx={{
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                    "& svg": { mr: 1 },
                  }}
                >
                  <LinkStyled href="/login">
                    <Icon fontSize="1.25rem" icon="tabler:chevron-left" />
                    <span>Back to login</span>
                  </LinkStyled>
                </Typography>
              </form>
            </Box>
          </Box>
        </RightWrapper>
        {!hidden ? (
          <Box
            sx={{
              flex: 1,
              display: "flex",
              position: "relative",
              alignItems: "center",
              borderRadius: "20px",
              justifyContent: "center",
              backgroundColor: "customColors.bodyBg",
              margin: (theme) => theme.spacing(10),
            }}
          >
            <ForgotPasswordIllustration
              alt="forgot-password-illustration"
              src={`/images/pages/reset.webp`}
            />
            <FooterIllustrationsV2 />
          </Box>
        ) : null}
      </Box>
    </>
  );
};
ForgotPassword.getLayout = (page) => <BlankLayout>{page}</BlankLayout>;
ForgotPassword.guestGuard = true;

export default ForgotPassword;
