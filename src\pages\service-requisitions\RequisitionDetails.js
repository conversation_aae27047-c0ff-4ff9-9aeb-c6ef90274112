import {
  Alert,
  Autocomplete,
  Box,
  Button,
  Card,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  Divider,
  FormControl,
  FormHelperText,
  Grid,
  IconButton,
  InputLabel,
  Select,
  Snackbar,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TextField,
  Typography
} from "@mui/material";
import { useContext, useEffect, useRef, useState,Fragment } from "react";

import Tooltip from "@mui/material/Tooltip";
import { Controller, useForm } from "react-hook-form";
import { AuthContext } from "src/context/AuthContext";
import { useAuth } from "src/hooks/useAuth";

import CustomAvatar from "src/@core/components/mui/avatar";

import Icon from "src/@core/components/icon";

import { useTheme } from "@emotion/react";
import { DataGrid } from "@mui/x-data-grid";
import axios from "axios";
import "react-datepicker/dist/react-datepicker.css";
import SelectAutoComplete from "src/@core/components/custom-components/SelectAutoComplete";
import authConfig from "src/configs/auth";
import { getAuthorizationHeaders, getUrl } from "src/helpers/utils";
import MUITableCell from "../SP/MUITableCell";
import CommentsDialog from "./CommentsDialog";
import SiteVisitTimings from "./SiteVisitTimings";

import { Menu, MenuItem } from "@mui/material";
import { fetchIpAddress } from "src/@core/components/custom-components/FetchIpAddress";
import SelectAutoCompleteCustom from "src/@core/components/custom-components/SelectAutoCompleteCustom";
import SelectAutoCompleteSociety from "src/@core/components/custom-components/SelectAutoCompleteSociety";
import SpecificationsDialog from "./SpecificationsDialog";

const field = {
  fontWeight: 500,
  fontSize: "12.75px",
};

const fieldValueStyle = {
  fontWeight: "bold",
  wordWrap: "break-word",
  whiteSpace: "pre-wrap",
  // color: "#66BB6A", // Sets color for the TableCell
  color:'#108A00',
  fontSize: "13px",
};
const tablecellLabelStyle = {
  width: { xs: "50%", sm: "50%", md: "40%" }, // Responsive width adjustment
  padding: "4px", // Ensure padding for better alignment
  textAlign: "right", // Align the text to the left
  verticalAlign: "middle", // Vertically align the text to the middle
  textIndent: {
    lg: "80px",
    md: "80px",
    sm: "100px",
    xs: "0",
  },
};

const tablecellValueStyle = {
  justifyContent: "flex-start",
  alignItems: "center",
  width: { xs: "50%", sm: "50%", md: "60%" }, // Responsive width for small and medium screens
  height: "10px",
};


const RequisitionDetails = ({
  open,
  onClose,
  employeesData,
  fetchRequisitions,
  pageSizeSR,
  pageSR,
  setReferenceData,
  referenceData,
  referralNameData,
  setReferralNameData,
}) => {
  const rowsPerPageOptions = [10, 15, 20, 25, 50, 100];
  const [pageSize, setPageSize] = useState(rowsPerPageOptions[0]);
  const [page, setPage] = useState(1);
  const [rowCount, setRowCount] = useState(0);
  const [currentRow, setCurrentRow] = useState("");
  const theme = useTheme();
  const [addSection, setAddSection] = useState(false);
  const [sectionsUpdate, setSectionsUpdate] = useState(false);
  const [budgetUnits, setBudgetUnits] = useState([]);
  const [budgetCondition, setBudgetCondition] = useState([]);
  const [siteVisitOpen, setSiteVisitOpen] = useState(false);

  const handleSiteVisitDialogOpen = () => setSiteVisitOpen(true);
  const handleSiteVisitDialogClose = () => {
    setSiteVisitOpen(false);
  };

  const auth = useAuth();
  const {
    user,
    getAllListValuesByListNameId,
    listValues,
    listNames,
    setListValues,
    userData,
    templateId,
    setTemplateId,
    templateDetails,
    setTemplateDetails,
  } = useContext(AuthContext);
  const {
    register,
    handleSubmit,
    control,
    reset,
    clearErrors,
    setValue,
    formState: { errors },
  } = useForm();

  const [addButton, setAddButton] = useState(false);

  

  const [dialogMessage, setDialogMessage] = useState("");
  const [openDialogContent, setOpenDialogContent] = useState(false);

  const [budget, setBudget] = useState("");
  const [rangeFrom,setRangeFrom] = useState("")
  const [convList, setConvList] = useState([]);

  const [conversation, setConversation] = useState({});
  const [openMoreInfoDialog, setOpenMoreInfoDialog] = useState(false);

  const handleInfoDialogClose = () => {
    setOpenMoreInfoDialog(false);
  };

  const [allServicesList, setAllServicesList] = useState([]);
  const [requisitionDialog, setRequisitionDialog] = useState(false);
  const [selectedSociety, setSelectedSociety] = useState("");
  const [selectedIndividual, setSelectedIndividual] = useState("");
  const [listOfSocieties, setListOfSocieties] = useState([]);
  const [listOfIndividuals, setListOfIndividuals] = useState([]);
  const [individualData, setIndividualData] = useState();
  const [organisationData, setOrganisationData] = useState({});
  const [isOtherSelected, setIsOtherSelected] = useState(false);
  const [serviceId, setServiceId] = useState("");

  const [specifications, setSpecifications] = useState([]);
  const [templates, setTemplates] = useState([]);
  const [template, setTemplate] = useState("");

  const [specificationsData, setSpecificationsData] = useState([]);
  const [checkedSections, setCheckedSections] = useState({});
  useEffect(() => {
    if (serviceId) {
      axios({
        method: "get",
        url:
          getUrl(authConfig.templateEndpoint) +
          "/?serviceTypeId=" +
          serviceId +
          "&featureType=SPECIFICATIONS",
        headers: getAuthorizationHeaders({
          accept: authConfig.TEMPLATE_GET_NAMES_RES_V1,
        }),
      })
        .then((res) => {
          setTemplates(
            res.data?.templateNames?.map((item) => ({
              value: item.id,
              key: item.name,
            }))
          );
        })
        .catch((err) => console.log("Categories error", err));
    }
  }, [serviceId]);

  useEffect(() => {
    setTemplate(templates?.[0]?.value);
  }, [templates]);

  useEffect(() => {
    if (template) {
      setTemplateId({
        ...templateId,
        id: template,
      });
    }
  }, [template]);

  const societyOptions = listOfSocieties?.map((item) => ({
    value: item.id,
    key: item.name,
  }));

  const individuals = listOfIndividuals?.map((item) => ({
    value: item.id,
    key: item.name,
  }));

  useEffect(() => {
    axios({
      method: "get",
      url: getUrl(authConfig.organisationsEndpoint) + "?category=SOCIETY",
      headers: getAuthorizationHeaders(authConfig.organisationListMIMEType),
    })
      .then((res) => {
        setListOfSocieties(res.data);
      })
      .catch((err) => console.log("error", err));
  }, [addSection]);

  useEffect(() => {
    axios({
      method: "post",
      url: getUrl(authConfig.allListNamesValues) + "?selectType=LIST_VALUES",
    })
      .then((res) => {
        setListValues(res.data.data);
        window.localStorage.setItem(
          authConfig.listValues,
          JSON.stringify(res.data.data)
        );
      })
      .catch((err) => console.log("List values error", err));
  }, [addSection]);

  useEffect(() => {
    if (user?.organisationCategory !== "EMPLOYEE") {
      axios({
        method: "get",
        url:
          getUrl(authConfig.organisationsEndpoint) +
          "/get-by-orgId/" +
          user?.orgId,
        headers: getAuthorizationHeaders(authConfig.individualListMIMEType),
      })
        .then((res) => {
          setOrganisationData(res.data);
        })
        .catch((err) => console.log("error", err));
    }
    if (selectedSociety) {
      axios({
        method: "get",
        url:
          getUrl(authConfig.individualEndpoint) +
          "/individualByOrgId/" +
          selectedSociety,
        headers: getAuthorizationHeaders(authConfig.individualListMIMEType),
      })
        .then((res) => {
          setListOfIndividuals(res.data);
        })
        .catch((err) => console.log("error", err));

      axios({
        method: "get",
        url:
          getUrl(authConfig.organisationsEndpoint) +
          "/get-by-orgId/" +
          selectedSociety,
        headers: getAuthorizationHeaders(authConfig.individualListMIMEType),
      })
        .then((res) => {
          setOrganisationData(res.data);
        })
        .catch((err) => console.log("error", err));
    }
  }, [selectedSociety]);

  useEffect(() => {
    if (user?.organisationCategory !== "EMPLOYEE") {
      axios({
        method: "get",
        url: getUrl(authConfig.individualEndpoint) + "/" + user?.id,
        headers: getAuthorizationHeaders(authConfig.individualGetMIMEType),
      })
        .then((res) => {
          setIndividualData(res.data);
        })
        .catch((err) => console.log("error", err));
    } else if (selectedIndividual) {
      axios({
        method: "get",
        url: getUrl(authConfig.individualEndpoint) + "/" + selectedIndividual,
        headers: getAuthorizationHeaders(authConfig.individualGetMIMEType),
      })
        .then((res) => {
          setIndividualData(res.data);
        })
        .catch((err) => console.log("error", err));
    }
  }, [selectedIndividual]);

  const [siteVisits, setSiteVisits] = useState([]);

  const handleSocietyChange = (newValue) => {
    setSelectedSociety(newValue);
  };

  const handleClick = () => {
    setRequisitionDialog(true);
  };

  const handleClickClose = () => {
    setRequisitionDialog(false);
  };

  const handleCancel = () => {
    reset();
    setSiteVisits([]);
    setConvList([]);
    setSelectedSociety("");
    setSelectedIndividual("");
    setIndividualData([]);
    setSpecificationsData([]);
    setSpecifications([]);
    setTemplateDetails(null);
    setServiceId("");
    setRangeFrom("")
    setValue("subServices", []);
    setValue("anyOtherService", "");
    setValue("requirementDeadLine", "");
    setValue("priority", "");
    setBudget("");
    setValue("specifications", "");
    setValue("societyRemarks", "");
    setReferenceType("");
    setReferralName("");
    setValue("assignedTo", "");
    setValue("workWithSP", "");
    setValue("workWithCHS", "");
    setValue("workForWorkOrder", "");
    setValue("status", "");
    setValue("houzerSocietyTeamMember", "");
    setValue("dataSentDate", "");
    setRequisitionDialog(false);
    setAddButton(false);
    setCurrentRow("");
    setTemplate("");
    setCheckedSections({})
    onClose();
  };

  const [statusData, setStatusData] = useState(null);

  const [priorityData, setPriorityData] = useState(null);

  const [referenceType, setReferenceType] = useState();
  const [referralName, setReferralName] = useState();

  const handlePrioritySuccess = (data) => {
    setPriorityData(data?.listValues);
  };

  const handleError = (error) => {
    console.error("society requisition: All Services:", error);
  };

  useEffect(() => {
    if (!!authConfig) {
      getAllListValuesByListNameId(
        authConfig.leadPriorityListNamesId,
        handlePrioritySuccess,
        handleError
      );
    }
  }, [authConfig]);

  useEffect(() => {
    getAllListValuesByListNameId(
      authConfig.allServicesListNameId,
      (data) =>
        setAllServicesList(
          data?.listValues.map((item) => ({
            value: item.id,
            key: item.listValue,
          }))
        ),
      handleError
    );
    getAllListValuesByListNameId(
      authConfig.statusListNamesId,
      (data) =>
        setStatusData(
          data?.listValues.map((item) => ({
            value: item.id,
            key: item.listValue,
          }))
        ),
      handleError
    );

    getAllListValuesByListNameId(
      authConfig.referenceTypeListNameId,
      (data) =>
        setReferenceData(
          data?.listValues.map((item) => ({
            value: item.id,
            key: item.listValue,
          }))
        ),
      handleError
    );

    getAllListValuesByListNameId(
      authConfig.referralNameId,
      (data) =>
        setReferralNameData(
          data?.listValues.map((item) => ({
            value: item.id,
            key: item.listValue,
          }))
        ),
      handleError
    );

    getAllListValuesByListNameId(
      authConfig.budgetUnits,
      (data) =>
        setBudgetUnits(
          data?.listValues.map((item) => ({
            value: item.id,
            key: item.listValue,
          }))
        ),
      handleError
    );
    getAllListValuesByListNameId(
      authConfig.budgetCondition,
      (data) =>
        setBudgetCondition(
          data?.listValues.map((item) => ({
            value: item.id,
            key: item.listValue,
          }))
        ),
      handleError
    );
  }, [authConfig, sectionsUpdate]);

  const handleSelectChange = (event) => {
    setRequisitionDialog(false);
    setTemplate("");
    setAddButton(true);
    setSpecificationsData([]);
    setSpecifications([]);
    setTemplateDetails(null);
    const value = event?.target.value;
    setServiceId(value);
    const name = value
      ? listValues?.find((item) => item?.id === value)?.name
      : null;
    if (name === "Any other") {
      setIsOtherSelected(true);
    } else {
      setIsOtherSelected(false);
    }
  };

  const handleSuccess = () => {
    const message = `
    <div> 
      <h3>Requisition added Successfully.</h3>
    </div>
  `;
    setDialogMessage(message);
    setOpenDialogContent(true);
  };

  const handleFailure = () => {
    const message = `
    <div> 
      <h3> Failed to Add Requisition. Please try again later.</h3>
    </div>
  `;
    setDialogMessage(message);
    setOpenDialogContent(true);
  };

  const isApiCalling = useRef(false);

  const getOrgName = (selectedSociety) => {
    const matchedSociety = societyOptions.find(
      (option) => option.value === selectedSociety
    );

    // Return the key if a match is found, otherwise return null or an appropriate fallback
    return matchedSociety ? matchedSociety.key : null;
  };
  const [showToast, setShowToast] = useState(false); // State for toast visibility
  const [toastMessage, setToastMessage] = useState("");
  const handleValidationErrors = (errors) => {
    const firstErrorKey = Object.keys(errors)[0];
    if (firstErrorKey) {
      const errorMessage =
        errors[firstErrorKey]?.message || "Validation error occurred!";
      setToastMessage(errorMessage);
      setShowToast(true);
    }
  };

  const handleToastClose = () => {
    setShowToast(false);
    setToastMessage("");
  };
  async function submit(data) {
    if (isApiCalling.current) {
      // API call is already in progress, return early
      return;
    }

    isApiCalling.current = true;
    const ipAddress = await fetchIpAddress();

    const currentDate = new Date();
    const currentTime = currentDate.toTimeString()?.split(" ")[0];

    const location =
      listValues?.find(
        (item) =>
          item.name === organisationData?.location &&
          item.listNamesId === authConfig.locationlistNameId
      )?.id || null;

    let fields;

    if (user?.organisationCategory !== "EMPLOYEE") {
      fields = {
        initiator: {
          id: user.id,
          firstName: individualData?.firstName,
          lastName: individualData?.lastName,
          email: individualData?.email,
          mobileNumber: individualData?.mobileNumber,
        },
        initiatingEntity: {
          id: user?.orgId,
          orgName: getOrgName(user?.orgId),
          location: location,
          zone: organisationData?.zone,
          address: organisationData?.societyAddress,
        },
        serviceTypeId: data?.serviceType,
        requirementDeadLine: data?.requirementDeadLine
          ? `${data?.requirementDeadLine}T${currentTime}`
          : null,
        priority: data?.priority,
        budget: {
          rangeFrom: rangeFrom,
          rangeTo:data?.rangeTo,
          units:budget,
          condition:data?.condition
        },
        remarks: data?.societyRemarks,
        specifications: {
          anyOtherServiceProvided: data?.anyOtherService,
          sections: specifications,
        },
        referral: {
          referenceType: referenceType,
          houzerSocietyTeamMember: data?.houzerSocietyTeamMember,
          teamReference: referralName,
          dataSentDate: data?.dataSentDate === "" ? null : data?.dataSentDate,
          deciMatrixPresentationDate: data?.deciMatrixPresentationDate,
          curatedBy: data?.curatedBy,
          curatedOn: data?.curatedOn,
        },
        status: data?.status,
        siteVisitTimings: siteVisits,
        conversations: convList,
        ipAddress: ipAddress,
      };
    } else {
      fields = {
        initiator: {
          id: selectedIndividual,
          firstName: individualData?.firstName,
          lastName: individualData?.lastName,
          email: individualData?.email,
          mobileNumber: individualData?.mobileNumber,
        },
        initiatingEntity: {
          id: selectedSociety,
          orgName: getOrgName(selectedSociety),
          location: location,
          zone: organisationData?.zone,
          address: organisationData?.societyAddress,
        },
        serviceTypeId: data?.serviceType,
        requirementDeadLine: data?.requirementDeadLine
          ? `${data?.requirementDeadLine}T${currentTime}`
          : null,
        priority: data?.priority,
        budget: {
          rangeFrom:rangeFrom,
          rangeTo:data?.rangeTo,
          units:budget,
          condition:data?.condition
        },
        remarks: data?.societyRemarks,
        specifications: {
          anyOtherServiceProvided: data?.anyOtherService,
          sections: specifications,
        },
        referral: {
          referenceType: referenceType,
          houzerSocietyTeamMember: data?.houzerSocietyTeamMember,
          teamReference: referralName,
          dataSentDate: data?.dataSentDate === "" ? null : data?.dataSentDate,
          deciMatrixPresentationDate: data?.deciMatrixPresentationDate,
          curatedBy: data?.curatedBy,
          curatedOn: data?.curatedOn,
        },
        status: data?.status,
        siteVisitTimings: siteVisits,
        workAssignments: [
          {
            workFor: "SP",
            assignedTo: data?.workWithSP,
            assignedDate: data?.dataSentDate
              ? `${data.dataSentDate}T${currentTime}`
              : null,
          },
          {
            workFor: "CHS",
            assignedTo: data?.workWithCHS,
            assignedDate: data?.dataSentDate
              ? `${data.dataSentDate}T${currentTime}`
              : null,
          },
          {
            workFor: "WORK_ORDERS",
            assignedTo: data?.workForWorkOrder,
            assignedDate: data?.dataSentDate
              ? `${data.dataSentDate}T${currentTime}`
              : null,
          },
          {
            workFor: "DRI",
            assignedTo: data?.assignedTo,
            assignedDate: data?.dataSentDate
              ? `${data.dataSentDate}T${currentTime}`
              : null,
          },
        ].filter((item) => item.assignedTo != null && item.assignedTo !== ""),
        conversations: convList,
        ipAddress: ipAddress,
      };
    }
    try {
      const response = await auth.postRequisition(
        fields,
        handleFailure,
        handleSuccess
      );
    } catch (error) {
      console.error("Employee Creation failed:", error);
      handleFailure();
    } finally {
      isApiCalling.current = false;
    }

    fetchRequisitions(pageSR, pageSizeSR);
    handleCancel();
    isApiCalling.current = false;
  }

  const handleButtonClick = () => {
    setOpenDialogContent(false);
  };

  const [menu, setMenu] = useState(null);

  const handleCloseMenuItems = () => {
    setMenu(null);
  };

  const cols = [
    { field: "contactPersonName", headerName: "Name", minWidth: 145, flex: 2 },
    {
      field: "contactNumber",
      headerName: "Contact Number",
      minWidth: 145,
      flex: 2,
    },
    {
      field: "siteVisitDate",
      headerName: "Site Visit Date",
      minWidth: 145,
      flex: 2,
    },
    { field: "startTime", headerName: "Start Time", minWidth: 140, flex: 2 },
    { field: "endTime", headerName: "End Time", minWidth: 140, flex: 2 },
    {
      field: "actions",
      headerName: "Actions",
      sortable: false,
      minWidth: 120,
      flex: 1,
      disableClickEventBubbling: true,
      renderCell: (params) => {
        const onClick = (event) => {
          event.stopPropagation();
          setMenu(event.currentTarget);
          const row = params.row;
          setCurrentRow(row);
        };

        const onClickViewProfile = () => {
          setSiteVisitOpen(true);
          handleCloseMenuItems();
        };
        const onClickDeleteProfile = () => {
          const sv = siteVisits.filter(
            (siteVisit) => siteVisit?.id !== currentRow?.id
          );
          setSiteVisits(sv);
          handleCloseMenuItems();
        };

        return (
          <div
            style={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
            }}
          >
            <Tooltip title="Actions">
              <CustomAvatar
                skin="light"
                variant="rounded"
                sx={{
                  mr: { xs: 2, lg: 4 },
                  width: 34,
                  height: 34,
                  cursor: "pointer",
                }}
                onClick={onClick}
              >
                <Icon icon="bi:three-dots-vertical" />
              </CustomAvatar>
            </Tooltip>
            <Menu
              id="actions-menu"
              anchorEl={menu}
              keepMounted
              open={Boolean(menu)}
              onClose={handleCloseMenuItems}
            >
              <MenuItem onClick={onClickViewProfile}>Edit</MenuItem>
              <MenuItem onClick={onClickDeleteProfile}>Delete</MenuItem>
            </Menu>
          </div>
        );
      },
    },
  ];

  const handlePageChange = (direction) => {
    if (direction === page) {
      setPage(page + 1);
    } else {
      setPage(page - 1);
    }
  };

  const handlePageSizeChange = (params) => {
    if (params) {
      setPageSize(params);
      setPage(1);
    }
  };

  return (
    <>
      <Dialog fullScreen open={open} onClose={handleCancel}>
        <DialogTitle
          sx={{
            position: "relative",
            borderBottom: (theme) => `1px solid ${theme.palette.divider}`,
            p: (theme) => `${theme.spacing(1.75, 5)} !important`,
            display: "flex",
            alignItems: "center",
            justifyContent: { xs: "start" },
            fontSize: { xs: 19, md: 20 },
            height: "50px", // Set fixed height for header
          }}
          textAlign={"center"}
        >
          {/* Create Service Requisition */}
          <Box
            sx={{
              fontSize: {
                xs: 14,
                sm: 15,
                md: 17,
                lg: 16,
              },
              fontWeight: 600,
              ml: {
                xs: 3,
                xl: 3,
              },
            }}
          >
            Create Service Requisition
          </Box>
          <Box
            sx={{
              position: "absolute",
              top: "9px",
              right: "10px",
              mr: {
                xs: 5.5,
                sm: 5.5,
                md: 5.5,
                lg: 5.5,
                xl: 10,
              },
            }}
          >
            <IconButton
              size="small"
              onClick={handleCancel}
              sx={{
                // p: "0.438rem",
                borderRadius: 1,
                color: "common.white",
                backgroundColor: "primary.main",
                "&:hover": {
                  backgroundColor: "#66BB6A",
                  transition: "background 0.5s ease, transform 0.5s ease",
                },
              }}
            >
              <Icon icon="tabler:x" fontSize="1rem" />
            </IconButton>
          </Box>
        </DialogTitle>
        <DialogContent
          sx={{
            position: "relative",
            pt: (theme) => `${theme.spacing(8)} !important`,
            pb: (theme) => `${theme.spacing(5)} !important`,
            px: (theme) => [`${theme.spacing(8)} !important`],
          }}
        >
          {user.organisationCategory !== "SOCIETY" && (
            <Card>
              <Grid
                sx={{
                  backgroundColor: "#f2f7f2",
                  mt: 4,
                  paddingTop: 0,
                  height: "36px",
                  display: "flex",
                  alignItems: "center",
                }}
              >
                <Typography
                  variant="body1"
                  fontWeight={"bold"}
                  sx={{ mt: 0, ml: 2 }}
                >
                  Society Information
                </Typography>
                <Divider />
              </Grid>
              <Divider />

              <Grid container spacing={5} style={{ padding: "16px" }}>
                <Grid item xs={12} sm={4}>
                  <FormControl
                    fullWidth
                    error={Boolean(errors.selectedSociety)}
                  >
                    <Controller
                      name="selectedSociety"
                      control={control}
                      rules={{
                        required: "Society is required",
                      }}
                      render={({ field }) => (
                        <SelectAutoCompleteSociety
                          id="selectedSociety"
                          label="Select Society *"
                          nameArray={societyOptions}
                          value={selectedSociety}
                          setAddSection={setAddSection}
                          addSection={addSection}
                          onChange={(event) => {
                            field.onChange(event.target?.value);
                            setSelectedSociety(event.target?.value);
                          }}
                        />
                      )}
                    />
                    {errors.selectedSociety && (
                      <FormHelperText sx={{ color: "error.main" }}>
                        {errors.selectedSociety.message}
                      </FormHelperText>
                    )}
                  </FormControl>
                </Grid>
                {selectedSociety && (
                  <Grid item xs={12} sm={4}>
                    <FormControl fullWidth error={Boolean(errors.individual)}>
                      <Controller
                        name="individual"
                        control={control}
                        rules={{
                          required: "Society Member is required",
                        }}
                        render={({ field }) => (
                          <SelectAutoComplete
                            id="individual"
                            label="Select Society Member *"
                            nameArray={individuals}
                            value={selectedIndividual}
                            onChange={(event) => {
                              field.onChange(event.target.value);
                              setSelectedIndividual(event.target.value);
                            }}
                          />
                        )}
                      />
                      {errors.individual && (
                        <FormHelperText sx={{ color: "error.main" }}>
                          {errors.individual.message}
                        </FormHelperText>
                      )}
                    </FormControl>
                  </Grid>
                )}
                {!selectedIndividual && <Grid item xs={12} sm={4}></Grid>}

                {selectedSociety && selectedIndividual && (
                  <>
                    <Grid item xs={12} sm={4}></Grid>
                    <Grid item xs={12} sm={4}>
                      <Typography fontSize="body1" sx={{ mr: 1, ml: 1 }}>
                        Email : <strong> {individualData?.email}</strong>
                      </Typography>
                    </Grid>
                    <Grid item xs={12} sm={4}>
                      <Typography fontSize="body1" sx={{ mr: 1, ml: 1 }}>
                        Mobile Number :{" "}
                        <strong> {individualData?.mobileNumber}</strong>
                      </Typography>
                    </Grid>
                  </>
                )}
                {selectedIndividual && <Grid item xs={12} sm={4}></Grid>}

                {selectedSociety && (
                  <>
                    <Grid item xs={12} sm={4}>
                      <Typography fontSize="body1" sx={{ mr: 1, ml: 1 }}>
                        Location : <strong>{organisationData?.location ||listValues?.find((item) => item.id === organisationData?.locationId)
                        ?.name}</strong>
                        
                      </Typography>
                    </Grid>
                    <Grid item xs={12} sm={4}>
                      <Typography fontSize="body1" sx={{ mr: 1, ml: 1 }}>
                        Zone : <strong> {organisationData?.zone}</strong>
                      </Typography>
                    </Grid>
                    <Grid item xs={12} sm={12}>
                      <Typography fontSize="body1" sx={{ mr: 1, ml: 1 }}>
                        Address :{" "}
                        <strong> {organisationData?.societyAddress}</strong>
                      </Typography>
                    </Grid>
                  </>
                )}
              </Grid>
            </Card>
          )}

          <Card>
            <Grid
              sx={{
                backgroundColor: "#f2f7f2",
                mt: 4,
                paddingTop: 0,
                height: "36px",
                display: "flex",
                alignItems: "center",
              }}
            >
              <Typography
                variant="body1"
                fontWeight={"bold"}
                sx={{ mt: 0, ml: 2 }}
              >
                Requisition Details
              </Typography>
              <Divider />
            </Grid>
            <Divider />

            <Grid container spacing={5} style={{ padding: "16px" }}>
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth error={Boolean(errors.serviceType)}>
                  <Controller
                    name="serviceType"
                    control={control}
                    rules={{ required: "Service Type is required " }}
                    render={({ field }) => (
                      <SelectAutoCompleteCustom
                        id="serviceType"
                        label="Service Type *"
                        nameArray={allServicesList}
                        value={serviceId}
                        setAddSection={setAddSection}
                        setValuesUpdate={setSectionsUpdate}
                        listNameId={authConfig.allServicesListNameId}
                        onChange={(e) => {
                          field.onChange(e.target.value);
                          handleSelectChange(e);
                          setSpecificationsData([]);
                        }}
                      />
                    )}
                  />
                  {errors.serviceType && (
                    <FormHelperText sx={{ color: "error.main" }}>
                      {errors.serviceType.message}
                    </FormHelperText>
                  )}
                </FormControl>
              </Grid>

              <Grid item xs={12} sm={6}>
                {template && (
                  <Button
                    display="flex"
                    justifyContent="center"
                    variant="contained"
                    color="primary"
                    onClick={handleClick}
                  >
                    Add/Edit Specifications
                  </Button>
                )}
              </Grid>

            

              {specificationsData?.length > 0 && (
                <>  {/* specificationsData */}
                  <TableContainer>
                    <Table>
                    <TableRow>
                            <MUITableCell sx={tablecellValueStyle}  colSpan={2}>
                              <Typography className="data-field" style={fieldValueStyle} sx={{ backgroundColor: "#f2f7f2" ,mt:2}}>
                                Specifications
                              </Typography>
                            </MUITableCell>
                          </TableRow>
 
                          {specificationsData?.map(
                            (section) => (
                              <Fragment key={section.id}>
                                {/* Display section ID */}
                                <TableRow>
                                  <MUITableCell
                                    colSpan={2}
                                   
                                  >
                                    <Typography
                                      variant="body2"
                                      fontWeight={"bold"}
                                      sx={{ mt: 0, ml: 2 }}
                                    >
                                      {
                                        listValues?.find(
                                          (item) => item.id === section.id
                                        )?.name
                                      }
                                    </Typography>
                                  </MUITableCell>
                                </TableRow>
 
                                {section.fields?.map((field) => (
                                  <TableRow key={field.id}>
                                    <MUITableCell sx={tablecellLabelStyle}>
                                      <Typography style={field} >
                                        {
                                          listNames?.find(
                                            (item) => item.id === field.labelId
                                          )?.name
                                        }
                                        {":"}
                                      </Typography>
                                    </MUITableCell>
                                    <MUITableCell sx={tablecellValueStyle}>
                                      <Typography
                                        className="data-field"
                                        style={fieldValueStyle}
                                      >
                                        {
                                          field.componentId ===
                                            authConfig.textFieldComponentId ||
                                          field.componentId ===
                                            authConfig.textAreaComponentId ||
                                          field.componentId ===
                                            authConfig.numberTextFieldComponentId
                                            ? field.providedTextValue // Show providedTextValue if the componentId matches
                                            : field.selectedDropdownValues
                                                ?.map((item) => item.key)
                                                .join(", ") // Show the keys as a comma-separated string
                                        }
                                      </Typography>
                                    </MUITableCell>
                                  </TableRow>
                                ))}
                              </Fragment>
                            )
                          )}
                      {/* <TableHead>
                        <TableRow>
                          <TableCell>Category</TableCell>
                          <TableCell>Selected Options</TableCell>
                        </TableRow>
                      </TableHead>
                    
                      <TableBody>
                        {specificationsData?.map((section) =>
                          section.fields.map((field) => (
                            <TableRow key={field.id}>
                              <TableCell>
                                <Typography>
                                  {
                                    listNames.find(
                                      (item) => item.id === field.labelId
                                    )?.name
                                  }
                                </Typography>
                              </TableCell>
                              <TableCell>
                                <Typography className="data-field">
                                  {
                                    field.componentId ===
                                      authConfig.textFieldComponentId ||
                                    field.componentId ===
                                      authConfig.textAreaComponentId ||
                                    field.componentId ===
                                      authConfig.numberTextFieldComponentId
                                      ? field.providedTextValue // Show providedTextValue if the componentId matches
                                      : field.selectedDropdownValues
                                          ?.map((item) => item.key)
                                          .join(", ") // Show the keys as a comma-separated string
                                  }
                                </Typography>
                              </TableCell>
                            </TableRow>
                          ))
                        )}
                      </TableBody> */}
                    </Table>
                  </TableContainer>
                </>
              )}
              {isOtherSelected && (
                <Grid item xs={12} sm={4} xl={2} md={3}>
                  <FormControl fullWidth>
                    <Controller
                      name="anyOtherService"
                      control={control}
                      render={({ field }) => (
                        <TextField
                          {...field}
                          label="Other Service"
                          InputLabelProps={{ shrink: true }}
                          size="small"
                          placeholder="Enter Other Service"
                          error={Boolean(errors.anyOtherService)}
                          helperText={errors.anyOtherService?.message}
                          aria-describedby="validation-basic-anyOtherService"
                        />
                      )}
                    />
                  </FormControl>
                </Grid>
              )}
              <Grid item xs={12} md={3} sm={3} lg={3} xl={2}>
                <FormControl fullWidth>
                  <Controller
                    name="requirementDeadLine"
                    control={control}
                    defaultValue=""
                    render={({ field }) => (
                      <TextField
                        {...field}
                        size="small"
                        label="Requirement DeadLine"
                        type="date"
                        InputLabelProps={{ shrink: true }}
                        aria-describedby="requirementDeadLine"
                        value={field?.value}
                        onChange={(e) => {
                          field.onChange(e.target.value);
                        }}
                      />
                    )}
                  />
                </FormControl>
              </Grid>

              <Grid item xs={12} sm={3} xl={2}>
                <FormControl fullWidth>
                  <InputLabel id="priority"> Priority</InputLabel>
                  <Controller
                    name="priority"
                    control={control}
                    render={({ field }) => (
                      <Select
                        {...field}
                        labelId="priority-label"
                        label="Priority"
                        id="priority"
                        size="small"
                      >
                        {priorityData?.map((status) => (
                          <MenuItem key={status.id} value={status.id}>
                            {status.listValue}
                          </MenuItem>
                        ))}
                      </Select>
                    )}
                  />
                </FormControl>
              </Grid>

              <Grid item xs={12} sm={6}>
                <Box display="flex" alignItems="center">
                  <Typography id="budget" gutterBottom>
                    Budget
                  </Typography>
                  <Grid item xs={4} sm={5} lg={2} sx={{ marginLeft: "6px" }}>
                    <FormControl fullWidth>
                      <Controller
                        name="rangeFrom"
                        control={control}
                        rules={{ required: true }}
                        render={({ field }) => (
                          <TextField
                            {...field}
                            label="from *"
                            type="number"
                            InputLabelProps={{ shrink: true }}
                            size="small"
                            value={rangeFrom}
                            onChange={(e) => {
                              field.onChange(e);
                              setRangeFrom(e.target.value)
                            }}
                            error={Boolean(errors.rangeFrom)}
                            helperText={errors.rangeFrom?.message}
                            aria-describedby="validation-basic-rangeFrom"
                          />
                        )}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item xs={4} sm={5} lg={2} sx={{ marginLeft: "6px" }}>
                    <FormControl fullWidth>
                      <Controller
                        name="rangeTo"
                        control={control}
                        rules={{ required: false }}
                        render={({ field }) => (
                          <TextField
                            {...field}
                            label="to"
                            type="number"
                            InputLabelProps={{ shrink: true }}
                            size="small"
                            error={Boolean(errors.rangeTo)}
                            helperText={errors.rangeTo?.message}
                            aria-describedby="validation-basic-rangeTo"
                          />
                        )}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item xs={4} sm={5} lg={3} sx={{ marginLeft: "6px" }}>
                     <FormControl fullWidth error={Boolean(errors.budgetUnits)}>
                      <Controller
                        name="budgetUnits"
                        control={control}
                        rules={{ required: rangeFrom > 0 ? "Budget Units are required" : false }}  
                        render={({ field }) => (
                          <SelectAutoComplete
                            id="budgetUnits"
                            size="small"
                            label="Units"
                            nameArray={budgetUnits || []}
                            value={budget}
                            onChange={(e) => {
                              field.onChange(e);
                              setBudget(e.target.value)
                            }}
                            error={Boolean(errors.budgetUnits)}
                            helperText={
                              errors.budgetUnits
                                ? errors.budgetUnits.message
                                : ""
                            }
                          />
                        )}
                      />
                      {errors.budgetUnits && (
                        <FormHelperText sx={{ color: "error.main" }}>
                          {errors.budgetUnits.message}
                        </FormHelperText>
                      )}
                    </FormControl>
                  </Grid>
                  <Grid item xs={4} sm={5} lg={3} sx={{ marginLeft: "6px" }}>
                    <FormControl fullWidth>
                      <Controller
                        name="condition"
                        control={control}
                        rules={{ required: false }}
                        render={({ field }) => (
                          <SelectAutoComplete
                            id="budget-condition"
                            size="small"
                            label="Condition"
                            nameArray={budgetCondition || []}
                            value={field.value || []}
                            onChange={(e) => {
                              field.onChange(e);
                            }}
                            error={Boolean(errors.condition)}
                          />
                        )}
                      />
                    </FormControl>
                  </Grid>
                </Box>              
              </Grid>

              <Grid item xs={12}>
                <FormControl fullWidth>
                  <Controller
                    name="societyRemarks"
                    control={control}
                    rules={{ required: false }}
                    //   defaultValue={formData?.awards}
                    render={({ field }) => (
                      <TextField
                        rows={3}
                        multiline
                        {...field}
                        label="Society Remarks"
                        InputLabelProps={{ shrink: true }}
                        inputProps={{ maxLength: 1000 }}
                        error={Boolean(errors.societyRemarks)}
                        aria-describedby="societyRemarks"
                      />
                    )}
                  />
                  {errors.societyRemarks && (
                    <FormHelperText
                      sx={{ color: "error.main" }}
                      id="societyRemarks"
                    >
                      This field is required
                    </FormHelperText>
                  )}
                </FormControl>
              </Grid>
            </Grid>
          </Card>

          {user.organisationCategory === "EMPLOYEE" && (
            <Card>
              <Grid
                sx={{
                  backgroundColor: "#f2f7f2",
                  mt: 4,
                  paddingTop: 0,
                  height: "36px",
                  display: "flex",
                  alignItems: "center",
                }}
              >
                <Typography
                  variant="body1"
                  fontWeight={"bold"}
                  sx={{ mt: 0, ml: 2 }}
                >
                  Status and Assignment Details
                </Typography>
                <Divider />
              </Grid>
              <Divider />

              <Grid container spacing={5} style={{ padding: "16px" }}>
                <Grid item xs={12} md={3} sm={4}>
                  <FormControl fullWidth error={Boolean(errors.assignedTo)}>
                    <Controller
                      name="assignedTo"
                      control={control}
                      rules={{ required: "Assigned To is required" }}
                      render={({ field }) => (
                        <Autocomplete
                          {...field}
                          onChange={(event, newValue) => {
                            field.onChange(newValue ? newValue.id : null);
                          }}
                          value={
                            employeesData
                              ?.map((data) => ({
                                id: data.id,
                                label: data.name,
                              }))
                              .find((emp) => emp.id === field.value) || null
                          }
                          options={
                            employeesData?.map((data) => ({
                              id: data.id,
                              label: data.name,
                            })) || []
                          }
                          getOptionLabel={(option) => option.label || ""}
                          renderInput={(params) => (
                            <TextField
                              {...params}
                              label="Assigned To *"
                              error={Boolean(errors.assignedTo)}
                              helperText={
                                errors.assignedTo
                                  ? errors.assignedTo.message
                                  : ""
                              }
                            />
                          )}
                          size="small"
                        />
                      )}
                    />
                  </FormControl>
                </Grid>

                <Grid item xs={12} md={3} sm={4}>
                  <FormControl fullWidth error={Boolean(errors.workWithSP)}>
                    <Controller
                      name="workWithSP"
                      control={control}
                      rules={{ required: false }}
                      render={({ field }) => (
                        <Autocomplete
                          {...field}
                          onChange={(event, newValue) => {
                            field.onChange(newValue ? newValue.id : null);
                          }}
                          value={
                            employeesData
                              ?.map((data) => ({
                                id: data.id,
                                label: data.name,
                              }))
                              .find((emp) => emp.id === field?.value) || null
                          }
                          options={
                            employeesData?.map((data) => ({
                              id: data.id,
                              label: data.name,
                            })) || []
                          }
                          getOptionLabel={(option) => option.label || ""}
                          renderInput={(params) => (
                            <TextField
                              {...params}
                              label="Work With SP"
                              error={Boolean(errors.workWithSP)}
                              helperText={
                                errors.workWithSP
                                  ? errors.workWithSP.message
                                  : ""
                              }
                            />
                          )}
                          size="small"
                        />
                      )}
                    />
                  </FormControl>
                </Grid>
                <Grid item xs={12} md={3} sm={4}>
                  <FormControl fullWidth error={Boolean(errors.workWithCHS)}>
                    <Controller
                      name="workWithCHS"
                      control={control}
                      rules={{ required: false }}
                      render={({ field }) => (
                        <Autocomplete
                          {...field}
                          onChange={(event, newValue) => {
                            field.onChange(newValue ? newValue.id : null);
                          }}
                          value={
                            employeesData
                              ?.map((data) => ({
                                id: data.id,
                                label: data.name,
                              }))
                              .find((emp) => emp.id === field?.value) || null
                          }
                          options={
                            employeesData?.map((data) => ({
                              id: data.id,
                              label: data.name,
                            })) || []
                          }
                          getOptionLabel={(option) => option.label || ""}
                          renderInput={(params) => (
                            <TextField
                              {...params}
                              label="Work With CHS"
                              error={Boolean(errors.workWithCHS)}
                              helperText={
                                errors.workWithCHS
                                  ? errors.workWithCHS.message
                                  : ""
                              }
                            />
                          )}
                          size="small"
                        />
                      )}
                    />
                  </FormControl>
                </Grid>
                <Grid item xs={12} md={3} sm={4}>
                  <FormControl
                    fullWidth
                    error={Boolean(errors.workForWorkOrder)}
                  >
                    <Controller
                      name="workForWorkOrder"
                      control={control}
                      rules={{ required: false }}
                      render={({ field }) => (
                        <Autocomplete
                          {...field}
                          onChange={(event, newValue) => {
                            field.onChange(newValue ? newValue.id : null);
                          }}
                          value={
                            employeesData
                              ?.map((data) => ({
                                id: data.id,
                                label: data.name,
                              }))
                              .find((emp) => emp.id === field?.value) || null
                          }
                          options={
                            employeesData?.map((data) => ({
                              id: data.id,
                              label: data.name,
                            })) || []
                          }
                          getOptionLabel={(option) => option.label || ""}
                          renderInput={(params) => (
                            <TextField
                              {...params}
                              label="Work For Work Order"
                              error={Boolean(errors.workForWorkOrder)}
                              helperText={
                                errors.workForWorkOrder
                                  ? errors.workForWorkOrder?.message
                                  : ""
                              }
                            />
                          )}
                          size="small"
                        />
                      )}
                    />
                  </FormControl>
                </Grid>

                <Grid item xs={12} md={3} sm={4}>
                  <FormControl fullWidth error={Boolean(errors.status)}>
                    <Controller
                      name="status"
                      control={control}
                      rules={{ required: false }}
                      render={({ field }) => (
                        <SelectAutoCompleteCustom
                          id="status"
                          label="Status"
                          nameArray={statusData}
                          value={field.value}
                          setAddSection={setAddSection}
                          setValuesUpdate={setSectionsUpdate}
                          listNameId={authConfig.statusListNamesId}
                          onChange={(e) => {
                            field.onChange(e.target.value);
                          }}
                        />
                      )}
                    />
                  </FormControl>
                </Grid>
                <Grid item xs={12} md={3} sm={4}>
                  <FormControl fullWidth error={Boolean(errors.curatedBy)}>
                    <Controller
                      name="curatedBy"
                      control={control}
                      rules={{ required: false }}
                      render={({ field }) => (
                        <Autocomplete
                          {...field}
                          onChange={(event, newValue) => {
                            field.onChange(newValue ? newValue.id : null);
                          }}
                          value={
                            employeesData
                              ?.map((data) => ({
                                id: data.id,
                                label: data.name,
                              }))
                              .find((emp) => emp.id === field.value) || null
                          }
                          options={
                            employeesData?.map((data) => ({
                              id: data.id,
                              label: data.name,
                            })) || []
                          }
                          getOptionLabel={(option) => option.label || ""}
                          renderInput={(params) => (
                            <TextField
                              {...params}
                              label="Curated By"
                              error={Boolean(errors.curatedBy)}
                              helperText={
                                errors.curatedBy ? errors.curatedBy.message : ""
                              }
                            />
                          )}
                          size="small"
                        />
                      )}
                    />
                  </FormControl>
                </Grid>
                <Grid item xs={12} md={3} sm={4}>
                  <FormControl fullWidth>
                    <Controller
                      name="curatedOn"
                      control={control}
                      render={({ field }) => (
                        <TextField
                          {...field}
                          size="small"
                          label="Curated On"
                          type="date"
                          InputLabelProps={{ shrink: true }}
                          aria-describedby="curatedOn"
                          value={field.value}
                          onChange={(e) => {
                            field.onChange(e.target.value);
                          }}
                        />
                      )}
                    />
                  </FormControl>
                </Grid>
                <Grid item xs={12} md={3} sm={4}></Grid>
                <Grid item xs={12} md={3} sm={4}>
                  <FormControl fullWidth>
                    <Controller
                      name="dataSentDate"
                      control={control}
                      render={({ field }) => (
                        <TextField
                          {...field}
                          size="small"
                          label={
                            <span
                              style={{ display: "flex", alignItems: "center" }}
                            >
                              Data Sent Date
                              <Tooltip
                                title="This field is for internal use and should indicate the date when the SR was sent to the Closing Team."
                                arrow
                                placement="top"
                              >
                                <span>
                                  <Icon
                                    icon="mingcute:information-line"
                                    style={{
                                      cursor: "pointer",
                                      marginLeft: 10,
                                      fontSize: 15,
                                    }}
                                  />
                                </span>
                              </Tooltip>
                            </span>
                          }
                          type="date"
                          InputLabelProps={{ shrink: true }}
                          aria-describedby="dataSentDate"
                          value={field.value}
                          onChange={(e) => {
                            field.onChange(e.target.value);
                          }}
                          inputProps={{
                            min: new Date().toISOString()?.split("T")[0],
                          }}
                        />
                      )}
                    />
                  </FormControl>
                </Grid>
                <Grid item xs={12} md={3} sm={4}>
                  <FormControl fullWidth>
                    <Controller
                      name="deciMatrixPresentationDate"
                      control={control}
                      render={({ field }) => (
                        <TextField
                          {...field}
                          size="small"
                          label={
                            <span
                              style={{ display: "flex", alignItems: "center" }}
                            >
                              Deci-matrix Presentation Date
                              <Tooltip
                                title="This field indicates the date on which the presentation was made to CHS."
                                arrow
                                placement="top"
                              >
                                <span>
                                  <Icon
                                    icon="mingcute:information-line"
                                    style={{
                                      cursor: "pointer",
                                      marginLeft: 10,
                                      fontSize: 15,
                                    }}
                                  />
                                </span>
                              </Tooltip>
                            </span>
                          }
                          type="date"
                          InputLabelProps={{ shrink: true }}
                          aria-describedby="deciMatrixPresentationDate"
                          value={field.value}
                          onChange={(e) => {
                            field.onChange(e.target.value);
                          }}
                        />
                      )}
                    />
                  </FormControl>
                </Grid>
                <Grid item xs={12} md={6} sm={4}></Grid>
                <Grid item xs={12} md={3} sm={4}>
                  <FormControl fullWidth>
                    <Controller
                      name="referenceType"
                      control={control}
                      rules={{ required: false }}
                      render={({ field }) => (
                        <SelectAutoCompleteCustom
                          id="referenceType"
                          label="Select Reference Type"
                          nameArray={referenceData}
                          value={referenceType}
                          setAddSection={setAddSection}
                          setValuesUpdate={setSectionsUpdate}
                          listNameId={authConfig.referenceTypeListNameId}
                          onChange={(e) => {
                            field.onChange(e.target.value);
                            setReferenceType(e.target.value);
                          }}
                        />
                      )}
                    />
                  </FormControl>
                </Grid>

                <Grid item xs={12} md={3} sm={4}>
                  <FormControl fullWidth>
                    <Controller
                      name="referralName"
                      control={control}
                      render={({ field }) => (
                        <SelectAutoCompleteCustom
                          id="referralName"
                          label="Select Referral Name"
                          nameArray={referralNameData}
                          value={referralName}
                          setAddSection={setAddSection}
                          setValuesUpdate={setSectionsUpdate}
                          listNameId={authConfig.referralNameId}
                          onChange={(e) => {
                            field.onChange(e.target.value);
                            setReferralName(e.target.value);
                          }}
                        />
                      )}
                    />
                  </FormControl>
                </Grid>
              </Grid>
            </Card>
          )}

          <Card>
            <Grid
              sx={{
                backgroundColor: "#f2f7f2",
                mt: 4,
                paddingTop: 0,
                height: "36px",
                display: "flex",
                alignItems: "center",
              }}
            >
              <Typography
                variant="body1"
                fontWeight={"bold"}
                sx={{ mt: 0, ml: 2 }}
              >
                Site Visit Timings
              </Typography>
              <Divider />
            </Grid>
            <Divider />
            <Grid item xs={12} sm={4}>
              <Box
                sx={{
                  display: "flex",
                  justifyContent: "flex-start",
                }}
              >
                <div>
                  <Button
                    sx={{ margin: "10px" }}
                    variant="contained"
                    onClick={handleSiteVisitDialogOpen}
                  >
                    Add site visit slot
                  </Button>
                </div>
              </Box>

              <>
                {currentRow ? (
                  <SiteVisitTimings
                    open={siteVisitOpen}
                    onClose={handleSiteVisitDialogClose}
                    siteVisits={siteVisits}
                    setSiteVisits={setSiteVisits}
                    rowData={currentRow}
                  />
                ) : (
                  <SiteVisitTimings
                    open={siteVisitOpen}
                    onClose={handleSiteVisitDialogClose}
                    siteVisits={siteVisits}
                    setSiteVisits={setSiteVisits}
                  />
                )}
              </>
            </Grid>
            {siteVisits?.length > 0 && (
              <Box style={{ height: "100%", width: "100%" }}>
                <DataGrid
                  rows={siteVisits}
                  columns={cols}
                  autoHeight
                  checkboxSelection
                  pagination
                  pageSize={pageSize}
                  page={page - 1}
                  rowsPerPageOptions={rowsPerPageOptions}
                  rowCount={rowCount}
                  paginationMode="server"
                  onPageChange={handlePageChange}
                  onPageSizeChange={handlePageSizeChange}
                  rowHeight={38}
                  headerHeight={38}
                />
              </Box>
            )}
          </Card>
          {user.organisationCategory !== "SERVICE_PROVIDER" && (
            <Card>
              <Grid
                sx={{
                  backgroundColor: "#f2f7f2",
                  mt: 4,
                  paddingTop: 0,
                  height: "36px",
                  display: "flex",
                  alignItems: "center",
                }}
              >
                <Typography
                  variant="body1"
                  fontWeight={"bold"}
                  sx={{ mt: 0, ml: 2 }}
                >
                  Conversations
                </Typography>
                <Divider />
              </Grid>
              <Divider />
              <Grid
                container
                sx={{ padding: "10px" }}
                justifyContent="flex-start"
              >
                <CommentsDialog setConvList={setConvList} list={[]} />
                {convList?.length > 0 && (
                  <TableContainer sx={{ marginTop: "16px" }}>
                    <Table>
                      <TableHead>
                        <TableRow>
                          {user?.organisationCategory === "EMPLOYEE" && (
                            <>
                              <MUITableCell>Last Contacted Date</MUITableCell>
                              <MUITableCell>Follow Up Date</MUITableCell>
                              <MUITableCell>Follow Up Action</MUITableCell>
                            </>
                          )}
                          <MUITableCell>Conversation</MUITableCell>
                          <MUITableCell>More Info</MUITableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {convList?.map((row, index) => (
                          <TableRow key={index}>
                            {user?.organisationCategory === "EMPLOYEE" && (
                              <>
                                <MUITableCell sx={{ width: "15%" }}>
                                  {row.lastContactedDate || ""}
                                </MUITableCell>
                                <MUITableCell sx={{ width: "10%" }}>
                                  {row.followUpDate}
                                </MUITableCell>
                                <MUITableCell sx={{ width: "30%" }}>
                                  {row.followUpActions}
                                </MUITableCell>
                              </>
                            )}

                            <MUITableCell sx={{ width: "30%" }}>
                              {row.message}
                            </MUITableCell>
                            <MUITableCell sx={{ width: "15%" }}>
                              <Tooltip title="More Info">
                                <CustomAvatar
                                  skin="light"
                                  variant="rounded"
                                  sx={{
                                    width: 28,
                                    height: 28,
                                    cursor: "pointer",
                                  }}
                                  onClick={() => {
                                    setConversation(row);
                                    setOpenMoreInfoDialog(true);
                                  }}
                                >
                                  <Icon
                                    icon="tabler:info-circle"
                                    fontSize="2.2rem"
                                  />
                                </CustomAvatar>
                              </Tooltip>
                            </MUITableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </TableContainer>
                )}
              </Grid>
            </Card>
          )}
        </DialogContent>
        <DialogActions
          sx={{
            justifyContent: "end",
            borderTop: (theme) => `1px solid ${theme.palette.divider}`,
            p: (theme) => `${theme.spacing(2.5)} !important`,
            height: "50px", // Set fixed height for footer
          }}
        >
          <Button
            display="flex"
            justifyContent="center"
            variant="outlined"
            color="primary"
            onClick={handleCancel}
          >
            Cancel
          </Button>
          <Button
            display="flex"
            justifyContent="center"
            variant="contained"
            color="primary"
            onClick={handleSubmit(submit, handleValidationErrors)}
            sx={{
              mr: {
                xs: 5.5,
                sm: 5.5,
                md: 5.5,
                lg: 5.5,
                xl: 10,
              },
            }}
          >
            Save
          </Button>
        </DialogActions>
      </Dialog>
      <SpecificationsDialog
        open={requisitionDialog}
        onClose={handleClickClose}
        data={templateDetails?.sections}
        specifications={specifications}
        setSpecifications={setSpecifications}
        setSpecificationsData={setSpecificationsData}
        checkedSections={checkedSections}
        setCheckedSections={setCheckedSections}
      />

      <Dialog
        open={openMoreInfoDialog}
        onClose={handleInfoDialogClose}
        fullWidth
        scroll="paper"
      >
        <DialogTitle
          sx={{
            position: "relative",
            borderBottom: (theme) => `1px solid ${theme.palette.divider}`,
            p: (theme) => `${theme.spacing(1.75, 4)} !important`,
            display: "flex",
            alignItems: "center",
            justifyContent: { xs: "start" },
            fontSize: { xs: 19, md: 20 },
            height: "50px", // Set fixed height for header
          }}
          textAlign={"center"}
        >
          Conversation Details
          <Box sx={{ position: "absolute", top: "4px", right: "10px" }}>
            <IconButton
              size="small"
              onClick={handleInfoDialogClose}
              sx={{
                p: "0.438rem",
                borderRadius: 1,
                color: "common.white",
                backgroundColor: "primary.main",
                "&:hover": {
                  backgroundColor: "#66BB6A",
                  transition: "background 0.5s ease, transform 0.5s ease",
                },
              }}
            >
              <Icon icon="tabler:x" fontSize="1rem" />
            </IconButton>
          </Box>
        </DialogTitle>
        <DialogContent
          sx={{
            position: "relative",
            pt: (theme) => `${theme.spacing(8)} !important`,
            pb: (theme) => `${theme.spacing(5)} !important`,
            px: (theme) => [`${theme.spacing(8)} !important`],
          }}
        >
          <TableContainer sx={{ padding: "2px 3px" }} className="tableBody">
            <Table>
              <TableBody
                sx={{
                  "& .MuiTableCell-root": {
                    p: `${theme.spacing(1.35, 1.125)} !important`,
                  },
                }}
              >
                <>
                  {user?.organisationCategory === "EMPLOYEE" && (
                    <>
                      <TableRow>
                        <MUITableCell>
                          <Typography sx={{ fontWeight: 600 }}>
                            Last Contacted Date
                          </Typography>
                        </MUITableCell>
                        <MUITableCell>
                          <Typography>
                            {conversation.lastContactedDate}
                          </Typography>
                        </MUITableCell>
                      </TableRow>
                      <TableRow>
                        <MUITableCell>
                          <Typography sx={{ fontWeight: 600 }}>
                            Follow Up Date
                          </Typography>
                        </MUITableCell>
                        <MUITableCell>
                          <Typography>{conversation.followUpDate}</Typography>
                        </MUITableCell>
                      </TableRow>
                      <TableRow>
                        <MUITableCell>
                          <Typography sx={{ fontWeight: 600 }}>
                            Follow Up Action
                          </Typography>
                        </MUITableCell>
                        <MUITableCell>
                          <Typography>
                            {conversation.followUpActions}
                          </Typography>
                        </MUITableCell>
                      </TableRow>
                    </>
                  )}

                  <TableRow>
                    <MUITableCell>
                      <Typography sx={{ fontWeight: 600 }}>
                        Conversation
                      </Typography>
                    </MUITableCell>
                    <MUITableCell>
                      <Typography
                        sx={{
                          wordWrap: "break-word",
                          wordBreak: "break-word",
                          whiteSpace: "normal",
                        }}
                      >
                        {conversation.message}
                      </Typography>
                    </MUITableCell>
                  </TableRow>
                </>
              </TableBody>
            </Table>
          </TableContainer>
        </DialogContent>
        <DialogActions
          sx={{
            justifyContent: "end",
            borderTop: (theme) => `1px solid ${theme.palette.divider}`,
            p: (theme) => `${theme.spacing(2.5)} !important`,
          }}
        >
          <Button
            size="medium"
            sx={{ mr: 3 }}
            onClick={() => handleInfoDialogClose()}
            variant="outlined"
            color="primary"
          >
            Close
          </Button>
        </DialogActions>
      </Dialog>

      <Dialog
        open={openDialogContent}
        onClose={handleButtonClick}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
        PaperProps={{
          sx: {
            p: (theme) => `${theme.spacing(2.5)} !important`,
            backgroundColor: (theme) => theme.palette.primary.background,
          },
        }}
      >
        <Box
          sx={{
            width: "100%",
            borderRadius: 1,
            textAlign: "center",
            border: (theme) => `1px solid ${theme.palette.divider}`,
            borderColor: "primary.main",
          }}
        >
          <DialogContent>
            <DialogContentText
              id="alert-dialog-description"
              color="primary.main"
            >
              <div dangerouslySetInnerHTML={{ __html: dialogMessage }} />
            </DialogContentText>
          </DialogContent>
          <DialogActions>
            <Button
              variant="contained"
              onClick={handleButtonClick}
              sx={{ margin: "auto", width: 100 }}
            >
              Okay
            </Button>
          </DialogActions>
        </Box>
      </Dialog>

      <>
        <Snackbar
          open={showToast}
          autoHideDuration={2000} // Toast will be visible for 2 seconds
          onClose={handleToastClose}
          anchorOrigin={{ vertical: "top", horizontal: "right" }} // Position of the toast
        >
          <Alert
            onClose={handleToastClose}
            severity="error"
            sx={{
              color: "black",
              padding: "4px 8px", // Reduce padding to make it smaller
              fontSize: "0.875rem", // Adjust font size for a more compact look
              borderRadius: "2px", // Optional: you can adjust the border radius
              border: "0.5px solid #ccc", // Optional: set a border or remove it completely
            }}
          >
            {toastMessage}
          </Alert>
        </Snackbar>
      </>
    </>
  );
};

export default RequisitionDetails;