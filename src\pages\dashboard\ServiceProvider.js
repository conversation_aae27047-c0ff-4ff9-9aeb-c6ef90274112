import ExpandLessIcon from "@mui/icons-material/ExpandLess";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import LocationOnTwoToneIcon from "@mui/icons-material/LocationOnTwoTone";
import ModeEditRoundedIcon from "@mui/icons-material/ModeEditRounded";
import QuestionAnswerOutlinedIcon from "@mui/icons-material/QuestionAnswerOutlined";
import { DataGrid } from "@mui/x-data-grid";
import * as FileSaver from "file-saver";
import { useRouter } from "next/router";
import React, { useContext, useEffect, useState } from "react";
import authConfig from "src/configs/auth";
import { getAuthorizationHeaders, getUrl } from "src/helpers/utils";
import { useAuth } from "src/hooks/useAuth";
import { useRBAC } from "src/pages/permission/RBACContext";
import * as XLSX from "xlsx";

import {
  Autocomplete,
  Button,
  CardContent,
  Checkbox,
  Chip,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  FormControl,
  FormControlLabel,
  FormHelperText,
  Grid,
  InputAdornment,
  InputLabel,
  Link,
  Menu,
  MenuItem,
  Radio,
  RadioGroup,
  Select,
  TextField,
  Tooltip,
  Typography,
  useMediaQuery,
  useTheme
} from "@mui/material";
import IconButton from "@mui/material/IconButton";
import Tab from "@mui/material/Tab";
import Tabs from "@mui/material/Tabs";
import Icon from "src/@core/components/icon";

import axios from "axios";
import CustomAvatar from "src/@core/components/mui/avatar";

import { Controller, useForm } from "react-hook-form";

import { Box } from "@mui/system";

import PageHeader from "src/@core/components/page-header";
import DatePickerWrapper from "src/@core/styles/libs/react-datepicker";
import { AuthContext } from "src/context/AuthContext";
import AreaOfExperties from "src/pages/SP/microsite/sections/AreaOfExpertiseView";
import AwardView from "src/pages/SP/microsite/sections/AwardView";
import EducationalInsightsView from "src/pages/SP/microsite/sections/EducationalInsightsView";
import FieldsView from "src/pages/SP/microsite/sections/FieldsView";
import ServiceTabs from "src/pages/SP/microsite/sections/ServiceTabs";
import TestimonialView from "src/pages/SP/microsite/sections/TestimonialsView";

import ContactsReferences from "src/pages/CHS/profile/sections/ContactsReferences";
import FsiDetails from "src/pages/CHS/profile/sections/FsiDetails";
import OtherDetails from "src/pages/CHS/profile/sections/OtherDetails";
import Requirements from "src/pages/CHS/profile/sections/Requirements";
import SocietyDetails from "src/pages/CHS/profile/sections/SocietyDetails";

import AssignmentAndStatus from "src/pages/CHS/profile/sections/AssignmentAndStatus";
import ParentComponent from "src/pages/SP/service-profile";

import FallbackSpinner from "src/@core/components/spinner";
import LandDetails from "src/pages/CHS/profile/sections/LandDetails";
import MemberShipView from "src/pages/SP/microsite/sections/MemberShipView";
import ProfileLongForm from "../SP/basic-profile/profileLongForm";
import AreaOfExpertiseLongForm from "../SP/microsite/sections/AreaOfExpertiseLongForm";
import AwardLongForm from "../SP/microsite/sections/AwardLongForm";
import EducationalInsightsLongForm from "../SP/microsite/sections/EducationalInsightsLongForm";
import FieldsLongForm from "../SP/microsite/sections/FieldLongForm";
import MemberShipLongForm from "../SP/microsite/sections/MemberShipLongForm";
import ServicesTabsLongForm from "../SP/microsite/sections/ServiceTabsLongForm";
import CloseExpandIcons from "../all-profiles-old/CloseExpandIcons";
import TestimonialLongForm from "../SP/microsite/sections/TestimonialsLongForm";
import ProfileView from "../SP/basic-profile/profileView";
import StatisticsPageLongForm from "../statistics/StatisticsPageLongForm";
import StatisticsParent from "../statistics";
import Index from "../conversations";

const ExcelDownMenu = ({ children }) => {
  const [anchorEl, setAnchorEl] = useState(null);

  const handleExcelClick = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  return (
    <>
      <Tooltip title="Export to Excel">
        <IconButton onClick={handleExcelClick} size="medium">
          <Icon icon="vscode-icons:file-type-excel" fontSize="2.2rem" />
        </IconButton>
      </Tooltip>

      <Menu
        keepMounted
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleClose}
        PaperProps={{
          style: {
            marginTop: "4px",
          },
        }}
      >
        {children}
      </Menu>
    </>
  );
};

const UsersOverView = ({
  data,
  editClick,
  handleAssignedToChange,
  assignedTo,
  assignedToName,
  createdBy,
  createdUserName,
}) => {
  const { can, rbacRoles } = useRBAC();
  const {
    micrositeBasicData,
    setMicrositeBasicData,
    micrositeGetEndpoint,
    getBasicProfileData,
    basicProfileGetData,
    projectsData,
    userData,
    entityData,
    getSocietyProfile,
    setBasicProfileAllProfiles,
    user,
    fetchOne,
    sendWatiMessage,
    sendWatiMessages,
    getAllListValuesByListNameId,
  } = useContext(AuthContext);
  const [menu, setMenu] = useState(null);

  const handleCloseMenuItems = () => {
    setMenu(null);
  };

  const [isScrollActive, setIsScrollActive] = useState(false);
  const toggleScroll = () => {
    setIsScrollActive((prev) => !prev);
  };

  const [menuPosition, setMenuPosition] = useState(null);

  // Handle row click
  const handleRowClick = (params) => {
    setCurrentRow(params.row);
    setEditDialogOpen(true);
  };

  // const handleMenuClose = () => {
  //   setAnchorEl(null); // Close menu
  //   setMenuPosition(null); // Reset menu position
  // };

  const onEditClick = () => {
    openEditDialog();
    handleCloseMenu();
  };

  const onConversationsClick = () => {
    openConversationDialog();
    handleCloseMenu();
  };

  const handleActivateStatus = () => {
    handleOpenConfirmationDialog(
      "Are you sure you want to activate this user?",
      true
    );
    handleCloseMenuItems();
  };

  const handleDeactivateStatus = () => {
    handleOpenConfirmationDialog(
      "Are you sure you want to de-activate this user?",
      false
    );
    handleCloseMenuItems();
  };

  const onManageSubscriptionClick = () => {
    setIsSubscriptionOpen(true);
    handleCloseMenuItems();
  };

  const [showAllServices, setShowAllServices] = useState(false);
  const [showAllLocations, setShowAllLocations] = useState(false);
  const [showAllLeadStatus, setShowAllLeadStatus] = useState(false);
  const [showAllLeadPriority, setShowAllLeadPriority] = useState(false);
  const [showAllAssignedTo, setShowAllAssignedTo] = useState(false);
  const [showAllPortalsRegistered, setShowAllPortalsRegistered] =
    useState(false);

  const getVisibleChips = (dataArray, showAll) => {
    return showAll ? dataArray : dataArray.slice(0, 5);
  };

  const [addUserOpen, setAddUserOpen] = useState(false);
  const toggleAddUserDrawer = () => setAddUserOpen(!addUserOpen);
  const [initialRowCount, setInitialRowCount] = useState(null);

  const [employeesData, setEmployeesData] = useState(null);
  const [selectedFilters, setSelectedFilters] = useState([]);

  const [priorityData, setPriorityData] = useState([]);

  const [whatsappOpenDialog, setWhatsappOpenDialog] = useState(false);
  const [templates, setTemplates] = useState([]);
  const [selectedTemplateName, setSelectedTemplateName] = useState("");
  const [messageStatus, setMessageStatus] = useState(null); // 'success', 'error', or null
  //const [dialogMessage, setDialogMessage] = useState(""); // Message to display in the dialog
  const [selectedTemplateParams, setSelectedTemplateParams] = useState([]);
  const [viewModeOpen, setViewModeOpen] = useState(false);

  const [societyName, setSocietyName] = useState("");
  const [requirementName, setRequirementName] = useState("");

  const [showSocietyName, setShowSocietyName] = useState(false);
  const [showServiceRequirement, setShowServiceRequirement] = useState(false);
  const [isButtonDisabled, setIsButtonDisabled] = useState(true);
  const [showGoogleFormLink, setShowGoogleFormLink] = useState(false);
  const [showLocation, setShowLocation] = useState(false);

  const [file, setFile] = useState(null);
  const [allServicesList, setAllServicesList] = useState([]);
  const [whatsappNumberUnavailableDialog, setWhatsappNumberUnavailableDialog] =
    useState(false);
  const [confirmationDialogOpen, setConfirmationDialogOpen] = useState(false);
  const [confirmationDialogMessage, setConfirmationDialogMessage] =
    useState("");
  const [isActivating, setIsActivating] = useState(false);
  const [dialogOpen, setDialogOpen] = useState(false);

  const [isBulkAction, setIsBulkAction] = useState(false); // New state to track if it's a bulk action
  const [missingPhoneNumbers, setMissingPhoneNumbers] = useState([]);
  const [missingPhoneNumbersEmails, setMissingPhoneNumbersEmails] = useState(
    []
  );
  const [missingPhoneNumbersDialogOpen, setMissingPhoneNumbersDialogOpen] =
    useState(false);
  const [
    missingPhoneNumbersEmailsDialogOpen,
    setMissingPhoneNumbersEmailsDialogOpen,
  ] = useState(false);

  const [showMissingNumbersDialog, setShowMissingNumbersDialog] =
    useState(false);

  const [radioValue, setRadioValue] = useState("");
  const [activity, setActivity] = useState(false);
  const [numberAvalabilty, setNumberAvalabilty] = useState(false);

  const [location, setLocation] = useState("");
  const [googleFormLink, setGoogleFormLink] = useState("");
  const [showImageUrl, setShowImageUrl] = useState(false);
  const [imageUrl, setImageUrl] = useState("");

  const [missingInfoDialogOpen, setMissingInfoDialogOpen] = useState(false);
  const [missingInfoMessage, setMissingInfoMessage] = useState("");

  const handleRadioChange = (event) => {
    setRadioValue(event.target.value);
  };

  const handleWhatsappOptionClick = () => {
    if (!currentRow?.mobileNumber) {
      setWhatsappNumberUnavailableDialog(true);
    } else {
      setIsBulkAction(false); // Individual action
      setWhatsappOpenDialog(true);
    }
    handleCloseMenu();
  };

  const handleSendMessages = () => {
    const missingMobileNumbers = selectedRows.filter(
      (row) => !row.mobileNumber
    );

    if (missingMobileNumbers?.length > 0) {
      const missingNames = missingMobileNumbers.map(
        (row) => row.firstName || row.companyName
      );
      const missingEmails = missingMobileNumbers.map((row) => row.email); // Capture emails
      setMissingPhoneNumbers(missingNames);
      setMissingPhoneNumbersEmails(missingEmails); // Store emails
      setMissingPhoneNumbersDialogOpen(true);
    } else {
      sendMessagesToUsersWithNumbers(selectedRows);
    }
  };

  const handleOpenConfirmationDialog = (message, isActivating) => {
    setConfirmationDialogMessage(message);
    setIsActivating(isActivating);
    setConfirmationDialogOpen(true);
  };

  const handleCloseConfirmationDialog = () => {
    setConfirmationDialogOpen(false);
  };

  const handleConfirmStatusChange = async () => {
    setConfirmationDialogOpen(false);
    try {
      if (isActivating) {
        const response = await axios({
          method: "patch",
          url:
            getUrl(authConfig.individualEndpoint) +
            `/activate/${currentRow.id}`,
          headers: getAuthorizationHeaders(),
        });
        setDialogMessage("User activated successfully");
      } else {
        const response = await axios({
          method: "patch",
          url:
            getUrl(authConfig.individualEndpoint) +
            `/deactivate/${currentRow.id}`,
          headers: getAuthorizationHeaders(),
        });
        setDialogMessage("User de-activated successfully");
      }
      fetchServiceProviders(
        page,
        pageSize,
        roleFilter,
        searchKeyword,
        selectedFilters
      ); // Refresh data
    } catch (error) {
      setDialogMessage(
        isActivating ? "Error activating status" : "Failed to Deactivate Status"
      );
    } finally {
      setDialogOpen(true);
    }
  };

  const handleWhatsAppMessages = () => {
    const missingMobileNumbers = selectedRows.filter(
      (row) => !row.mobileNumber
    );

    if (missingMobileNumbers?.length > 0) {
      const missingNames = missingMobileNumbers.map(
        (row) => row.firstName || row.companyName
      );
      setMissingPhoneNumbers(missingNames);
      setShowMissingNumbersDialog(true);
    } else {
      sendWhatsAppMessagesToUsersWithNumbers(selectedRows);
    }
  };

  const sendWhatsAppMessagesToUsersWithNumbers = (rows) => {
    // Implement your logic to send WhatsApp messages to users with numbers
    console.log("Sending WhatsApp messages to users with numbers:", rows);
  };

  // const handleCloseDialog = () => {
  //   setShowMissingNumbersDialog(false);
  // };

  const handleWhatsappMessagesOptionClick = () => {
    setIsBulkAction(true); // Bulk action
    setWhatsappOpenDialog(true);
    handleCloseMenu();
  };

  const handleCloseDialog = () => {
    setWhatsappOpenDialog(false);
    setSelectedTemplateName("");
    setSelectedTemplateParams([]);
    setShowSocietyName(false);
    setShowServiceRequirement(false);
    setShowMeasurements(false);
    setShowGoogleFormLink(false);
    setShowLocation(false);
    setSelectedSociety("");
    setRequirementName("");
    setMeasurements("");
    setIsButtonDisabled(true);
    setDialogOpen(false);
    setMessageStatus(null);
    setShowMissingNumbersDialog(false);
    setGoogleFormLink("");
    setLocation("");
    setImageUrl("");
    setShowImageUrl(false);
    setMissingInfoDialogOpen(false);
  };

  const [detailsExpanded, setDetailsExpanded] = useState(false);

  const toggleDetails = () => {
    setDetailsExpanded(!detailsExpanded);
  };

  const [error, setError] = useState(false);

  // Handle template change
  const handleTemplateChange = (event) => {
    const templateName = event.target.value;
    setSelectedTemplateName(templateName);
    setError(templateName === "");

    const selectedTemplate = templates.find(
      (template) => template.name === templateName
    );

    if (selectedTemplate) {
      setSelectedTemplateParams(selectedTemplate.parameters || []);
      setShowSocietyName(
        selectedTemplate.parameters.some(
          (param) => param.paramName === "society_name"
        )
      );
      setShowServiceRequirement(
        selectedTemplate.parameters.some(
          (param) => param.paramName === "service_requirement"
        )
      );
      setShowMeasurements(
        selectedTemplate.parameters.some(
          (param) => param.paramName === "measurements"
        )
      );
      setShowGoogleFormLink(
        selectedTemplate.parameters.some(
          (param) => param.paramName === "slot_url"
        )
      );
      setShowLocation(
        selectedTemplate.parameters.some(
          (param) => param.paramName === "location"
        )
      );
      setShowImageUrl(
        selectedTemplate.parameters.some(
          (param) => param.paramName === "image_url"
        )
      );
    } else {
      setSelectedTemplateParams([]);
      setShowSocietyName(false);
      setShowServiceRequirement(false);
      setShowMeasurements(false);
      setShowGoogleFormLink(false);
      setShowLocation(false);
      setShowImageUrl(false);
    }

    // Validate button state
    const isAllFilled =
      templateName &&
      (!showSocietyName || selectedSociety) &&
      (!showServiceRequirement || requirementName) &&
      (!showMeasurements || measurements) &&
      (!showGoogleFormLink || googleFormLink) &&
      (!showImageUrl || imageUrl) &&
      (!showLocation || location);
    setIsButtonDisabled(!isAllFilled);

    setViewModeOpen(true);
  };

  const renderViewModeDialog = () => (
    <Dialog
      open={viewModeOpen}
      onClose={() => setViewModeOpen(false)}
      maxWidth="sm"
      fullWidth
      sx={{
        "& .MuiDialog-paper": {
          width: "500px",
          maxWidth: "100%",
        },
      }}
    >
      <DialogTitle>Template Parameters</DialogTitle>
      <DialogContent>
        {selectedTemplateParams?.length > 0 ? (
          <Box>
            <Typography variant="h6">Parameters:</Typography>
            <ul>
              {selectedTemplateParams.map((param, index) => (
                <li key={index}>
                  <Typography variant="body1">
                    <strong>{param.name}:</strong> {param.value}
                  </Typography>
                </li>
              ))}
            </ul>
          </Box>
        ) : (
          <Typography variant="body1">
            No parameters available for this template.
          </Typography>
        )}
      </DialogContent>
      <DialogActions>
        <Button onClick={() => setViewModeOpen(false)}>Close</Button>
      </DialogActions>
    </Dialog>
  );

  const getServiceTypeValue = (id) => {
    const service = allServicesList.find((service) => service.id === id);
    return service ? service.listValue : "";
  };

  const sendMessage = async () => {
    setError(!selectedSociety);
    if (!selectedTemplateName || !currentRow.mobileNumber) {
      setDialogMessage("Template or WhatsApp number is missing");
      setMessageStatus("error");
      return;
    }

    const whatsappNumber = currentRow.mobileNumber;
    const templateName = selectedTemplateName;
    const broadcastName = selectedTemplateName;
    const parameters = [
      { name: "name", value: currentRow.firstName },
      {
        name: "company_name",
        value: currentRow.basicProfileData?.companyName || "N/A",
      },
      { name: "society_name", value: selectedSociety },
      {
        name: "service_requirement",
        value: getServiceTypeValue(requirementName),
      },
      { name: "measurements", value: measurements },
      { name: "location", value: location },
      { name: "slot_url", value: googleFormLink },
      { name: "image_url", value: imageUrl },
    ];

    sendWatiMessage(
      whatsappNumber,
      templateName,
      broadcastName,
      parameters,
      (error) => {
        setDialogMessage("Error sending message. Please try again.");
        setMessageStatus("error");
      },
      (data) => {
        if (data.validWhatsAppNumber) {
          setDialogMessage(
            `WhatsApp message successfully delivered to ${
              currentRow.basicProfileData?.companyName || "their company"
            }
            (${currentRow.firstName} ${
              currentRow.lastName
            } - ${whatsappNumber}).`
          );
          setMessageStatus("success");
        } else {
          setDialogMessage(
            `WhatsApp message not delivered to unregistered number ${currentRow.basicProfileData?.companyName} (${currentRow.firstName} ${currentRow.lastName}).`
          );
          setMessageStatus("error");
        }
      }
    );

    // Reset fields after sending the message
    setSelectedTemplateName("");
    setSelectedTemplateParams([]);
    setShowSocietyName(false);
    setShowServiceRequirement(false);
    setShowMeasurements(false);
    setShowGoogleFormLink(false);
    setShowLocation(false);
    setShowImageUrl(false);
    setSelectedSociety("");
    setRequirementName("");
    setIsButtonDisabled(true);
  };

  const [searchingState, setSearchingState] = useState(false);
  const sendMessages = async () => {
    const missingMobileNumbers = selectedRows.filter(
      (row) => !row.mobileNumber
    );

    if (missingMobileNumbers?.length > 0) {
      const missingNames = missingMobileNumbers.map(
        (row) => row.firstName || row.companyName
      );

      // Show dialog with missing phone number details
      setDialogMessage(
        `Mobile number is required for: ${missingNames.join(", ")}`
      );
      setMessageStatus("error");
      return; // Stop execution here if any mobile numbers are missing
    }

    // If all selected rows have mobile numbers, proceed to send messages
    sendMessagesToUsersWithNumbers(selectedRows);

    const usersWithoutPhoneNumbers = selectedRows.filter(
      (row) => !row.mobileNumber
    );

    const inActiveRows = selectedRows.filter(
      (row) => row.status === "INACTIVE"
    );

    if (usersWithoutPhoneNumbers?.length > 0 && inActiveRows?.length > 0) {
      const names = usersWithoutPhoneNumbers.map(
        (row) => row.basicProfileData?.companyName || row.firstName
      );
      setMissingPhoneNumbers(names);
      setRadioValue("");
      setShowMissingNumbersDialog(true);
      return;
    }

    if (usersWithoutPhoneNumbers > 0) {
      setActivity(true);
      const names = usersWithoutPhoneNumbers.map(
        (row) => row.basicProfileData?.companyName || row.firstName
      );
      setMissingPhoneNumbers(names);
      setRadioValue("");
      setShowMissingNumbersDialog(true);
      return;
    }

    if (inActiveRows > 0) {
      setNumberAvalabilty(true);
      setRadioValue("");
      setShowMissingNumbersDialog(true);
      return;
    }

    sendMessagesToUsersWithNumbers(selectedRows);
  };

  const sendMessagesToUsersWithNumbers = async (rows) => {
    const templateName = selectedTemplateName;
    const broadcastName = selectedTemplateName;
    let sentMessages = [];
    let notSentMessages = [];
    let invalidWhatsAppNumbers = [];

    for (const row of rows) {
      if (row.mobileNumber) {
        const params = [
          { name: "name", value: row.firstName },
          { name: "society_name", value: selectedSociety },
          {
            name: "service_requirement",
            value: getServiceTypeValue(requirementName),
          },
          { name: "measurements", value: measurements },
          { name: "location", value: location },
          { name: "slot_url", value: googleFormLink },
          { name: "image_url", value: imageUrl },
        ];

        await sendWatiMessage(
          row.mobileNumber,
          templateName,
          broadcastName,
          params,
          (error) => {
            notSentMessages.push({
              name: row.firstName + " " + row.lastName || "",
              mobileNumber: row.mobileNumber,
              companyName: row.basicProfileData?.companyName || "", // Ensure companyName is captured
              reason: "Failed to send message",
            });
          },
          (data) => {
            if (data.validWhatsAppNumber) {
              sentMessages.push({
                name: row.firstName + " " + row.lastName || "",
                mobileNumber: row.mobileNumber,
                companyName: row.basicProfileData?.companyName || "", // Ensure companyName is captured
              });
            } else {
              invalidWhatsAppNumbers.push({
                name: row.firstName + " " + row.lastName || "",
                mobileNumber: row.mobileNumber,
                companyName: row.basicProfileData?.companyName || "", // Ensure companyName is captured
              });
            }
          }
        );
      } else {
        notSentMessages.push({
          name: row.firstName + " " + row.lastName || "",
          mobileNumber: "N/A", // Handle the case where the number is missing
          companyName: row.basicProfileData?.companyName || "", // Ensure companyName is captured
          reason: "Missing mobile number",
        });
      }
    }

    const sentList = sentMessages
      .map(
        (entry, index) =>
          `${index + 1}. ${entry.companyName || "No Company Name"} (${
            entry.name
          } - ${entry.mobileNumber || "N/A"})`
      )
      .join("\n");

    const notSentList = notSentMessages
      .map(
        (entry, index) =>
          `${index + 1}. ${entry.companyName || "No Company Name"} (${
            entry.name
          } - ${entry.mobileNumber || "N/A"}) - ${entry.reason}`
      )
      .join("\n");

    const invalidList = invalidWhatsAppNumbers
      .map(
        (entry, index) =>
          `${index + 1}. ${entry.companyName || "No Company Name"} (${
            entry.name
          } - ${entry.mobileNumber || "N/A"})`
      )
      .join("\n");

    let message = "";

    if (sentList) {
      message += `WhatsApp messages sent successfully to:\n${sentList}\n`;
    }

    if (notSentList) {
      message += `\nMessages could not be sent to:\n${notSentList}\n`;
    }

    if (invalidList) {
      message += `\nThe below Mobile Numbers are Invalid or not registered with WhatsApp:\n${invalidList}`;
    }

    const formattedMessage = message.split("\n").map((line, index) => (
      <React.Fragment key={index}>
        {line}
        <br />
      </React.Fragment>
    ));

    setDialogMessage(formattedMessage);
    setMessageStatus("info");

    // Reset fields after sending the message
    setSelectedTemplateName("");
    setSelectedTemplateParams([]);
    setShowSocietyName(false);
    setShowServiceRequirement(false);
    setShowMeasurements(false);
    setShowGoogleFormLink(false);
    setShowLocation(false);
    setShowImageUrl(false);
    setSelectedSociety("");
    setRequirementName("");
    setIsButtonDisabled(true);
  };

  const [subscribingOptions, setSubscribingOptions] = useState([]);

  const token =
    "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.akjOAtxhzjTui78FKtgAWOgziULQsn0FoTnIAshlGTA";
  useEffect(() => {
    const fetchTemplates = async () => {
      try {
        const response = await fetch(
          "https://live-mt-server.wati.io/321777/api/v1/getMessageTemplates",
          {
            method: "GET",
            headers: {
              Authorization: `Bearer ${token}`,
              "Content-Type": "application/json",
            },
          }
        );
        const data = await response.json();
        const templateData = data?.messageTemplates?.map((template) => ({
          id: template.id,
          name: template.elementName,
          parameters: template.customParams || [{ name: "name", value: "" }],
          status: template.status,
        }));
        const filteredTemplates = templateData.filter(
          (template) => template.status !== "DELETED"
        );

        setTemplates(filteredTemplates);
      } catch (error) {}
    };

    fetchTemplates();
  }, [token]);

  const handleServicesSuccess = (data) => {
    setAllServicesList(data?.listValues);
  };

  useEffect(() => {
    if (!!authConfig) {
      getAllListValuesByListNameId(
        authConfig.allServicesListNameId,
        handleServicesSuccess,
        handleError
      );
    }
  }, [authConfig]);

  const handleError = (error) => {};

  // Constants
  const columns = [
    {
      field: "name",
      minWidth: 90,
      headerName: "Name",
      flex: 0.32,
      valueGetter: (params) => {
        const { firstName, lastName } = params?.row;
        return lastName ? `${firstName} ${lastName}` : firstName;
      },
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <span>{params.value}</span>
        </Tooltip>
      ),
    },
    {
      field: "companyName",
      minWidth: 110,
      headerName: "Company Name",
      flex: 0.3,
      valueGetter: (params) => params?.row?.companyName,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <span>{params.value}</span>
        </Tooltip>
      ),
    },
    {
      field: "email",
      minWidth: 95,
      headerName: "Email",
      flex: 0.37,
      renderCell: (params) => {
        const email = params?.value;

        return email?.length > 21 ? (
          <Tooltip title={email}>
            <Link
              href={`mailto:${email}`}
              target="_blank"
              rel="noopener noreferrer"
              sx={{ color: "#6666ff" }}
            >
              <span>{email}</span>
            </Link>
          </Tooltip>
        ) : (
          <Link
            href={`mailto:${email}`}
            target="_blank"
            rel="noopener noreferrer"
            sx={{ color: "#6666ff" }}
          >
            {email}
          </Link>
        );
      },
    },
    {
      field: "mobileNumber",
      minWidth: 95,
      headerName: "Mobile No",
      flex: 0.15,
      renderCell: (params) => {
        const mobileNumber = params?.value;

        return mobileNumber?.length > 9 ? (
          <Tooltip title={mobileNumber}>
            <Link
              href={`tel:${mobileNumber}`}
              target="_blank"
              rel="noopener noreferrer"
              sx={{ color: "#6666ff" }}
            >
              <span>{mobileNumber}</span>
            </Link>
          </Tooltip>
        ) : (
          <Link
            href={`tel:${mobileNumber}`}
            target="_blank"
            rel="noopener noreferrer"
            sx={{ color: "#6666ff" }}
          >
            {mobileNumber}
          </Link>
        );
      },
    },
    {
      flex: 0.15,
      minWidth: 90,
      field: "assignedTo",
      headerName: "Assigned to",
      renderCell: (params) => {
        const assignedTo = employeesData?.find(
          (item) => item?.id === params?.row?.assignedTo
        );
        return (
          <Tooltip title={assignedTo ? assignedTo?.name : ""}>
            <span>{assignedTo ? assignedTo?.name : ""}</span>
          </Tooltip>
        );
      },
    },
    {
      field: "isActive",
      minWidth: 70,
      headerName: "Status",
      flex: 0.12,
      renderCell: (params) => {
        const status = params?.row.isActive;
        const currentTime = new Date();
        const formattedTime = currentTime.toLocaleString();

        return (
          <Tooltip title={status ? "ACTIVE" : "INACTIVE"}>
            <CustomAvatar
              skin="none"
              sx={{
                width: status ? 13 : 13,
                height: status ? 13 : 13,
                // m: 5,
                m: "auto",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
              }}
              color={status ? "success" : "error"}
            >
              <Icon
                icon={
                  status
                    ? "fluent-emoji-flat:green-circle"
                    : "fluent-emoji-flat:red-circle"
                }
                style={{ width: 15, height: 15 }}
              />
            </CustomAvatar>
          </Tooltip>
        );
      },
    },
    {
      flex: 0.11,
      field: "actions",
      headerName: "Actions",
      sortable: false,
      minWidth: 80,
      disableClickEventBubbling: true,
      renderCell: (params) => {
        const handleClickMenu = (event) => {
          event.stopPropagation();
          setMenu(event.currentTarget);
          setCurrentRow(params.row);
          setBasicProfileAllProfiles(params.row.organisationId);
        };
        return (
          <div
            style={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
            }}
          >
            <CustomAvatar
              skin="light"
              variant="rounded"
              sx={{
                mr: { xs: 2, lg: 4 },
                width: 30,
                height: 30,
                cursor: "pointer",
              }}
              onClick={handleClickMenu}
            >
              <Icon icon="bi:three-dots-vertical" />
            </CustomAvatar>
            <Menu
              id="actions-menu"
              anchorEl={menu}
              keepMounted
              open={Boolean(menu)}
              onClose={handleCloseMenuItems}
            >
              <MenuItem onClick={onEditClick}>
                <ModeEditRoundedIcon style={{ marginRight: 8 }} />
                Edit
              </MenuItem>
              <MenuItem onClick={onConversationsClick}>
                <QuestionAnswerOutlinedIcon style={{ marginRight: 8 }} />
                New Conversation
              </MenuItem>
              {/* <MenuItem onClick={handleWhatsappOptionClick}>
                <WhatsAppIcon style={{ marginRight: 8 }} />
                Send WhatsApp message
              </MenuItem> */}
              {currentRow?.isActive ? (
                <MenuItem onClick={() => handleDeactivateStatus()}>
                  <Icon
                    icon="fluent:person-delete-20-filled"
                    style={{ marginRight: 8, fontSize: 25 }}
                  />{" "}
                  De-activate
                </MenuItem>
              ) : (
                <MenuItem onClick={() => handleActivateStatus()}>
                  {" "}
                  <Icon
                    icon="mdi:account-reactivate"
                    style={{ marginRight: 8, fontSize: 25 }}
                  />{" "}
                  Activate
                </MenuItem>
              )}
              {/* <MenuItem onClick={() => onManageSubscriptionClick()}>
                <Icon
                  icon="streamline:subscription-cashflow"
                  style={{ marginRight: 8, fontSize: 25 }}
                />{" "}
                Manage Subscription
              </MenuItem> */}
            </Menu>
          </div>
        );
      },
    },
  ];
  // Use States
  const {
    register,
    handleSubmit,
    control,
    reset,
    clearErrors,
    formState: { errors },
  } = useForm();
  const theme = useTheme();
  const isSmallScreen = useMediaQuery(theme.breakpoints.down(410));
  const isExtraSmallScreen = useMediaQuery(theme.breakpoints.down(365));
  const auth = useAuth();
  const [currentRow, setCurrentRow] = useState(null);
  const [userList, setUserList] = useState([]);
  const rowsPerPageOptions = [10, 15, 20, 25, 50, 100];

  const [pageSize, setPageSize] = useState(rowsPerPageOptions[0]);
  const [roleFilter, setRoleFilter] = useState(
    "3676706b-fc31-4b41-956c-3c4c758aa663"
  );
  const [loading, setLoading] = useState(true);
  const [page, setPage] = useState(1);
  const totalGridHeight = pageSize * 52 + 80;
  const [searchKeyword, setSearchKeyword] = useState("");

  const [searchData, setSearchData] = useState({});

  const [rowCount, setRowCount] = useState(0);
  const [openMenu, setOpenMenu] = useState(null);
  const [openDialog, setOpenDialog] = useState(false);
  const [selectedTab, setSelectedTab] = useState(0);
  const [selectedRows, setSelectedRows] = useState([]);
  const [isEditDialogOpen, setEditDialogOpen] = useState(false);
  const [isEditDialogOpens, setEditDialogOpens] = useState(false);
  const [isSubscriptionOpen, setIsSubscriptionOpen] = useState(false);
  const [isConversationDialogOpen, setConversationDialogOpen] = useState(false);
  const [activeTab, setActiveTab] = useState(0);
  const [expanded, setExpanded] = useState(true);
  const [placeholderText, setPlaceholderText] = useState(
    "Search by Company name"
  );

  const [keyword, setKeyword] = useState("");

  const [selectedSociety, setSelectedSociety] = useState("");
  const [listOfSocieties, setListOfSocieties] = useState([]);
  const [showMeasurements, setShowMeasurements] = useState(false);
  const [measurements, setMeasurements] = useState("");

  useEffect(() => {
    reset(searchData);
  }, [searchData]);

  const handleRemoveFilter = (filterType, value = null) => {
    const index = subscribingOptions.findIndex(
      (option) => option.value === filterType
    );
    if (index !== -1) {
      subscribingOptions.splice(index, 1);
    }
    const updatedData = { ...searchData };

    if (typeof updatedData[filterType] === "boolean") {
      updatedData[filterType] = false;
    } else if (typeof updatedData[filterType] === "string") {
      updatedData[filterType] = "";
    } else {
      if (value !== null) {
        updatedData[filterType] = updatedData[filterType].filter(
          (item) => item.key !== value
        );
      }
    }

    setSearchData(updatedData);
    fetchServiceProviders(
      page,
      pageSize,
      roleFilter,
      searchKeyword,
      updatedData
    );
    router.replace(
      {
        // pathname: router.pathname,
        query: {}, // Clear all query parameters
      },
      undefined,
      { shallow: false } // Allow page reload if necessary
    );
    setSearchingState(false);
  };

  const societyOptions = listOfSocieties
    .filter((society) => society?.name)
    .map((society) => ({
      value: society?.name,
      key: society?.name,
    }));

  useEffect(() => {
    const fetchSocieties = async () => {
      try {
        const response = await axios({
          method: "get",
          url:
            getUrl(authConfig.selectDropdownNew) +
            "?selectionType=SOCIETY_NAME",
          headers: getAuthorizationHeaders(),
        });
        const metadataArray = response.data?.data?.map(
          (item) => item?.metaData
        );
        setListOfSocieties(metadataArray);
      } catch (err) {}
    };
    fetchSocieties();
  }, [openDialog]);

  useEffect(() => {
    const isAllFilled =
      selectedTemplateName &&
      (!showSocietyName || selectedSociety) &&
      (!showServiceRequirement || requirementName) &&
      (!showMeasurements || measurements) &&
      (!showLocation || location) &&
      (!showImageUrl || imageUrl) &&
      (!showGoogleFormLink || googleFormLink);
    setIsButtonDisabled(!isAllFilled);
  }, [
    selectedTemplateName,
    selectedSociety,
    requirementName,
    showSocietyName,
    showServiceRequirement,
    showMeasurements,
    showLocation,
    showGoogleFormLink,
    showImageUrl,
    imageUrl,
  ]);

  const [listValues, setListValues] = useState(null);
  useEffect(() => {
    // Fetch all employees
    axios({
      method: "get",
      url:
        getUrl(authConfig.selectDropdownNew) + "?selectionType=EMPLOYEES",
      headers: getAuthorizationHeaders(),
    })
      .then((res) => {
        setEmployeesData(res.data.data);
      })
      .catch((err) => console.log("Employees error", err));

    axios({
      method: "post",
      url: getUrl(authConfig.allListNamesValues) + "?selectType=LIST_VALUES",
    })
      .then((res) => {
        setListValues(res.data.data);
        window.localStorage.setItem(
          authConfig.listValues,
          JSON.stringify(res.data.data)
        );
      })
      .catch((err) => console.log("List values error", err));

    const phrases = ["Company name", "name", "email", "mobile number"];
    let phraseIndex = 0;
    const intervalId = setInterval(() => {
      phraseIndex = (phraseIndex + 1) % phrases?.length;
      setPlaceholderText(`Search by ${phrases[phraseIndex]}`);
    }, 1000);

    return () => clearInterval(intervalId);
  }, []);

  useEffect(() => {
    if (!!authConfig) {
      getAllListValuesByListNameId(
        authConfig.leadPriorityListNamesId,
        (data) =>
          setPriorityData(
            data?.listValues?.map((item) => ({
              value: item.id,
              key: item.listValue,
            }))
          ),
        handleError
      );
    }
  }, [authConfig]);

  const handleApplyFilters = (filters) => {
    setSelectedFilters(filters); // Set filters for chips
    setSearchingState(true); // Trigger search
  };

  const handleRemoveFilterCHS = (key, id) => {
    if (
      (key === "priorityFilter" || key === "assignedToGivenEmployeeId") &&
      id
    ) {
      // Remove the specific ID from the servicesFilter value
      const updatedFilters = selectedFilters
        ?.map((filter) => {
          if (filter.key === key) {
            return {
              ...filter,
              value: filter.value?.filter((serviceId) => serviceId !== id),
            };
          }
          return filter;
        })
        ?.filter((filter) => filter.value?.length > 0); // Remove the filter if no values remain
      setSelectedFilters(updatedFilters);
    } else {
      // Remove the entire filter
      setSelectedFilters(
        selectedFilters?.filter((filter) => filter.key !== key)
      );
    }
  };

  // Clear all filters
  const clearAllFilters = () => {
    setSelectedFilters([]);
    setSearchingState(false); // Reset search state if needed
  };
  // HandleChanges
  const handleMenuOpen = (event) => {
    setOpenMenu(event.currentTarget);
  };

  const handleMenuClose = () => {
    setOpenMenu(null);
  };

  const handleOptionSelect = (option) => {
    setOpenMenu(null);
    // setOpenDialog(true);
  };

  const handleDialogClose = () => {
    setOpenDialog(false);
  };

  const handleSelectChange = (event) => {
    const value = event.target.value;
  };

  const handlePageChange = (direction) => {
    if (direction === page) {
      setPage(page + 1);
    } else {
      setPage(page - 1);
    }
  };

  const navigateToGoogleMaps = () => {
    const url =
      "https://www.google.com/maps/search/?api=1&query=current+location";
    window.open(url, "_blank");
  };

  const handlePageSizeChange = (params) => {
    if (params) {
      setPageSize(params);
      setPage(1);
    }
  };

  const router = useRouter();
  const { month, ...restQuery } = router.query;
  const { leadStatus } = router.query;
  const { assigned } = router.query;
  const { created } = router.query;
  const { seriesName, duration } = router.query;
  const { frequency } = router.query;

  const clearMonthFilter = () => {
    // Update the query to remove the month parameter
    router.replace(
      {
        query: {}, // Clear all query parameters
      },
      undefined,
      { shallow: false } // Allow full page reload
    );

    // Clear the month and update the search data
    setSearchData((prev) => ({ ...prev, month: null }));
  };

  const clearFilter = () => {
    router.replace(
      {
        query: {}, // Clear all query parameters
      },
      undefined,
      { shallow: false } // Allow full page reload
    );

    setSearchData(() => ({
      assignedTo: false,
      createdBy: false,
      searchByDurationDaily: false,
      searchByDurationThisWeek: false,
      searchByDurationMonthly: false,
      searchByDurationQuarterly: false,
      searchByDurationYearly: false,
    }));
  };

  useEffect(() => {
    if (month) {
      setSearchData((prev) => ({ ...prev, month: parseInt(month, 10) }));
    }
  }, [month]);

  useEffect(() => {
    if (seriesName || duration) {
      setSearchData((prev) => {
        const updatedData = { ...prev };

        if (seriesName) {
          if (seriesName === "assignedTo") {
            updatedData.assignedTo = true;
          } else if (seriesName === "createdBy") {
            updatedData.createdBy = true;
          }
        }

        if (duration) {
          if (duration === "daily") {
            updatedData.searchByDurationDaily = true;
          } else if (duration === "weekly") {
            updatedData.searchByDurationThisWeek = true;
          } else if (duration === "monthly") {
            updatedData.searchByDurationMonthly = true;
          }
        }

        return updatedData;
      });
    }
  }, [seriesName, duration]);

  useEffect(() => {
    if (frequency) {
      setSearchData((prev) => {
        const updatedData = { ...prev };

        if (frequency) {
          if (frequency === "daily") {
            updatedData.searchByDurationDaily = true;
          } else if (frequency === "monthly") {
            updatedData.searchByDurationMonthly = true;
          } else if (frequency === "quarterly") {
            updatedData.searchByDurationQuarterly = true;
          } else if (frequency === "yearly") {
            updatedData.searchByDurationYearly = true;
          }
        }

        return updatedData;
      });
    }
  }, [frequency]);

  useEffect(() => {
    if (assigned) {
      setSearchData((prev) => {
        const updatedData = { ...prev };

        updatedData.assignedTo = assigned;

        return updatedData;
      });
    }
  }, [assigned]);

  useEffect(() => {
    if (created) {
      setSearchData((prev) => {
        const updatedData = { ...prev };

        updatedData.createdBy = created;

        return updatedData;
      });
    }
  }, [created]);

  useEffect(() => {
    if (leadStatus && listValues) {
      setSearchData((prev) => ({
        ...prev,
        leadStatusUUIDs: [
          {
            value: leadStatus,
            key: listValues?.find((listItem) => listItem.id === leadStatus)
              ?.name,
          },
        ],
      }));
      setSearchingState(true);
    }
  }, [leadStatus, listValues]);

  useEffect(() => {
    fetchServiceProviders(
      page,
      pageSize,
      roleFilter,
      searchKeyword,
      selectedFilters
    );
  }, [page, pageSize, roleFilter, searchKeyword, selectedFilters]);

  const openConversationDialog = () => {
    setConversationDialogOpen(true);
  };

  const closeConversationDialog = () => {
    setMenu(null);
    setConversationDialogOpen(false);
  };

  const openEditDialog = () => {
    setEditDialogOpen(true);
  };

  const closeSubscriptionDialog = () => {
    setIsSubscriptionOpen(false);
  };

  const closeEditDialog = () => {
    setMenu(null);
    setEditDialogOpen(false);
    setCurrentRow(null);
    setMicrositeBasicData(null);
    setActiveTab(0);
    fetchServiceProviders(
      page,
      pageSize,
      roleFilter,
      searchKeyword,
      selectedFilters
    );
    setBasicProfileAllProfiles(null);
  };

  const closeEditDialogs = () => {
    setEditDialogOpens(false);
  };

  const openEditDialogs = () => {
    setEditDialogOpens(true);
  };

  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };

  const fetchServiceProviders = async (
    currentPage,
    currentPageSize,
    roleFilter,
    searchKeyword,
    selectedFilters
  ) => {
    let url;
    if (user?.roleId === authConfig?.superAdminRoleId) {
      url = getUrl(authConfig.individualEndpoint) + "/all";
    } else {
      url = getUrl(authConfig.individualEndpoint) + "/employee-all";
    }

    let headers;

    if (user?.roleId === authConfig?.superAdminRoleId) {
      headers = getAuthorizationHeaders({
        contentType: authConfig.INDIVIDUAL_GET_ALL_REQ_V1,
        accept: authConfig.INDIVIDUAL_GET_ALL_RES_V1,
      });
    } else {
      headers = getAuthorizationHeaders({
        contentType: authConfig.INDIVIDUAL_GET_ALL_EMPLOYEE_REQ_V1,
        accept: authConfig.INDIVIDUAL_GET_ALL_EMPLOYEE_RES_V1,
      });
    }

    const data = {
      page: currentPage,
      pageSize: currentPageSize,
      roleFilter: roleFilter,
      searchKeyWord: searchKeyword,
    };

    selectedFilters?.forEach((filter) => {
      const key = filter.key;
      data[key] = filter.value;
    });

    try {
      const response = await axios({
        method: "post",
        url: url,
        headers: headers,
        data: data,
      });

      if (initialRowCount == null) {
        setInitialRowCount(response.data.rowCount || 0);
      }

      if (response.data) {
        setRowCount(response.data?.rowCount || 0);
        setUserList(response.data?.individualResponseDTOS || []);
      } else {
        console.error("Unexpected API response format:", response);
      }
    } catch (error) {
      console.error("Error fetching users:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleToggle = () => {
    setExpanded(!expanded);
  };


  const [userId, setUserId] = useState(null);

  useEffect(() => {
    if (!!currentRow) {
      setUserId(currentRow?.organisationId);
      getBasicProfileData(currentRow?.organisationId);
    }
  }, [currentRow]);

  useEffect(() => {
    if (currentRow) {
      if (activeTab === 0) {
        getBasicProfileData(currentRow?.organisationId);
      } else if (activeTab === 1) {
        micrositeGetEndpoint(currentRow?.id);
        getBasicProfileData(userId);
      }
    }
  }, [activeTab]);

  const [serviceNames, setServiceNames] = useState([]);
  useEffect(() => {
    const namesWithIds = basicProfileGetData?.servicesProvided
      ?.map((serviceId) => {
        const service = listValues?.find((item) => item.id === serviceId);

        return service ? { id: service.id, name: service.name } : null;
      })
      .filter(Boolean);
    setServiceNames(namesWithIds);
  }, [basicProfileGetData?.servicesProvided, listValues]);

  const [pleaseVerifyEmailMessage, setPleaseVerifyEmailMessage] =
    useState(false);

  const [disableVerifyEmailButton, setDisableVerifyEmailButton] =
    useState(false);
  const [showForm, setShowForm] = useState(false);
  const [showOTPOptions, setShowOTPOptions] = useState(false);
  const [selectedColumns, setSelectedColumns] = useState(columns);
  const [empanelDialog, setEmpanelDialog] = useState(false);

  const [strategicDialog, setStrategicDialog] = useState(false);

  const [strategicDialogOpen, setStrategicDialogOpen] = useState(false);

  const [isListingEmpanelled, setIsListingEmpanelled] = useState(false);
  const [isMicrositeEmpanelled, setIsMicrositeEmpanelled] = useState(false);

  const [anchorEl, setAnchorEl] = useState(null);
  const [entityType, setEntityType] = useState("");
  const [dialogMessage, setDialogMessage] = useState("");
  const [selectedRoleDialog, setSelectedRoleDialog] = useState(false);
  const [submitSuccess, setSubmitSuccess] = useState(false);
  const [role, setRole] = useState(null);
  const open = Boolean(anchorEl);

  const handleCloseMenu = () => {
    setAnchorEl(null);
  };
  const handleActionsClick = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const [anchorEls, setAnchorEls] = React.useState(null);
  const opens = Boolean(anchorEls);

  const handleMenuClicks = (event) => {
    setAnchorEls(event.currentTarget);
  };

  const handleMenuCloses = () => {
    setAnchorEls(null);
  };

  const handleMessageClose = () => {
    setSubmitSuccess(false);
    setIsListingEmpanelled(false);
    setIsMicrositeEmpanelled(false);
    setPleaseVerifyEmailMessage(false);
  };

  const handleStrategicDialogClose = () => {
    setStrategicDialogOpen(false);
  };

  const handleYes = async () => {
    setSubmitSuccess(false);
    let successFlag = true;
    let allAlreadyEmpanelled = true;
    const successIds = [];
    const failedIds = [];
    const updatePromises = selectedRows.map((row) => {
      if (!row.isListingEmpanelled || !row.isMicrositeEmpanelled) {
        allAlreadyEmpanelled = false;
        const fields = {
          userId: row.id,
          isListingEmpanelled: row.isListingEmpanelled
            ? true
            : isListingEmpanelled,
          isMicrositeEmpanelled: row.isMicrositeEmpanelled
            ? true
            : isMicrositeEmpanelled,
        };

        return auth
          .updateIsEmpanelled(fields)
          .then((response) => {
            successIds.push(row.id); // Add to success list
            return response;
          })
          .catch((error) => {
            failedIds.push(row.id); // Add to failed list
            successFlag = false;
          });
      }
    });

    setEmpanelDialog(false);
    await Promise.all(updatePromises);

    let message = `<div><h3>`;
    if (allAlreadyEmpanelled) {
      message += `All Selected Rows are Already Empanelled!`;
    } else if (successFlag) {
      message += `Empanelled success for ${successIds?.length} user(s).`;
      if (failedIds?.length > 0) {
        message += ` Failed for ${failedIds?.length} user(s).`;
      }
    } else {
      message += `Empanelment failed for ${failedIds.length} user(s).`;
    }
    message += `</h3></div>`;

    setDialogMessage(message);
    setSubmitSuccess(true);
    setSelectedRows([]);

    fetchServiceProviders(
      page,
      pageSize,
      roleFilter,
      searchKeyword,
      selectedFilters
    );
  };

  const handleNo = async () => {
    setSubmitSuccess(false);
    let successFlag = true;
    let allAlreadyUnEmpanelled = true;
    const successIds = [];
    const failedIds = [];

    const updatePromises = selectedRows.map((row) => {
      if (row.isListingEmpanelled || row.isMicrositeEmpanelled) {
        allAlreadyUnEmpanelled = false;
        const fields = {
          userId: row.id,
          isListingEmpanelled: isListingEmpanelled
            ? false
            : row.isListingEmpanelled,

          isMicrositeEmpanelled: isMicrositeEmpanelled
            ? false
            : row.isMicrositeEmpanelled,
        };

        return auth
          .updateIsEmpanelled(fields)
          .then((response) => {
            successIds.push(row.id); // Add to success list
            return response;
          })
          .catch((error) => {
            failedIds.push(row.id); // Add to failed list
            successFlag = false;
          });
      }
    });

    setEmpanelDialog(false);
    await Promise.all(updatePromises);

    let message = `<div><h3>`;
    if (allAlreadyUnEmpanelled) {
      message += `All Selected Rows are Already UnEmpanelled!`;
    } else if (successFlag) {
      message += `Unempanelled success for ${successIds.length} user(s).`;
      if (failedIds.length > 0) {
        message += ` Failed for ${failedIds.length} user(s).`;
      }
    } else {
      message += `Unempanelment failed for ${failedIds.length} user(s).`;
    }
    message += `</h3></div>`;

    setDialogMessage(message);
    setSubmitSuccess(true);
    setSelectedRows([]);

    fetchServiceProviders(
      page,
      pageSize,
      roleFilter,
      searchKeyword,
      selectedFilters
    );
  };

  const handleIsEmpanelled = async (selectedRows) => {
    let allRequiredInfoPresent = true;
    const inactiveRows = [];
    const noServicesProvidedRows = [];

    selectedRows.forEach((row) => {
      const isActive = row.isActive;
      const servicesProvided = row.basicProfileData?.servicesProvided || [];
      if (!isActive) {
        allRequiredInfoPresent = false;
        inactiveRows.push(row.firstName || row.companyName || "");
      } else if (servicesProvided?.length === 0) {
        allRequiredInfoPresent = false;
        noServicesProvidedRows.push(row.firstName || row.companyName || "");
      }
    });

    if (!allRequiredInfoPresent) {
      const message = `
          <div>
            <h3>Missing Information or Inactive Status</h3>
            <p>The following Service Providers lack required information or are inactive:</p>
            <div>
              <strong>Inactive Service Providers:</strong>
              <div>
                ${inactiveRows.join("<br>")}
              </div>
            </div>
            <div>
              <strong>Service Providers with No Services Provided:</strong>
              <div>
                ${noServicesProvidedRows.join("<br>")}
              </div>
            </div>
            <p>Please ensure that all selected Service Providers have services provided and are active.</p>
          </div>
        `;
      setMissingInfoMessage(message);
      setMissingInfoDialogOpen(true);
      return;
    }

    const message = `
        <div>
          <h3>Please select the page(s) to set the status.</h3>
          <p>The status will be applied to the selected row(s).</p>
        </div>
      `;
    setDialogMessage(message);
    setSubmitSuccess(true);
    setEmpanelDialog(true);
  };

  const handleIsStrategicPartner = async () => {
    const message = `
      <div>
        <h3>Confirm Action</h3>
        <p>Applying "Strategic Partner" status will impact all selected row(s).</p>
      </div>
    `;

    setDialogMessage(message);
    setStrategicDialog(true);
    setStrategicDialogOpen(true);
  };

  const handleAssignStrategic = async () => {
    setStrategicDialogOpen(false);
    let successFlag = true;
    let allAlreadyStrategicPartnered = true;
    const updatePromises = selectedRows.map((row) => {
      if (!row.isStrategicPartner) {
        allAlreadyStrategicPartnered = false;
        const fields = {
          orgId: row.organisationId,
          isStrategicPartner: true,
        };

        // Return the update promise
        return auth
          .updateIsStrategicPartner(fields)
          .then((response) => {
            return response; // This will be used to check if at least one call was successful
          })
          .catch((error) => {
            successFlag = false;
          });
      }
    });

    setStrategicDialog(false);

    const responses = await Promise.all(updatePromises);

    if (allAlreadyStrategicPartnered) {
      const message = `<div> <h3>Action Not Required</h3><p>Selected entities are already strategic partners.</p></div>
        `;

      setDialogMessage(message);
      setStrategicDialogOpen(true);
      setSelectedRows([]);
    } else if (successFlag) {
      const message = `<div><h3>Update Successful</h3><p>Status updated to strategic partner.</p></div>

      `;
      setDialogMessage(message);
      setStrategicDialogOpen(true);
      setSelectedRows([]);
    } else {
      const message = `<div>
        <h3>Update Unsuccessful</h3>
        <p>The system encountered an issue while updating the status. Please try again.</p>
      </div>      
      `;
      setDialogMessage(message);
      setStrategicDialogOpen(true);
    }

    if (responses.some((response) => response !== null)) {
      fetchServiceProviders(
        page,
        pageSize,
        roleFilter,
        searchKeyword,
        selectedFilters
      );
    }
  };

  const handleRemoveStrategic = async () => {
    setStrategicDialogOpen(false);
    let successFlag = true;
    let allAlreadyStrategicPartnered = true;
    const updatePromises = selectedRows.map((row) => {
      if (row.isStrategicPartner) {
        allAlreadyStrategicPartnered = false;
        const fields = {
          orgId: row.organisationId,
          isStrategicPartner: false,
        };

        // Return the update promise
        return auth
          .updateIsStrategicPartner(fields)
          .then((response) => {
            return response; // This will be used to check if at least one call was successful
          })
          .catch((error) => {
            successFlag = false;
          });
      }
    });

    setStrategicDialog(false);

    const responses = await Promise.all(updatePromises);

    if (allAlreadyStrategicPartnered) {
      const message = `<div>
        <h3>Action Not Required</h3>
        <p>No changes were made as the selected entity or entities are not designated as strategic partners.</p>
      </div>`;
      setDialogMessage(message);
      setStrategicDialogOpen(true);
      setSelectedRows([]);
    } else if (successFlag) {
      const message = `<div>
        <h3>Update Successful</h3>
        <p>The strategic partner status has been successfully removed.</p>
      </div>
      `;
      setDialogMessage(message);
      setStrategicDialogOpen(true);
      setSelectedRows([]);
    } else {
      const message = `<div>
        <h3>Update Unsuccessful</h3>
        <p>The system encountered an issue while updating the status. Please try again.</p>
      </div>
      `;
      setDialogMessage(message);
      setStrategicDialogOpen(true);
    }

    if (responses.some((response) => response !== null)) {
      fetchServiceProviders(
        page,
        pageSize,
        roleFilter,
        searchKeyword,
        selectedFilters
      );
    }
  };

  const handleSelection = (selectionModel) => {
    // Assuming `userList` is an array of objects and each object has a unique `id` that corresponds to the `selectionModel`
    const selectedData = userList.filter((row) =>
      selectionModel.includes(row.id)
    );
    const selectedRoles = selectedData.map((data) => data.role);
    setRole(selectedRoles);
    setSelectedRows(selectedData);
  };

  const handleSociety = () => {
    if (role.includes("Society")) {
      setDialogMessage(
        "Please deselect Society Profile(s) to Empanel/UnEmpanel Service Provider(s)"
      );
      setSelectedRoleDialog(true);
    }
  };

  const handleClose = () => {
    setPleaseVerifyEmailMessage(false);
    setDisableVerifyEmailButton(false);
    setShowForm(false);
    setShowOTPOptions(false);

    reset({
      firstName: "",
      lastName: "",
      mobileNumber: "",
      email: "",
      entityType: "",
    });
    setEntityType("");
  };

  const exportToCSV = (csvData, fileName) => {
    const filteredData = csvData.map((row) => {
      const filteredRow = {};
      Object.keys(row).forEach((key) => {
        if (
          ![
            "microSiteData",
            "societyData",
            "id",
            "userCategoryId",
            "roleId",
            "assignedTo",
            "basicProfileData",
          ].includes(key)
        ) {
          filteredRow[
            key === "firstName"
              ? "First Name"
              : key === "lastName"
              ? "Last Name"
              : key === "email"
              ? "Email"
              : key === "mobileNumber"
              ? "Mobile Number"
              : key
          ] = row[key];
        }
      });
      return filteredRow;
    });

    const message = `
      <div>
        <h3>The data have been exported successfully</h3>
      </div>
    `;
    setDialogMessage(message);
    setSubmitSuccess(true);

    const fileType =
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8";
    const fileExtension = ".xlsx";

    const ws = XLSX.utils.json_to_sheet(filteredData);
    const wb = { Sheets: { data: ws }, SheetNames: ["data"] };
    const excelBuffer = XLSX.write(wb, { bookType: "xlsx", type: "array" });
    const data = new Blob([excelBuffer], { type: fileType });
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, "0");
    const day = String(now.getDate()).padStart(2, "0");
    const hours = String(now.getHours()).padStart(2, "0");
    const minutes = String(now.getMinutes()).padStart(2, "0");
    const seconds = String(now.getSeconds()).padStart(2, "0");

    const uuid = generateUUID();
    const newFileName = `${fileName}_${uuid}${fileExtension}`;

    FileSaver.saveAs(data, newFileName);
  };
  const chipDisplayLimit = 5;

  const [showAllChips, setShowAllChips] = useState(false);

  // Number of chips to show initially
  const initialVisibleCount = 5;

  // Helper function to slice chips
  const visibleChips = (chips) =>
    showAllChips ? chips : chips.slice(0, initialVisibleCount);

  // Example UUID generation function
  function generateUUID() {
    return Math.random().toString(36).substr(2, 9);
  }

  const handleColumnSelection = (columnField) => {
    setSelectedColumns((prevState) =>
      prevState.find((col) => col.field === columnField)
        ? prevState.filter((col) => col.field !== columnField)
        : [...prevState, columns.find((col) => col.field === columnField)]
    );
  };

  const handleSocietyDialogClose = () => {
    setSelectedRoleDialog(false);
  };

  const handleOpenDialog = () => {
    setOpenDialog(true);
  };


  return (
    <>
      <Grid>
        <Dialog
          open={dialogOpen}
          aria-labelledby="alert-dialog-title"
          aria-describedby="alert-dialog-description"
          PaperProps={{
            sx: {
              p: (theme) => `${theme.spacing(2.5)} !important`,
              backgroundColor: (theme) => theme.palette.primary.background,
            },
          }}
        >
          <Box
            sx={{
              width: "100%",
              borderRadius: 1,
              textAlign: "center",
              border: (theme) => `1px solid ${theme.palette.divider}`,
              borderColor: "primary.main",
            }}
          >
            <DialogContent>
              <DialogContentText
                id="alert-dialog-description"
                color="primary.main"
              >
                <div dangerouslySetInnerHTML={{ __html: dialogMessage }} />
              </DialogContentText>
            </DialogContent>
            <DialogActions>
              <Button
                variant="contained"
                onClick={handleCloseDialog}
                style={{ margin: "10px auto", width: 100 }}
              >
                Okay
              </Button>
            </DialogActions>
          </Box>
        </Dialog>

       
        <Menu
          anchorEl={openMenu}
          open={Boolean(openMenu)}
          onClose={handleCloseMenu}
        >
          <MenuItem onClick={() => handleOptionSelect("SERVICE_PROVIDER")}>
            Service Provider
          </MenuItem>
          <MenuItem onClick={() => handleOptionSelect("SOCIETY")}>
            Society Member
          </MenuItem>
        </Menu>

        <CardContent>
          <div style={{ fontSize: "13px", marginBottom: "12px" }}>
            {selectedFilters?.map((filter) => {
              if (filter.label === "Priority" && Array.isArray(filter.value)) {
                // If the filter is for services and the value is an array of IDs
                return filter.value?.map((id) => {
                  // Find the corresponding object in servicesList for each ID
                  const matchedItem = priorityData.find(
                    (item) => item.value === id
                  );

                  // Use the key of matchedItem if found, otherwise display the ID itself
                  const displayValue = matchedItem ? matchedItem.key : id;

                  return (
                    <Chip
                      key={`${filter.key}-${id}`} // Ensure unique key for each chip
                      label={`${filter.label}: ${displayValue}`}
                      onDelete={() => handleRemoveFilterCHS(filter.key, id)} // Pass both filter key and ID
                      sx={{ mr: 1, mb: 1 }}
                    />
                  );
                });
              }

              if (
                filter.label === "Assigned To" &&
                Array.isArray(filter.value)
              ) {
                // If the filter is for services and the value is an array of IDs
                return filter.value?.map((id) => {
                  // Find the corresponding object in servicesList for each ID
                  const matchedItem = employeesData.find(
                    (item) => item.id === id
                  );

                  // Use the key of matchedItem if found, otherwise display the ID itself
                  const displayValue = matchedItem ? matchedItem.name : id;

                  return (
                    <Chip
                      key={`${filter.key}-${id}`} // Ensure unique key for each chip
                      label={`${filter.label}: ${displayValue}`}
                      onDelete={() => handleRemoveFilterCHS(filter.key, id)} // Pass both filter key and ID
                      sx={{ mr: 1, mb: 1 }}
                    />
                  );
                });
              }

              // For other filters, render a single chip
              return (
                filter.label && ( // Only render the Chip if label is not null or undefined
                  <Chip
                    key={filter.key}
                    label={`${filter.label}: ${filter.value}`}
                    onDelete={() => handleRemoveFilterCHS(filter.key)}
                    sx={{ mr: 1, mb: 1 }}
                  />
                )
              );
            })}
          </div>
          <>
            {month && (
              <Chip
                label={
                  <>
                    SP's in{" "}
                    <Typography
                      component="span"
                      sx={{ fontWeight: "bold", color: "primary.main" }}
                    >
                      {
                        [
                          "January",
                          "February",
                          "March",
                          "April",
                          "May",
                          "June",
                          "July",
                          "August",
                          "September",
                          "October",
                          "November",
                          "December",
                        ][parseInt(month, 10) - 1]
                      }
                    </Typography>{" "}
                    Month
                  </>
                }
                color="primary"
                variant="outlined"
                sx={{ mb: 3 }}
                onDelete={clearMonthFilter}
              />
            )}
            {(seriesName || duration) && (
              <Chip
                label={
                  <>
                    SP's{" "}
                    <Typography
                      component="span"
                      sx={{ fontWeight: "bold", color: "primary.main" }}
                    >
                      {(() => {
                        if (seriesName === "assignedTo")
                          return " Assigned to me";
                        if (seriesName === "createdBy") return " Created by me";
                      })()}
                      {(() => {
                        if (duration === "daily") return " today";
                        if (duration === "weekly") return " this week";
                        if (duration === "monthly") return " this month";
                        return " till now";
                      })()}
                    </Typography>{" "}
                  </>
                }
                color="primary"
                variant="outlined"
                sx={{ mb: 3 }}
                onDelete={clearFilter}
              />
            )}
            {frequency && (
              <Chip
                label={
                  <>
                    SP's onboarded{" "}
                    <Typography
                      component="span"
                      sx={{ fontWeight: "bold", color: "primary.main" }}
                    >
                      {(() => {
                        if (frequency === "daily") return " today";
                        if (frequency === "monthly") return " this month";
                        if (frequency === "quarterly") return " this quarter";
                        if (frequency === "yearly") return " this Year";
                      })()}
                    </Typography>{" "}
                  </>
                }
                color="primary"
                variant="outlined"
                sx={{ mb: 3 }}
                onDelete={clearFilter}
              />
            )}
          </>

          <div style={{ height: 380, width: "100%" }}>
            {loading ? (
              <Box
                display="flex"
                justifyContent="center"
                alignItems="center"
                height="60vh"
              >
                <FallbackSpinner />
              </Box>
            ) : (
              <>
                <DataGrid
                  rows={userList || []}
                  columns={columns}
                  getRowId={(row) => row.id}
                  checkboxSelection
                  pagination
                  pageSize={pageSize}
                  page={page - 1}
                  rowsPerPageOptions={rowsPerPageOptions}
                  rowCount={rowCount}
                  paginationMode="server"
                  onPageChange={handlePageChange}
                  onPageSizeChange={handlePageSizeChange}
                  onSelectionModelChange={handleSelection}
                  rowHeight={38}
                  headerHeight={38}
                  // onRowClick={handleRowClick}
                  components={{
                    NoRowsOverlay: () => (
                      <Typography
                        variant="body1"
                        align="center"
                        sx={{ marginTop: "120px" }}
                      >
                        {userList?.length === 0 ? "No Data" : "No Rows"}
                      </Typography>
                    ),
                  }}
                />
              </>
            )}
          </div>
        </CardContent>

        <Dialog
          fullScreen
          open={isConversationDialogOpen}
          onClose={closeConversationDialog}
        >
          <DialogTitle
            sx={{
              position: "relative",
              borderBottom: (theme) => `1px solid ${theme.palette.divider}`,
              p: (theme) => `${theme.spacing(1.5)} !important`,
              display: "flex",
              flexDirection: "row",
              alignItems: "center",
              justifyContent: "space-between",
            }}
            textAlign="center"
            fontSize="20px !important"
            fontWeight="bold"
          >
            <div style={{ flex: 1, textAlign: "center" }}>
              <Typography
                variant="h6"
                fontWeight="bold"
                display="flex-start"
                sx={{ fontSize: "18px", textAlign: "left", marginLeft: 4 }}
              >
                Conversation Details
              </Typography>
            </div>
            <div
              style={{
                display: "flex",
                alignItems: "center",
                justifyContent: "flex-end",
                gap: 2,
              }}
            >
              {/* <Box sx={{
                marginTop:2
              }}>
                <CloseExpandIcons
                  expanded={expanded}
                  onToggle={handleToggle}
                />
                </Box> */}
              <IconButton
                size="small"
                onClick={closeConversationDialog}
                sx={{
                  // p: "0.438rem",
                  borderRadius: 1,
                  mt: 5,
                  mb: 3,
                  mr: 2,
                  color: "common.white",
                  backgroundColor: "primary.main",
                  marginRight: 4.5,
                  "&:hover": {
                    backgroundColor: "#66BB6A",
                    transition: "background 0.5s ease, transform 0.5s ease",
                  },
                }}
              >
                <Icon icon="tabler:x" fontSize="1rem" />
              </IconButton>
            </div>
          </DialogTitle>

          <DialogContent>
            <Index
              currentRow={basicProfileGetData}
              expanded={expanded}
              closeConversationDialog={close}
              employeeData={employeesData}
            />
          </DialogContent>

          <DialogActions sx={{ justifyContent: "center" }}>
            <Grid item xs={12} sx={{ mt: 2 }}></Grid>
          </DialogActions>
        </Dialog>

        <Dialog
          open={showMissingNumbersDialog}
          onClose={() => setShowMissingNumbersDialog(false)}
          aria-labelledby="alert-dialog-title"
          aria-describedby="alert-dialog-description"
          maxWidth="sm"
          fullWidth
        >
          <DialogTitle id="alert-dialog-title" style={{ fontWeight: "bold" }}>
            Phone Numbers and Activity Status
          </DialogTitle>
          {!activity && (
            <DialogContent
              dividers
              style={{ overflowY: "auto", maxHeight: 300 }}
            >
              {" "}
              {/* Adjust maxHeight as needed */}
              <DialogContentText id="alert-dialog-description">
                <strong>
                  The following Service Providers do not have phone numbers:
                </strong>
                <ul>
                  {missingPhoneNumbers.map((name, index) => (
                    <li key={index}>{name}</li>
                  ))}
                </ul>
              </DialogContentText>
            </DialogContent>
          )}
          <hr />
          {!numberAvalabilty && (
            <DialogContent>
              {" "}
              {/* This part remains fixed and not scrollable */}
              <DialogContentText style={{ fontWeight: "bold" }}>
                Send message to inactive users as well?
              </DialogContentText>
              <RadioGroup
                aria-label="inactive-users"
                name="inactive-users"
                value={radioValue}
                onChange={handleRadioChange}
              >
                <FormControlLabel value="yes" control={<Radio />} label="Yes" />
                <FormControlLabel value="no" control={<Radio />} label="No" />
              </RadioGroup>
            </DialogContent>
          )}
          <DialogActions>
            <Button onClick={() => setShowMissingNumbersDialog(false)}>
              Cancel
            </Button>
            <Button
              onClick={() => {
                setShowMissingNumbersDialog(false);
                if (radioValue === "no") {
                  sendMessagesToUsersWithNumbers(
                    selectedRows.filter((row) => row.isActive)
                  );
                } else {
                  sendMessagesToUsersWithNumbers(selectedRows);
                }
                setActivity(false);
                setNumberAvalabilty(false);
              }}
              variant="contained"
              color="primary"
              disabled={!radioValue}
            >
              Send Messages
            </Button>
          </DialogActions>
        </Dialog>

        <Dialog
          open={whatsappNumberUnavailableDialog}
          onClose={() => setWhatsappNumberUnavailableDialog(false)}
          aria-labelledby="alert-dialog-title"
          aria-describedby="alert-dialog-description"
          PaperProps={{
            sx: {
              p: (theme) => `${theme.spacing(2.5)} !important`,
            },
          }}
        >
          <Box
            sx={{
              width: "100%",
              borderRadius: 1,
              textAlign: "center",
              border: (theme) => `1px solid ${theme.palette.divider}`,
              borderColor: "primary.main",
            }}
          >
            <DialogContent>
              <DialogContentText
                id="alert-dialog-description"
                color="primary.main"
              >
                Mobile number is required for{" "}
                {currentRow?.firstName || currentRow?.companyName}.
              </DialogContentText>
            </DialogContent>
            <DialogActions
              sx={{
                display: "flex",
                justifyContent: "center",
              }}
            >
              <Button
                onClick={() => setWhatsappNumberUnavailableDialog(false)}
                sx={{ margin: "auto", width: 100 }}
              >
                Okay
              </Button>
            </DialogActions>
          </Box>
        </Dialog>

        <Dialog fullScreen open={isEditDialogOpen} onClose={closeEditDialog}>
          <DialogTitle
            sx={{
              position: "relative",
              borderBottom: (theme) => `1px solid ${theme.palette.divider}`,
              p: (theme) => `${theme.spacing(1.5)} !important`,
              display: "flex",

              flexDirection: "row", // Align items side by side
              alignItems: "center", // Center items vertically
              justifyContent: "space-between", // Space out the elements
              height: "50px", // height
            }}
            textAlign="center"
            fontSize="20px !important"
            fontWeight="bold"
          >
            {/* Typography on the left */}
            <Typography
              sx={{ fontWeight: "bold", flex: "0 0 auto", marginLeft: 6 }}
            >
              {currentRow?.userCategory === "SERVICE_PROVIDER"
                ? "Update SP Info"
                : currentRow?.userCategory}
            </Typography>

            {/* Tabs centered */}
            <Box sx={{ flex: 1, display: "flex", justifyContent: "center" }}>
              {currentRow?.userCategory !== authConfig?.userCategorySociety && (
                <Tabs value={activeTab} onChange={handleTabChange}>
                  <Tab label="Basic Profile" />
                  <Tab label="For Microsite" />
                  <Tab label="Service Profile" />
                </Tabs>
              )}
            </Box>
            <Box
              sx={{
                position: "absolute",
                top: "9px",
                right: "43px",
                mr: {
                  xs: -5,
                  sm: -5,
                  md: -5,
                  lg: -4.5,
                  xl: -1,
                },
              }}
            >
              <IconButton
                size="small"
                onClick={closeEditDialog}
                sx={{
                  borderRadius: 1,
                  color: "common.white",
                  backgroundColor: "primary.main",
                  "&:hover": {
                    backgroundColor: "#66BB6A",
                    transition: "background 0.5s ease, transform 0.5s ease",
                  },
                }}
              >
                <Icon icon="tabler:x" fontSize="1rem" />
              </IconButton>
            </Box>
          </DialogTitle>

          <DialogContent>
            <Box>
              {activeTab === 0 && (
                <Grid>
                  <ProfileView
                    data={basicProfileGetData}
                    userData={currentRow}
                    expanded={expanded}
                    employeesData={employeesData}
                  />
                </Grid>
              )}
              {activeTab === 1 && (
                <DatePickerWrapper>
                  <Grid container spacing={6} className="match-height">
                    <Grid item xs={12}>
                      <Grid
                        container
                        spacing={2}
                        sx={{
                          display: "flex",
                          alignItems: "center",
                          justifyContent: "space-between",
                        }}
                      >
                        <Grid item xs={8}>
                          <PageHeader
                            subtitle={<Typography variant="body2"></Typography>}
                          />
                        </Grid>
                        <Grid
                          item
                          xs={0.5}
                          sx={{
                            width: "auto",
                            textAlign: "end",
                            justifyItems: "end",
                          }}
                        >
                          <CloseExpandIcons
                            expanded={expanded}
                            onToggle={handleToggle}
                          />
                        </Grid>
                      </Grid>
                    </Grid>
                    <Grid item xs={12}>
                      <FieldsView
                        data={micrositeBasicData}
                        expanded={expanded}
                        userData={currentRow}
                      ></FieldsView>
                    </Grid>
                    <Grid item xs={12}>
                      <AreaOfExperties
                        data={micrositeBasicData}
                        expanded={expanded}
                        userData={currentRow}
                      ></AreaOfExperties>
                    </Grid>
                    <Grid item xs={12}>
                      <AwardView
                        data={micrositeBasicData}
                        expanded={expanded}
                        userData={currentRow}
                      ></AwardView>
                    </Grid>
                    <Grid item xs={12}>
                      <MemberShipView
                        data={micrositeBasicData}
                        expanded={expanded}
                        userData={currentRow}
                      />
                    </Grid>
                    <Grid item xs={12}>
                      <TestimonialView
                        data={micrositeBasicData}
                        expanded={expanded}
                        userData={currentRow}
                      ></TestimonialView>
                    </Grid>
                    <Grid item xs={12}>
                      <EducationalInsightsView
                        data={micrositeBasicData}
                        expanded={expanded}
                        userData={currentRow}
                      ></EducationalInsightsView>
                    </Grid>
                    <Grid item xs={12}>
                      <ServiceTabs
                        tabContents={serviceNames}
                        data={projectsData}
                        expanded={expanded}
                        userData={currentRow}
                      />
                    </Grid>

                    <Grid item xs={12}>
                      <StatisticsParent
                        userDataAllProfile={currentRow}
                        expanded={expanded}
                      />
                    </Grid>
                  </Grid>
                </DatePickerWrapper>
              )}
              {activeTab === 2 && (
                <Grid>
                  <ParentComponent userDataAllProfile={currentRow} />
                </Grid>
              )}
            </Box>
          </DialogContent>
          <DialogActions>
            {/* <Button onClick={closeEditDialog} color="primary">
            Cancel
          </Button>
          <Button onClick={closeEditDialog} color="primary">
            Save
          </Button> */}
          </DialogActions>
        </Dialog>

        {/* LongForm */}
        <Dialog fullScreen open={isEditDialogOpens} onClose={closeEditDialogs}>
          <DialogTitle
            sx={{
              position: "relative",
              borderBottom: (theme) => `1px solid ${theme.palette.divider}`,
              p: (theme) => `${theme.spacing(1.5)} !important`,
              display: "flex",

              flexDirection: "row", // Align items side by side
              alignItems: "center", // Center items vertically
              justifyContent: "space-between", // Space out the elements
              height: "50px", // Set fixed height for header
            }}
            textAlign="center"
            fontSize="20px !important"
            fontWeight="bold"
          >
            {/* Typography on the left */}
            {/* <Typography sx={{ fontWeight: "bold", flex: "0 0 auto", marginLeft:4}}>
                {currentRow?.userCategory === "SERVICE_PROVIDER"
                  ? "Update SP Info"
                  : currentRow?.userCategory}
              </Typography> */}

            <Typography
              sx={{ fontWeight: "bold", flex: "0 0 auto", marginLeft: 7 }}
            >
              SP profile
            </Typography>

            {/* Tabs centered */}
            <Box sx={{ flex: 1, display: "flex", justifyContent: "center" }}>
              {currentRow?.userCategory !== authConfig?.userCategorySociety && (
                <Tabs
                  value={activeTab}
                  onChange={handleTabChange}
                  variant="scrollable"
                >
                  <Tab label="Basic Profile" />
                  <Tab label="For Microsite" />
                  <Tab label="Service Profile" />
                </Tabs>
              )}
            </Box>
            <Box
              sx={{
                position: "absolute",
                top: "6px",
                right: "43px",
                mr: {
                  xs: -5,
                  sm: -5,
                  md: -5,
                  lg: -5,
                  xl: -1,
                },
              }}
            >
              <IconButton
                size="small"
                onClick={closeEditDialogs}
                sx={{
                  borderRadius: 1,
                  color: "common.white",
                  backgroundColor: "primary.main",
                  "&:hover": {
                    backgroundColor: "#66BB6A",
                    transition: "background 0.5s ease, transform 0.5s ease",
                  },
                }}
              >
                <Icon icon="tabler:x" fontSize="1rem" />
              </IconButton>
            </Box>
          </DialogTitle>

          <DialogContent>
            {currentRow?.userCategory === authConfig?.userCategorySociety ? (
              <Box>
                <Grid container spacing={2} className="match-height">
                  <Grid item xs={12}>
                    <Grid
                      container
                      spacing={2}
                      sx={{
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "space-between",
                      }}
                    >
                      <Grid item xs={12} sx={{ position: "relative" }}>
                        <PageHeader
                          title={
                            <Typography variant="h5">
                              Society Profile
                            </Typography>
                          }
                          subtitle={<Typography variant="body2"></Typography>}
                        />
                        <Box sx={{ position: "absolute", top: 1, right: 0 }}>
                          <CloseExpandIcons
                            expanded={expanded}
                            onToggle={handleToggle}
                          />
                        </Box>
                      </Grid>
                    </Grid>
                  </Grid>
                  <Grid item xs={12}>
                    <SocietyDetails
                      data={entityData}
                      userData={currentRow}
                      expanded={expanded}
                      employeesData={employeesData}
                    ></SocietyDetails>
                  </Grid>
                  <Grid item xs={12}>
                    <LandDetails
                      data={entityData}
                      expanded={expanded}
                    ></LandDetails>
                  </Grid>
                  <Grid item xs={12}>
                    <FsiDetails
                      data={entityData}
                      expanded={expanded}
                    ></FsiDetails>
                  </Grid>
                  <Grid item xs={12}>
                    <Requirements
                      data={entityData}
                      expanded={expanded}
                    ></Requirements>
                  </Grid>
                  <Grid item xs={12}>
                    <ContactsReferences
                      data={entityData}
                      expanded={expanded}
                    ></ContactsReferences>
                  </Grid>
                  <Grid item xs={12}>
                    <OtherDetails
                      data={entityData}
                      expanded={expanded}
                    ></OtherDetails>
                  </Grid>
                  <Grid item xs={12}>
                    <AssignmentAndStatus
                      data={entityData}
                      expanded={expanded}
                      employeesData={employeesData}
                    />
                  </Grid>
                </Grid>
              </Box>
            ) : (
              <>
                <Box>
                  {activeTab === 0 && (
                    <Grid>
                      <ProfileLongForm
                        userData={userData}
                        formData={data}
                        onCancel={editClick}
                        employeesData={employeesData}
                        handleAssignedToChange={handleAssignedToChange}
                        assignedTo={assignedTo}
                        assignedToName={assignedToName}
                        createdBy={createdUserName}
                      />
                    </Grid>
                  )}
                  {activeTab === 1 && (
                    <DatePickerWrapper>
                      <Grid container spacing={6} className="match-height">
                        <Grid item xs={12}>
                          <Grid
                            container
                            spacing={2}
                            sx={{
                              display: "flex",
                              alignItems: "center",
                              justifyContent: "space-between",
                            }}
                          >
                            <Grid item xs={8}>
                              <PageHeader
                                subtitle={
                                  <Typography variant="body2"></Typography>
                                }
                              />
                            </Grid>
                            <Grid
                              item
                              xs={0.5}
                              sx={{
                                width: "auto",
                                textAlign: "end",
                                justifyItems: "end",
                              }}
                            >
                              <CloseExpandIcons
                                expanded={expanded}
                                onToggle={handleToggle}
                              />
                            </Grid>
                          </Grid>
                        </Grid>
                        <Grid item xs={12}>
                          <FieldsLongForm
                            data={micrositeBasicData}
                            expanded={expanded}
                            userData={currentRow}
                          ></FieldsLongForm>
                        </Grid>
                        <Grid item xs={12}>
                          <AreaOfExpertiseLongForm
                            data={micrositeBasicData}
                            expanded={expanded}
                            userData={currentRow}
                          ></AreaOfExpertiseLongForm>
                        </Grid>
                        <Grid item xs={12}>
                          <AwardLongForm
                            data={micrositeBasicData}
                            expanded={expanded}
                            userData={currentRow}
                          ></AwardLongForm>
                        </Grid>
                        <Grid item xs={12}>
                          <MemberShipLongForm
                            data={micrositeBasicData}
                            expanded={expanded}
                            userData={currentRow}
                          />
                        </Grid>
                        <Grid item xs={12}>
                          <TestimonialLongForm
                            data={micrositeBasicData}
                            expanded={expanded}
                            userData={currentRow}
                          ></TestimonialLongForm>
                        </Grid>

                        <Grid item xs={12}>
                          <EducationalInsightsLongForm
                            data={micrositeBasicData}
                            expanded={expanded}
                            userData={currentRow}
                          ></EducationalInsightsLongForm>
                        </Grid>
                        <Grid item xs={12}>
                          <ServicesTabsLongForm
                            tabContents={serviceNames}
                            data={projectsData}
                            expanded={expanded}
                            userData={currentRow}
                          />
                        </Grid>
                        <Grid item xs={12}>
                          <StatisticsPageLongForm
                            userDataAllProfile={currentRow}
                            expanded={expanded}
                          />
                        </Grid>
                      </Grid>
                    </DatePickerWrapper>
                  )}
                  {activeTab === 2 && (
                    <Grid textAlign={"center"} fontWeight={"bold"}>
                      {/* <ParentComponent userDataAllProfile={currentRow} /> */}
                      please select Type of Profession
                    </Grid>
                  )}
                </Box>
              </>
            )}
          </DialogContent>
          {activeTab === 0 && (
            <DialogActions
              sx={{
                justifyContent: "end",
                borderTop: (theme) => `1px solid ${theme.palette.divider}`,
                p: (theme) => `${theme.spacing(2.5)} !important`,
                height: "50px", // Set fixed height for footer
                justifyContent: "flex-end",
              }}
            >
              <Button onClick={closeEditDialogs} color="primary">
                Cancel
              </Button>
              <Button
                variant="contained"
                sx={{
                  mr: {
                    xs: 4,
                    sm: 3,
                    md: 3,
                    lg: 3.5,
                    xl: 7,
                  },
                }}
              >
                Save
              </Button>
            </DialogActions>
          )}
        </Dialog>

        {/* isSubscriptionOpen  closeSubscriptionDialog*/}

        <Dialog
          fullScreen
          open={isSubscriptionOpen}
          onClose={closeSubscriptionDialog}
        >
          <DialogTitle
            sx={{
              position: "relative",
              borderBottom: (theme) => `1px solid ${theme.palette.divider}`,
              p: (theme) => `${theme.spacing(1.75, 4)} !important`,
              display: "flex",
              alignItems: "center",
              justifyContent: "start",
              fontSize: { xs: 19, md: 20 },
            }}
            textAlign={"center"}
          >
            <Typography sx={{ fontWeight: "bold", mb: 4 }}>
              Manage Subscription
            </Typography>

            <Box sx={{ position: "absolute", top: "4px", right: "12px" }}>
              <IconButton
                size="small"
                onClick={closeSubscriptionDialog}
                sx={{
                  // p: "0.438rem",
                  borderRadius: 1,
                  color: "common.white",
                  backgroundColor: "primary.main",
                  "&:hover": {
                    backgroundColor: "#66BB6A",
                    transition: "background 0.5s ease, transform 0.5s ease",
                  },
                }}
              >
                <Icon icon="tabler:x" fontSize="1rem" />
              </IconButton>
            </Box>
          </DialogTitle>
          <DialogContent></DialogContent>
          <DialogActions
            sx={{
              justifyContent: "end",
              borderTop: (theme) => `1px solid ${theme.palette.divider}`,
              p: (theme) => `${theme.spacing(2.5)} !important`,
            }}
          >
            <Button
              display="flex"
              justifyContent="center"
              variant="outlined"
              color="primary"
              onClick={closeSubscriptionDialog}
            >
              Close
            </Button>
          </DialogActions>
        </Dialog>

        <Dialog
          open={confirmationDialogOpen}
          aria-labelledby="alert-dialog-title"
          aria-describedby="alert-dialog-description"
          PaperProps={{
            sx: {
              p: (theme) => `${theme.spacing(2.5)} !important`,
              backgroundColor: (theme) => theme.palette.primary.background,
            },
          }}
        >
          <Box
            sx={{
              width: "100%",
              borderRadius: 1,
              textAlign: "center",
              border: (theme) => `1px solid ${theme.palette.divider}`,
              borderColor: "primary.main",
            }}
          >
            <DialogContent>
              <DialogContentText
                id="alert-dialog-description"
                color="primary.main"
              >
                <div
                  dangerouslySetInnerHTML={{
                    __html: confirmationDialogMessage,
                  }}
                />
              </DialogContentText>
            </DialogContent>
            <DialogActions
              sx={{
                justifyContent: "center",
              }}
            >
              <Button
                onClick={handleConfirmStatusChange}
                style={{ margin: "0 10px auto", width: 100 }}
                variant="contained"
              >
                Yes
              </Button>
              <Button
                onClick={handleCloseConfirmationDialog}
                style={{ margin: "0 10px auto", width: 100 }}
              >
                No
              </Button>
            </DialogActions>
          </Box>
        </Dialog>

        <Dialog
          open={submitSuccess || pleaseVerifyEmailMessage}
          aria-labelledby="alert-dialog-title"
          aria-describedby="alert-dialog-description"
          PaperProps={{
            sx: {
              p: (theme) => `${theme.spacing(2.5)} !important`,
            },
          }}
          onClose={(event, reason) => {
            if (reason == "backdropClick") {
              handleMessageClose();
            }
          }}
        >
          <Box
            sx={{
              width: "100%",
              borderRadius: 1,
              textAlign: "center",
              border: (theme) => `1px solid ${theme.palette.divider}`,
              borderColor: "primary.main",
            }}
          >
            <DialogContent>
              <DialogContentText
                id="alert-dialog-description"
                color="primary.main"
              >
                <div dangerouslySetInnerHTML={{ __html: dialogMessage }} />
              </DialogContentText>
            </DialogContent>
            <DialogActions
              style={{ display: "flex", justifyContent: "center" }}
            >
              {empanelDialog ? (
                <div>
                  <Box
                    sx={{
                      backgroundColor: "white",
                      padding: 2,
                      borderRadius: "8px",
                      mb: { xs: 2, lg: 4 },
                      paddingLeft: 6,
                    }}
                  >
                    <FormControlLabel
                      control={
                        <Checkbox
                          checked={isListingEmpanelled}
                          onChange={(event) =>
                            setIsListingEmpanelled(event.target.checked)
                          }
                        />
                      }
                      label={
                        <span style={{ color: "black" }}>Listing Page</span>
                      }
                    />

                    <FormControlLabel
                      control={
                        <Checkbox
                          checked={isMicrositeEmpanelled}
                          onChange={(event) =>
                            setIsMicrositeEmpanelled(event.target.checked)
                          }
                        />
                      }
                      label={
                        <span style={{ color: "black" }}>Microsite Page</span>
                      }
                    />
                  </Box>
                  <Button
                    variant="contained"
                    onClick={handleYes}
                    sx={{
                      margin: "10px",
                      backgroundColor: "primary.main",
                      "&:disabled": {
                        backgroundColor: "white",
                        color: "grey",
                      },
                    }}
                    disabled={!isListingEmpanelled && !isMicrositeEmpanelled}
                  >
                    Empanel
                  </Button>
                  <Button
                    variant="contained"
                    onClick={handleNo}
                    sx={{
                      margin: "10px",
                      backgroundColor: "primary.main",
                      "&:disabled": {
                        backgroundColor: "white",
                        color: "grey",
                      },
                    }}
                    disabled={!isListingEmpanelled && !isMicrositeEmpanelled}
                  >
                    UnEmpanel
                  </Button>
                </div>
              ) : (
                <Button
                  variant="contained"
                  onClick={handleMessageClose}
                  sx={{ margin: "auto", width: 100 }}
                >
                  Okay
                </Button>
              )}
            </DialogActions>
          </Box>
        </Dialog>

        <Dialog
          open={strategicDialogOpen}
          aria-labelledby="alert-dialog-title"
          aria-describedby="alert-dialog-description"
          PaperProps={{
            sx: {
              p: (theme) => `${theme.spacing(2.5)} !important`,
            },
          }}
          //disableEscapeKeyDown
          onClose={(event, reason) => {
            if (reason === "backdropClick") {
              handleStrategicDialogClose();
            }
          }}
        >
          <Box
            sx={{
              width: "100%",
              borderRadius: 1,
              textAlign: "center",
              border: (theme) => `1px solid ${theme.palette.divider} `,
              borderColor: "primary.main",
            }}
          >
            <DialogContent>
              <DialogContentText
                id="alert-dialog-description"
                color="primary.main"
              >
                <div dangerouslySetInnerHTML={{ __html: dialogMessage }} />
              </DialogContentText>
            </DialogContent>
            <DialogActions
              style={{ display: "flex", justifyContent: "center" }}
            >
              {strategicDialog ? (
                <div>
                  <Button
                    variant="contained"
                    onClick={handleAssignStrategic}
                    sx={{ margin: "10px" }}
                  >
                    Assign
                  </Button>
                  <Button
                    variant="contained"
                    onClick={handleRemoveStrategic}
                    sx={{ margin: "10px" }}
                  >
                    Remove
                  </Button>
                </div>
              ) : (
                <Button
                  variant="contained"
                  onClick={handleStrategicDialogClose}
                  sx={{ margin: "auto", width: 100 }}
                >
                  Okay
                </Button>
              )}
            </DialogActions>
          </Box>
        </Dialog>
        <Dialog
          open={selectedRoleDialog}
          aria-labelledby="alert-dialog-title"
          aria-describedby="alert-dialog-description"
          PaperProps={{
            sx: {
              p: (theme) => `${theme.spacing(2.5)} !important`,
            },
          }}
          //disableEscapeKeyDown
          onClose={(event, reason) => {
            if (reason === "backdropClick") {
              handleSocietyDialogClose();
            }
          }}
        >
          <Box
            sx={{
              width: "100%",
              borderRadius: 1,
              textAlign: "center",
              border: (theme) => `1px solid ${theme.palette.divider} `,
              borderColor: "primary.main",
            }}
          >
            <DialogContent>
              <DialogContentText
                id="alert-dialog-description"
                color="primary.main"
              >
                <div dangerouslySetInnerHTML={{ __html: dialogMessage }} />
              </DialogContentText>
            </DialogContent>
            <DialogActions
              style={{ display: "flex", justifyContent: "center" }}
            >
              <Button
                variant="contained"
                onClick={handleSocietyDialogClose}
                sx={{
                  margin: "10px",
                  backgroundColor: "primary.main",
                  "&:disabled": {
                    backgroundColor: "white",
                    color: "grey",
                  },
                }}
              >
                Okay
              </Button>
            </DialogActions>
          </Box>
        </Dialog>
        <Dialog
          open={whatsappOpenDialog}
          onClose={handleCloseDialog}
          maxWidth="sm"
          fullWidth={!isBulkAction}
          PaperProps={{
            sx: {
              display: "flex",
              flexDirection: "column",
              height: isBulkAction ? "50vh" : "60vh",
              width: isBulkAction ? "60vw" : "60vw",
            },
          }}
        >
          <DialogTitle
            sx={{
              position: "relative",
              borderBottom: (theme) => `1px solid ${theme.palette.divider}`,
              p: (theme) => `${theme.spacing(1.75, 4)} !important`,
              display: "flex",
              alignItems: "center",
              justifyContent: { xs: "start" },
              fontSize: { xs: 19, md: 20 },
            }}
            textAlign={"center"}
          >
            <Box
              sx={{
                marginLeft: 4,
                fontSize: {
                  xs: 14, // Font size for extra-small screens
                  sm: 17, // Font size for small screens
                  md: 17, // Font size for medium screens
                  lg: 17, // Font size for large screens
                  xl: 17, // Font size for extra-large screens
                },
              }}
            >
              Send Message via WhatsApp
            </Box>
            <Box
              sx={{
                justifyContent: "flex-end",
                position: "absolute",
                top: {
                  xs: "3.5px",
                  sm: "5px",
                  md: "5px",
                  lg: "5px",
                  xl: "6px",
                },
                right: "10px",
                marginRight: 5.4,
              }}
            >
              <IconButton
                size="small"
                onClick={handleCloseDialog}
                sx={{
                  borderRadius: 1,
                  color: "common.white",
                  backgroundColor: "primary.main",
                  "&:hover": {
                    backgroundColor: "#66BB6A",
                    transition: "background 0.5s ease, transform 0.5s ease",
                  },
                }}
              >
                <Icon icon="tabler:x" fontSize="1rem" />
              </IconButton>
            </Box>
          </DialogTitle>
          <DialogContent
            sx={{
              position: "relative",
              p: (theme) => `${theme.spacing(10, 8)} !important`,
            }}
          >
            <FormControl fullWidth sx={{ mt: 2 }} error={!errors}>
              <InputLabel id="select-label">Select a Template</InputLabel>
              <Select
                labelId="select-label"
                value={selectedTemplateName}
                onChange={handleTemplateChange}
                label="Select a Template"
                size="small"
              >
                {templates?.map((template) => (
                  <MenuItem key={template.id} value={template.name}>
                    {template.name}
                  </MenuItem>
                ))}
              </Select>
              {!errors && (
                <FormHelperText>This field is required</FormHelperText>
              )}
            </FormControl>

            {!isBulkAction && (
              <Box mt={2}>
                <Typography
                  variant="body1"
                  sx={{
                    display: "flex",
                    justifyContent: "space-between",
                    alignItems: "center",
                    cursor: "pointer",
                    marginLeft: "0.1in",
                    fontWeight: "bold",
                  }}
                  onClick={toggleDetails}
                >
                  Service provider details:
                  {detailsExpanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
                </Typography>
                {!detailsExpanded && (
                  <Box mt={2}>
                    <Typography variant="body1" sx={{ marginLeft: "0.3in" }}>
                      <strong>Name:</strong> {currentRow?.firstName || ""}
                    </Typography>
                    <Typography
                      variant="body1"
                      sx={{ marginTop: "10px", marginLeft: "0.3in" }}
                    >
                      <strong>Services:</strong>
                      {currentRow?.basicProfileData?.servicesProvided?.length >
                      0 ? (
                        <ul
                          style={{
                            listStyle: "none",
                            padding: 0,
                            marginTop: "10px",
                          }}
                        >
                          {currentRow.basicProfileData.servicesProvided
                            .map((serviceId) => getServiceTypeValue(serviceId))
                            .filter(Boolean)
                            .map((serviceName, index) => (
                              <li key={index} style={{ marginBottom: "5px" }}>
                                {serviceName}
                              </li>
                            ))}
                        </ul>
                      ) : (
                        <span
                          style={{
                            display: "inline-block",
                            marginTop: "10px",
                          }}
                        >
                          No services provided
                        </span>
                      )}
                    </Typography>
                  </Box>
                )}
              </Box>
            )}

            <Box mt={2}>
              {showSocietyName && (
                <FormControl
                  fullWidth
                  sx={{ mt: 2 }}
                  error={Boolean(errors.societyName)}
                >
                  <Autocomplete
                    id="society-name-autocomplete"
                    options={societyOptions}
                    getOptionLabel={(option) => option.key}
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        label="Select Society"
                        size="small"
                        error={Boolean(errors.societyName)}
                        helperText={errors.societyName?.message}
                      />
                    )}
                    value={
                      societyOptions.find(
                        (option) => option.value === selectedSociety
                      ) || null
                    }
                    onChange={(event, newValue) => {
                      setSelectedSociety(newValue?.value || "");
                      setError(newValue?.value.trim() === "");
                      const isAllFilled =
                        selectedTemplateName &&
                        (!showSocietyName || newValue?.value) &&
                        (!showServiceRequirement || requirementName) &&
                        (!showMeasurements || measurements) &&
                        (!showLocation || location) &&
                        (!showImageUrl || imageUrl) &&
                        (googleFormLink || !showGoogleFormLink);
                      setIsButtonDisabled(!isAllFilled);
                    }}
                  />
                  {error && (
                    <FormHelperText style={{ color: "red" }}>
                      This field is required
                    </FormHelperText>
                  )}
                </FormControl>
              )}

              {showServiceRequirement && (
                <FormControl
                  fullWidth
                  error={Boolean(errors.serviceType)}
                  sx={{ mt: 2 }}
                >
                  <Autocomplete
                    id="serviceType"
                    options={allServicesList}
                    getOptionLabel={(option) => option.listValue}
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        label="Service requirement"
                        size="small"
                        error={Boolean(errors.serviceType)}
                        helperText={errors.serviceType?.message}
                      />
                    )}
                    value={
                      allServicesList.find(
                        (service) => service.id === requirementName
                      ) || null
                    }
                    onChange={(event, newValue) => {
                      setRequirementName(newValue?.id || "");
                      const isAllFilled =
                        selectedTemplateName &&
                        (!showSocietyName || selectedSociety) &&
                        (!showServiceRequirement || newValue?.id) &&
                        (!showMeasurements || measurements) &&
                        (!showLocation || location) &&
                        (!showImageUrl || imageUrl) &&
                        (googleFormLink || !showGoogleFormLink);
                      setIsButtonDisabled(!isAllFilled);
                    }}
                  />
                  {error && (
                    <FormHelperText style={{ color: "red" }}>
                      This field is required
                    </FormHelperText>
                  )}
                </FormControl>
              )}

              {showMeasurements && (
                <FormControl
                  fullWidth
                  error={Boolean(errors.measurements)}
                  sx={{ mt: 2 }}
                >
                  <TextField
                    id="measurements"
                    label="Measurements"
                    size="small"
                    value={measurements}
                    onChange={(e) => {
                      setMeasurements(e.target.value);
                      const isAllFilled =
                        selectedTemplateName &&
                        (!showSocietyName || selectedSociety) &&
                        (!showServiceRequirement || requirementName) &&
                        (!showMeasurements || e.target.value) &&
                        (!showLocation || location) &&
                        (!showImageUrl || imageUrl) &&
                        (googleFormLink || !showGoogleFormLink);
                      setIsButtonDisabled(!isAllFilled);
                    }}
                    error={Boolean(errors.measurements)}
                    helperText={errors.measurements?.message}
                  />
                  {error && (
                    <FormHelperText style={{ color: "red" }}>
                      This field is required
                    </FormHelperText>
                  )}
                </FormControl>
              )}
              {showLocation && (
                <FormControl
                  fullWidth
                  sx={{ mt: 2 }}
                  error={Boolean(errors.location)}
                >
                  <Controller
                    name="location"
                    control={control}
                    rules={{ required: true }}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        label="Society Location"
                        InputLabelProps={{ shrink: true }}
                        size="small"
                        onChange={(e) => {
                          setLocation(e.target.value);
                          const isAllFilled =
                            selectedTemplateName &&
                            (!showSocietyName || selectedSociety) &&
                            (!showServiceRequirement || requirementName) &&
                            (!showMeasurements || measurements) &&
                            (!showLocation || e.target.value) &&
                            (!showImageUrl || imageUrl) &&
                            (googleFormLink || !showGoogleFormLink);
                          setIsButtonDisabled(!isAllFilled);
                        }}
                        placeholder="Click on the icon to navigate & paste the URL here"
                        error={Boolean(errors.location)}
                        helperText={errors.location?.message}
                        aria-describedby="validation-basic-location"
                        InputProps={{
                          endAdornment: (
                            <InputAdornment position="end">
                              <Tooltip title="Click to navigate to Google Maps">
                                <IconButton
                                  sx={{ cursor: "pointer" }}
                                  onClick={navigateToGoogleMaps}
                                  edge="end"
                                >
                                  <LocationOnTwoToneIcon />
                                </IconButton>
                              </Tooltip>
                            </InputAdornment>
                          ),
                        }}
                      />
                    )}
                  />
                </FormControl>
              )}
              {showImageUrl && (
                <FormControl
                  fullWidth
                  sx={{ mt: 2 }}
                  error={Boolean(errors.imageUrl)}
                >
                  <TextField
                    id="imageUrl"
                    label="Image URL"
                    size="small"
                    InputLabelProps={{ shrink: true }}
                    value={imageUrl}
                    onChange={(e) => {
                      setImageUrl(e.target.value);
                      const isAllFilled =
                        selectedTemplateName &&
                        (!showSocietyName || selectedSociety) &&
                        (!showServiceRequirement || requirementName) &&
                        (!showMeasurements || measurements) &&
                        (!showLocation || location) &&
                        (!showImageUrl || e.target.value) &&
                        (googleFormLink || !showGoogleFormLink);
                      setIsButtonDisabled(!isAllFilled);
                    }}
                    placeholder="Enter the URL for the image here"
                    error={Boolean(errors.imageUrl)}
                    helperText={errors.imageUrl?.message}
                  />
                </FormControl>
              )}
              {showGoogleFormLink && (
                <FormControl
                  fullWidth
                  sx={{ mt: 2 }}
                  error={Boolean(errors.googleFormLink)}
                >
                  <TextField
                    id="googleFormLink"
                    label="Google Form Link"
                    size="small"
                    InputLabelProps={{ shrink: true }}
                    value={googleFormLink}
                    onChange={(e) => {
                      setGoogleFormLink(e.target.value);
                      const isAllFilled =
                        selectedTemplateName &&
                        (!showSocietyName || selectedSociety) &&
                        (!showServiceRequirement || requirementName) &&
                        (!showMeasurements || measurements) &&
                        (!showLocation || location) &&
                        (!showImageUrl || imageUrl) &&
                        (e.target.value || !showGoogleFormLink);
                      setIsButtonDisabled(!isAllFilled);
                    }}
                    placeholder="Enter the Google Form URL here"
                    error={Boolean(errors.googleFormLink)}
                    helperText={errors.googleFormLink?.message}
                  />
                </FormControl>
              )}
            </Box>
          </DialogContent>

          <DialogActions
            sx={{
              justifyContent: "end",
              borderTop: (theme) => `1px solid ${theme.palette.divider}`,
              p: (theme) => `${theme.spacing(2.5)} !important`,
            }}
          >
            <Button onClick={handleCloseDialog}>Cancel</Button>
            {isBulkAction ? (
              <Button
                sx={{ marginRight: 5.8 }}
                onClick={() => {
                  const missingMobileNumbers = selectedRows.filter(
                    (row) => !row.mobileNumber
                  );

                  if (missingMobileNumbers?.length > 0) {
                    const missingNames = missingMobileNumbers.map(
                      (row) => row.firstName || row.companyName
                    );
                    setMissingPhoneNumbers(missingNames);
                    setMissingPhoneNumbersDialogOpen(true);
                  } else {
                    sendMessagesToUsersWithNumbers(selectedRows);
                  }
                }}
                disabled={isButtonDisabled || selectedRows?.length === 0}
                variant="contained"
                color="primary"
              >
                Send Messages
              </Button>
            ) : (
              <Button
                onClick={sendMessage}
                disabled={isButtonDisabled || !currentRow?.mobileNumber}
                variant="contained"
                color="primary"
                sx={{
                  marginRight: 5.2,
                }}
              >
                Send Message
              </Button>
            )}
          </DialogActions>
        </Dialog>

        <Dialog
          open={messageStatus !== null}
          onClose={() => setMessageStatus(null)}
          aria-labelledby="alert-dialog-title"
          aria-describedby="alert-dialog-description"
          PaperProps={{
            sx: {
              p: (theme) => `${theme.spacing(2.5)} !important`,
              color: (theme) => theme.palette.text.secondary,
            },
          }}
        >
          <Box
            sx={{
              width: "100%",
              borderRadius: 1,
              textAlign: "center",
              border: (theme) => `1px solid ${theme.palette.divider}`,
              borderColor: "primary.main",
            }}
          >
            <DialogContent>
              <DialogContentText
                id="alert-dialog-description"
                color="primary.main"
                fontWeight={"bold"}
              >
                {dialogMessage}
              </DialogContentText>
            </DialogContent>
            <DialogActions
              sx={{
                display: "flex",
                justifyContent: "center",
              }}
            >
              <Button
                onClick={() => {
                  setMessageStatus(null);
                  handleCloseDialog();
                  handleCloseMenu(); // Close WhatsApp dialog as well
                }}
              >
                OK
              </Button>
            </DialogActions>
          </Box>
        </Dialog>

        <Dialog
          open={missingPhoneNumbersDialogOpen}
          onClose={() => setMissingPhoneNumbersDialogOpen(false)}
          aria-labelledby="alert-dialog-title"
          aria-describedby="alert-dialog-description"
          PaperProps={{
            sx: {
              p: (theme) => `${theme.spacing(2.5)} !important`,
              backgroundColor: (theme) => theme.palette.primary.background,
            },
          }}
        >
          <Box
            sx={{
              width: "100%",
              borderRadius: 1,
              textAlign: "center",
              border: (theme) => `1px solid ${theme.palette.divider}`,
              borderColor: "primary.main",
            }}
          >
            <DialogContent>
              <DialogContentText
                id="alert-dialog-description"
                color="primary.main"
              >
                The following Service Providers do not have phone numbers:
                <ul>
                  {missingPhoneNumbers.map((name, index) => {
                    const provider = selectedRows.find(
                      (row) =>
                        row.basicProfileData?.companyName || row.firstName
                    );
                    return (
                      <li key={index}>
                        <strong>
                          {provider?.basicProfileData?.companyName || "N/A"}
                        </strong>
                      </li>
                    );
                  })}
                </ul>
              </DialogContentText>
            </DialogContent>
            <DialogActions
              sx={{
                display: "flex",
                justifyContent: "center",
              }}
            >
              <Button
                onClick={() => {
                  setMissingPhoneNumbersDialogOpen(false);
                }}
                sx={{
                  margin: "10px auto",
                  width: 100,
                }}
              >
                Okay
              </Button>
            </DialogActions>
          </Box>
        </Dialog>

        <Dialog
          open={missingInfoDialogOpen}
          onClose={() => setMissingInfoDialogOpen(false)}
          aria-labelledby="alert-dialog-title"
          aria-describedby="alert-dialog-description"
          PaperProps={{
            sx: {
              p: (theme) => `${theme.spacing(2.5)} !important`,
              backgroundColor: (theme) => theme.palette.primary.background,
            },
          }}
        >
          <Box
            sx={{
              width: "100%",
              borderRadius: 1,
              textAlign: "center",
              border: (theme) => `1px solid ${theme.palette.divider}`,
              borderColor: "primary.main",
            }}
          >
            <DialogContent>
              <DialogContentText
                id="alert-dialog-description"
                color="primary.main"
              >
                <div dangerouslySetInnerHTML={{ __html: missingInfoMessage }} />
              </DialogContentText>
            </DialogContent>
            <DialogActions
              style={{ display: "flex", justifyContent: "center" }}
            >
              <Button
                variant="contained"
                onClick={() => setMissingInfoDialogOpen(false)}
                sx={{ margin: "auto", width: 100 }}
              >
                Okay
              </Button>
            </DialogActions>
          </Box>
        </Dialog>
      </Grid>
    </>
  );
};

export default UsersOverView;