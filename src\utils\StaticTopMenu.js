const staticTopMenu = [
    {
      name: "User_Management",
      title: "User Permissions",
      icon: "tabler:users",
      path: null,
      children: [
        { name: "Users", title: "Users", icon: "tabler:users", path: "/user-management/users" },
        { name: "Roles", title: "Roles", icon: "tabler:users", path: "/user-management/roles" },
        { name: "Site_Map", title: "Site Map", icon: "tabler:users", path: "/user-management/sitemap" }
      ]
    },
    {
      name: "Static_Data",
      title: "Static Data",
      icon: "tabler:chart-pie",
      path: null,
      children: [
        { name: "Documents", title: "Documents", icon: "tabler:users", path: "/static-data/documents" },
        { name: "Employees", title: "Employees", icon: "tabler:users", path: "/static-data/employees" },
        { name: "FSI", title: "FSI", icon: "tabler:users", path: "/static-data/FSI" },
        { name: "Look_Ups", title: "Look Ups", icon: "gridicons:posts", path: "/static-data/lookups" },
        { name: "Service_Requisitions", title: "Service Requisitions Master", icon: "tabler:users", path: "/static-data/service-requisition" },
        { name: "Quotations", title: "Quotations", icon: "tabler:users", path: "/static-data/quotations" },
        { name: "Work_Orders", title: "Work Orders", icon: "tabler:users", path: "/static-data/work-orders" },
        { name: "Invoices", title: "Invoices", icon: "tabler:users", path: "/static-data/invoices" }
      ]
    },
    {
      name: "Service_Providers",
      title: "Service Providers SP",
      icon: "tabler:chart-pie",
      path: null,
      children: [
        { name: "Service_Profile", title: "Service Profiles", icon: "gridicons:posts", path: "/SP/service-profiles" },
        { name: "Configuration", title: "Configuration", icon: "gridicons:posts", path: "/SP/configuration" }
      ]
    },
    {
      name: "Society_CHS",
      title: "Society CHS",
      icon: "tabler:chart-pie",
      path: null,
      children: [
        { name: "Blog_Posts_And_Videos_For_Readiness", title: "Blog Posts and Videos for Readiness", icon: "gridicons:posts", path: "/CHS/readiness-posts/" },
        { name: "Configuration", title: "Configuration", icon: "gridicons:posts", path: "/CHS/configuration/" }
      ]
    }
  ];
  
  export default staticTopMenu;
  