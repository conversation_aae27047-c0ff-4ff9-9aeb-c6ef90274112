import React, { useCallback, useEffect, useRef, useState } from "react";
import dynamic from "next/dynamic";
import dayjs from "dayjs";
import isBetween from "dayjs/plugin/isBetween";
import {
  IconButton,
  Menu,
  MenuItem,
  Box,
  Typography,
  Card,
} from "@mui/material";
import { Icon } from "@iconify/react";
import axios from "axios";
import { getAuthorizationHeaders, getUrl } from "src/helpers/utils";
import authConfig from "src/configs/auth";
import { useRouter } from "next/router";

dayjs.extend(isBetween);

const MenuProps = {
  PaperProps: {
    style: {
      maxHeight: 200,
      overflowY: "auto",
    },
  },
};

const ApexChart = dynamic(() => import("react-apexcharts"), { ssr: false });

const ServiceProviderOnboardingChartFilter = () => {
  const router = useRouter();
  const [sampleData, setSampleData] = useState({});
  const [employeesData, setEmployeesData] = useState(null);
  const [employeeId, setEmployeeId] = useState(null);
  const [fromDate, setFromDate] = useState(dayjs().format("YYYY-MM-DD"));
  const [toDate, setToDate] = useState(dayjs().format("YYYY-MM-DD"));
  const [anchorEl, setAnchorEl] = useState(null);

  useEffect(() => {
    const data = {
      roleId: "3676706b-fc31-4b41-956c-3c4c758aa663",
      fromDate: fromDate,
      toDate: toDate,
      employeeId: employeeId,
    };
    const url = getUrl(
      authConfig?.statisticsEndpointGraphs + "/sp-chs-statistics"
    );

    const headers = getAuthorizationHeaders({
      contentType: authConfig.STATISTICS_GET_SP_CHS_STATISTICS_REQUEST,
      accept: authConfig?.STATISTICS_GET_SP_CHS_STATISTICS_RESPONSE,
    });

    axios({
      method: "post",
      url: url,
      headers: headers,
      data: data,
    })
      .then((res) => {
        setSampleData(res?.data?.spAndChsStatistics);
      })
      .catch((err) => console.log("Employees error", err));
  }, [fromDate, toDate, employeeId]);

  useEffect(() => {
    axios({
      method: "get",
      url: getUrl(authConfig.selectDropdown) + "?selectionType=EMPLOYEES",
      headers: getAuthorizationHeaders(),
    })
      .then((res) => {
        setEmployeesData(res.data.data);
      })
      .catch((err) => console.log("Employees error", err));
  }, []);

  const categories = Object.keys(sampleData);
  const seriesData = Object.values(sampleData);

  const stateRef = useRef();
  stateRef.current = { fromDate, toDate, employeeId };

  const handleBarClick = (event, chartContext, config) => {
    const clickedCategory = categories[config.dataPointIndex];
    const { fromDate, toDate, employeeId } = stateRef.current;

    const queryParams = {
      fromDate: fromDate,
      toDate: toDate,
      employee: employeeId,
    };

    switch (clickedCategory) {
      case "Onboarded":
        router.push({ pathname: "/SP", query: queryParams });
        break;
      case "Meetings Happened":
        router.push({ pathname: "/appointments-calendar", query: queryParams });
        break;
      case "Total Conversations":
        router.push({ pathname: "/SP/conversations", query: queryParams });
        break;
      case "Empanelled":
        router.push({
          pathname: "/SP",
          query: { ...queryParams, empanelled: true },
        });
        break;
      default:
        break;
    }
  };

  const handleFromDateChange = (e) => {
    const newFromDate = e.target.value;
    setFromDate(newFromDate);
    if (dayjs(newFromDate).isAfter(toDate)) {
      setToDate(newFromDate);
    }
  };

  const handleToDateChange = (e) => {
    const newToDate = e.target.value;
    setToDate(newToDate);
    if (dayjs(newToDate).isBefore(fromDate)) {
      setFromDate(newToDate);
    }
  };

  const options = {
    chart: {
      type: "bar",
      height: 350,
      toolbar: { show: false },
      // events: { dataPointSelection: handleBarClick },
    },
    xaxis: {
      categories,
      title: { text: "SP's Count" },
    },
    yaxis: {
      title: { text: "Category" },
    },
    plotOptions: { bar: { horizontal: true } },
    colors: ["rgb(96, 194, 96)"],
    dataLabels: {
      enabled: true,
      style: { colors: ["black"] },
    },
  };

  return (
    <Card
      sx={{
        mb: 2,
        mt: 2,
        p: 2,
        borderRadius: 2,
        boxShadow: 2,
      }}
    >
      <Box width="100%" p={3}>
        <Box
          display="flex"
          alignItems="center"
          justifyContent="space-between"
          mb={3}
        >
          <Typography variant="body1" fontWeight="bold" color="black">
            {employeeId
              ? `Service Provider metrics onboarded by ${
                  employeesData?.find((item) => item.id === employeeId)?.name ||
                  ""
                }`
              : "Total SP metrics"}
          </Typography>
          <IconButton onClick={(e) => setAnchorEl(e.currentTarget)}>
            <Icon
              icon="tabler:baseline-density-medium"
              width="20"
              height="20"
              style={{ cursor: "pointer", color: "black" }}
            />
          </IconButton>
        </Box>

        <Box display="flex" gap={2} mb={3}>
          <label>
            From:
            <input
              type="date"
              value={fromDate}
              onChange={handleFromDateChange}
              max={dayjs().format("YYYY-MM-DD")}
            />
          </label>
          <label>
            To:
            <input
              type="date"
              value={toDate}
              onChange={handleToDateChange}
              min={fromDate}
              max={dayjs().format("YYYY-MM-DD")}
            />
          </label>
          <label>
            <Typography
              variant="body1"
              component="span"
              sx={{ fontWeight: "bold" }}
            >
              No. of Day(s):
            </Typography>
            <Typography variant="body1" component="span" sx={{ marginLeft: 1 }}>
              {dayjs(toDate).diff(dayjs(fromDate), "days") + 1 || "0"}
            </Typography>
          </label>
        </Box>

        <Menu
          anchorEl={anchorEl}
          open={Boolean(anchorEl)}
          onClose={() => setAnchorEl(null)}
          {...MenuProps}
        >
          <MenuItem
            key="all"
            onClick={() => {
              setEmployeeId(null);
              setAnchorEl(null);
            }}
          >
            All Employees
          </MenuItem>
          {employeesData?.map((employee) => (
            <MenuItem
              key={employee.id}
              onClick={() => {
                setEmployeeId(employee.id);
                setAnchorEl(null);
              }}
            >
              {employee.name}
            </MenuItem>
          ))}
        </Menu>

        <ApexChart
          options={options}
          series={[{ name: "Count", data: seriesData }]}
          type="bar"
          height={350}
        />
      </Box>
    </Card>
  );
};

export default ServiceProviderOnboardingChartFilter;
