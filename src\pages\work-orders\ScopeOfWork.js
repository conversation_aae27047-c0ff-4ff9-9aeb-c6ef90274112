import { useTheme } from "@emotion/react";
import {
  Add as AddIcon,
  Check as CheckIcon,
  Close as CloseIcon,
  Delete as DeleteIcon,
  DragIndicator as DragIndicatorIcon,
  Edit as EditIcon,
  ExpandLess as ExpandLessIcon,
  ExpandMore as ExpandMoreIcon,
} from "@mui/icons-material";
import {
  Collapse,
  IconButton,
  List,
  ListItem,
  ListItemText,
  TextareaAutosize,
  TextField,
  Typography,
  useMediaQuery,
  styled,
} from "@mui/material";
import Paper from "@mui/material/Paper";
import { Box } from "@mui/system";
import "jspdf-autotable";
import { useState } from "react";
import { DragDropContext, Draggable, Droppable } from "react-beautiful-dnd";

const StyledTextarea = styled(TextareaAutosize)(({ theme }) => ({
  width: '100%',           // Always full width of container
  minWidth: '98%',        // Minimum width
  minHeight: '100px',       // Minimum height
  maxHeight: '200px',       // Maximum height (optional)
  padding: '8px',
  fontSize: '16px',
  borderRadius: '8px',
  border: '1px solid #ccc',
  resize: 'vertical',       // Allow only vertical resizing
}));

const ScopeOfWork = ({ scopeData, setScopeData }) => {
  const theme = useTheme();

  const [expanded, setExpanded] = useState({});

  const handleToggleExpand = (index) => {
    setExpanded((prev) => ({ ...prev, [index]: !prev[index] }));
  };

  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));

  const [editingMainPointIndex, setEditingMainPointIndex] = useState(null);
  const [editingSubPointIndex, setEditingSubPointIndex] = useState(null);
  const [editMainPointValue, setEditMainPointValue] = useState("");
  const [editSubPointValue, setEditSubPointValue] = useState("");
  const [newMainPoint, setNewMainPoint] = useState("");
  const [newSubPoint, setNewSubPoint] = useState({});

  // Main Point Handlers
  const handleAddMainPoint = () => {
    if (newMainPoint.trim()) {
      setScopeData([
        ...scopeData,
        {
          id: crypto.randomUUID(),
          name: newMainPoint.trim(),
          sequence: scopeData?.length + 1,
          subPoints: [],
        },
      ]);
      setNewMainPoint("");
    }
  };

  const handleEditMainPoint = (index) => {
    setEditingMainPointIndex(index);
    setEditMainPointValue(scopeData[index].name);
  };

  const handleSaveMainPoint = (index) => {
    const updatedScope = [...scopeData];
    updatedScope[index] = {
      ...updatedScope[index],
      name: editMainPointValue.trim(),
    };
    setScopeData(updatedScope);
    setEditingMainPointIndex(null);
    setEditMainPointValue("");
  };
  const handleCancelEditMainPoint = () => {
    setEditingMainPointIndex(null);
    setEditMainPointValue("");
  };

  const handleDeleteMainPoint = (index) => {
    const updatedScope = scopeData
      .filter((_, i) => i !== index)
      ?.map((item, seq) => ({ ...item, sequence: seq + 1 })); // Re-sequencing
    setScopeData(updatedScope);
  };

  const handleAddSubPoint = (index) => {
    if (newSubPoint[index]?.trim()) {
      const updatedScope = [...scopeData];
      const mainPoint = updatedScope[index];
      const newSubPointData = {
        id: crypto.randomUUID(),
        name: newSubPoint[index].trim(),
        sequence: mainPoint.subPoints?.length + 1,
      };
      mainPoint.subPoints.push(newSubPointData);
      setScopeData(updatedScope);
      setNewSubPoint({ ...newSubPoint, [index]: "" });
    }
  };

  const handleEditSubPoint = (mainIndex, subIndex) => {
    setEditingSubPointIndex({ mainIndex, subIndex });
    setEditSubPointValue(scopeData[mainIndex].subPoints[subIndex].name);
  };

  const handleSaveSubPoint = () => {
    const { mainIndex, subIndex } = editingSubPointIndex;
    const updatedScope = [...scopeData];
    const subPoint = updatedScope[mainIndex].subPoints[subIndex];
    updatedScope[mainIndex].subPoints[subIndex] = {
      ...subPoint,
      name: editSubPointValue.trim(),
    };
    setScopeData(updatedScope);
    setEditingSubPointIndex(null);
    setEditSubPointValue("");
  };

  const handleCancelEditSubPoint = () => {
    setEditingSubPointIndex(null);
    setEditSubPointValue("");
  };

  const handleDeleteSubPoint = (mainIndex, subIndex) => {
    const updatedScope = [...scopeData];
    updatedScope[mainIndex].subPoints = updatedScope[mainIndex].subPoints
      .filter((_, i) => i !== subIndex)
      ?.map((item, seq) => ({ ...item, sequence: seq + 1 })); // Re-sequencing
    setScopeData(updatedScope);
  };

  // Drag and Drop Handlers
  const onDragEnd = (result) => {
    const { source, destination, type } = result;

    if (!destination) return;

    const updatedScope = [...scopeData];

    // Handling main point drag and drop
    if (type === "mainPoint") {
      const [removed] = updatedScope.splice(source.index, 1);
      updatedScope.splice(destination.index, 0, removed);
      updatedScope.forEach((item, index) => (item.sequence = index + 1)); // Re-sequencing
    }

    // Handling sub-point drag and drop
    if (type === "subPoint") {
      const sourceMainIndex = parseInt(source.droppableId.split("-")[1]);
      const destMainIndex = parseInt(destination.droppableId.split("-")[1]);

      const sourceSubPoints = [...updatedScope[sourceMainIndex].subPoints];
      const [removed] = sourceSubPoints.splice(source.index, 1);

      if (sourceMainIndex === destMainIndex) {
        sourceSubPoints.splice(destination.index, 0, removed);
        sourceSubPoints.forEach((item, index) => (item.sequence = index + 1)); // Re-sequencing
        updatedScope[sourceMainIndex].subPoints = sourceSubPoints;
      } else {
        const destSubPoints = [...updatedScope[destMainIndex].subPoints];
        destSubPoints.splice(destination.index, 0, removed);
        sourceSubPoints.forEach((item, index) => (item.sequence = index + 1)); // Re-sequencing
        destSubPoints.forEach((item, index) => (item.sequence = index + 1)); // Re-sequencing
        updatedScope[sourceMainIndex].subPoints = sourceSubPoints;
        updatedScope[destMainIndex].subPoints = destSubPoints;
      }
    }

    setScopeData(updatedScope);
  };

  return (
    <>
      <DragDropContext onDragEnd={onDragEnd}>
        <Droppable droppableId="mainPoints" type="mainPoint">
          {(provided) => (
            <List
              {...provided.droppableProps}
              ref={provided.innerRef}
              sx={{ bgcolor: "background.paper" }}
            >
              {scopeData?.map((item, index) => (
                <Draggable
                  key={index}
                  draggableId={`main-${index}`} // Correct template literal usage
                  index={index}
                >
                  {(providedDrag) => (
                    <Paper
                      ref={providedDrag.innerRef}
                      {...providedDrag.draggableProps}
                      {...providedDrag.dragHandleProps} // Ensuring drag handle props are passed
                      sx={{
                        mb: 0.5,
                        boxShadow: "0px 1px 3px rgba(0, 0, 0, 0.2)",
                      }}
                    >
                      <ListItem
                        alignItems="flex-start"
                        dense
                        sx={{ py: 0.5 }}
                        secondaryAction={
                          editingMainPointIndex === index ? null : (
                            <>
                              <IconButton
                                edge="end"
                                aria-label="expand"
                                onClick={() => handleToggleExpand(index)}
                                size="small"
                              >
                                {expanded[index] ? (
                                  <ExpandLessIcon fontSize="small" />
                                ) : (
                                  <ExpandMoreIcon fontSize="small" />
                                )}
                              </IconButton>
                              <IconButton
                                edge="end"
                                aria-label="edit"
                                onClick={() => handleEditMainPoint(index)}
                                size="small"
                              >
                                <EditIcon fontSize="small" />
                              </IconButton>
                              <IconButton
                                edge="end"
                                aria-label="delete"
                                onClick={() => handleDeleteMainPoint(index)}
                                size="small"
                              >
                                <DeleteIcon fontSize="small" />
                              </IconButton>
                            </>
                          )
                        }
                      >
                        <Box
                          {...providedDrag.dragHandleProps}
                          sx={{ mr: 1, mt: 0.5 }}
                        >
                          <DragIndicatorIcon
                            fontSize="small"
                            sx={{ mt: 1.5 }}
                          />
                        </Box>
                        {editingMainPointIndex === index ? (
                          <Box
                            sx={{
                              display: "flex",
                              alignItems: "center",
                              width: "100%",
                            }}
                          >
                            <StyledTextarea
                              value={editMainPointValue}
                              onChange={(e) =>
                                setEditMainPointValue(e.target.value)
                              }
                              autoFocus
                              rows={4}
                              multiline
                              fullWidth
                              sx={{ mr: 1 }}
                              inputProps={{
                                style: {
                                  fontSize: isMobile ? "0.9rem" : "1rem",
                                },
                              }}
                            />
                            <IconButton
                              onClick={() => handleSaveMainPoint(index)}
                              size="small"
                              color="primary"
                            >
                              <CheckIcon fontSize="small" />
                            </IconButton>
                            <IconButton
                              onClick={handleCancelEditMainPoint}
                              size="small"
                              color="secondary"
                            >
                              <CloseIcon fontSize="small" />
                            </IconButton>
                          </Box>
                        ) : (
                          <ListItemText
                            primary={
                              <Typography
                                variant="subtitle1"
                                fontWeight="bold"
                                sx={{
                                  fontSize: isMobile ? "0.75rem" : "0.8rem",
                                  wordBreak: "break-word",
                                }}
                              >
                                {`${index + 1}. ${item?.name}`}{" "}
                                {/* Correct use of template literals */}
                              </Typography>
                            }
                          />
                        )}
                      </ListItem>

                      {/* Sub-Points */}
                      <Collapse
                        in={expanded[index]}
                        timeout="auto"
                        unmountOnExit
                      >
                        <Droppable droppableId={`sub-${index}`} type="subPoint">
                          {(providedSub) => (
                            <List
                              component="div"
                              disablePadding
                              sx={{ pl: 4 }}
                              {...providedSub.droppableProps}
                              ref={providedSub.innerRef}
                            >
                              {item?.subPoints?.map((subItem, subIndex) => (
                                <Draggable
                                  key={subIndex}
                                  draggableId={`sub-${index}-${subIndex}`} // Correct template literal usage
                                  index={subIndex}
                                >
                                  {(providedSubDrag) => (
                                    <ListItem
                                      ref={providedSubDrag.innerRef}
                                      {...providedSubDrag.draggableProps}
                                      {...providedSubDrag.dragHandleProps}
                                      dense
                                      sx={{ py: 0.5 }}
                                      secondaryAction={
                                        editingSubPointIndex?.mainIndex ===
                                          index &&
                                        editingSubPointIndex?.subIndex ===
                                          subIndex ? null : (
                                          <>
                                            <IconButton
                                              edge="end"
                                              aria-label="edit"
                                              onClick={() =>
                                                handleEditSubPoint(
                                                  index,
                                                  subIndex
                                                )
                                              }
                                              size="small"
                                            >
                                              <EditIcon fontSize="small" />
                                            </IconButton>
                                            <IconButton
                                              edge="end"
                                              aria-label="delete"
                                              onClick={() =>
                                                handleDeleteSubPoint(
                                                  index,
                                                  subIndex
                                                )
                                              }
                                              size="small"
                                            >
                                              <DeleteIcon fontSize="small" />
                                            </IconButton>
                                          </>
                                        )
                                      }
                                    >
                                      <Box
                                        {...providedSubDrag.dragHandleProps}
                                        sx={{ mr: 1, mt: 0.5 }}
                                      >
                                        <DragIndicatorIcon
                                          fontSize="small"
                                          sx={{ mt: 1.5 }}
                                        />
                                      </Box>
                                      {editingSubPointIndex?.mainIndex ===
                                        index &&
                                      editingSubPointIndex?.subIndex ===
                                        subIndex ? (
                                        <Box
                                          sx={{
                                            display: "flex",
                                            alignItems: "center",
                                            width: "100%",
                                          }}
                                        >
                                          <StyledTextarea
                                            value={editSubPointValue}
                                            onChange={(e) =>
                                              setEditSubPointValue(
                                                e.target.value
                                              )
                                            }
                                            autoFocus
                                            rows={4}
                                            multiline
                                            fullWidth
                                            sx={{ mr: 1 }}
                                            inputProps={{
                                              style: {
                                                fontSize: isMobile
                                                  ? "0.73rem"
                                                  : "0.75rem",
                                              },
                                            }}
                                          />
                                          <IconButton
                                            onClick={handleSaveSubPoint}
                                            size="small"
                                            color="primary"
                                          >
                                            <CheckIcon fontSize="small" />
                                          </IconButton>
                                          <IconButton
                                            onClick={handleCancelEditSubPoint}
                                            size="small"
                                            color="secondary"
                                          >
                                            <CloseIcon fontSize="small" />
                                          </IconButton>
                                        </Box>
                                      ) : (
                                        <ListItemText
                                          primary={
                                            <Typography
                                              variant="body1"
                                              sx={{
                                                fontSize: isMobile
                                                  ? "0.73rem"
                                                  : "0.75rem",
                                                wordBreak: "break-word",
                                              }}
                                            >
                                              {`${index + 1}.${subIndex + 1} ${
                                                subItem?.name
                                              }`}
                                              {/* Correct template literal usage */}
                                            </Typography>
                                          }
                                        />
                                      )}
                                    </ListItem>
                                  )}
                                </Draggable>
                              ))}
                              {providedSub.placeholder}

                              {/* Add Sub-Point */}
                              <ListItem dense sx={{ py: 0.5 }}>
                                <StyledTextarea
                                  value={newSubPoint[index] || ""}
                                  onChange={(e) =>
                                    setNewSubPoint({
                                      ...newSubPoint,
                                      [index]: e.target.value,
                                    })
                                  }
                                  placeholder="Add sub-point"
                                  rows={4}
                                  multiline
                                  fullWidth
                                  sx={{ mt: 2, mb: 2 }}
                                  inputProps={{
                                    style: {
                                      fontSize: isMobile
                                        ? "0.73rem"
                                        : "0.75rem",
                                    },
                                  }}
                                />

                                <IconButton
                                  variant="contained"
                                  color="primary"
                                  sx={{
                                    ml: 1,
                                    "&:hover": {
                                      boxShadow:
                                        "0px 0px 8px rgba(0, 0, 0, 0.2)",
                                    },
                                  }}
                                  onClick={() => handleAddSubPoint(index)}
                                >
                                  <AddIcon fontSize="small" />
                                </IconButton>
                              </ListItem>
                            </List>
                          )}
                        </Droppable>
                      </Collapse>
                    </Paper>
                  )}
                </Draggable>
              ))}
              {provided.placeholder}
            </List>
          )}
        </Droppable>
      </DragDropContext>

      {/* Add Main Point */}
      <Box sx={{ display: "flex", mt: 1 }}>
        <StyledTextarea
          value={newMainPoint}
          onChange={(e) => setNewMainPoint(e.target.value)}
          rows={4}
          multiline
          fullWidth
          inputProps={{
            style: {
              fontSize: isMobile ? "0.75rem" : "0.8rem",
            },
          }}
        />
        <IconButton
          variant="contained"
          color="primary"
          sx={{
            ml: 1,
            "&:hover": {
              boxShadow: "0px 0px 5px rgba(0, 0, 0, 0.2)",
            },
          }}
          onClick={handleAddMainPoint}
        >
          <AddIcon fontSize="small" />
        </IconButton>
      </Box>
    </>
  );
};

export default ScopeOfWork;
