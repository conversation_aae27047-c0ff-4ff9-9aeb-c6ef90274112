import React from "react";
import axios from "axios";
import { getUrl, getAuthorizationHeaders, getFileUploadPDFHeaders } from "src/helpers/utils";
import authConfig from "src/configs/auth";
import { fetchIpAddress } from "src/@core/components/custom-components/FetchIpAddress";
import { saveAs } from "file-saver";

export const fetchWorkOrders = async (
  page,
  pageSize,
  selectedFilters,
  setWorkOrdersList,
  setRowCount,
  setLoading,
  user
) => {
  setLoading(true);

  let url;
  if (user?.roleId === authConfig?.superAdminRoleId) {
    url = getUrl(authConfig.workOrdersEndpoint) + "/admin/all";
  } else {
    url = getUrl(authConfig.workOrdersEndpoint) + "/all";
  }

  let headers;

  if (user?.roleId === authConfig.superAdminRoleId) {
    headers = getAuthorizationHeaders({
      contentType: authConfig.WORK_ORDERS_GET_ALL_BY_ADMIN_REQ_V1,
      accept: authConfig.WORK_ORDERS_GET_ALL_BY_ADMIN_RES_V1,
    });
  } else {
    headers = getAuthorizationHeaders({
      contentType: authConfig.WORK_ORDERS_GET_ALL_FOR_SP_CHS_EMP_REQ_V1,
      accept: authConfig.WORK_ORDERS_GET_ALL_FOR_SP_CHS_EMP_RES_V1,
    });
  }

  const data = {
    page: page,
    pageSize: pageSize,
  };

  selectedFilters?.forEach((filter) => {
    const key = filter.key;
    data[key] = filter.value;
  });

  try {
    const response = await axios({
      method: "post",
      url: url,
      headers: headers,
      data: data,
    });
    if (response.data) {
      setRowCount(response.data?.count || 0);
      setWorkOrdersList(response.data?.workOrderResponseDTOS || []);
    } else {
      console.error("Unexpected API response format:", response);
    }
  } catch (error) {
    console.error("Error fetching users:", error);
  } finally {
    setLoading(false);
  }
};

export const handleUpdateWorkOrder = async (
  fields,
  workOrderDetails,
  handleFailure,
  handleSuccess,
  selectedFiles,
  setDialogMessage,
  setDialogSuccess,
  updatedFile,
  patchWorkOrderUpload,
  auth,
  fetchWorkOrders,
  page,
  pageSize,
  selectedFilters,
  setWorkOrdersList,
  setRowCount,
  setLoading,
  user,
  handleCloseDialog
) => {
  const ipAddress = await fetchIpAddress();

  try {
    const response = await auth.updateWorkOrder(
      workOrderDetails?.id,
      fields,
      handleFailure,
      handleSuccess
    );
  } catch (error) {
    console.error("Work order Updation failed:", error);
    handleFailure();
  }

  if (updatedFile) {
    const formData = new FormData();
    if (selectedFiles[0] && selectedFiles[0]?.size > 0) {
      formData.append("file", selectedFiles[0]);
    } else {
      formData.append("file", binaryText, document?.location?.split("/").pop());
    }

    formData.append("legalBindingRequest", ipAddress);

    await patchWorkOrderUpload(
      workOrderDetails?.id,
      formData,
      () => {
        const message = `Work Order file uploaded Successfully`;
        setDialogMessage(message);
        setDialogSuccess(true);
      },
      () => {
        const message = `Failed to upload work order file`;
        setDialogMessage(message);
        setDialogSuccess(true);
      }
    );
  }
  handleCloseDialog()
  fetchWorkOrders(
    page,
    pageSize,
    selectedFilters,
    setWorkOrdersList,
    setRowCount,
    setLoading,
    user
  );
};

export const handleSaveWorkOrder = async (
  fields,
  selectedFiles,
  setDialogMessage,
  setDialogSuccess,
  fetchWorkOrders,
  page,
  pageSize,
  selectedFilters,
  setWorkOrdersList,
  setRowCount,
  setLoading,
  user,
  postWorkOrder,
  handleCloseDialog
) => {
  const formData = new FormData();
  formData.append("file", selectedFiles[0]);
  formData.append("workOrderDTO", JSON.stringify(fields));
  // Handle form submission logic here
  await postWorkOrder(
    formData,
    () => {
      const message = `Work Order Created Successfully`;
      setDialogMessage(message);
      setDialogSuccess(true);
    },
    () => {
      const message = `Failed to create work order`;
      setDialogMessage(message);
      setDialogSuccess(true);
    }
  );
  handleCloseDialog()
  fetchWorkOrders(
    page,
    pageSize,
    selectedFilters,
    setWorkOrdersList,
    setRowCount,
    setLoading,
    user
  );
};

 function base64ToBlob(base64, contentType) {
    const binaryString = window.atob(base64);
    const len = binaryString?.length;
    const bytes = new Uint8Array(len);
    for (let i = 0; i < len; i++) {
      bytes[i] = binaryString.charCodeAt(i);
    }
    return new Blob([bytes], { type: contentType });
  }

export const fetchFile = async (document,setBinaryText,getFileByLocation) => {
    try {
      const response = await getFileByLocation(document?.location);
      const fileName = document?.location.split("/").pop();
      const fileExtension = fileName.split(".").pop().toLowerCase();
      let inferredFileType;
      switch (fileExtension) {
        case "pdf":
          inferredFileType = "application/pdf";
          break;
        case "docx":
          inferredFileType =
            "application/vnd.openxmlformats-officedocument.wordprocessingml.document";
          break;
        case "jpg":
        case "jpeg":
        case "png":
          inferredFileType = `image/${fileExtension}`;
          break;
        default:
          inferredFileType = "unknown";
      }
      const blob = base64ToBlob(response?.data?.data, inferredFileType);
      setBinaryText(blob);
    } catch (error) {
      console.error("Error fetching file content:", error);
    }
  };

  export const handleDownloadPDF = async (fields,setGeneratingPDF) => {
    const url = getUrl(authConfig.workOrdersEndpoint) + "/generate";
    const headers = getFileUploadPDFHeaders({
      contentType: "application/json",
    });

    try {
      const response = await axios({
        method: "post",
        url: url,
        headers: headers,
        data: fields,
        responseType: "arraybuffer",
      });

      if (response.status === 200) {
        const pdfBlob = new Blob([response.data], { type: "application/pdf" });
        saveAs(pdfBlob, "Work_Order.pdf");
      } else {
        console.error("Document object is not available.");
      }
    } catch (error) {
      console.error("Error fetching users:", error);
    }
    setGeneratingPDF(false)
  }

  const WorkOrders = () => {
    return null; // This component is just a placeholder for export purposes
  };
  
  export default WorkOrders;
