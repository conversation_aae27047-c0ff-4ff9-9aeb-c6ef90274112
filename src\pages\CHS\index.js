import { Card } from "@mui/material";
import { useRBAC } from "src/pages/permission/RBACContext";
import { MENUS, PAGES, PERMISSIONS } from "src/constants";
import { useRouter } from "next/router";
import NavTabsProfiles from "src/@core/components/custom-components/NavTabsProfiles";
import { useEffect,useState } from "react";
import authConfig from "src/configs/auth";
import LeadSnapshots from "../leads/snapshots";
import UsersOverView from "./UsersOverview";
import Email from "../storedEmails";



const AllUsers = () => {

  const { canMenuPage, rbacRoles,canMenuPageSection } = useRBAC();

const router = useRouter();

 const [activeTab, setActiveTab] = useState(""); 

const canAccessSociety = (requiredPermission) =>
canMenuPageSection(MENUS.LEFT, PAGES.GROWTH, PAGES.SOCIETY_CHS , requiredPermission);

useEffect(() => {
  if (rbacRoles != null && rbacRoles?.length > 0) {
    if (!canAccessSociety(PERMISSIONS.READ)) {
      router.push("/401");
    }
  }
}, [rbacRoles]);

useEffect(()=>{
  setActiveTab("onboarded")
},[])



  if(canAccessSociety(PERMISSIONS.READ)) {
  return (
    <div>
      <NavTabsProfiles
       activeTab={activeTab}
       onTabChange={(newTab) => setActiveTab(newTab)}
        tabContent1={
          <>
            <UsersOverView />
          </>
        }
        tabContent2={
          <>
            <LeadSnapshots category={"SOCIETY"} type={"CHS"} />
          </>
        }
        tabContent3 = {
          <>
            <Email role={authConfig.societyRoleId} />
          </>
        }
      />
    </div>
  );
} else {
  return null;
}
};

export default AllUsers;