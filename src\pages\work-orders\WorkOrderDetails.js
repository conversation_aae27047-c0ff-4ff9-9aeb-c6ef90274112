import { useTheme } from "@emotion/react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>onte<PERSON>,
  <PERSON>,
  Divider,
  Grid,
  Typography,
  useMediaQuery,
} from "@mui/material";
import { Box } from "@mui/system";
import axios from "axios";
import "jspdf-autotable";
import { useRouter } from "next/router";
import { useContext, useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import ViewSnapshotByLocation from "src/@core/components/custom-components/ViewSnapshotByLocation";
import authConfig from "src/configs/auth";
import { AuthContext } from "src/context/AuthContext";
import {
  getAuthorizationHeaders,
  getUrl
} from "src/helpers/utils";
import { useAuth } from "src/hooks/useAuth";
import AdvancedSearch from "./AdvancedSearch";
import AnchorWorkOrders from "./AnchorWorkOrders";
import DeActivateDialog from "./DeActivateDialog";
import FilterChips from "./FilterChips";
import PDFGenerationDialog from "./PDFGenetationDialog";
import SuccessDialog from "./SuccessDialog";
import WorkOrdersViewDialog from "./view-work-order";
import WorkOrderDialog from "./WorkOrderDialog";
import useColumns from "./WorkOrdersColumns";
import WorkOrdersDataGrid from "./WorkOrdersDataGrid";
import { fetchFile, fetchWorkOrders, handleDownloadPDF, handleSaveWorkOrder, handleUpdateWorkOrder } from "./WorkOrderUtils";

const WorkOrderDetails = () => {
  const auth = useAuth();
  const theme = useTheme();
 

  const {
    user,
    postWorkOrder,
    workOrderDetails,
    requisitionDataDetails,
    setRequisitionDataDetails,
    setWorkOrderDetails,
    patchWorkOrderUpload,
    getFileByLocation,
  } = useContext(AuthContext);

  const [generatingPDF, setGeneratingPDF] = useState(false);

  const {
    reset,
  } = useForm();
  
   // Check if the device is mobile
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));

    // Router instance to handle routing
  const router = useRouter();
  const { id, signedWorkOrders, currentWeekWorkOrders } = router.query;
  const { fromDate,toDate,employeeId} = router.query;
  
  // dropdowns
  const [societyOptions, setSocietyOptions] = useState([]);
  const [requirements, setRequirements] = useState([]);
  const [templatesList, setTemplatesList] = useState([]);

  //Data grid related states
  const rowsPerPageOptions = [10, 15, 20, 25, 50, 100];
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(rowsPerPageOptions[0]);
  const [rowCount, setRowCount] = useState(0);
  const [loading, setLoading] = useState(false);
  const [workOrdersList, setWorkOrdersList] = useState([]);

  // Advanced search related states
  const [selectedFilters, setSelectedFilters] = useState([]);
  const [searchingState, setSearchingState] = useState(false);
  const [addUserOpen, setAddUserOpen] = useState(false);
  const toggleAddUserDrawer = () => setAddUserOpen(!addUserOpen);
  const [spsList, setSpsList] = useState([]);
  // Create/Edit dialog states
  const [openCreateDialog, setOpenCreateDialog] = useState(false);
  const [openEditDialog, setOpenEditDialog] = useState(false);

  const [societySelected, setSocietySelected] = useState("");
  const [serviceRequirement, setServiceRequirement] = useState("");

   // state to save scope of work data
   const [scopeData, setScopeData] = useState([]);

   //Payment section related states
  const [editablePaymentData, setEditablePaymentData] = useState([]);
  const [contractValue, setContractValue] = useState("");
  const [gstPercentage, setGstPercentage] = useState();
  const [newSectionData, setNewSectionData] = useState({});
  const [paymentData,setPaymentData] = useState([]);

  //Terms and conditions section related states
  const [selectedTemplate, setSelectedTemplate] = useState([]);
  const [editDescription, setEditDescription] = useState("");
  const [terms, setTerms] = useState([]);

    // State to manage document and file selections
  const [document, setDocument] = useState({});
  const [selectedFiles, setSelectedFiles] = useState([]);
  const [selectedDocument, setSelectedDocument] = useState(null);
  const [updatedFile, setUpdatedFile] = useState(false);
  const [location, setLocation] = useState(null);
  const [binaryText, setBinaryText] = useState();

  const [dialogMessage, setDialogMessage] = useState("");
  const [dialogSuccess, setDialogSuccess] = useState(false);
  const [openDeleteDialog, setOpenDeleteDialog] = useState(false);
  const [currentRow, setCurrentRow] = useState("");
  const [OpenViewDialog, setOpenViewDialog] = useState(false);
  const [employeesData, setEmployeesData] = useState([]);

  useEffect(() => {
    axios({
      method: "get",
      url: getUrl(authConfig.individualEndpoint) + "/employees",
      headers: getAuthorizationHeaders(authConfig.individualEmployeesMIMEType),
    })
      .then((res) => {
        setEmployeesData(res.data);
      })
      .catch((err) => console.log("error", err));
  }, []);

    // Function to close the document dialog
  const handleDialogClose = () => {
    setSelectedDocument(null);
  };

  const ClosehandleViewDialog = () => {
    setOpenViewDialog(false);
  };

   // useEffect to set filters based on query parameters
  useEffect(() => {
    const newFilters = [];
    if (id) {
      newFilters.push({ key: "idFilter", value: id });
    }
    if (signedWorkOrders) {
      newFilters.push({ key: "signedWorkOrders", value: signedWorkOrders });
    }
    if (currentWeekWorkOrders) {
      newFilters.push({
        key: "currentWeekSignedWorkOrders",
        value: currentWeekWorkOrders,
      });
    }
    setSelectedFilters(newFilters);
  }, [id, signedWorkOrders, currentWeekWorkOrders]);

   useEffect(() => {
      if (fromDate && toDate) {
  
        setSelectedFilters((prevFilters) => {
          const newFilters = [...prevFilters];
    
          newFilters.push({ key: "fromDate", value: fromDate });
          newFilters.push({ key: "toDate", value: toDate });
         
          if(employeeId){
            newFilters.push({ key: "assignedToEmployeeId", value: employeeId });
          }
    
          return newFilters;
        });
      }
    }, [fromDate, toDate,employeeId]);

    const clearFilterSR = () => {
      setSelectedFilters((prevFilters) =>
        prevFilters.filter((filter) => filter.key !== "fromDate")
      );
  
      setSelectedFilters((prevFilters) =>
        prevFilters.filter((filter) => filter.key !== "toDate")
      );
  
      setSelectedFilters((prevFilters) =>
        prevFilters.filter((filter) => filter.key !== "assignedToEmployeeId")
      );
  
      // Update the URL to remove the status query parameter
      router.replace(
        {
          // pathname: router.pathname,
          query: {}, // Clear all query parameters
        },
        undefined,
        { shallow: false } // Allow page reload if necessary
      );
    };

  const handleIdClearFilter = () => {
    // Clear the status filter from selected filters
    setSelectedFilters((prevFilters) =>
      prevFilters?.filter((filter) => filter.key !== "idFilter")
    );
    // Update the URL to remove the status query parameter
    router.replace(
      {
        query: {}, // Clear all query parameters
      },
      undefined,
      { shallow: false } // Allow page reload if necessary
    );
  };

    // Function to clear the work order filter
  const handleWorkOrderClearFilter = () => {
    // Clear the status filter from selected filters
    setSelectedFilters((prevFilters) =>
      prevFilters?.filter((filter) => filter.key !== "signedWorkOrders")
    );
    // Update the URL to remove the status query parameter
    router.replace(
      {
        query: {}, // Clear all query parameters
      },
      undefined,
      { shallow: false } // Allow page reload if necessary
    );
  };

   // Function to clear the current week filter
  const handleCurrentWeekClearFilter = () => {
    setSelectedFilters((prevFilters) =>
      prevFilters?.filter(
        (filter) => filter.key !== "currentWeekSignedWorkOrders"
      )
    );
    // Update the URL to remove the zone query parameter
    router.replace(
      {
        query: {}, // Clear all query parameters
      },
      undefined,
      { shallow: false } // Allow page reload if necessary
    );
  };

  // useEffect to set states based on work order details from get api call
  useEffect(() => {
    if (workOrderDetails) {
      setSocietySelected(workOrderDetails?.societyId);
      setServiceRequirement(workOrderDetails?.srId);
      setScopeData(workOrderDetails?.scopeOfWork);
      setEditablePaymentData(workOrderDetails?.paymentSchedule?.payment);
      setGstPercentage(workOrderDetails?.paymentSchedule?.gstPercentage);
      setContractValue(workOrderDetails?.paymentSchedule?.totalContractValue);
      setSelectedTemplate(
        workOrderDetails?.termsAndConditions?.selectedTemplates
      );
      setTerms(workOrderDetails?.termsAndConditions?.descriptions);
      setDocument({ 
        id: workOrderDetails?.fileLocation?.split("/")?.slice(-2, -1)?.[0],
        location: workOrderDetails?.fileLocation,
      });
      setNewSectionData({
        lastContactDate: workOrderDetails?.workOrderMetaData?.lastContactDate,
        nextFollowUpDate: workOrderDetails?.workOrderMetaData?.nextFollowUpDate,
        workStatus: workOrderDetails?.workOrderMetaData?.workStatus,
        workOrderStatus: workOrderDetails?.workOrderMetaData?.workOrderStatus, // fixed key typo here
      });
      setPaymentData({
        recurring: workOrderDetails?.paymentSchedule?.recurring,
        frequency: workOrderDetails?.paymentSchedule?.frequency,
        followUpFrequency: workOrderDetails?.paymentSchedule?.followUpFrequency,
        paymentTerms: workOrderDetails?.paymentSchedule?.paymentTerms, 
        remarks: workOrderDetails?.paymentSchedule?.remarks, 
        houzerPayment: workOrderDetails?.paymentSchedule?.houzerPayment, 
        renewalDate: workOrderDetails?.paymentSchedule?.renewalDate, 
        dateOfStart: workOrderDetails?.paymentSchedule?.dateOfStart, 
        dateOfFinish: workOrderDetails?.paymentSchedule?.dateOfFinish, 
      });
    } else {
      setSocietySelected("");
      setServiceRequirement("");
      setScopeData([]);
      setEditablePaymentData([]);
      setGstPercentage("");
      setContractValue("");
      setSelectedTemplate([]);
      setTerms([]);
      setDocument({});
      setNewSectionData({});
      setPaymentData({})
    }
  }, [workOrderDetails]);

   // useEffect to fetch the file when document location is set
  useEffect(() => {
    if (document?.location) {
      fetchFile(document,setBinaryText,getFileByLocation);
    }
  }, [document]);

    // Function to handle template change and fetch terms
  const handleTemplateChange = async (newValue) => {
    setSelectedTemplate(newValue);
    // Fetch terms for selected templates
    const newTerms = [];
    for (const template of newValue) {
      const templateId = template?.value;
      try {
        const response = await axios({
          method: "get",
          url:
            getUrl(authConfig.serviceRequisitionsEndpoint) +
            "/get-template-names-content/" +
            templateId,
          headers: getAuthorizationHeaders(
            authConfig.WORK_ORDERS_GET_TEMPLATE_CONTENT_RES_V1
          ),
        });
        const fetchedTerms = response?.data || [];
        newTerms.push(...fetchedTerms);
      } catch (err) {
        console.error(`Error fetching terms for templateId ${templateId}`, err);
      }
    }
    setTerms(newTerms);
  };

  const handleClose = () => {
    setDialogSuccess(false);
  };

  // Function to close the delete dialog and refetch work orders
  const handleCloseDeleteDialog = () => {
    setOpenDeleteDialog(false);
    fetchWorkOrders(page, pageSize, selectedFilters, setWorkOrdersList, setRowCount, setLoading, user)
  };

  const [dialogOpen, setDialogOpen] = useState(false);

  const handleCloses = () => {
    setDialogOpen(false);
    setCurrentRow(null);
  };

  // columns for datagrid
  const columns = useColumns({
    setLocation,
    setCurrentRow,
    currentRow,
    setOpenViewDialog,
    setOpenEditDialog,
    setOpenDeleteDialog,
    setDialogOpen,
  });

   // Effect to fetch society options
  useEffect(() => {
    axios({
      method: "get",
      url:
        getUrl(authConfig.serviceRequisitionsEndpoint) + "/get-society-names",
      headers: getAuthorizationHeaders(),
    })
      .then((res) => {
        setSocietyOptions(res.data);
      })
      .catch((err) => console.log("Categories error", err));
  }, []);

   // useEffect to fetch sr's based on selected society
  useEffect(() => {
    if (societySelected) {
      axios({
        method: "get",
        url:
          getUrl(authConfig.serviceRequisitionsEndpoint) +
          "/get-service-type-names/" +
          societySelected,
        headers: getAuthorizationHeaders(),
      })
        .then((res) => {
          setRequirements(res.data);
        })
        .catch((err) => console.log("Categories error", err));
    }
  }, [societySelected]);

   // Effect to fetch templates based on service type
  useEffect(() => {
    if (requisitionDataDetails?.serviceTypeId) {
      axios({
        method: "get",
        url:
          getUrl(authConfig.serviceRequisitionsEndpoint) +
          "/get-template-names/" +
          requisitionDataDetails?.serviceTypeId,
        headers: getAuthorizationHeaders(
          authConfig.WORK_ORDERS_GET_TEMPLATE_NAMES_RES_V1
        ),
      })
        .then((res) => {
          const transformedTemplatesList = res?.data?.map((template) => ({
            value: template?.id,
            key: template?.name,
          }));
          setTemplatesList(transformedTemplatesList);
        })
        .catch((err) => console.log("Templates error", err));
    }
  }, [requisitionDataDetails]);

  const handleOpenDialog = () => {
    const message = `Are you sure you want to download a PDF?`;
    setDialogMessage(message);
    setGeneratingPDF(true);
  };

  const handleClosePDFDialog = () => {
    setGeneratingPDF(false); // Close the dialog
  };

  const handleSuccess = () => {
    const message = `Work Order Updated Successfully`;
    setDialogMessage(message);
    setDialogSuccess(true);
  };
  const handleFailure = () => {
    const message = `Failed to Update work order`;
    setDialogMessage(message);
    setDialogSuccess(true);
  };

    // Function to handle form submission
  const onSubmit = () => {
    const fields = {
      srId: serviceRequirement,
      scopeOfWork: scopeData,
      paymentSchedule: {
        payment: editablePaymentData,
        totalContractValue: contractValue,
        gstPercentage: gstPercentage,
        recurring: paymentData?.recurring,
        frequency: paymentData?.frequency,
        followUpFrequency: paymentData?.followUpFrequency,
        paymentTerms: paymentData?.paymentTerms,
        remarks: paymentData?.remarks,
        houzerPayment: paymentData?.houzerPayment,
        renewalDate: paymentData?.renewalDate,
        dateOfStart: paymentData?.dateOfStart,
        dateOfFinish: paymentData?.dateOfFinish,
      },
      termsAndConditions: {
        selectedTemplates: selectedTemplate,
        descriptions: terms,
      },
      workOrderMetaData:{
        lastContactDate: newSectionData?.lastContactDate,
        nextFollowUpDate: newSectionData?.nextFollowUpDate, 
        workStatus: newSectionData?.workStatus,
        workOrderStatus: newSectionData?.workOrderStatus,
      }
    };

    handleSaveWorkOrder(fields,selectedFiles,setDialogMessage,setDialogSuccess,fetchWorkOrders,
      page, pageSize, selectedFilters, setWorkOrdersList, setRowCount, setLoading, user,postWorkOrder,handleCloseDialog)
  };

   // Effect to fetch work orders based on page, page size, and filters
  useEffect(() => {
    fetchWorkOrders(page, pageSize, selectedFilters, 
      setWorkOrdersList, setRowCount, setLoading, user)
  }, [page, pageSize, selectedFilters]);

   // Function to handle work order update
  const handleUpdate = () => {
    const fields = {
      srId: serviceRequirement,
      scopeOfWork: scopeData,
      paymentSchedule: {
        payment: editablePaymentData,
        totalContractValue: contractValue,
        gstPercentage: gstPercentage,
        recurring: paymentData?.recurring,
        frequency: paymentData?.frequency,
        followUpFrequency: paymentData?.followUpFrequency,
        paymentTerms: paymentData?.paymentTerms,
        remarks: paymentData?.remarks,
        houzerPayment: paymentData?.houzerPayment,
        renewalDate: paymentData?.renewalDate,
        dateOfStart: paymentData?.dateOfStart,
        dateOfFinish: paymentData?.dateOfFinish,
      },
      termsAndConditions: {
        selectedTemplates: selectedTemplate,
        descriptions: terms,
      },
      workOrderMetaData:{
        lastContactDate: newSectionData?.lastContactDate,
        nextFollowUpDate: newSectionData?.nextFollowUpDate, 
        workStatus: newSectionData?.workStatus,
        workOrderStatus: newSectionData?.workOrderStatus,
      }
    };
    handleUpdateWorkOrder(fields,workOrderDetails,
      handleFailure,handleSuccess,selectedFiles,setDialogMessage,
      setDialogSuccess,updatedFile,patchWorkOrderUpload,auth,fetchWorkOrders,
      page, pageSize, selectedFilters, setWorkOrdersList, setRowCount, setLoading, user,handleCloseDialog)
 
  }

    // Function to open the create dialog
  const handleCreateDialogOpen = () => {
    setWorkOrderDetails(null);
    setSocietySelected("");
    setOpenCreateDialog(true);
  };

  const handleCloseDialog = () => {
    reset();
    setOpenCreateDialog(false);
    setOpenEditDialog(false);
    setSocietySelected("");
    setServiceRequirement("");
    setScopeData([]);
    setEditablePaymentData([]);
    setSelectedTemplate([]);
    setTerms([]);
    setSelectedFiles([]);
    setContractValue("");
    setGstPercentage("");
    setUpdatedFile(false);
    setRequisitionDataDetails(null);
  };

   // Function to handle PDF generation
  const handlePDFGeneration = async () => { 
    const fields = {
      srId: serviceRequirement,
      scopeOfWork: scopeData,
      paymentSchedule: {
        payment: editablePaymentData,
        totalContractValue: contractValue,
        gstPercentage: gstPercentage,
      },
      termsAndConditions: {
        selectedTemplates: selectedTemplate,
        descriptions: terms,
      },
    };
    handleDownloadPDF(fields,setGeneratingPDF) 
  };
  const handleApplyFilters = (filters) => {
    setSelectedFilters(filters); // Set filters for chips
    setSearchingState(true); // Trigger search
  };
  // Function to remove a single filter
  const handleRemoveFilter = (filterKey) => {
    setSelectedFilters((prevFilters) =>
      prevFilters?.filter((filter) => filter.key !== filterKey)
    );
  };
  // Clear all filters
  const clearAllFilters = () => {
    setSelectedFilters([]);
    setSearchingState(false); // Reset search state if needed
  };

  return (
    <>
      <Box
        sx={{
          py: 3,
          px: 6,
          rowGap: 2,
          columnGap: 4,
          display: "flex",
          flexWrap: "wrap",
          alignItems: "center",
          justifyContent: "space-between",
        }}
      >
        <Grid container spacing={3} alignItems="center">
          <Grid item xs={12} sm={4} sx={{ textAlign: "flex-start" }}>
            <Typography variant="h6">Work Orders</Typography>
          </Grid>

          <Grid item xs={12} sm={8}>
            <Grid
              container
              spacing={2}
              alignItems="center"
              justifyContent="flex-end"
            >
              <Grid
                item
                sx={{
                  paddingTop: { xs: "15px", sm: "25px" },
                  mr: "6px",
                  ml: "6px",
                  marginTop: { xs: "0.5rem", sm: "0rem" },
                }}
              >
                <AdvancedSearch
                  open={addUserOpen}
                  toggle={toggleAddUserDrawer}
                  searchingState={searchingState}
                  setSearchingState={setSearchingState}
                  societyOptions={societyOptions}
                  selectedFilters={selectedFilters}
                  clearAllFilters={clearAllFilters}
                  onApplyFilters={handleApplyFilters}
                  spsList={spsList}
                  setSpsList={setSpsList}
                />
              </Grid>

              <Grid item xs="auto" sm="auto">
                {user.organisationCategory === "EMPLOYEE" && (
                  <Button
                    variant="contained"
                    onClick={handleCreateDialogOpen}
                    sx={{
                      mt: {
                        xs: 2,
                        sm: 0,
                      },
                    }}
                  >
                    Add Work Order
                  </Button>
                )}
              </Grid>
            </Grid>
          </Grid>
        </Grid>
      </Box>
      <Divider />
      <CardContent>
        <FilterChips
          selectedFilters={selectedFilters}
          onRemoveFilter={handleRemoveFilter}
          societyOptions={societyOptions}
          spsList={spsList}
        />
        {id && (
          <Chip
            label={<>Recently Uploaded Work Order by approved SP</>}
            color="primary"
            variant="outlined"
            sx={{ mb: 3 }}
            onDelete={handleIdClearFilter}
          />
        )}
        {currentWeekWorkOrders && (
          <Chip
            label={<> Signed Work Orders this week </>}
            color="primary"
            variant="outlined"
            sx={{ mb: 3 }}
            onDelete={handleCurrentWeekClearFilter}
          />
        )}
        {signedWorkOrders && (
          <Chip
            label={<>Signed Work Orders</>}
            color="primary"
            variant="outlined"
            sx={{ mb: 3 }}
            onDelete={handleWorkOrderClearFilter}
          />
        )}
          {(fromDate && toDate) && (
                         <Chip
                         label={
                           <>
                             <Typography
                               component="span"
                               sx={{ fontWeight: 'bold', color: 'primary.main' }}
                             >
        
                              
                               {(() => {
                              
                                if (employeeId) return `Consolidated Work Orders by ${
                                  employeesData?.find((item) => item.id === employeeId)?.name ||
                                  ""
                                }`;
                              else return `Total consolidated Work Orders from ${fromDate} to ${toDate}`
                              
                            })()}
                              
                             </Typography>{' '}
                           </>
                         }
                         color="primary"
                         variant="outlined"
                         sx={{ mb: 3 }}
                         onDelete={clearFilterSR}
                       />      
                        )}
        <WorkOrdersDataGrid
          workOrdersList={workOrdersList || []}
          columns={columns}
          page={page}
          pageSize={pageSize || 10}
          loading={loading}
          rowCount={rowCount}
          setPage={setPage}
          setPageSize={setPageSize}
          rowsPerPageOptions={rowsPerPageOptions}
        />
      </CardContent>
      <WorkOrderDialog
        open={openCreateDialog || openEditDialog}
        handleCloseDialog={handleCloseDialog}
        workOrderDetails={workOrderDetails}
        onSubmit={onSubmit}
        handleUpdate={handleUpdate}
        societyOptions={societyOptions}
        serviceRequirement={serviceRequirement}
        setServiceRequirement={setServiceRequirement}
        societySelected={societySelected}
        setSocietySelected={setSocietySelected}
        requirements={requirements}
        scopeData={scopeData}
        setScopeData={setScopeData}
        editablePaymentData={editablePaymentData}
        setEditablePaymentData={setEditablePaymentData}
        contractValue={contractValue}
        setContractValue={setContractValue}
        gstPercentage={gstPercentage}
        setGstPercentage={setGstPercentage}
        selectedTemplate={selectedTemplate}
        templatesList={templatesList}
        handleTemplateChange={handleTemplateChange}
        terms={terms}
        setTerms={setTerms}
        editDescription={editDescription}
        setEditDescription={setEditDescription}
        isMobile={isMobile}
        handleOpenDialog={handleOpenDialog}
        selectedFiles={selectedFiles}
        setSelectedFiles={setSelectedFiles}
        setUpdatedFile={setUpdatedFile}
        document={document}
        setDocument={setDocument}
        newSectionData={newSectionData}
        setNewSectionData={setNewSectionData}
        paymentData={paymentData}
        setPaymentData={setPaymentData}
      />
      <ViewSnapshotByLocation
        location={selectedDocument}
        setSelectedLocation={setSelectedDocument}
        onClose={handleDialogClose}
      />
      <ViewSnapshotByLocation
        location={location}
        setSelectedLocation={setLocation}
      />
      <DeActivateDialog
        open={openDeleteDialog}
        onClose={handleCloseDeleteDialog}
        data={currentRow}
      />
      <WorkOrdersViewDialog
        open={OpenViewDialog}
        onClose={ClosehandleViewDialog}
        handleOpenDialog={handleOpenDialog}
      />
      <SuccessDialog
        open={dialogSuccess}
        handleClose={handleClose}
        dialogMessage={dialogMessage}
      />
      <PDFGenerationDialog
        generatingPDF={generatingPDF}
        handleClosePDFDialog={handleClosePDFDialog}
        handlePDFGeneration={handlePDFGeneration}
        dialogMessage={dialogMessage}
      />
      <AnchorWorkOrders
        open={dialogOpen}
        onClose={handleCloses}
        currentRow={currentRow}
      />
    </>
  );
};

export default WorkOrderDetails;
